/**
 * Customer Service Bot Template (Beginner)
 * Demonstrates basic customer service with TEXT, BUTTONS, and HANDOFF nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  customerServiceBot: generateUUID(),
  serviceWelcome: generateUUID(),
  serviceOptions: generateUUID(),
  contactDetails: generateUUID(),
  businessHours: generateUUID(),
  locationInfo: generateUUID(),
  agentHandoff: generateUUID()
};

module.exports = {
  name: 'Customer Service Bot',
  description: 'Basic customer service bot with common support options',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.serviceWelcome,
      name: 'Service_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '👋 Hello! I\'m your customer service assistant. I\'m here to help with any questions or concerns you may have.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.serviceOptions,
      name: 'Service_Options',
      type: 'buttons',
      content: {
        type: 'buttons',
        text: 'How can I help you today?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📞 Contact Information', value: 'contact'},
          {id: generateUUID(), text: '⏰ Business Hours', value: 'hours'},
          {id: generateUUID(), text: '📍 Location & Directions', value: 'location'},
          {id: generateUUID(), text: '👤 Speak with Agent', value: 'agent'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.contactDetails,
      name: 'Contact_Details',
      type: 'text',
      content: {
        type: 'text',
        text: '📞 **Contact Information**\n\n• Phone: (*************\n• Email: <EMAIL>\n• Live Chat: Available on our website\n• Emergency Line: (555) 911-HELP\n\nOur team typically responds within 2 hours during business hours.',
        delay: 1000
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.businessHours,
      name: 'Business_Hours',
      type: 'text',
      content: {
        type: 'text',
        text: '⏰ **Business Hours**\n\n• Monday - Friday: 9:00 AM - 6:00 PM EST\n• Saturday: 10:00 AM - 4:00 PM EST\n• Sunday: Closed\n• Holiday Hours: Check our website\n\nEmergency Support: Available 24/7 for critical issues.',
        delay: 1000
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.locationInfo,
      name: 'Location_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '📍 **Our Location**\n\n123 Business Street\nSuite 456\nCity, State 12345\n\n🚗 Parking: Free visitor parking available\n🚌 Public Transit: Bus routes 15, 22, 45\n🚇 Metro: Downtown Station (0.3 miles)\n\nNeed directions? Use GPS: "123 Business St, City, State"',
        delay: 1000
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.agentHandoff,
      name: 'Agent_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '👤 I\'m connecting you with one of our customer service agents. Please hold on for just a moment while I find someone to assist you...',
        delay: 1000,
        reason: 'Customer requested human agent'
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.serviceWelcome, targetNodeId: nodeIds.serviceOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.contactDetails, condition: 'contact'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.businessHours, condition: 'hours'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.locationInfo, condition: 'location'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.agentHandoff, condition: 'agent'}
  ],
  variables: [],
  metadata: {
    totalNodes: 6,
    difficulty: 'beginner',
    estimatedSetupTime: 12,
    tags: ['customer-service', 'support', 'basic', 'contact-info'],
    nodeTypes: ['text', 'buttons', 'handoff'],
    description: 'Essential for any business. Provides key contact information and human handoff capability.'
  }
};

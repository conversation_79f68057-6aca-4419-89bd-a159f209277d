/**
 * Company Information Bot Template (Beginner)
 * Demonstrates company showcase with TEXT and BUTTONS nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  companyWelcome: generateUUID(),
  companyMenu: generateUUID(),
  aboutCompany: generateUUID(),
  ourTeam: generateUUID(),
  ourMission: generateUUID(),
  ourValues: generateUUID()
};

module.exports = {
  name: 'Company Information Bot',
  description: 'Basic company information bot with about us and contact details',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.companyWelcome,
      name: 'Company_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🏢 Welcome! I\'m here to tell you about our company and help you get to know us better.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.companyMenu,
      name: 'Company_Menu',
      type: 'buttons',
      content: {
        type: 'buttons',
        text: 'What would you like to know about us?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📖 About Our Company', value: 'about'},
          {id: generateUUID(), text: '👥 Our Team', value: 'team'},
          {id: generateUUID(), text: '🎯 Our Mission', value: 'mission'},
          {id: generateUUID(), text: '🏆 Our Values', value: 'values'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.aboutCompany,
      name: 'About_Company',
      type: 'text',
      content: {
        type: 'text',
        text: '📖 **About Our Company**\n\nFounded in 2010, we\'ve been serving customers with innovative solutions and exceptional service. Our company specializes in technology solutions that help businesses grow and succeed.\n\n• 13+ years of experience\n• 500+ satisfied clients\n• Award-winning customer service\n• Global presence in 15 countries',
        delay: 1500
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.ourTeam,
      name: 'Our_Team',
      type: 'text',
      content: {
        type: 'text',
        text: '👥 **Our Team**\n\nOur team consists of 150+ experienced professionals from diverse backgrounds, all committed to delivering the best possible service to our clients.\n\n• Expert developers and engineers\n• Dedicated customer success managers\n• Experienced business consultants\n• 24/7 support specialists',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.ourMission,
      name: 'Our_Mission',
      type: 'text',
      content: {
        type: 'text',
        text: '🎯 **Our Mission**\n\nTo empower businesses with innovative technology solutions while providing exceptional customer service. We believe in building long-term partnerships with our clients and helping them achieve their goals.\n\n"Innovation through collaboration, success through partnership."',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.ourValues,
      name: 'Our_Values',
      type: 'text',
      content: {
        type: 'text',
        text: '🏆 **Our Core Values**\n\n• **Innovation**: Constantly pushing boundaries\n• **Integrity**: Honest and transparent in all dealings\n• **Excellence**: Delivering quality in everything we do\n• **Collaboration**: Working together for success\n• **Customer Focus**: Your success is our priority',
        delay: 1500
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.companyWelcome, targetNodeId: nodeIds.companyMenu},
    {id: generateUUID(), sourceNodeId: nodeIds.companyMenu, targetNodeId: nodeIds.aboutCompany, condition: 'about'},
    {id: generateUUID(), sourceNodeId: nodeIds.companyMenu, targetNodeId: nodeIds.ourTeam, condition: 'team'},
    {id: generateUUID(), sourceNodeId: nodeIds.companyMenu, targetNodeId: nodeIds.ourMission, condition: 'mission'},
    {id: generateUUID(), sourceNodeId: nodeIds.companyMenu, targetNodeId: nodeIds.ourValues, condition: 'values'}
  ],
  variables: [],
  metadata: {
    totalNodes: 6,
    difficulty: 'beginner',
    estimatedSetupTime: 12,
    tags: ['company', 'information', 'about-us', 'corporate'],
    nodeTypes: ['text', 'buttons'],
    description: 'Essential for corporate websites. Showcases company information and builds trust with visitors.'
  }
};

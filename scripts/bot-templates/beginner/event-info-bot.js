/**
 * Event Information Bot Template (Beginner)
 * Demonstrates event information with TEXT and BUTTONS nodes
 */
const uuid = require('uuid')
function generateUUID() {
  return uuid.v4()
}

// Generate consistent UUIDs for this template
const nodeIds = {
  eventInformationBot: generateUUID(),
  eventWelcome: generateUUID(),
  eventTypes: generateUUID(),
  conferenceInfo: generateUUID(),
  webinarInfo: generateUUID(),
  workshopInfo: generateUUID(),
  specialEvents: generateUUID(),
  registrationOptions: generateUUID(),
  registrationInfo: generateUUID(),
  updatesInfo: generateUUID(),
  contactOrganizer: generateUUID()
};

module.exports = {
  name: 'Event Information Bot',
  description: 'Simple event information bot for conferences, webinars, and workshops',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.eventWelcome,
      name: 'Event_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🎉 Welcome to our event information center! I can help you learn about our upcoming events and how to participate.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.eventTypes,
      name: 'Event_Types',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What type of event are you interested in?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🎤 Conferences', value: 'conferences'},
          {id: generateUUID(), text: '💻 Webinars', value: 'webinars'},
          {id: generateUUID(), text: '🛠️ Workshops', value: 'workshops'},
          {id: generateUUID(), text: '🎪 Special Events', value: 'special'}
        ],
        storeInVariable: 'event_type'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.conferenceInfo,
      name: 'Conference_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '🎤 **Upcoming Conferences**\n\n• **Annual Tech Summit** - March 15-17, 2024\n  Location: Convention Center, Downtown\n  Topics: AI, Cloud Computing, Cybersecurity\n\n• **Business Innovation Conference** - June 8-10, 2024\n  Location: Grand Hotel, Business District\n  Topics: Leadership, Strategy, Digital Transformation\n\nFor registration and details, visit events.example.com',
        delay: 1500
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.webinarInfo,
      name: 'Webinar_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '💻 **Weekly Webinars**\n\n• **Tech Talks Tuesday** - Every Tuesday, 2 PM EST\n  Duration: 1 hour\n  Format: Live presentation + Q&A\n\n• **Business Insights Thursday** - Every Thursday, 3 PM EST\n  Duration: 45 minutes\n  Format: Expert panel discussion\n\nAll webinars are free and include recordings for registered attendees.',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workshopInfo,
      name: 'Workshop_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '🛠️ **Hands-on Workshops**\n\n• **Beginner Coding Workshop** - Monthly, First Saturday\n  Duration: 4 hours\n  Cost: $99\n  Includes: Materials, lunch, certificate\n\n• **Advanced Analytics Workshop** - Quarterly\n  Duration: 2 days\n  Cost: $299\n  Includes: Software license, materials, networking dinner',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.specialEvents,
      name: 'Special_Events',
      type: 'text',
      content: {
        type: 'text',
        text: '🎪 **Special Events**\n\n• **Annual Gala** - December 15, 2024\n  Black-tie networking event with awards ceremony\n\n• **Summer Picnic** - July 20, 2024\n  Family-friendly outdoor event with activities\n\n• **Holiday Party** - December 22, 2024\n  Celebrate the year with food, music, and prizes\n\nAll special events are invitation-only for members and partners.',
        delay: 1500
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.registrationOptions,
      name: 'Registration_Options',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'Would you like to register or get more information?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📝 Register Now', value: 'register'},
          {id: generateUUID(), text: '📧 Get Updates', value: 'updates'},
          {id: generateUUID(), text: '📞 Contact Organizer', value: 'contact'}
        ],
        storeInVariable: 'registration_action'
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.registrationInfo,
      name: 'Registration_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '📝 **Event Registration**\n\nVisit our registration portal at: register.example.com\n\n• Browse all upcoming events\n• Secure online payment\n• Instant confirmation\n• Calendar integration\n• Mobile tickets\n\nEarly bird discounts available for most events!',
        delay: 1000
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.updatesInfo,
      name: 'Updates_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '📧 **Stay Updated**\n\nSubscribe to our event newsletter:\n• Weekly event announcements\n• Early access to registration\n• Exclusive member discounts\n• Speaker spotlights\n\nSign up at: newsletter.example.com\nOr text "EVENTS" to (555) 123-NEWS',
        delay: 1000
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.contactOrganizer,
      name: 'Contact_Organizer',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: "📞 I'm connecting you with our event organizer who can answer specific questions and help with custom arrangements.'",
        delay: 1000,
        reason: 'Event inquiry - organizer assistance'
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.eventWelcome, targetNodeId: nodeIds.eventTypes},
    {id: generateUUID(), sourceNodeId: nodeIds.eventTypes, targetNodeId: nodeIds.conferenceInfo, condition: 'conferences'},
    {id: generateUUID(), sourceNodeId: nodeIds.eventTypes, targetNodeId: nodeIds.webinarInfo, condition: 'webinars'},
    {id: generateUUID(), sourceNodeId: nodeIds.eventTypes, targetNodeId: nodeIds.workshopInfo, condition: 'workshops'},
    {id: generateUUID(), sourceNodeId: nodeIds.eventTypes, targetNodeId: nodeIds.specialEvents, condition: 'special'},
    {id: generateUUID(), sourceNodeId: nodeIds.conferenceInfo, targetNodeId: nodeIds.registrationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.webinarInfo, targetNodeId: nodeIds.registrationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.workshopInfo, targetNodeId: nodeIds.registrationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.specialEvents, targetNodeId: nodeIds.registrationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.registrationOptions, targetNodeId: nodeIds.registrationInfo, condition: 'register'},
    {id: generateUUID(), sourceNodeId: nodeIds.registrationOptions, targetNodeId: nodeIds.updatesInfo, condition: 'updates'},
    {id: generateUUID(), sourceNodeId: nodeIds.registrationOptions, targetNodeId: nodeIds.contactOrganizer, condition: 'contact'}
  ],
  variables: [
    {
      name: 'event_type',
      type: 'string',
      description: 'Type of event user is interested in',
      required: false
    },
    {
      name: 'registration_action',
      type: 'string',
      description: 'User selected registration or information action',
      required: false
    }
  ],
  metadata: {
    totalNodes: 10,
    difficulty: 'beginner',
    estimatedSetupTime: 16,
    tags: ['events', 'conferences', 'information', 'registration'],
    nodeTypes: ['text', 'quick_reply', 'handoff'],
    description: 'Great for event organizers. Shows event types, registration options, and contact methods. All variables follow lowercase naming conventions and uses quick_reply nodes for user interactions.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

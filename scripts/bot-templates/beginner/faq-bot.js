/**
 * Basic FAQ Bot Template (Beginner)
 * Demonstrates TEXT and BUTTONS nodes with FAQ navigation
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  basicFaqBot: generateUUID(),
  faqWelcome: generateUUID(),
  faqCategories: generateUUID(),
  billingInfo: generateUUID(),
  accountInfo: generateUUID(),
  technicalInfo: generateUUID(),
  contactSupport: generateUUID()
};

module.exports = {
  name: 'Basic FAQ Bot',
  description: 'Simple FAQ bot demonstrating TEXT and BUTTONS nodes with clear navigation',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.faqWelcome,
      name: 'FAQ_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: 'Hi! Welcome to our FAQ Bot. I can help answer common questions about our services. What would you like to know?',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.faqCategories,
      name: 'FAQ_Categories',
      type: 'buttons',
      content: {
        type: 'buttons',
        text: 'Please select a category for your question:',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💳 Billing & Payments', value: 'billing'},
          {id: generateUUID(), text: '⚙️ Account Settings', value: 'account'},
          {id: generateUUID(), text: '🔧 Technical Support', value: 'technical'},
          {id: generateUUID(), text: '📞 Contact Support', value: 'contact'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.billingInfo,
      name: 'Billing_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '💳 **Billing & Payments FAQ**\n\n• Billing cycles run monthly from your signup date\n• Payments are processed automatically\n• You can view invoices in your account dashboard\n• For billing disputes, contact <EMAIL>\n\nNeed more help? Contact our billing team directly.',
        delay: 1500
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.accountInfo,
      name: 'Account_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '⚙️ **Account Settings FAQ**\n\n• Reset password: Use "Forgot Password" on login page\n• Update profile: Go to Settings > Profile\n• Team management: Settings > Team Members\n• Delete account: Contact support for assistance\n\nStill need help? Our support team is here for you.',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.technicalInfo,
      name: 'Technical_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '🔧 **Technical Support FAQ**\n\n• Check our documentation at docs.example.com\n• Common issues: Clear browser cache and cookies\n• API issues: Verify your API keys are correct\n• For complex technical issues, contact our tech team\n\nNeed immediate technical assistance? Contact support.',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.contactSupport,
      name: 'Contact_Support',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '📞 I\'m connecting you with our support team. They\'ll be able to provide personalized assistance. Please wait a moment...',
        delay: 1000,
        reason: 'User requested human support'
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.faqWelcome, targetNodeId: nodeIds.faqCategories},
    {id: generateUUID(), sourceNodeId: nodeIds.faqCategories, targetNodeId: nodeIds.billingInfo, condition: 'billing'},
    {id: generateUUID(), sourceNodeId: nodeIds.faqCategories, targetNodeId: nodeIds.accountInfo, condition: 'account'},
    {id: generateUUID(), sourceNodeId: nodeIds.faqCategories, targetNodeId: nodeIds.technicalInfo, condition: 'technical'},
    {id: generateUUID(), sourceNodeId: nodeIds.faqCategories, targetNodeId: nodeIds.contactSupport, condition: 'contact'}
  ],
  variables: [],
  metadata: {
    totalNodes: 6,
    difficulty: 'beginner',
    estimatedSetupTime: 12,
    tags: ['faq', 'support', 'basic', 'handoff'],
    nodeTypes: ['text', 'buttons', 'handoff'],
    description: 'Ideal for customer support. Demonstrates basic FAQ structure with human handoff option.'
  }
};

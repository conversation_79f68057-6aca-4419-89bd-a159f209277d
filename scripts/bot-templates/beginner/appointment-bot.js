/**
 * Appointment Booking Bot Template (Beginner)
 * Demonstrates basic appointment scheduling with TEXT and BUTTONS nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  appointmentBookingBot: generateUUID(),
  bookingWelcome: generateUUID(),
  appointmentTypes: generateUUID(),
  businessConsultation: generateUUID(),
  technicalSupport: generateUUID(),
  salesMeeting: generateUUID(),
  generalMeeting: generateUUID(),
  bookingOptions: generateUUID(),
  onlineBooking: generateUUID(),
  phoneBooking: generateUUID(),
  schedulerHandoff: generateUUID()
};

module.exports = {
  name: 'Appointment Booking Bot',
  description: 'Simple appointment booking bot with basic scheduling options',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.bookingWelcome,
      name: 'Booking_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '📅 Welcome to our appointment booking system! I\'ll help you schedule a meeting with our team.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.appointmentTypes,
      name: 'Appointment_Types',
      type: 'buttons',
      content: {
        type: 'buttons',
        text: 'What type of appointment would you like to schedule?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💼 Business Consultation', value: 'business'},
          {id: generateUUID(), text: '🛠️ Technical Support', value: 'technical'},
          {id: generateUUID(), text: '💰 Sales Meeting', value: 'sales'},
          {id: generateUUID(), text: '📋 General Meeting', value: 'general'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.businessConsultation,
      name: 'Business_Consultation',
      type: 'text',
      content: {
        type: 'text',
        text: '💼 **Business Consultation**\n\nDuration: 60 minutes\nAvailable: Monday-Friday, 9 AM - 5 PM\nWhat to expect: Strategic planning, business analysis, and recommendations\n\nTo book your consultation, please call (************* or visit our online calendar.',
        delay: 1500
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.technicalSupport,
      name: 'Technical_Support',
      type: 'text',
      content: {
        type: 'text',
        text: '🛠️ **Technical Support Session**\n\nDuration: 30-90 minutes\nAvailable: Monday-Friday, 8 AM - 6 PM\nWhat to expect: Technical troubleshooting, system setup, and training\n\nFor urgent technical issues, call our priority line: (555) 911-TECH',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.salesMeeting,
      name: 'Sales_Meeting',
      type: 'text',
      content: {
        type: 'text',
        text: '💰 **Sales Meeting**\n\nDuration: 45 minutes\nAvailable: Monday-Friday, 9 AM - 6 PM\nWhat to expect: Product demos, pricing discussion, and custom solutions\n\nReady to boost your business? Let\'s schedule your meeting today!',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.generalMeeting,
      name: 'General_Meeting',
      type: 'text',
      content: {
        type: 'text',
        text: '📋 **General Meeting**\n\nDuration: 30 minutes\nAvailable: Monday-Friday, 9 AM - 5 PM\nWhat to expect: Open discussion, Q&A, and general information\n\nPerfect for getting to know our services and exploring opportunities.',
        delay: 1500
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.bookingOptions,
      name: 'Booking_Options',
      type: 'buttons',
      content: {
        type: 'buttons',
        text: 'How would you like to complete your booking?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🌐 Online Calendar', value: 'online'},
          {id: generateUUID(), text: '📞 Call to Book', value: 'phone'},
          {id: generateUUID(), text: '👤 Speak with Scheduler', value: 'scheduler'}
        ]
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.onlineBooking,
      name: 'Online_Booking',
      type: 'text',
      content: {
        type: 'text',
        text: '🌐 **Online Booking**\n\nVisit our online calendar at: calendar.example.com\n\n1. Select your appointment type\n2. Choose available time slot\n3. Enter your contact information\n4. Receive confirmation email\n\nBooking available 24/7!',
        delay: 1000
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.phoneBooking,
      name: 'Phone_Booking',
      type: 'text',
      content: {
        type: 'text',
        text: '📞 **Call to Book**\n\nPhone: (*************\nHours: Monday-Friday, 8 AM - 6 PM\n\nOur friendly staff will help you find the perfect time slot and answer any questions about your upcoming appointment.',
        delay: 1000
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.schedulerHandoff,
      name: 'Scheduler_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '👤 I\'m connecting you with our scheduling specialist who can help you find the perfect appointment time and answer any questions.',
        delay: 1000,
        reason: 'Appointment scheduling assistance'
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.bookingWelcome, targetNodeId: nodeIds.appointmentTypes},
    {id: generateUUID(), sourceNodeId: nodeIds.appointmentTypes, targetNodeId: nodeIds.businessConsultation, condition: 'business'},
    {id: generateUUID(), sourceNodeId: nodeIds.appointmentTypes, targetNodeId: nodeIds.technicalSupport, condition: 'technical'},
    {id: generateUUID(), sourceNodeId: nodeIds.appointmentTypes, targetNodeId: nodeIds.salesMeeting, condition: 'sales'},
    {id: generateUUID(), sourceNodeId: nodeIds.appointmentTypes, targetNodeId: nodeIds.generalMeeting, condition: 'general'},
    {id: generateUUID(), sourceNodeId: nodeIds.businessConsultation, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.technicalSupport, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.salesMeeting, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.generalMeeting, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.bookingOptions, targetNodeId: nodeIds.onlineBooking, condition: 'online'},
    {id: generateUUID(), sourceNodeId: nodeIds.bookingOptions, targetNodeId: nodeIds.phoneBooking, condition: 'phone'},
    {id: generateUUID(), sourceNodeId: nodeIds.bookingOptions, targetNodeId: nodeIds.schedulerHandoff, condition: 'scheduler'}
  ],
  variables: [],
  metadata: {
    totalNodes: 10,
    difficulty: 'beginner',
    estimatedSetupTime: 18,
    tags: ['appointments', 'booking', 'scheduling', 'business'],
    nodeTypes: ['text', 'buttons', 'handoff'],
    description: 'Perfect for service businesses. Shows appointment types and multiple booking methods.'
  }
};

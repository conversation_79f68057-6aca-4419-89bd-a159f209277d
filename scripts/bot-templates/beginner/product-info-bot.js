/**
 * Product Information Bot Template (Beginner)
 * Demonstrates product showcase with TEXT and BUTTONS nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  productInformationBot: generateUUID(),
  productWelcome: generateUUID(),
  productMenu: generateUUID(),
  softwareDetails: generateUUID(),
  mobileDetails: generateUUID(),
  cloudDetails: generateUUID(),
  allProducts: generateUUID(),
  nextSteps: generateUUID(),
  contactSales: generateUUID()
};

module.exports = {
  name: 'Product Information Bot',
  description: 'Simple product information bot with basic navigation and product details',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.productWelcome,
      name: 'Product_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🛍️ Welcome! I\'m here to help you learn about our products. What would you like to explore?',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.productMenu,
      name: 'Product_Menu',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'Which product category interests you?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💻 Software Solutions', value: 'software'},
          {id: generateUUID(), text: '📱 Mobile Apps', value: 'mobile'},
          {id: generateUUID(), text: '☁️ Cloud Services', value: 'cloud'},
          {id: generateUUID(), text: '🎯 View All Products', value: 'all'}
        ],
        storeInVariable: 'product_category'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.softwareDetails,
      name: 'Software_Details',
      type: 'text',
      content: {
        type: 'text',
        text: '💻 **Software Solutions**\n\n• Enterprise Management Suite - $299/month\n• Project Management Tools - $99/month\n• Analytics Dashboard - $149/month\n• Custom Development - Contact for pricing\n\nAll software includes 24/7 support and regular updates. Would you like to schedule a demo?',
        delay: 1500
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.mobileDetails,
      name: 'Mobile_Details',
      type: 'text',
      content: {
        type: 'text',
        text: '📱 **Mobile Apps**\n\n• iOS Business App - $199/month\n• Android Enterprise App - $199/month\n• Cross-Platform Solution - $349/month\n• Custom Mobile Development - Contact us\n\nAll apps include app store optimization and maintenance. Ready to get started?',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.cloudDetails,
      name: 'Cloud_Details',
      type: 'text',
      content: {
        type: 'text',
        text: '☁️ **Cloud Services**\n\n• Basic Cloud Hosting - $49/month\n• Premium Cloud Suite - $149/month\n• Enterprise Cloud - $499/month\n• Custom Infrastructure - Contact us\n\nAll plans include 99.9% uptime guarantee and automatic backups. Need help choosing?',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.allProducts,
      name: 'All_Products',
      type: 'text',
      content: {
        type: 'text',
        text: '🎯 **Complete Product Portfolio**\n\nWe offer comprehensive business solutions including software, mobile apps, and cloud services. Our products are designed to work together seamlessly.\n\n📋 Download our complete product catalog\n📞 Schedule a consultation\n💬 Chat with our product specialists',
        delay: 1500
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.nextSteps,
      name: 'Next_Steps',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What would you like to do next?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📅 Schedule Demo', value: 'demo'},
          {id: generateUUID(), text: '💰 Get Pricing', value: 'pricing'},
          {id: generateUUID(), text: '📞 Talk to Sales', value: 'sales'}
        ],
        storeInVariable: 'next_action'
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.contactSales,
      name: 'Contact_Sales',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🎯 Perfect! I\'m connecting you with our sales team who can provide detailed information and help you choose the right solution.',
        delay: 1000,
        reason: 'Product inquiry - sales consultation'
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.productWelcome, targetNodeId: nodeIds.productMenu},
    {id: generateUUID(), sourceNodeId: nodeIds.productMenu, targetNodeId: nodeIds.softwareDetails, condition: 'software'},
    {id: generateUUID(), sourceNodeId: nodeIds.productMenu, targetNodeId: nodeIds.mobileDetails, condition: 'mobile'},
    {id: generateUUID(), sourceNodeId: nodeIds.productMenu, targetNodeId: nodeIds.cloudDetails, condition: 'cloud'},
    {id: generateUUID(), sourceNodeId: nodeIds.productMenu, targetNodeId: nodeIds.allProducts, condition: 'all'},
    {id: generateUUID(), sourceNodeId: nodeIds.softwareDetails, targetNodeId: nodeIds.nextSteps},
    {id: generateUUID(), sourceNodeId: nodeIds.mobileDetails, targetNodeId: nodeIds.nextSteps},
    {id: generateUUID(), sourceNodeId: nodeIds.cloudDetails, targetNodeId: nodeIds.nextSteps},
    {id: generateUUID(), sourceNodeId: nodeIds.allProducts, targetNodeId: nodeIds.nextSteps},
    {id: generateUUID(), sourceNodeId: nodeIds.nextSteps, targetNodeId: nodeIds.contactSales, condition: 'demo'},
    {id: generateUUID(), sourceNodeId: nodeIds.nextSteps, targetNodeId: nodeIds.contactSales, condition: 'pricing'},
    {id: generateUUID(), sourceNodeId: nodeIds.nextSteps, targetNodeId: nodeIds.contactSales, condition: 'sales'}
  ],
  variables: [
    {
      name: 'product_category',
      type: 'string',
      description: 'Product category user is interested in',
      required: false
    },
    {
      name: 'next_action',
      type: 'string',
      description: 'User selected next action (demo, pricing, sales)',
      required: false
    }
  ],
  metadata: {
    totalNodes: 8,
    difficulty: 'beginner',
    estimatedSetupTime: 15,
    tags: ['products', 'information', 'sales', 'navigation'],
    nodeTypes: ['text', 'quick_reply', 'handoff'],
    description: 'Great for showcasing products and services. Features linear navigation flow and sales handoff. All variables follow lowercase naming conventions and uses quick_reply nodes for user interactions.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

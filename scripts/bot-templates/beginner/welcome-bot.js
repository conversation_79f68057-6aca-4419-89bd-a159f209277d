/**
 * Simple Welcome Bot Template (Beginner)
 * Demonstrates basic TEXT and BUTTONS nodes with simple navigation
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  welcomeMessage: generateUUID(),
  mainMenu: generateUUID(),
  learnMore: generateUUID(),
  contactInfo: generateUUID(),
  helpOptions: generateUUID()
};

module.exports = {
  name: 'Simple Welcome Bot',
  description: 'Basic welcome bot with simple greeting and navigation options',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.welcomeMessage,
      name: 'Welcome_Message',
      type: 'text',
      content: {
        type: 'text',
        text: 'Hello! Welcome to our website. I\'m here to help you get started. How can I assist you today?',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.mainMenu,
      name: 'Main_Menu',
      type: 'buttons',
      content: {
        type: 'buttons',
        text: 'What would you like to do?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📖 Learn More', value: 'learn'},
          {id: generateUUID(), text: '📞 Contact Us', value: 'contact'},
          {id: generateUUID(), text: '❓ Get Help', value: 'help'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.learnMore,
      name: 'Learn_More',
      type: 'text',
      content: {
        type: 'text',
        text: 'Great! You can explore our website to learn about our products and services. Check out our features page or browse our blog for the latest updates.',
        delay: 1000
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.contactInfo,
      name: 'Contact_Info',
      type: 'text',
      content: {
        type: 'text',
        text: 'You can reach <NAME_EMAIL> or call us at (555) 123-4567. Our team is available Monday-Friday, 9 AM to 5 PM.',
        delay: 1000
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.helpOptions,
      name: 'Help_Options',
      type: 'text',
      content: {
        type: 'text',
        text: 'I\'d be happy to help! You can browse our FAQ section, check our documentation, or contact our support team for personalized assistance.',
        delay: 1000
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.welcomeMessage, targetNodeId: nodeIds.mainMenu},
    {id: generateUUID(), sourceNodeId: nodeIds.mainMenu, targetNodeId: nodeIds.learnMore, condition: 'learn'},
    {id: generateUUID(), sourceNodeId: nodeIds.mainMenu, targetNodeId: nodeIds.contactInfo, condition: 'contact'},
    {id: generateUUID(), sourceNodeId: nodeIds.mainMenu, targetNodeId: nodeIds.helpOptions, condition: 'help'}
  ],
  variables: [],
  metadata: {
    totalNodes: 5,
    difficulty: 'beginner',
    estimatedSetupTime: 10,
    tags: ['welcome', 'basic', 'navigation'],
    nodeTypes: ['text', 'buttons'],
    description: 'Perfect for first-time bot builders. Shows basic conversation flow with simple navigation.'
  }
};

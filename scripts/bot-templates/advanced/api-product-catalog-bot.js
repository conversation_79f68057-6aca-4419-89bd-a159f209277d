/**
 * API-driven Product Catalog Bot Template (Advanced)
 * Demonstrates real-time product search with API integration and dynamic pricing
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

const baseApi = process.env.NODE_ENV !== "production" ? "http://localhost:3001" : "https://example.cinstance.com";
// Generate consistent UUIDs for this template
const nodeIds = {
  apiDrivenProductCatalogBot: generateUUID(),
  catalogWelcome: generateUUID(),
  searchMethod: generateUUID(),
  productSearchForm: generateUUID(),
  categoryApiCall: generateUUID(),
  productSearchApi: generateUUID(),
  priceCheckApi: generateUUID(),
  productOutOfStock: generateUUID(),
  inventoryCheckApi: generateUUID(),
  dynamicPricingApi: generateUUID(),
  productActions: generateUUID(),
  salesSpecialistHandoff: generateUUID(),
  categoryActions: generateUUID(),
  pricingActions: generateUUID(),
  catalogApiKey: generateUUID(),
  customerId: generateUUID(),
  searchTerm: generateUUID(),
  productId: generateUUID(),
  resultsCount: generateUUID()
};

module.exports = {
  name: 'API-driven Product Catalog Bot',
  description: 'Real-time product search with API integration, dynamic pricing, and inventory management',
  isTemplate: true,
  
  nodes: [
    {
      id: nodeIds.catalogWelcome,
      name: 'Catalog_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🛒 Welcome to our smart product catalog! I can help you find products, check real-time pricing, and verify availability. What are you looking for today?',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.searchMethod,
      name: 'Search_Method',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'How would you like to search for products?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🔍 Search by Name', value: 'name'},
          {id: generateUUID(), text: '📂 Browse Categories', value: 'category'},
          {id: generateUUID(), text: '💰 Search by Price Range', value: 'price'},
          {id: generateUUID(), text: '🏷️ Search by SKU/Barcode', value: 'sku'},
          {id: generateUUID(), text: '⭐ Popular Products', value: 'popular'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.productSearchForm,
      name: 'Product_Search_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🔍 Let me help you find the perfect product:',
        delay: 1000,
        formFields: [
          {
            id: 'search_term',
            type: 'text',
            label: 'Product Name or Keywords',
            required: true
          },
          {
            id: 'category',
            type: 'select',
            label: 'Category (optional)',
            options: ['Electronics', 'Clothing', 'Home & Garden', 'Sports', 'Books', 'Toys', 'Health & Beauty'],
            required: false
          },
          {
            id: 'min_price',
            type: 'number',
            label: 'Minimum Price',
            required: false
          },
          {
            id: 'max_price',
            type: 'number',
            label: 'Maximum Price',
            required: false
          },
          {
            id: 'sort_by',
            type: 'select',
            label: 'Sort Results By',
            options: ['Relevance', 'Price: Low to High', 'Price: High to Low', 'Customer Rating', 'Newest First'],
            required: false
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.categoryApiCall,
      name: 'Category_API_Call',
      type: 'api_call',
      content: {
        type: 'api_call',
        url: `${baseApi}/categories`,
        method: 'GET',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{catalog_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '',
        responseVariable: 'category_data',
        arrayFieldName: 'categories',
        displayFields: ['name', 'product_count', 'description', 'featured'],
        templateConfig: {
          title: "Product Categories",
          description: "Browse our available categories",
          customTemplate: "🏷️ **Product Categories**\n\nBrowse our available categories:\n\n{{#each categories}}\n📂 **{{name}}**\n📊 {{product_count}} products\n📝 {{description}}\n{{#if featured}}⭐ Featured Category{{/if}}\n\n---\n{{/each}}\n\n🔍 Which category interests you?",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'name', label: 'Name', type: 'text'},
            {id: 'field-1', name: 'product_count', label: 'Product Count', type: 'text'},
            {id: 'field-2', name: 'description', label: 'Description', type: 'text'},
            {id: 'field-3', name: 'featured', label: 'Featured', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: [],
            loopVariables: [],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Category API
          sampleData: {
            total: 5,
            count: 5,
            categories: [
              {
                name: "Electronics",
                product_count: 1247,
                description: "Latest tech gadgets and devices",
                featured: true
              },
              {
                name: "Clothing",
                product_count: 892,
                description: "Fashion and apparel for all seasons",
                featured: false
              },
              {
                name: "Home & Garden",
                product_count: 634,
                description: "Everything for your home and outdoor space",
                featured: true
              },
              {
                name: "Sports & Outdoors",
                product_count: 456,
                description: "Gear for active lifestyles",
                featured: false
              },
              {
                name: "Books & Media",
                product_count: 789,
                description: "Books, movies, and digital content",
                featured: false
              }
            ]
          }
        }
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: true, // Self-contained - no need for next node
      validationErrors: []
    },
    {
      id: nodeIds.productSearchApi,
      name: 'Product_Search_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        url: `${baseApi}/products/search`,
        method: 'POST',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{catalog_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"query": "{{search_term}}", "category": "{{category}}", "price_min": "{{min_price}}", "price_max": "{{max_price}}", "sort_by": "{{sort_by}}", "page": 1, "pageSize": 5, "include_inventory": true, "include_pricing": true}',
        responseVariable: 'search_results',
        arrayFieldName: 'products',
        displayFields: ['name', 'price', 'stock', 'rating', 'description'],
        // Self-contained template configuration
        templateConfig: {
          title: "Search Results",
          description: "Found {{total_results}} products matching your criteria",
          customTemplate: "🎯 **Search Results**\n\nFound {{total_results}} products matching your criteria:\n\n{{#if query}}Search term: \"{{query}}\"{{/if}}\n{{#if category}}Category: {{category}}{{/if}}\n\n{{#each products}}\n📦 **{{name}}**\n💰 Price: ${{price}}\n📊 Stock: {{stock}} units\n⭐ Rating: {{rating}}/5 stars\n📝 {{description}}\n\n---\n{{/each}}\n\n📄 Showing {{results_count}} of {{total_results}} products",
          displayFields: [
            {id: 'field-0', name: 'name', label: 'Name', type: 'text'},
            {id: 'field-1', name: 'price', label: 'Price', type: 'text'},
            {id: 'field-2', name: 'stock', label: 'Stock', type: 'text'},
            {id: 'field-3', name: 'rating', label: 'Rating', type: 'text'},
            {id: 'field-4', name: 'description', label: 'Description', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: [],
            loopVariables: [],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Product Search API
          sampleData: {
            total_results: 24,
            search_term: "laptop",
            query: "laptop",
            category: "Electronics",
            results_count: 5,
            products: [
              {
                name: "MacBook Pro 16\"",
                price: "2499.99",
                stock: 12,
                rating: "4.8",
                description: "Powerful laptop for professionals"
              },
              {
                name: "Dell XPS 13",
                price: "1299.99",
                stock: 8,
                rating: "4.6",
                description: "Ultrabook with premium design"
              },
              {
                name: "HP Spectre x360",
                price: "1199.99",
                stock: 15,
                rating: "4.5",
                description: "Convertible laptop with touch screen"
              },
              {
                name: "Lenovo ThinkPad X1",
                price: "1899.99",
                stock: 6,
                rating: "4.7",
                description: "Business laptop with excellent keyboard"
              },
              {
                name: "ASUS ZenBook 14",
                price: "899.99",
                stock: 20,
                rating: "4.4",
                description: "Lightweight laptop for everyday use"
              }
            ]
          }
        }
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: true, // Self-contained - no need for next node
      validationErrors: []
    },
    {
      id: nodeIds.priceCheckApi,
      name: 'Price_Check_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        url: `${baseApi}/products/{{product_id}}/pricing`,
        method: 'GET',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{catalog_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '',
        responseVariable: 'pricing_data',
        arrayFieldName: 'pricing_tiers',
        displayFields: ['tier', 'price', 'discount', 'availability'],
        // Self-contained template configuration
        templateConfig: {
          title: "Product Pricing Information",
          description: "Current pricing and availability for {{product_name}}",
          customTemplate: "💰 **Product Pricing Information**\n\n📱 **{{product_name}}**\n💰 **Current Price**: ${{current_price}}\n{{#if discount_info}}💸 {{discount_info}}{{/if}}\n📦 **In Stock**: {{stock_quantity}} units\n🚚 **Shipping**: {{shipping_info}}\n⭐ **Rating**: {{rating}}/5 ({{review_count}} reviews)\n\n🎯 **Available Pricing Tiers**:\n{{#each pricing_tiers}}\n🏷️ **{{tier}}**\n💰 Price: ${{price}}\n💸 Discount: {{discount}}%\n📋 {{availability}}\n\n---\n{{/each}}\n\n🛒 Ready to add to cart or need more information?",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'tier', label: 'Tier', type: 'text'},
            {id: 'field-1', name: 'price', label: 'Price', type: 'text'},
            {id: 'field-2', name: 'discount', label: 'Discount', type: 'text'},
            {id: 'field-3', name: 'availability', label: 'Availability', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['product_name', 'current_price', 'discount_info', 'stock_quantity', 'shipping_info', 'rating', 'review_count'],
            loopVariables: ['pricing_tiers'],
            conditionalVariables: ['discount_info']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Price Check API
          sampleData: {
            product_name: "MacBook Pro 16\"",
            current_price: "2499.99",
            discount_info: "15% off - Limited time offer",
            stock_quantity: 12,
            shipping_info: "Free shipping on orders over $50",
            rating: "4.8",
            review_count: 1247,
            pricing_tiers: [
              {tier: "Standard", price: "2499.99", discount: "0", availability: "In Stock"},
              {tier: "Student", price: "2249.99", discount: "10", availability: "In Stock"},
              {tier: "Business", price: "2124.99", discount: "15", availability: "In Stock"},
              {tier: "Enterprise", price: "1999.99", discount: "20", availability: "Contact Sales"}
            ]
          }
        }
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false, // This leads to conditional logic
      validationErrors: []
    },
    {
      id: nodeIds.productOutOfStock,
      name: 'Product_Out_Of_Stock',
      type: 'conditional',
      content: {
        type: 'conditional',
        conditionVariable: 'stock_quantity',
        conditionOperator: 'equals',
        conditionValue: '0'
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.inventoryCheckApi,
      name: 'Inventory_Check_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        url: `${baseApi}/inventory/check?product_ids={{selected_product_ids}}&location={{customer_location}}&include_nearby=true`,
        method: 'GET',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{catalog_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '',
        responseVariable: 'inventory_data',
        arrayFieldName: 'locations',
        displayFields: ['store_name', 'stock_qty', 'distance', 'availability'],
        // Self-contained template configuration
        templateConfig: {
          title: "Inventory Check Results",
          description: "Available locations for your selected products",
          customTemplate: "📍 **Inventory Check Results**\n\nHere are the available locations for your selected products:\n\n{{#each locations}}\n🏪 **{{store_name}}**\n📦 Stock: {{stock_qty}} units\n📍 Distance: {{distance}} miles\n✅ {{availability}}\n---\n{{/each}}\n\n📞 Would you like to reserve items at any of these locations?",
          displayFields: [
            {id: 'field-0', name: 'store_name', label: 'Store Name', type: 'text'},
            {id: 'field-1', name: 'stock_qty', label: 'Stock Quantity', type: 'text'},
            {id: 'field-2', name: 'distance', label: 'Distance', type: 'text'},
            {id: 'field-3', name: 'availability', label: 'Availability', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: [],
            loopVariables: ['locations'],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Inventory Check API
          sampleData: {
            total: 4,
            count: 4,
            locations: [
              {store_name: "Downtown Store", stock_qty: 8, distance: "2.3", availability: "Available for pickup"},
              {store_name: "Mall Location", stock_qty: 12, distance: "5.7", availability: "Available for pickup"},
              {store_name: "Warehouse Outlet", stock_qty: 25, distance: "8.1", availability: "Available for pickup"},
              {store_name: "Airport Store", stock_qty: 3, distance: "12.4", availability: "Limited stock"}
            ]
          }
        }
      },
      position: {x: 500, y: 700},
      isStartNode: false,
      isEndNode: false, // This leads to pricing API
      validationErrors: []
    },
    {
      id: nodeIds.dynamicPricingApi,
      name: 'Dynamic_Pricing_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        url: `${baseApi}/pricing/calculate`,
        method: 'POST',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{catalog_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"customer_id": "{{customer_id}}", "product_ids": "{{cart_product_ids}}", "quantities": "{{cart_quantities}}", "promo_code": "{{promo_code}}", "location": "{{customer_location}}"}',
        responseVariable: 'pricing_calc',
        arrayFieldName: 'price_breakdown',
        displayFields: ['item_name', 'base_price', 'discount_amt', 'final_price'],
        // Self-contained template configuration
        templateConfig: {
          title: "Dynamic Pricing Calculation",
          description: "Personalized pricing breakdown for your order",
          customTemplate: "💰 **Dynamic Pricing Calculation**\n\nHere's your personalized pricing breakdown:\n\n{{#each price_breakdown}}\n📦 **{{item_name}}**\n💵 Base Price: ${{base_price}}\n💸 Discount: -${{discount_amt}}\n✨ **Final Price: ${{final_price}}**\n\n---\n{{/each}}\n\n🛒 Ready to proceed with these prices?",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'item_name', label: 'Item Name', type: 'text'},
            {id: 'field-1', name: 'base_price', label: 'Base Price', type: 'text'},
            {id: 'field-2', name: 'discount_amt', label: 'Discount Amount', type: 'text'},
            {id: 'field-3', name: 'final_price', label: 'Final Price', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: [],
            loopVariables: ['price_breakdown'],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Dynamic Pricing API
          sampleData: {
            total: 3,
            count: 3,
            price_breakdown: [
              {item_name: "MacBook Pro 16\"", base_price: "2499.99", discount_amt: "375.00", final_price: "2124.99"},
              {item_name: "USB-C Hub", base_price: "79.99", discount_amt: "8.00", final_price: "71.99"},
              {item_name: "Laptop Sleeve", base_price: "49.99", discount_amt: "5.00", final_price: "44.99"}
            ]
          }
        }
      },
      position: {x: 650, y: 700},
      isStartNode: false,
      isEndNode: false, // This leads to pricing actions
      validationErrors: []
    },
    {
      id: nodeIds.productActions,
      name: 'Product_Actions',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What would you like to do next?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🛒 Add to Cart', value: 'add_cart'},
          {id: generateUUID(), text: '📋 Product Details', value: 'details'},
          {id: generateUUID(), text: '📊 Compare Products', value: 'compare'},
          {id: generateUUID(), text: '🔍 Search Again', value: 'search'},
          {id: generateUUID(), text: '💬 Speak with Sales', value: 'sales'}
        ]
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    
    
    {
      id: nodeIds.categoryActions,
      name: 'Category_Actions',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What would you like to do next?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🔍 Search Products', value: 'search'},
          {id: generateUUID(), text: '💰 Check Pricing', value: 'pricing'},
          {id: generateUUID(), text: '🔄 Browse Again', value: 'browse'},
          {id: generateUUID(), text: '💬 Speak with Sales', value: 'sales'}
        ]
      },
      position: {x: 350, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    
    {
      id: nodeIds.pricingActions,
      name: 'Pricing_Actions',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'Ready to proceed with your order?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🛒 Add to Cart', value: 'cart'},
          {id: generateUUID(), text: '📊 Compare Options', value: 'compare'},
          {id: generateUUID(), text: '🔍 Search More', value: 'search'},
          {id: generateUUID(), text: '💬 Speak with Sales', value: 'sales'}
        ]
      },
      position: {x: 650, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.salesSpecialistHandoff,
      name: 'Sales_Specialist_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🛍️ I\'m connecting you with our product specialist who can provide detailed information, help with bulk orders, and assist with any special requirements.',
        delay: 1000,
        reason: 'Product consultation and sales assistance'
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    // Initial flow
    {id: generateUUID(), sourceNodeId: nodeIds.catalogWelcome, targetNodeId: nodeIds.searchMethod},
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.searchMethod,
      targetNodeId: nodeIds.productSearchForm,
      condition: 'name'
    },
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.searchMethod,
      targetNodeId: nodeIds.categoryApiCall,
      condition: 'category'
    },
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.searchMethod,
      targetNodeId: nodeIds.productSearchForm,
      condition: 'price'
    },
    
    // Self-contained API calls - most are end nodes, some continue to conditional logic
    {id: generateUUID(), sourceNodeId: nodeIds.productSearchForm, targetNodeId: nodeIds.productSearchApi},
    // categoryApiCall and productSearchApi are self-contained end nodes (no outgoing connections)
    
    // Price check API leads to conditional logic based on availability
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.priceCheckApi,
      targetNodeId: nodeIds.productActions,
      condition: 'available'
    },
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.priceCheckApi,
      targetNodeId: nodeIds.productOutOfStock,
      condition: 'unavailable'
    },
    
    // Inventory flow - self-contained API nodes
    {id: generateUUID(), sourceNodeId: nodeIds.productOutOfStock, targetNodeId: nodeIds.inventoryCheckApi},
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.inventoryCheckApi,
      targetNodeId: nodeIds.dynamicPricingApi,
      condition: 'inventory'
    },
    {id: generateUUID(), sourceNodeId: nodeIds.dynamicPricingApi, targetNodeId: nodeIds.pricingActions},
    
    // Action nodes to handoff
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.productActions,
      targetNodeId: nodeIds.salesSpecialistHandoff,
      condition: 'sales'
    },
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.categoryActions,
      targetNodeId: nodeIds.salesSpecialistHandoff,
      condition: 'sales'
    },
    {
      id: generateUUID(),
      sourceNodeId: nodeIds.pricingActions,
      targetNodeId: nodeIds.salesSpecialistHandoff,
      condition: 'sales'
    }
  ],
  variables: [
    {
      name: 'catalog_api_key',
      type: 'string',
      description: 'API key for product catalog service',
      required: true
    },
    {
      name: 'customer_id',
      type: 'string',
      description: 'Customer ID for personalized pricing',
      required: false
    },
    {
      name: 'search_term',
      type: 'string',
      description: 'Product search query',
      required: false
    },
    {
      name: 'min_price',
      type: 'number',
      description: 'Minimum price filter for product search',
      required: false
    },
    {
      name: 'max_price',
      type: 'number',
      description: 'Maximum price filter for product search',
      required: false
    },
    {
      name: 'sort_by',
      type: 'string',
      description: 'Sort order for product search results',
      required: false
    },
    {
      name: 'product_id',
      type: 'string',
      description: 'Selected product ID',
      required: false
    },
    {
      name: 'results_count',
      type: 'number',
      description: 'Number of search results',
      required: false
    },
    // Array-based variables (automatically created by the system)
    {
      name: 'products',
      type: 'array',
      description: 'Array of product search results',
      required: false
    },
    {
      name: 'categories',
      type: 'array',
      description: 'Array of product categories',
      required: false
    },
    {
      name: 'pricing_tiers',
      type: 'array',
      description: 'Array of pricing tier options',
      required: false
    },
    {
      name: 'locations',
      type: 'array',
      description: 'Array of store locations with inventory',
      required: false
    },
    {
      name: 'price_breakdown',
      type: 'array',
      description: 'Array of pricing calculation breakdown',
      required: false
    },
    {
      name: 'total_results',
      type: 'number',
      description: 'Total number of results found',
      required: false
    },
    {
      name: 'current_page',
      type: 'number',
      description: 'Current page number for pagination',
      required: false
    },
    {
      name: 'total_pages',
      type: 'number',
      description: 'Total number of pages available',
      required: false
    },
    {
      name: 'has_next_page',
      type: 'boolean',
      description: 'Whether there are more pages available',
      required: false
    },
    {
      name: 'query',
      type: 'string',
      description: 'Search query used for product search',
      required: false
    },
    {
      name: 'category',
      type: 'string',
      description: 'Selected product category',
      required: false
    },
    {
      name: 'product_name',
      type: 'string',
      description: 'Name of the selected product',
      required: false
    },
    {
      name: 'current_price',
      type: 'string',
      description: 'Current price of the product',
      required: false
    },
    {
      name: 'discount_info',
      type: 'string',
      description: 'Discount information for the product',
      required: false
    },
    {
      name: 'stock_quantity',
      type: 'number',
      description: 'Available stock quantity',
      required: false
    },
    {
      name: 'shipping_info',
      type: 'string',
      description: 'Shipping information for the product',
      required: false
    },
    {
      name: 'rating',
      type: 'number',
      description: 'Product rating',
      required: false
    },
    {
      name: 'review_count',
      type: 'number',
      description: 'Number of product reviews',
      required: false
    },
    {
      name: 'featured',
      type: 'boolean',
      description: 'Whether a category or product is featured',
      required: false
    }
  ],
  metadata: {
    totalNodes: 13,
    difficulty: 'advanced',
    estimatedSetupTime: 30,
    tags: ['api-integration', 'product-catalog', 'real-time-pricing', 'inventory', 'e-commerce', 'array-handling', 'self-contained-api'],
    nodeTypes: ['text', 'quick_reply', 'form', 'api_call', 'conditional', 'handoff'],
    description: 'Fully self-contained API node architecture with enterprise-grade product catalog, real-time integration, dynamic pricing, and inventory management. All API nodes include complete template configurations for built-in response formatting.',
    apiRequirements: {
      endpoints: [
        'GET /categories - Product category listing (self-contained)',
        'POST /products/search - Product search with filters (self-contained)',
        'GET /products/{id}/pricing - Real-time pricing data (conditional flow)',
        'GET /inventory/check - Multi-location inventory (self-contained)',
        'POST /pricing/calculate - Dynamic pricing calculation (self-contained)'
      ],
      authentication: 'Bearer token via catalog_api_key variable',
      responseFormat: 'JSON with array fields processed by built-in templates',
      timeout: '30 seconds with 3 retry attempts',
      displayLimits: 'Maximum 5 items per list, 5 fields per item (20 chars each)',
      architecture: 'Self-contained API nodes with templateConfig - no separate formatting nodes required'
    }
  }
};

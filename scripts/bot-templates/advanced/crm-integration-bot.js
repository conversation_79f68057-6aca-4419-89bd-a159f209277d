/**
 * Advanced CRM Integration Bot Template (Advanced)
 * Demonstrates API_CALL, complex CONDITIONAL logic, and advanced routing
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  advancedCrmIntegrationBot: generateUUID(),
  crmWelcome: generateUUID(),
  emailLookup: generateUUID(),
  crmApiLookup: generateUUID(),
  existingCustomer: generateUUID(),
  newLeadQualification: generateUUID(),
  leadScoringApi: generateUUID(),
  createCrmLead: generateUUID(),
  highPriorityRouting: generateUUID(),
  mediumPriorityRouting: generateUUID(),
  enterpriseSupport: generateUUID(),
  customerTier: generateUUID(),
  leadScore: generateUUID(),
  crmApiKey: generateUUID(),
  accountStatus: generateUUID(),
  leadId: generateUUID()
};

module.exports = {
  name: 'Advanced CRM Integration Bot',
  description: 'Complex lead management with CRM API integration and advanced routing',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.crmWelcome,
      name: 'CRM_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🚀 Welcome! I\'m your intelligent sales assistant. I can help qualify leads, check existing customer status, and route you to the right team member.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.emailLookup,
      name: 'Email_Lookup',
      type: 'form',
      content: {
        type: 'form',
        text: 'Let me check if you\'re already in our system. Please provide your email address:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'email',
            label: 'Email Address',
            placeholder: 'Enter your email address',
            required: true
          }
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.crmApiLookup,
      name: 'CRM_API_Lookup',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'GET',
        url: 'https://api.crm.example.com/contacts/lookup',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{crm_api_key}}',
          'Content-Type': 'application/json'
        },
        queryParams: {
          'email': '{{email}}'
        },
        body: '',
        responseVariable: 'lookup_status',
        arrayFieldName: 'customer_data',
        displayFields: ['customer_tier', 'account_status', 'last_activity', 'total_value'],
        // Self-contained template configuration
        templateConfig: {
          title: "Customer Lookup Results",
          description: "Customer information retrieved from CRM system",
          customTemplate: "🔍 **Customer Lookup**\n{{#if customer_found}}✅ **Welcome back, valued customer!**\n\n👤 **Customer Profile**:\n• **Tier**: {{customer_tier}}\n• **Account Status**: {{account_status}}\n• **Last Activity**: {{last_activity}}\n• **Total Value**: ${{total_value}}\n• **Member Since**: {{member_since}}\n\n🎯 I'll connect you with the right team based on your profile.{{else}}📋 **New Customer Detected**\n\nI don't see you in our system yet, but that's great! I'd love to learn more about your needs and get you set up with the perfect solution.\n\nLet me gather some information to ensure you get the best possible experience.{{/if}}",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'customer_tier', label: 'Customer Tier', type: 'text'},
            {id: 'field-1', name: 'account_status', label: 'Account Status', type: 'text'},
            {id: 'field-2', name: 'last_activity', label: 'Last Activity', type: 'text'},
            {id: 'field-3', name: 'total_value', label: 'Total Value', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['customer_tier', 'account_status', 'last_activity', 'total_value', 'member_since'],
            loopVariables: ['customer_data'],
            conditionalVariables: ['customer_found']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for CRM Customer Lookup API
          sampleData: {
            customer_found: true,
            customer_tier: "Enterprise",
            account_status: "Active",
            last_activity: "2024-06-05",
            total_value: "125,000",
            member_since: "2022-03-15",
            customer_data: [
              { customer_tier: "Enterprise", account_status: "Active", last_activity: "2024-06-05", total_value: "125,000" }
            ]
          }
        }
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.existingCustomer,
      name: 'Existing_Customer',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Welcome back! I see you\'re already a valued customer.',
        delay: 1000,
        conditions: [
          {
            type: 'equals',
            field: 'customer_tier',
            value: 'enterprise',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'customer_tier',
            value: 'premium',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'account_status',
            value: 'at_risk',
            operator: 'and'
          }
        ],
        defaultNextNodeId: 'standard-support'
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.newLeadQualification,
      name: 'New_Lead_Qualification',
      type: 'form',
      content: {
        type: 'form',
        text: 'Great! I\'d love to learn more about your needs. Please share some details:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'text',
            label: 'Company Name',
            placeholder: 'Enter your company name',
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Company Size',
            options: ['1-10', '11-50', '51-200', '201-1000', '1000+'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Budget Range',
            options: ['Under $1K', '$1K-$5K', '$5K-$25K', '$25K-$100K', '$100K+'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Implementation Timeline',
            options: ['Immediate', '1-3 months', '3-6 months', '6+ months'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Industry',
            options: ['Technology', 'Healthcare', 'Finance', 'Manufacturing', 'Retail', 'Other'],
            required: false
          }
        ]
      },
      position: {x: 350, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.leadScoringApi,
      name: 'Lead_Scoring_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.crm.example.com/leads/score',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{crm_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"company_size": "{{company_size}}", "budget_range": "{{budget_range}}", "timeline": "{{timeline}}", "industry": "{{industry}}"}',
        responseVariable: 'lead_score',
        arrayFieldName: 'scoring_factors',
        displayFields: ['factor_name', 'score_value', 'weight', 'impact'],
        // Self-contained template configuration
        templateConfig: {
          title: "Lead Qualification Score",
          description: "AI-powered lead scoring based on qualification criteria",
          customTemplate: "🎯 **Lead Qualification Complete**\n\n📊 **Your Lead Score**: {{total_score}}/100\n🏆 **Priority Level**: {{priority_level}}\n📈 **Qualification Status**: {{qualification_status}}\n\n💡 **Scoring Breakdown**:\n{{#each scoring_factors}}\n• **{{factor_name}}**: {{score_value}} points ({{impact}} impact)\n{{/each}}\n\n🚀 **Next Steps**:\n{{#if high_priority}}I'm connecting you with our senior sales team for immediate assistance!{{else if medium_priority}}I'll route you to one of our experienced sales representatives.{{else}}I'll create your profile and our team will follow up within 24 hours.{{/if}}",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'factor_name', label: 'Scoring Factor', type: 'text'},
            {id: 'field-1', name: 'score_value', label: 'Score Value', type: 'text'},
            {id: 'field-2', name: 'weight', label: 'Weight', type: 'text'},
            {id: 'field-3', name: 'impact', label: 'Impact Level', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['total_score', 'priority_level', 'qualification_status'],
            loopVariables: ['scoring_factors'],
            conditionalVariables: ['high_priority', 'medium_priority']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Lead Scoring API
          sampleData: {
            total_score: 85,
            priority_level: "High",
            qualification_status: "Qualified",
            high_priority: true,
            medium_priority: false,
            scoring_factors: [
              { factor_name: "Company Size", score_value: 25, weight: "High", impact: "Major" },
              { factor_name: "Budget Range", score_value: 20, weight: "High", impact: "Major" },
              { factor_name: "Timeline", score_value: 15, weight: "Medium", impact: "Moderate" },
              { factor_name: "Industry Match", score_value: 15, weight: "Medium", impact: "Moderate" },
              { factor_name: "Decision Authority", score_value: 10, weight: "Low", impact: "Minor" }
            ]
          }
        }
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.createCrmLead,
      name: 'Create_CRM_Lead',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.crm.example.com/leads',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{crm_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"email": "{{email}}", "company_name": "{{company_name}}", "company_size": "{{company_size}}", "budget_range": "{{budget_range}}", "timeline": "{{timeline}}", "industry": "{{industry}}", "source": "chatbot", "status": "new", "lead_score": "{{lead_score}}"}',
        responseVariable: 'lead_creation',
        arrayFieldName: 'next_steps',
        displayFields: ['step_name', 'description', 'timeline', 'assigned_to'],
        // Self-contained template configuration
        templateConfig: {
          title: "Lead Profile Created",
          description: "New lead successfully created in CRM system",
          customTemplate: "✅ **Profile Created Successfully!**\n\n🆔 **Lead Details**:\n• **Lead ID**: {{lead_id}}\n• **Priority Score**: {{final_score}}/100\n• **Status**: {{lead_status}}\n• **Assigned To**: {{assigned_rep}}\n• **Created**: {{created_date}}\n\n📋 **Next Steps**:\n{{#each next_steps}}\n{{@index}}. **{{step_name}}** ({{timeline}})\n   {{description}}\n   *Assigned to: {{assigned_to}}*\n\n{{/each}}🎯 **What Happens Next**:\nOur team will review your information and contact you within {{contact_timeline}}. You'll receive a confirmation email shortly with your lead reference number.\n\n📞 **Need immediate assistance?** Call us at {{support_phone}} and reference Lead ID: {{lead_id}}",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'step_name', label: 'Next Step', type: 'text'},
            {id: 'field-1', name: 'description', label: 'Description', type: 'text'},
            {id: 'field-2', name: 'timeline', label: 'Timeline', type: 'text'},
            {id: 'field-3', name: 'assigned_to', label: 'Assigned To', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['lead_id', 'final_score', 'lead_status', 'assigned_rep', 'created_date', 'contact_timeline', 'support_phone'],
            loopVariables: ['next_steps'],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Create CRM Lead API
          sampleData: {
            lead_id: "LEAD-2024-001247",
            final_score: 85,
            lead_status: "New - High Priority",
            assigned_rep: "Sarah Johnson",
            created_date: "2024-06-08",
            contact_timeline: "2 business hours",
            support_phone: "(*************",
            next_steps: [
              { step_name: "Initial Contact", description: "Senior sales rep will call within 2 hours", timeline: "2 hours", assigned_to: "Sarah Johnson" },
              { step_name: "Needs Assessment", description: "Detailed discovery call to understand requirements", timeline: "1-2 days", assigned_to: "Sarah Johnson" },
              { step_name: "Solution Proposal", description: "Custom proposal based on needs assessment", timeline: "3-5 days", assigned_to: "Solutions Team" },
              { step_name: "Demo Scheduling", description: "Live product demonstration", timeline: "1 week", assigned_to: "Technical Team" }
            ]
          }
        }
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.highPriorityRouting,
      name: 'High_Priority_Routing',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🎯 Excellent! Based on your profile, I\'m connecting you directly with one of our senior sales specialists for priority assistance.',
        delay: 1000,
        reason: 'High-value lead - priority routing'
      },
      position: {x: 50, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.mediumPriorityRouting,
      name: 'Medium_Priority_Routing',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '💼 Great! I\'m connecting you with one of our experienced sales representatives who can help you explore our solutions.',
        delay: 1000,
        reason: 'Medium-value lead - standard sales routing'
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.enterpriseSupport,
      name: 'Enterprise_Support',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🏢 Welcome back! I\'m connecting you with your dedicated enterprise account manager.',
        delay: 1000,
        reason: 'Enterprise customer - dedicated account manager'
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.crmWelcome, targetNodeId: nodeIds.emailLookup},
    {id: generateUUID(), sourceNodeId: nodeIds.emailLookup, targetNodeId: nodeIds.crmApiLookup},
    {id: generateUUID(), sourceNodeId: nodeIds.crmApiLookup, targetNodeId: nodeIds.existingCustomer, condition: 'found'},
    {id: generateUUID(), sourceNodeId: nodeIds.crmApiLookup, targetNodeId: nodeIds.newLeadQualification, condition: 'not_found'},
    {id: generateUUID(), sourceNodeId: nodeIds.existingCustomer, targetNodeId: nodeIds.enterpriseSupport, condition: 'enterprise'},
    {id: generateUUID(), sourceNodeId: nodeIds.newLeadQualification, targetNodeId: nodeIds.leadScoringApi},
    {id: generateUUID(), sourceNodeId: nodeIds.leadScoringApi, targetNodeId: nodeIds.highPriorityRouting, condition: 'high_score'},
    {id: generateUUID(), sourceNodeId: nodeIds.leadScoringApi, targetNodeId: nodeIds.mediumPriorityRouting, condition: 'medium_score'},
    {id: generateUUID(), sourceNodeId: nodeIds.leadScoringApi, targetNodeId: nodeIds.createCrmLead, condition: 'standard'}
  ],
  variables: [
    {
      name: 'crm_api_key',
      type: 'string',
      description: 'CRM API authentication key',
      required: true
    },
    {
      name: 'email',
      type: 'string',
      description: 'Customer email address',
      required: false
    },
    {
      name: 'customer_tier',
      type: 'string',
      description: 'Customer tier from CRM lookup',
      required: false
    },
    {
      name: 'account_status',
      type: 'string',
      description: 'Current account status',
      required: false
    },
    {
      name: 'company_name',
      type: 'string',
      description: 'Company name from lead qualification',
      required: false
    },
    {
      name: 'company_size',
      type: 'string',
      description: 'Company size category',
      required: false
    },
    {
      name: 'budget_range',
      type: 'string',
      description: 'Budget range for the project',
      required: false
    },
    {
      name: 'timeline',
      type: 'string',
      description: 'Implementation timeline',
      required: false
    },
    {
      name: 'industry',
      type: 'string',
      description: 'Company industry',
      required: false
    },
    {
      name: 'lead_score',
      type: 'number',
      description: 'Calculated lead score from CRM',
      required: false
    },
    {
      name: 'lead_id',
      type: 'string',
      description: 'Generated lead ID from CRM',
      required: false
    },
    {
      name: 'lookup_status',
      type: 'string',
      description: 'Status result from CRM lookup',
      required: false
    },
    // Array-based variables (automatically created by the system)
    {
      name: 'customer_data',
      type: 'array',
      description: 'Array of customer profile data',
      required: false
    },
    {
      name: 'scoring_factors',
      type: 'array',
      description: 'Array of lead scoring factors and weights',
      required: false
    },
    {
      name: 'next_steps',
      type: 'array',
      description: 'Array of next steps for lead follow-up',
      required: false
    },
    // Additional variables for template rendering
    {
      name: 'customer_found',
      type: 'boolean',
      description: 'Whether customer was found in CRM system',
      required: false
    },
    {
      name: 'last_activity',
      type: 'string',
      description: 'Date of last customer activity',
      required: false
    },
    {
      name: 'total_value',
      type: 'string',
      description: 'Total customer value',
      required: false
    },
    {
      name: 'member_since',
      type: 'string',
      description: 'Customer membership start date',
      required: false
    },
    {
      name: 'total_score',
      type: 'number',
      description: 'Total lead qualification score',
      required: false
    },
    {
      name: 'priority_level',
      type: 'string',
      description: 'Lead priority level (High/Medium/Low)',
      required: false
    },
    {
      name: 'qualification_status',
      type: 'string',
      description: 'Lead qualification status',
      required: false
    },
    {
      name: 'high_priority',
      type: 'boolean',
      description: 'Whether lead is high priority',
      required: false
    },
    {
      name: 'medium_priority',
      type: 'boolean',
      description: 'Whether lead is medium priority',
      required: false
    },
    {
      name: 'final_score',
      type: 'number',
      description: 'Final calculated lead score',
      required: false
    },
    {
      name: 'lead_status',
      type: 'string',
      description: 'Current lead status',
      required: false
    },
    {
      name: 'assigned_rep',
      type: 'string',
      description: 'Assigned sales representative',
      required: false
    },
    {
      name: 'created_date',
      type: 'string',
      description: 'Lead creation date',
      required: false
    },
    {
      name: 'contact_timeline',
      type: 'string',
      description: 'Expected contact timeline',
      required: false
    },
    {
      name: 'support_phone',
      type: 'string',
      description: 'Support phone number',
      required: false
    }
  ],
  metadata: {
    totalNodes: 9,
    difficulty: 'advanced',
    estimatedSetupTime: 35,
    tags: ['crm-integration', 'lead-qualification', 'customer-lookup', 'lead-scoring', 'advanced-routing', 'self-contained-api'],
    nodeTypes: ['text', 'form', 'api_call', 'conditional', 'handoff'],
    description: 'Fully self-contained API node architecture with enterprise-grade CRM integration, intelligent lead scoring, customer lookup, and advanced routing. All API nodes include complete template configurations for built-in response formatting.',
    apiRequirements: {
      endpoints: [
        'GET /contacts/lookup - Customer lookup by email (self-contained)',
        'POST /leads/score - AI-powered lead scoring (self-contained)',
        'POST /leads - Lead creation with next steps (self-contained)'
      ],
      authentication: 'Bearer token via crm_api_key variable',
      responseFormat: 'JSON with array fields processed by built-in templates',
      timeout: '30 seconds with 3 retry attempts',
      displayLimits: 'Maximum 5 items per list, 4 fields per item (20 chars each)',
      architecture: 'Self-contained API nodes with templateConfig - no separate formatting nodes required'
    }
  }
};

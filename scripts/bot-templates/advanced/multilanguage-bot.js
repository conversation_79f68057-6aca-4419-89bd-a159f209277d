/**
 * Multi-language Support Bot Template (Advanced)
 * Demonstrates language detection, API translation, and conditional routing
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  multiLanguageSupportBot: generateUUID(),
  languageWelcome: generateUUID(),
  languageSelection: generateUUID(),
  languageDetectionApi: generateUUID(),
  englishWelcome: generateUUID(),
  spanishWelcome: generateUUID(),
  frenchWelcome: generateUUID(),
  germanWelcome: generateUUID(),
  serviceMenuMultilang: generateUUID(),
  englishServices: generateUUID(),
  spanishServices: generateUUID(),
  translationApiCall: generateUUID(),
  languageSpecialistRouting: generateUUID(),
  spanishSpecialist: generateUUID(),
  frenchSpecialist: generateUUID(),
  germanSpecialist: generateUUID(),
  englishSpecialist: generateUUID(),
  selectedLanguage: generateUUID(),
  translationApiKey: generateUUID(),
  detectedLanguage: generateUUID(),
  translationConfidence: generateUUID()
};

module.exports = {
  name: 'Multi-language Support Bot',
  description: 'Advanced multi-language support with automatic translation and language-specific routing',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.languageWelcome,
      name: 'Language_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🌍 Welcome! | ¡Bienvenido! | Bienvenue! | Willkommen! | 欢迎! | ようこそ!\n\nI can assist you in multiple languages. Please select your preferred language.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.languageSelection,
      name: 'Language_Selection',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'Please choose your language / Elige tu idioma / Choisissez votre langue:',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🇺🇸 English', value: 'en'},
          {id: generateUUID(), text: '🇪🇸 Español', value: 'es'},
          {id: generateUUID(), text: '🇫🇷 Français', value: 'fr'},
          {id: generateUUID(), text: '🇩🇪 Deutsch', value: 'de'},
          {id: generateUUID(), text: '🇨🇳 中文', value: 'zh'},
          {id: generateUUID(), text: '🌐 Auto-Detect', value: 'auto'}
        ],
        storeInVariable: 'selected_language'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.languageDetectionApi,
      name: 'Language_Detection_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.translate.example.com/detect',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{translation_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"text": "{{user_input}}", "confidence_threshold": 0.8}',
        responseVariable: 'language_detection',
        arrayFieldName: 'language_candidates',
        displayFields: ['language_code', 'language_name', 'confidence_score', 'region'],
        // Self-contained template configuration
        templateConfig: {
          title: "Language Detection Results",
          description: "Automatic language detection and confidence analysis",
          customTemplate: "🌍 **Language Detection Complete**\n\n🎯 **Primary Language**: {{primary_language}} ({{primary_language_name}})\n📊 **Confidence Score**: {{confidence_percentage}}%\n🌐 **Region**: {{detected_region}}\n\n🔍 **Detection Analysis**:\n{{#each language_candidates}}\n• **{{language_name}}** ({{language_code}})\n  - Confidence: {{confidence_score}}%\n  - Region: {{region}}\n\n{{/each}}{{#if high_confidence}}✅ **High Confidence Detection**\n\nI'm confident about your language preference! I'll continue our conversation in {{primary_language_name}}.{{else if medium_confidence}}⚠️ **Medium Confidence Detection**\n\nI detected {{primary_language_name}} but with moderate confidence. If this is incorrect, please select your preferred language manually.{{else}}❓ **Low Confidence Detection**\n\nI'm having difficulty detecting your language automatically. Please select your preferred language from the options below for the best experience.{{/if}}\n\n🚀 **Next Steps**: Continuing in {{primary_language_name}}...",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'language_code', label: 'Language Code', type: 'text'},
            {id: 'field-1', name: 'language_name', label: 'Language Name', type: 'text'},
            {id: 'field-2', name: 'confidence_score', label: 'Confidence Score', type: 'text'},
            {id: 'field-3', name: 'region', label: 'Region', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['primary_language', 'primary_language_name', 'confidence_percentage', 'detected_region'],
            loopVariables: ['language_candidates'],
            conditionalVariables: ['high_confidence', 'medium_confidence']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Language Detection API
          sampleData: {
            primary_language: "es",
            primary_language_name: "Spanish",
            confidence_percentage: 94.8,
            detected_region: "Latin America",
            high_confidence: true,
            medium_confidence: false,
            language_candidates: [
              { language_code: "es", language_name: "Spanish", confidence: 94.8, region: "Latin America" },
              { language_code: "pt", language_name: "Portuguese", confidence: 12.3, region: "Brazil" },
              { language_code: "en", language_name: "English", confidence: 8.1, region: "United States" },
              { language_code: "fr", language_name: "French", confidence: 3.2, region: "France" }
            ]
          }
        }
      },
      position: {x: 500, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.englishWelcome,
      name: 'English_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🇺🇸 **Welcome!**\n\nHello! I\'m here to help you with any questions or support you need. How can I assist you today?',
        delay: 1000
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.spanishWelcome,
      name: 'Spanish_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🇪🇸 **¡Bienvenido!**\n\n¡Hola! Estoy aquí para ayudarte con cualquier pregunta o soporte que necesites. ¿Cómo puedo asistirte hoy?',
        delay: 1000
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.frenchWelcome,
      name: 'French_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🇫🇷 **Bienvenue!**\n\nBonjour! Je suis là pour vous aider avec toutes vos questions ou besoins de support. Comment puis-je vous aider aujourd\'hui?',
        delay: 1000
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.germanWelcome,
      name: 'German_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🇩🇪 **Willkommen!**\n\nHallo! Ich bin hier, um Ihnen bei allen Fragen oder Support-Bedürfnissen zu helfen. Wie kann ich Ihnen heute helfen?',
        delay: 1000
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.serviceMenuMultilang,
      name: 'Service_Menu_Multilang',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Routing to appropriate language service...',
        delay: 500,
        conditions: [
          {
            type: 'equals',
            field: 'selected_language',
            value: 'es',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'selected_language',
            value: 'fr',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'selected_language',
            value: 'de',
            operator: 'or'
          }
        ],
        defaultNextNodeId: 'english-services'
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.englishServices,
      name: 'English_Services',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What can I help you with?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💬 General Support', value: 'support'},
          {id: generateUUID(), text: '💰 Billing Questions', value: 'billing'},
          {id: generateUUID(), text: '🔧 Technical Help', value: 'technical'},
          {id: generateUUID(), text: '👤 Speak with Agent', value: 'agent'}
        ],
        storeInVariable: 'service_selection'
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.spanishServices,
      name: 'Spanish_Services',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: '¿En qué puedo ayudarte?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💬 Soporte General', value: 'support'},
          {id: generateUUID(), text: '💰 Preguntas de Facturación', value: 'billing'},
          {id: generateUUID(), text: '🔧 Ayuda Técnica', value: 'technical'},
          {id: generateUUID(), text: '👤 Hablar con Agente', value: 'agent'}
        ],
        storeInVariable: 'service_selection'
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.translationApiCall,
      name: 'Translation_API_Call',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.translate.example.com/translate',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{translation_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"text": "{{user_message}}", "source_language": "{{selected_language}}", "target_language": "en", "preserve_formatting": true}',
        responseVariable: 'translation_result',
        arrayFieldName: 'translation_alternatives',
        displayFields: ['translated_text', 'confidence_score', 'alternative_rank', 'context_notes'],
        // Self-contained template configuration
        templateConfig: {
          title: "Translation Results",
          description: "Real-time translation with confidence analysis",
          customTemplate: "🔄 **Translation Complete**\n\n📝 **Original Text** ({{source_language_name}}):\n*\"{{original_text}}\"*\n\n✅ **Translated Text** ({{target_language_name}}):\n**\"{{primary_translation}}\"**\n\n📊 **Translation Quality**:\n• **Confidence**: {{translation_confidence}}%\n• **Source Language**: {{source_language_name}} ({{source_language}})\n• **Target Language**: {{target_language_name}} ({{target_language}})\n• **Processing Time**: {{processing_time}}ms\n\n🔄 **Alternative Translations**:\n{{#each translation_alternatives}}\n{{@index}}. \"{{translated_text}}\" ({{confidence_score}}% confidence)\n   *{{context_notes}}*\n\n{{/each}}{{#if high_quality}}✨ **High Quality Translation**\n\nThe translation quality is excellent! I'm confident this accurately represents your message.{{else if medium_quality}}⚠️ **Good Translation**\n\nThe translation is good, but there might be some nuances. If something seems unclear, please let me know.{{else}}❓ **Basic Translation**\n\nI've provided a basic translation, but complex meanings might not be fully captured. Please clarify if needed.{{/if}}\n\n💬 **Your message has been processed and our team can now assist you effectively!**",
          displayFields: [
            {id: 'field-0', name: 'translated_text', label: 'Translation', type: 'text'},
            {id: 'field-1', name: 'confidence_score', label: 'Confidence', type: 'text'},
            {id: 'field-2', name: 'alternative_rank', label: 'Rank', type: 'text'},
            {id: 'field-3', name: 'context_notes', label: 'Context Notes', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['original_text', 'primary_translation', 'translation_confidence', 'source_language_name', 'target_language_name', 'source_language', 'target_language', 'processing_time'],
            loopVariables: ['translation_alternatives'],
            conditionalVariables: ['high_quality', 'medium_quality']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Translation API
          sampleData: {
            original_text: "Hola, necesito ayuda con mi pedido",
            primary_translation: "Hello, I need help with my order",
            translation_confidence: 97.2,
            source_language_name: "Spanish",
            target_language_name: "English",
            source_language: "es",
            target_language: "en",
            processing_time: "0.8 seconds",
            high_quality: true,
            medium_quality: false,
            translation_alternatives: [
              { alternative_text: "Hello, I need help with my order", confidence: 97.2, quality_score: "Excellent" },
              { alternative_text: "Hi, I need assistance with my order", confidence: 94.1, quality_score: "Very Good" },
              { alternative_text: "Hello, I require help with my purchase", confidence: 89.5, quality_score: "Good" },
              { alternative_text: "Greetings, I need help with my request", confidence: 82.3, quality_score: "Fair" }
            ]
          }
        }
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.languageSpecialistRouting,
      name: 'Language_Specialist_Routing',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Connecting you with a language specialist...',
        delay: 1000,
        conditions: [
          {
            type: 'equals',
            field: 'selected_language',
            value: 'es',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'selected_language',
            value: 'fr',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'selected_language',
            value: 'de',
            operator: 'or'
          }
        ],
        defaultNextNodeId: 'english-specialist'
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.spanishSpecialist,
      name: 'Spanish_Specialist',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🇪🇸 Te estoy conectando con uno de nuestros especialistas que habla español. Un momento por favor...',
        delay: 1000,
        reason: 'Spanish language support specialist'
      },
      position: {x: 50, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.frenchSpecialist,
      name: 'French_Specialist',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🇫🇷 Je vous connecte avec l\'un de nos spécialistes francophones. Un moment s\'il vous plaît...',
        delay: 1000,
        reason: 'French language support specialist'
      },
      position: {x: 200, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.germanSpecialist,
      name: 'German_Specialist',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🇩🇪 Ich verbinde Sie mit einem unserer deutschsprachigen Spezialisten. Einen Moment bitte...',
        delay: 1000,
        reason: 'German language support specialist'
      },
      position: {x: 350, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.englishSpecialist,
      name: 'English_Specialist',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🇺🇸 I\'m connecting you with one of our English-speaking specialists. Please hold on...',
        delay: 1000,
        reason: 'English language support specialist'
      },
      position: {x: 500, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.languageWelcome, targetNodeId: nodeIds.languageSelection},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSelection, targetNodeId: nodeIds.englishWelcome, condition: 'en'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSelection, targetNodeId: nodeIds.spanishWelcome, condition: 'es'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSelection, targetNodeId: nodeIds.frenchWelcome, condition: 'fr'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSelection, targetNodeId: nodeIds.germanWelcome, condition: 'de'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSelection, targetNodeId: nodeIds.languageDetectionApi, condition: 'auto'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageDetectionApi, targetNodeId: nodeIds.spanishWelcome, condition: 'es'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageDetectionApi, targetNodeId: nodeIds.frenchWelcome, condition: 'fr'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageDetectionApi, targetNodeId: nodeIds.germanWelcome, condition: 'de'},
    {id: generateUUID(), sourceNodeId: nodeIds.englishWelcome, targetNodeId: nodeIds.serviceMenuMultilang},
    {id: generateUUID(), sourceNodeId: nodeIds.spanishWelcome, targetNodeId: nodeIds.serviceMenuMultilang},
    {id: generateUUID(), sourceNodeId: nodeIds.frenchWelcome, targetNodeId: nodeIds.serviceMenuMultilang},
    {id: generateUUID(), sourceNodeId: nodeIds.germanWelcome, targetNodeId: nodeIds.serviceMenuMultilang},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceMenuMultilang, targetNodeId: nodeIds.englishServices, condition: 'en'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceMenuMultilang, targetNodeId: nodeIds.spanishServices, condition: 'es'},
    {id: generateUUID(), sourceNodeId: nodeIds.englishServices, targetNodeId: nodeIds.languageSpecialistRouting, condition: 'agent'},
    {id: generateUUID(), sourceNodeId: nodeIds.spanishServices, targetNodeId: nodeIds.languageSpecialistRouting, condition: 'agent'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSpecialistRouting, targetNodeId: nodeIds.spanishSpecialist, condition: 'es'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSpecialistRouting, targetNodeId: nodeIds.frenchSpecialist, condition: 'fr'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSpecialistRouting, targetNodeId: nodeIds.germanSpecialist, condition: 'de'},
    {id: generateUUID(), sourceNodeId: nodeIds.languageSpecialistRouting, targetNodeId: nodeIds.englishSpecialist, condition: 'en'}
  ],
  variables: [
    {
      name: 'translation_api_key',
      type: 'string',
      description: 'API key for translation service',
      required: true
    },
    {
      name: 'selected_language',
      type: 'string',
      description: 'User selected or detected language code',
      required: false
    },
    {
      name: 'detected_language',
      type: 'string',
      description: 'Auto-detected language from API',
      required: false
    },
    {
      name: 'translation_confidence',
      type: 'number',
      description: 'Confidence score for translation',
      required: false
    },
    {
      name: 'user_input',
      type: 'string',
      description: 'User input text for language detection',
      required: false
    },
    {
      name: 'user_message',
      type: 'string',
      description: 'User message to be translated',
      required: false
    },
    {
      name: 'service_selection',
      type: 'string',
      description: 'User selected service type',
      required: false
    },
    // Array-based variables (automatically created by the system)
    {
      name: 'language_candidates',
      type: 'array',
      description: 'Array of detected language candidates with confidence scores',
      required: false
    },
    {
      name: 'translation_alternatives',
      type: 'array',
      description: 'Array of alternative translation options',
      required: false
    },
    // Additional variables for template rendering
    {
      name: 'primary_language',
      type: 'string',
      description: 'Primary detected language code',
      required: false
    },
    {
      name: 'primary_language_name',
      type: 'string',
      description: 'Primary detected language name',
      required: false
    },
    {
      name: 'confidence_percentage',
      type: 'number',
      description: 'Language detection confidence percentage',
      required: false
    },
    {
      name: 'detected_region',
      type: 'string',
      description: 'Detected language region',
      required: false
    },
    {
      name: 'high_confidence',
      type: 'boolean',
      description: 'Whether detection confidence is high',
      required: false
    },
    {
      name: 'medium_confidence',
      type: 'boolean',
      description: 'Whether detection confidence is medium',
      required: false
    },
    {
      name: 'original_text',
      type: 'string',
      description: 'Original text before translation',
      required: false
    },
    {
      name: 'primary_translation',
      type: 'string',
      description: 'Primary translation result',
      required: false
    },
    {
      name: 'source_language_name',
      type: 'string',
      description: 'Source language name for translation',
      required: false
    },
    {
      name: 'target_language_name',
      type: 'string',
      description: 'Target language name for translation',
      required: false
    },
    {
      name: 'source_language',
      type: 'string',
      description: 'Source language code for translation',
      required: false
    },
    {
      name: 'target_language',
      type: 'string',
      description: 'Target language code for translation',
      required: false
    },
    {
      name: 'processing_time',
      type: 'string',
      description: 'Translation processing time',
      required: false
    },
    {
      name: 'high_quality',
      type: 'boolean',
      description: 'Whether translation quality is high',
      required: false
    },
    {
      name: 'medium_quality',
      type: 'boolean',
      description: 'Whether translation quality is medium',
      required: false
    }
  ],
  metadata: {
    totalNodes: 16,
    difficulty: 'advanced',
    estimatedSetupTime: 40,
    tags: ['multilanguage-support', 'translation-api', 'language-detection', 'international-routing', 'self-contained-api'],
    nodeTypes: ['text', 'quick_reply', 'api_call', 'conditional', 'handoff'],
    description: 'Fully self-contained API node architecture with enterprise-grade multi-language support, automatic translation, language detection, and specialist routing. All API nodes include complete template configurations for built-in response formatting.',
    apiRequirements: {
      endpoints: [
        'POST /detect - Automatic language detection with confidence scoring (self-contained)',
        'POST /translate - Real-time translation with alternatives (self-contained)'
      ],
      authentication: 'Bearer token via translation_api_key variable',
      responseFormat: 'JSON with array fields processed by built-in templates',
      timeout: '30 seconds with 3 retry attempts',
      displayLimits: 'Maximum 5 items per list, 4 fields per item (20 chars each)',
      architecture: 'Self-contained API nodes with templateConfig - no separate formatting nodes required'
    }
  }
};

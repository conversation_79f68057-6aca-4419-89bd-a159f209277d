/**
 * Enterprise Security Bot Template (Advanced)
 * Demonstrates security incident management with API integration and conditional escalation
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  enterpriseSecurityBot: generateUUID(),
  securityWelcome: generateUUID(),
  securityAuthentication: generateUUID(),
  authenticationApi: generateUUID(),
  securityMenu: generateUUID(),
  incidentReportForm: generateUUID(),
  threatAssessmentApi: generateUUID(),
  incidentEscalationLogic: generateUUID(),
  criticalThreatResponse: generateUUID(),
  elevatedThreatResponse: generateUUID(),
  complianceNotificationApi: generateUUID(),
  securityOperationsHandoff: generateUUID(),
  securityApiKey: generateUUID(),
  employeeId: generateUUID(),
  securityClearance: generateUUID(),
  incidentId: generateUUID(),
  threatScore: generateUUID(),
  severityLevel: generateUUID()
};

module.exports = {
  name: 'Enterprise Security Bot',
  description: 'Advanced security incident management with threat assessment and automated escalation',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.securityWelcome,
      name: 'Security_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🔒 Welcome to the Enterprise Security Center. I\'m here to help with security incidents, policy questions, and threat reporting. All interactions are logged for security purposes.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.securityAuthentication,
      name: 'Security_Authentication',
      type: 'form',
      content: {
        type: 'form',
        text: '🔐 Please authenticate yourself to access security services:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'text',
            label: 'Employee ID',
            placeholder: 'Enter your employee ID',
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Department',
            options: ['IT', 'Security', 'HR', 'Finance', 'Operations', 'Legal', 'Executive'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Security Clearance Level',
            options: ['Level 1 - Basic', 'Level 2 - Standard', 'Level 3 - Elevated', 'Level 4 - High', 'Level 5 - Critical'],
            required: true
          }
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.authenticationApi,
      name: 'Authentication_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.security.example.com/auth/verify',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{security_api_key}}',
          'Content-Type': 'application/json',
          'X-Security-Context': 'bot-authentication'
        },
        body: '{"employee_id": "{{employee_id}}", "department": "{{department}}", "clearance_level": "{{security_clearance}}", "session_id": "{{session_id}}", "timestamp": "{{current_timestamp}}"}',
        responseVariable: 'auth_status',
        arrayFieldName: 'permissions',
        displayFields: ['permission_name', 'access_level', 'expiry_date', 'granted_by'],
        // Self-contained template configuration
        templateConfig: {
          title: "Security Authentication",
          description: "Employee authentication and security clearance verification",
          customTemplate: "🔐 **Authentication Complete**\n\n{{#if auth_verified}}✅ **Access Granted**\n\n👤 **Security Profile**:\n• **Employee ID**: {{employee_id}}\n• **Department**: {{department}}\n• **Clearance Level**: {{clearance_level}}\n• **Session ID**: {{session_id}}\n• **Valid Until**: {{session_expiry}}\n\n🔑 **Active Permissions**:\n{{#each permissions}}\n• **{{permission_name}}** ({{access_level}})\n  *Expires: {{expiry_date}} | Granted by: {{granted_by}}*\n\n{{/each}}🛡️ **Security Status**: All systems accessible based on your clearance level.\n\nHow can I assist you with security matters today?{{else}}❌ **Authentication Failed**\n\n⚠️ **Access Denied**: Your credentials could not be verified.\n\n**Possible Issues**:\n• Invalid employee ID\n• Insufficient security clearance\n• Expired credentials\n• System maintenance\n\n🔒 Please contact your security administrator or try again.{{/if}}",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'permission_name', label: 'Permission', type: 'text'},
            {id: 'field-1', name: 'access_level', label: 'Access Level', type: 'text'},
            {id: 'field-2', name: 'expiry_date', label: 'Expiry Date', type: 'text'},
            {id: 'field-3', name: 'granted_by', label: 'Granted By', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['employee_id', 'department', 'clearance_level', 'session_id', 'session_expiry'],
            loopVariables: ['permissions'],
            conditionalVariables: ['auth_verified']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Authentication API
          sampleData: {
            auth_verified: true,
            employee_id: "EMP-2024-5678",
            department: "Information Security",
            clearance_level: "Level 3 - Confidential",
            session_id: "SEC-SESSION-789456",
            session_expiry: "2024-06-08 18:00:00",
            permissions: [
              { permission_name: "Security Dashboard Access", access_level: "Full", expiry_date: "2024-12-31", granted_by: "Security Admin" },
              { permission_name: "Incident Management", access_level: "Read/Write", expiry_date: "2024-12-31", granted_by: "SOC Manager" },
              { permission_name: "Threat Intelligence", access_level: "Read", expiry_date: "2024-09-30", granted_by: "Threat Analyst" },
              { permission_name: "Compliance Reporting", access_level: "Read", expiry_date: "2024-12-31", granted_by: "Compliance Officer" }
            ]
          }
        }
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.securityMenu,
      name: 'Security_Menu',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'How can I assist you with security matters today?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🚨 Report Security Incident', value: 'incident'},
          {id: generateUUID(), text: '🔍 Threat Assessment', value: 'threat'},
          {id: generateUUID(), text: '📋 Security Policy Query', value: 'policy'},
          {id: generateUUID(), text: '🔐 Access Request', value: 'access'},
          {id: generateUUID(), text: '⚠️ Emergency Escalation', value: 'emergency'}
        ],
        storeInVariable: 'user_selection'
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.incidentReportForm,
      name: 'Incident_Report_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🚨 Please provide details about the security incident:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'select',
            label: 'Incident Type',
            options: ['Data Breach', 'Malware Detection', 'Unauthorized Access', 'Phishing Attempt', 'System Compromise', 'Physical Security', 'Other'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Severity Level',
            options: ['Low', 'Medium', 'High', 'Critical', 'Emergency'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'text',
            label: 'Affected Systems/Applications',
            placeholder: 'List affected systems, applications, or services',
            required: true
          },
          {
            id: generateUUID(),
            type: 'textarea',
            label: 'Detailed Description',
            placeholder: 'Provide a detailed description of the incident',
            required: true
          },
          {
            id: generateUUID(),
            type: 'text',
            label: 'When was this discovered?',
            placeholder: 'Enter date and time (e.g., 2024-01-15 14:30)',
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Potential Business Impact',
            options: ['Minimal', 'Low', 'Medium', 'High', 'Severe'],
            required: true
          }
        ]
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.threatAssessmentApi,
      name: 'Threat_Assessment_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.security.example.com/threat/assess',
        timeout: 45000,
        retries: 2,
        headers: {
          'Authorization': 'Bearer {{security_api_key}}',
          'Content-Type': 'application/json',
          'X-Threat-Context': 'automated-assessment'
        },
        body: '{"incident_type": "{{incident_type}}", "affected_systems": "{{affected_systems}}", "severity": "{{severity_level}}", "reporter_clearance": "{{security_clearance}}", "timestamp": "{{discovery_time}}", "scan_depth": "comprehensive"}',
        responseVariable: 'threat_assessment',
        arrayFieldName: 'threat_indicators',
        displayFields: ['indicator_type', 'severity_score', 'confidence_level', 'mitigation_action'],
        // Self-contained template configuration
        templateConfig: {
          title: "Security Threat Assessment",
          description: "Comprehensive threat analysis and risk evaluation",
          customTemplate: "🔍 **Threat Assessment Complete**\n\n📊 **Overall Threat Score**: {{overall_score}}/10\n🎯 **Risk Level**: {{risk_level}}\n⚡ **Urgency**: {{urgency_level}}\n🆔 **Incident ID**: {{incident_id}}\n\n🚨 **Threat Analysis**:\n{{#each threat_indicators}}\n• **{{indicator_type}}**\n  - Severity: {{severity_score}}/10\n  - Confidence: {{confidence_level}}%\n  - Action: {{mitigation_action}}\n\n{{/each}}🛡️ **Recommended Actions**:\n{{#if critical_threat}}🚨 **IMMEDIATE ESCALATION REQUIRED**\n• Isolate affected systems immediately\n• Activate incident response team\n• Notify executive leadership\n• Begin forensic analysis{{else if elevated_threat}}⚠️ **ELEVATED RESPONSE NEEDED**\n• Enhanced monitoring activated\n• Security team assigned\n• Containment procedures initiated\n• Regular status updates{{else}}📋 **STANDARD PROCEDURES**\n• Monitor for additional indicators\n• Document incident details\n• Follow standard response protocols\n• Schedule follow-up assessment{{/if}}\n\n⏰ **Next Update**: {{next_update_time}}",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'indicator_type', label: 'Threat Indicator', type: 'text'},
            {id: 'field-1', name: 'severity_score', label: 'Severity Score', type: 'text'},
            {id: 'field-2', name: 'confidence_level', label: 'Confidence Level', type: 'text'},
            {id: 'field-3', name: 'mitigation_action', label: 'Mitigation Action', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['overall_score', 'risk_level', 'urgency_level', 'incident_id', 'next_update_time'],
            loopVariables: ['threat_indicators'],
            conditionalVariables: ['critical_threat', 'elevated_threat']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Threat Assessment API
          sampleData: {
            overall_score: 8,
            risk_level: "High",
            urgency_level: "Immediate",
            incident_id: "INC-2024-SEC-001247",
            next_update_time: "2024-06-08 15:30:00",
            critical_threat: false,
            elevated_threat: true,
            threat_indicators: [
              { indicator_type: "Malware Signature", severity_score: 9, confidence_level: 95, mitigation_action: "Isolate affected systems" },
              { indicator_type: "Network Anomaly", severity_score: 7, confidence_level: 85, mitigation_action: "Enhanced monitoring" },
              { indicator_type: "Privilege Escalation", severity_score: 8, confidence_level: 90, mitigation_action: "Revoke elevated access" },
              { indicator_type: "Data Exfiltration", severity_score: 6, confidence_level: 75, mitigation_action: "Block external connections" }
            ]
          }
        }
      },
      position: {x: 50, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.incidentEscalationLogic,
      name: 'Incident_Escalation_Logic',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Determining escalation path based on threat assessment...',
        delay: 1000,
        conditions: [
          {
            type: 'contains',
            field: 'severity_level',
            value: 'Critical',
            operator: 'or'
          },
          {
            type: 'contains',
            field: 'severity_level',
            value: 'Emergency',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'potential_impact',
            value: 'Severe',
            operator: 'and'
          },
          {
            type: 'equals',
            field: 'incident_type',
            value: 'Data Breach',
            operator: 'and'
          }
        ],
        defaultNextNodeId: 'standard-incident-handling'
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.criticalThreatResponse,
      name: 'Critical_Threat_Response',
      type: 'text',
      content: {
        type: 'text',
        text: '🚨 **CRITICAL THREAT DETECTED** 🚨\n\n⚠️ **Immediate Actions Taken:**\n• Security team has been alerted\n• Affected systems are being isolated\n• Incident response team activated\n• Executive leadership notified\n\n📋 **Incident ID**: {{incident_id}}\n🕐 **Response Time**: < 5 minutes\n👥 **Assigned Team**: Critical Response Unit\n\n**DO NOT** take any additional actions on affected systems.',
        delay: 1500
      },
      position: {x: 50, y: 1300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.elevatedThreatResponse,
      name: 'Elevated_Threat_Response',
      type: 'text',
      content: {
        type: 'text',
        text: '⚡ **ELEVATED THREAT RESPONSE**\n\n🎯 **Actions Initiated:**\n• Security analysts assigned\n• Enhanced monitoring activated\n• Containment procedures started\n• Stakeholders notified\n\n📋 **Incident ID**: {{incident_id}}\n🕐 **Response Time**: < 15 minutes\n👥 **Assigned Team**: Security Operations Center\n\nYou will receive updates every 30 minutes.',
        delay: 1500
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.complianceNotificationApi,
      name: 'Compliance_Notification_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.security.example.com/compliance/notify',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{security_api_key}}',
          'Content-Type': 'application/json',
          'X-Compliance-Alert': 'data-breach'
        },
        body: '{"incident_id": "{{incident_id}}", "incident_type": "{{incident_type}}", "severity": "{{severity_level}}", "affected_data_types": "{{affected_data}}", "regulatory_requirements": ["GDPR", "CCPA", "HIPAA", "SOX"], "notification_timeline": "immediate"}',
        responseVariable: 'compliance_notification',
        arrayFieldName: 'regulatory_actions',
        displayFields: ['regulation_name', 'notification_status', 'deadline', 'required_actions'],
        // Self-contained template configuration
        templateConfig: {
          title: "Compliance Notification Status",
          description: "Regulatory compliance notifications and required actions",
          customTemplate: "📋 **Compliance Notifications Sent**\n\n✅ **Notification Status**: {{notification_status}}\n🆔 **Compliance Reference**: {{compliance_ref_id}}\n⏰ **Timestamp**: {{notification_timestamp}}\n🎯 **Priority Level**: {{compliance_priority}}\n\n📜 **Regulatory Requirements**:\n{{#each regulatory_actions}}\n• **{{regulation_name}}**\n  - Status: {{notification_status}}\n  - Deadline: {{deadline}}\n  - Actions: {{required_actions}}\n\n{{/each}}⚖️ **Legal Obligations**:\n{{#if immediate_disclosure}}🚨 **IMMEDIATE DISCLOSURE REQUIRED**\n• Regulatory authorities must be notified within {{disclosure_timeframe}}\n• Customer notification required within {{customer_notification_timeframe}}\n• Legal team activated for breach response{{else if standard_reporting}}📋 **STANDARD REPORTING PROCEDURES**\n• Internal compliance review initiated\n• Documentation requirements in progress\n• Regulatory timeline: {{standard_timeline}}{{/if}}\n\n📞 **Next Steps**:\n• Legal team will contact you within {{legal_contact_time}}\n• Compliance officer assigned: {{assigned_officer}}\n• Documentation deadline: {{documentation_deadline}}",
          displayFields: [
            {id: 'field-0', name: 'regulation_name', label: 'Regulation', type: 'text'},
            {id: 'field-1', name: 'notification_status', label: 'Status', type: 'text'},
            {id: 'field-2', name: 'deadline', label: 'Deadline', type: 'text'},
            {id: 'field-3', name: 'required_actions', label: 'Required Actions', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['notification_status', 'compliance_ref_id', 'notification_timestamp', 'compliance_priority', 'disclosure_timeframe', 'customer_notification_timeframe', 'standard_timeline', 'legal_contact_time', 'assigned_officer', 'documentation_deadline'],
            loopVariables: ['regulatory_actions'],
            conditionalVariables: ['immediate_disclosure', 'standard_reporting']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Compliance Notification API
          sampleData: {
            notification_status: "Sent",
            compliance_ref_id: "COMP-2024-SEC-789",
            notification_timestamp: "2024-06-08 14:45:00",
            compliance_priority: "High",
            disclosure_timeframe: "72 hours",
            customer_notification_timeframe: "24 hours",
            standard_timeline: "30 days",
            legal_contact_time: "2 hours",
            assigned_officer: "Jane Smith, CISO",
            documentation_deadline: "2024-06-15",
            immediate_disclosure: true,
            standard_reporting: false,
            regulatory_actions: [
              { regulation_name: "GDPR", notification_status: "Sent", deadline: "2024-06-10 14:45:00", required_actions: "Data subject notification, DPA reporting" },
              { regulation_name: "CCPA", notification_status: "Pending", deadline: "2024-06-11 14:45:00", required_actions: "Consumer notification, AG reporting" },
              { regulation_name: "HIPAA", notification_status: "Not Applicable", deadline: "N/A", required_actions: "No PHI involved" },
              { regulation_name: "SOX", notification_status: "Sent", deadline: "2024-06-09 14:45:00", required_actions: "Internal controls assessment" }
            ]
          }
        }
      },
      position: {x: 350, y: 1300},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.securityOperationsHandoff,
      name: 'Security_Operations_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🔒 I\'m immediately connecting you with our Security Operations Center. They have been briefed on your incident and will take over from here.',
        delay: 1000,
        reason: 'Security incident requiring immediate SOC intervention'
      },
      position: {x: 200, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.securityWelcome, targetNodeId: nodeIds.securityAuthentication},
    {id: generateUUID(), sourceNodeId: nodeIds.securityAuthentication, targetNodeId: nodeIds.authenticationApi},
    {id: generateUUID(), sourceNodeId: nodeIds.authenticationApi, targetNodeId: nodeIds.securityMenu, condition: 'verified'},
    {id: generateUUID(), sourceNodeId: nodeIds.securityMenu, targetNodeId: nodeIds.incidentReportForm, condition: 'incident'},
    {id: generateUUID(), sourceNodeId: nodeIds.securityMenu, targetNodeId: nodeIds.securityOperationsHandoff, condition: 'emergency'},
    {id: generateUUID(), sourceNodeId: nodeIds.incidentReportForm, targetNodeId: nodeIds.threatAssessmentApi},
    {id: generateUUID(), sourceNodeId: nodeIds.threatAssessmentApi, targetNodeId: nodeIds.criticalThreatResponse, condition: 'critical'},
    {id: generateUUID(), sourceNodeId: nodeIds.threatAssessmentApi, targetNodeId: nodeIds.elevatedThreatResponse, condition: 'elevated'},
    {id: generateUUID(), sourceNodeId: nodeIds.incidentReportForm, targetNodeId: nodeIds.incidentEscalationLogic},
    {id: generateUUID(), sourceNodeId: nodeIds.incidentEscalationLogic, targetNodeId: nodeIds.complianceNotificationApi, condition: 'data_breach'},
    {id: generateUUID(), sourceNodeId: nodeIds.incidentEscalationLogic, targetNodeId: nodeIds.securityOperationsHandoff, condition: 'immediate'},
    {id: generateUUID(), sourceNodeId: nodeIds.criticalThreatResponse, targetNodeId: nodeIds.securityOperationsHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.elevatedThreatResponse, targetNodeId: nodeIds.securityOperationsHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.complianceNotificationApi, targetNodeId: nodeIds.securityOperationsHandoff}
  ],
  variables: [
    {
      name: 'security_api_key',
      type: 'string',
      description: 'API key for security management system',
      required: true
    },
    {
      name: 'employee_id',
      type: 'string',
      description: 'Employee identification number',
      required: false
    },
    {
      name: 'department',
      type: 'string',
      description: 'Employee department',
      required: false
    },
    {
      name: 'security_clearance',
      type: 'string',
      description: 'Employee security clearance level',
      required: false
    },
    {
      name: 'incident_type',
      type: 'string',
      description: 'Type of security incident reported',
      required: false
    },
    {
      name: 'severity_level',
      type: 'string',
      description: 'Incident severity classification',
      required: false
    },
    {
      name: 'affected_systems',
      type: 'string',
      description: 'Systems affected by the incident',
      required: false
    },
    {
      name: 'incident_description',
      type: 'string',
      description: 'Detailed description of the incident',
      required: false
    },
    {
      name: 'discovery_time',
      type: 'string',
      description: 'When the incident was discovered',
      required: false
    },
    {
      name: 'potential_impact',
      type: 'string',
      description: 'Potential business impact of the incident',
      required: false
    },
    {
      name: 'incident_id',
      type: 'string',
      description: 'Generated incident tracking ID',
      required: false
    },
    {
      name: 'threat_score',
      type: 'number',
      description: 'Calculated threat severity score (1-10)',
      required: false
    },
    {
      name: 'auth_status',
      type: 'string',
      description: 'Authentication status result',
      required: false
    },
    {
      name: 'session_id',
      type: 'string',
      description: 'Current session identifier',
      required: false
    },
    {
      name: 'current_timestamp',
      type: 'string',
      description: 'Current timestamp for API calls',
      required: false
    },
    {
      name: 'affected_data',
      type: 'string',
      description: 'Types of data affected in the incident',
      required: false
    },
    {
      name: 'compliance_notified',
      type: 'string',
      description: 'Compliance notification status',
      required: false
    },
    {
      name: 'user_selection',
      type: 'string',
      description: 'User menu selection from security options',
      required: false
    },
    // Array-based variables (automatically created by the system)
    {
      name: 'permissions',
      type: 'array',
      description: 'Array of user security permissions',
      required: false
    },
    {
      name: 'threat_indicators',
      type: 'array',
      description: 'Array of detected threat indicators',
      required: false
    },
    {
      name: 'regulatory_actions',
      type: 'array',
      description: 'Array of required regulatory compliance actions',
      required: false
    },
    // Additional variables for template rendering
    {
      name: 'auth_verified',
      type: 'boolean',
      description: 'Whether authentication was successful',
      required: false
    },
    {
      name: 'clearance_level',
      type: 'string',
      description: 'User security clearance level',
      required: false
    },
    {
      name: 'session_expiry',
      type: 'string',
      description: 'Session expiration timestamp',
      required: false
    },
    {
      name: 'overall_score',
      type: 'number',
      description: 'Overall threat assessment score',
      required: false
    },
    {
      name: 'risk_level',
      type: 'string',
      description: 'Calculated risk level',
      required: false
    },
    {
      name: 'urgency_level',
      type: 'string',
      description: 'Incident urgency level',
      required: false
    },
    {
      name: 'next_update_time',
      type: 'string',
      description: 'Next scheduled update time',
      required: false
    },
    {
      name: 'critical_threat',
      type: 'boolean',
      description: 'Whether threat is classified as critical',
      required: false
    },
    {
      name: 'elevated_threat',
      type: 'boolean',
      description: 'Whether threat is classified as elevated',
      required: false
    },
    {
      name: 'notification_status',
      type: 'string',
      description: 'Compliance notification status',
      required: false
    },
    {
      name: 'compliance_ref_id',
      type: 'string',
      description: 'Compliance reference identifier',
      required: false
    },
    {
      name: 'notification_timestamp',
      type: 'string',
      description: 'Compliance notification timestamp',
      required: false
    },
    {
      name: 'compliance_priority',
      type: 'string',
      description: 'Compliance notification priority level',
      required: false
    },
    {
      name: 'immediate_disclosure',
      type: 'boolean',
      description: 'Whether immediate disclosure is required',
      required: false
    },
    {
      name: 'standard_reporting',
      type: 'boolean',
      description: 'Whether standard reporting procedures apply',
      required: false
    },
    {
      name: 'disclosure_timeframe',
      type: 'string',
      description: 'Required disclosure timeframe',
      required: false
    },
    {
      name: 'customer_notification_timeframe',
      type: 'string',
      description: 'Customer notification timeframe',
      required: false
    },
    {
      name: 'standard_timeline',
      type: 'string',
      description: 'Standard compliance timeline',
      required: false
    },
    {
      name: 'legal_contact_time',
      type: 'string',
      description: 'Legal team contact timeframe',
      required: false
    },
    {
      name: 'assigned_officer',
      type: 'string',
      description: 'Assigned compliance officer',
      required: false
    },
    {
      name: 'documentation_deadline',
      type: 'string',
      description: 'Documentation completion deadline',
      required: false
    }
  ],
  metadata: {
    totalNodes: 11,
    difficulty: 'advanced',
    estimatedSetupTime: 45,
    tags: ['security-incident', 'threat-assessment', 'compliance-automation', 'authentication', 'escalation', 'self-contained-api'],
    nodeTypes: ['text', 'form', 'api_call', 'quick_reply', 'conditional', 'handoff'],
    description: 'Fully self-contained API node architecture with enterprise-grade security incident management, automated threat assessment, compliance notifications, and intelligent escalation. All API nodes include complete template configurations for built-in response formatting.',
    apiRequirements: {
      endpoints: [
        'POST /auth/verify - Employee authentication and permissions (self-contained)',
        'POST /threat/assess - Comprehensive threat analysis (self-contained)',
        'POST /compliance/notify - Regulatory compliance notifications (self-contained)'
      ],
      authentication: 'Bearer token via security_api_key variable',
      responseFormat: 'JSON with array fields processed by built-in templates',
      timeout: '30-45 seconds with 2-3 retry attempts',
      displayLimits: 'Maximum 5 items per list, 4 fields per item (20 chars each)',
      architecture: 'Self-contained API nodes with templateConfig - no separate formatting nodes required'
    }
  }
};

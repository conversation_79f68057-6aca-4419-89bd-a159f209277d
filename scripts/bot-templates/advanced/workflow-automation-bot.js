/**
 * Complex Workflow Automation Bot Template (Advanced)
 * Demonstrates multi-step workflow automation with API integrations and conditional logic
 */
const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  complexWorkflowAutomationBot: generateUUID(),
  workflowWelcome: generateUUID(),
  workflowTypeSelection: generateUUID(),
  workflowRequirementsForm: generateUUID(),
  systemIntegrationCheck: generateUUID(),
  onboardingWorkflowSetup: generateUUID(),
  invoiceProcessingSetup: generateUUID(),
  workflowComplexityRouter: generateUUID(),
  enterpriseWorkflowSetup: generateUUID(),
  complexApprovalSetup: generateUUID(),
  workflowTestingApi: generateUUID(),
  workflowSuccess: generateUUID(),
  workflowMonitoringOptions: generateUUID(),
  workflowSpecialistHandoff: generateUUID(),
  workflowApiKey: generateUUID(),
  workflowId: generateUUID(),
  selectedWorkflowType: generateUUID(),
  complexityLevel: generateUUID(),
  systemsCount: generateUUID()
};

module.exports = {
  name: 'Complex Workflow Automation Bot',
  description: 'Advanced workflow automation with multi-system integration and intelligent routing',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.workflowWelcome,
      name: 'Workflow_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '⚙️ Welcome to the Workflow Automation Center! I can help you automate complex business processes, integrate systems, and streamline operations.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workflowTypeSelection,
      name: 'Workflow_Type_Selection',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What type of workflow would you like to automate?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📋 Employee Onboarding', value: 'onboarding'},
          {id: generateUUID(), text: '💰 Invoice Processing', value: 'invoice'},
          {id: generateUUID(), text: '📊 Report Generation', value: 'reports'},
          {id: generateUUID(), text: '🔄 Data Synchronization', value: 'sync'},
          {id: generateUUID(), text: '🎯 Custom Workflow', value: 'custom'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workflowRequirementsForm,
      name: 'Workflow_Requirements_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🎯 Let me understand your workflow requirements:',
        delay: 1000,
        formFields: [
          {
            id: 'workflow_name',
            type: 'text',
            label: 'Workflow Name',
            required: true
          },
          {
            id: 'trigger_type',
            type: 'select',
            label: 'Workflow Trigger',
            options: ['Manual Start', 'Time-based Schedule', 'Event-driven', 'API Webhook', 'File Upload', 'Email Received'],
            required: true
          },
          {
            id: 'systems_involved',
            type: 'text',
            label: 'Systems/Applications Involved',
            required: true
          },
          {
            id: 'complexity_level',
            type: 'select',
            label: 'Complexity Level',
            options: ['Simple (2-3 steps)', 'Medium (4-6 steps)', 'Complex (7-10 steps)', 'Enterprise (10+ steps)'],
            required: true
          },
          {
            id: 'approval_required',
            type: 'select',
            label: 'Approval Process Required',
            options: ['No Approval', 'Single Approver', 'Multi-level Approval', 'Conditional Approval'],
            required: true
          }
        ]
      },
      position: {x: 500, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.systemIntegrationCheck,
      name: 'System_Integration_Check',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.workflow.example.com/systems/validate',
        timeout: 45000,
        retries: 2,
        headers: {
          'Authorization': 'Bearer {{workflow_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"systems": "{{systems_involved}}", "workflow_type": "{{selected_workflow_type}}", "complexity": "{{complexity_level}}", "check_permissions": true, "validate_apis": true}',
        responseVariable: 'integration_check',
        arrayFieldName: 'system_validations',
        displayFields: ['system_name', 'status', 'api_version', 'compatibility_score'],
        // Self-contained template configuration
        templateConfig: {
          title: "System Integration Analysis",
          description: "Comprehensive system compatibility and API validation results",
          customTemplate: "🔍 **System Integration Analysis Complete**\n\n📊 **Overall Compatibility**: {{overall_compatibility}}%\n🎯 **Integration Status**: {{integration_status}}\n⚡ **Estimated Setup Time**: {{estimated_setup_time}}\n🔧 **Complexity Level**: {{validated_complexity}}\n\n🖥️ **System Validation Results**:\n{{#each system_validations}}\n• **{{system_name}}**\n  - Status: {{status}}\n  - API Version: {{api_version}}\n  - Compatibility: {{compatibility_score}}%\n  - Notes: {{validation_notes}}\n\n{{/each}}{{#if all_systems_compatible}}✅ **All Systems Compatible**\n\nGreat news! All your systems are compatible and ready for integration. We can proceed with the workflow setup immediately.{{else if partial_compatibility}}⚠️ **Partial Compatibility**\n\nMost systems are compatible, but some may require additional configuration or updates. Our team can help resolve any compatibility issues.{{else}}❌ **Compatibility Issues Detected**\n\nSome systems have compatibility issues that need to be addressed before proceeding. We recommend consulting with our integration specialists.{{/if}}\n\n🚀 **Next Steps**:\n{{#if ready_to_proceed}}• Proceed with workflow configuration\n• Begin system integration setup\n• Configure authentication and permissions{{else}}• Review compatibility issues\n• Plan system updates or alternatives\n• Schedule consultation with integration team{{/if}}",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'system_name', label: 'System Name', type: 'text'},
            {id: 'field-1', name: 'status', label: 'Status', type: 'text'},
            {id: 'field-2', name: 'api_version', label: 'API Version', type: 'text'},
            {id: 'field-3', name: 'compatibility_score', label: 'Compatibility Score', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['overall_compatibility', 'integration_status', 'estimated_setup_time', 'validated_complexity'],
            loopVariables: ['system_validations'],
            conditionalVariables: ['all_systems_compatible', 'partial_compatibility', 'ready_to_proceed']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for System Integration API
          sampleData: {
            overall_compatibility: 92,
            integration_status: "Compatible",
            estimated_setup_time: "2-3 hours",
            validated_complexity: "Medium",
            all_systems_compatible: true,
            partial_compatibility: false,
            ready_to_proceed: true,
            system_validations: [
              { system_name: "Active Directory", status: "Compatible", api_version: "v2.1", compatibility_score: 98, validation_notes: "Full compatibility confirmed" },
              { system_name: "Email System", status: "Compatible", api_version: "v1.8", compatibility_score: 95, validation_notes: "Minor version update recommended" },
              { system_name: "HR Management", status: "Compatible", api_version: "v3.0", compatibility_score: 88, validation_notes: "All features supported" },
              { system_name: "Asset Management", status: "Compatible", api_version: "v2.5", compatibility_score: 90, validation_notes: "Integration ready" }
            ]
          }
        }
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.onboardingWorkflowSetup,
      name: 'Onboarding_Workflow_Setup',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.workflow.example.com/workflows/onboarding/create',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{workflow_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"workflow_name": "{{workflow_name}}", "trigger": "{{trigger_type}}", "steps": [{"step": 1, "action": "create_user_accounts", "systems": ["AD", "Email", "HR_System"]}, {"step": 2, "action": "assign_equipment", "systems": ["Asset_Management", "IT_Ticketing"]}, {"step": 3, "action": "schedule_orientation", "systems": ["Calendar", "Learning_Management"]}, {"step": 4, "action": "send_welcome_package", "systems": ["Email", "Document_Management"]}], "approval_workflow": "{{approval_required}}"}',
        responseVariable: 'onboarding_workflow',
        arrayFieldName: 'workflow_steps',
        displayFields: ['step_name', 'description', 'systems_involved', 'estimated_duration'],
        // Self-contained template configuration
        templateConfig: {
          title: "Employee Onboarding Workflow Created",
          description: "Automated employee onboarding workflow configuration and setup",
          customTemplate: "👥 **Employee Onboarding Workflow Created Successfully!**\n\n🆔 **Workflow Details**:\n• **Name**: {{workflow_name}}\n• **Workflow ID**: {{workflow_id}}\n• **Status**: {{workflow_status}}\n• **Trigger**: {{trigger_type}}\n• **Created**: {{creation_timestamp}}\n\n⚙️ **Automated Workflow Steps**:\n{{#each workflow_steps}}\n**Step {{@index}}**: {{step_name}}\n• Description: {{description}}\n• Systems: {{systems_involved}}\n• Duration: {{estimated_duration}}\n• Status: {{step_status}}\n\n{{/each}}🔄 **Automation Features**:\n• **Account Creation**: Automatic user accounts in AD, Email, and HR systems\n• **Equipment Assignment**: Automated IT asset allocation and tracking\n• **Orientation Scheduling**: Calendar integration for training sessions\n• **Welcome Package**: Automated document delivery and communication\n\n{{#if approval_enabled}}✅ **Approval Workflow**: {{approval_type}} approval process configured\n• Approval required for: {{approval_triggers}}\n• Notification system: Active\n• Escalation rules: Configured{{/if}}\n\n📊 **Monitoring & Tracking**:\n• Real-time progress tracking\n• Automated status notifications\n• Exception handling and alerts\n• Performance metrics collection\n\n🎯 **Your onboarding workflow is now active and will automatically process new employee requests!**",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'step_name', label: 'Step Name', type: 'text'},
            {id: 'field-1', name: 'description', label: 'Description', type: 'text'},
            {id: 'field-2', name: 'systems_involved', label: 'Systems', type: 'text'},
            {id: 'field-3', name: 'estimated_duration', label: 'Duration', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['workflow_name', 'workflow_id', 'workflow_status', 'trigger_type', 'creation_timestamp', 'approval_type', 'approval_triggers'],
            loopVariables: ['workflow_steps'],
            conditionalVariables: ['approval_enabled']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Employee Onboarding Workflow API
          sampleData: {
            workflow_name: "New Employee Onboarding",
            workflow_id: "WF-ONBOARD-2024-001",
            workflow_status: "Active",
            trigger_type: "HR System Integration",
            creation_timestamp: "2024-06-08 14:30:00",
            approval_type: "Manager",
            approval_triggers: "Equipment requests over $500",
            approval_enabled: true,
            workflow_steps: [
              { step_name: "Account Creation", description: "Create user accounts in all systems", systems_involved: "AD, Email, HR System", estimated_duration: "15 minutes", step_status: "Automated" },
              { step_name: "Equipment Assignment", description: "Allocate and track IT equipment", systems_involved: "Asset Management, IT Ticketing", estimated_duration: "30 minutes", step_status: "Semi-Automated" },
              { step_name: "Orientation Scheduling", description: "Schedule training and orientation sessions", systems_involved: "Calendar, Learning Management", estimated_duration: "10 minutes", step_status: "Automated" },
              { step_name: "Welcome Package", description: "Send welcome documents and information", systems_involved: "Email, Document Management", estimated_duration: "5 minutes", step_status: "Automated" }
            ]
          }
        }
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.invoiceProcessingSetup,
      name: 'Invoice_Processing_Setup',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.workflow.example.com/workflows/invoice/create',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{workflow_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"workflow_name": "{{workflow_name}}", "trigger": "email_received", "steps": [{"step": 1, "action": "extract_invoice_data", "systems": ["OCR_Service", "AI_Processing"]}, {"step": 2, "action": "validate_vendor", "systems": ["ERP", "Vendor_Database"]}, {"step": 3, "action": "route_for_approval", "systems": ["Approval_System", "Email"]}, {"step": 4, "action": "process_payment", "systems": ["Accounting", "Banking_API"]}], "approval_rules": {"amount_threshold": 1000, "department_approval": true, "finance_approval": true}}',
        responseVariable: 'invoice_workflow',
        arrayFieldName: 'processing_steps',
        displayFields: ['step_name', 'automation_level', 'processing_time', 'accuracy_rate'],
        // Self-contained template configuration
        templateConfig: {
          title: "Invoice Processing Workflow Created",
          description: "Automated invoice processing workflow with AI-powered data extraction",
          customTemplate: "💰 **Invoice Processing Workflow Created Successfully!**\n\n🆔 **Workflow Details**:\n• **Name**: {{workflow_name}}\n• **Workflow ID**: {{workflow_id}}\n• **Status**: {{workflow_status}}\n• **Trigger**: Email-based invoice receipt\n• **Created**: {{creation_timestamp}}\n\n🤖 **AI-Powered Processing Steps**:\n{{#each processing_steps}}\n**{{step_name}}**\n• Automation Level: {{automation_level}}\n• Processing Time: {{processing_time}}\n• Accuracy Rate: {{accuracy_rate}}\n• Systems: {{systems_involved}}\n\n{{/each}}💡 **Smart Features**:\n• **OCR Data Extraction**: Automatic invoice data capture with {{ocr_accuracy}}% accuracy\n• **Vendor Validation**: Real-time vendor verification against ERP database\n• **Intelligent Routing**: Smart approval routing based on amount and department\n• **Payment Processing**: Automated payment execution with banking API integration\n\n📋 **Approval Rules**:\n• **Amount Threshold**: ${{amount_threshold}} (requires approval above this amount)\n• **Department Approval**: {{department_approval_required}}\n• **Finance Approval**: {{finance_approval_required}}\n• **Auto-Approval**: {{auto_approval_percentage}}% of invoices qualify\n\n⚡ **Performance Metrics**:\n• **Processing Speed**: {{average_processing_time}} average\n• **Accuracy Rate**: {{overall_accuracy}}%\n• **Cost Savings**: {{estimated_cost_savings}} per month\n• **Error Reduction**: {{error_reduction_percentage}}% fewer manual errors\n\n🎯 **Your invoice processing is now fully automated and ready to handle incoming invoices!**",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'step_name', label: 'Processing Step', type: 'text'},
            {id: 'field-1', name: 'automation_level', label: 'Automation Level', type: 'text'},
            {id: 'field-2', name: 'processing_time', label: 'Processing Time', type: 'text'},
            {id: 'field-3', name: 'accuracy_rate', label: 'Accuracy Rate', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['workflow_name', 'workflow_id', 'workflow_status', 'creation_timestamp', 'ocr_accuracy', 'amount_threshold', 'department_approval_required', 'finance_approval_required', 'auto_approval_percentage', 'average_processing_time', 'overall_accuracy', 'estimated_cost_savings', 'error_reduction_percentage'],
            loopVariables: ['processing_steps'],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Invoice Processing Workflow API
          sampleData: {
            workflow_name: "Automated Invoice Processing",
            workflow_id: "WF-INVOICE-2024-002",
            workflow_status: "Active",
            creation_timestamp: "2024-06-08 14:45:00",
            ocr_accuracy: 98.5,
            amount_threshold: 1000,
            department_approval_required: "Yes",
            finance_approval_required: "Yes",
            auto_approval_percentage: 75,
            average_processing_time: "3.2 minutes",
            overall_accuracy: 97.8,
            estimated_cost_savings: "$12,500",
            error_reduction_percentage: 85,
            processing_steps: [
              { step_name: "OCR Data Extraction", automation_level: "Fully Automated", processing_time: "45 seconds", accuracy_rate: 98.5, systems_involved: "OCR Service, AI Processing" },
              { step_name: "Vendor Validation", automation_level: "Fully Automated", processing_time: "30 seconds", accuracy_rate: 99.2, systems_involved: "ERP, Vendor Database" },
              { step_name: "Approval Routing", automation_level: "Rule-Based", processing_time: "15 seconds", accuracy_rate: 100, systems_involved: "Approval System, Email" },
              { step_name: "Payment Processing", automation_level: "Semi-Automated", processing_time: "2 minutes", accuracy_rate: 99.8, systems_involved: "Accounting, Banking API" }
            ]
          }
        }
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workflowComplexityRouter,
      name: 'Workflow_Complexity_Router',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Analyzing workflow complexity and routing to appropriate setup...',
        delay: 1000,
        conditions: [
          {
            type: 'equals',
            field: 'complexity_level',
            value: 'Enterprise (10+ steps)',
            operator: 'and'
          },
          {
            type: 'equals',
            field: 'approval_required',
            value: 'Multi-level Approval',
            operator: 'and'
          },
          {
            type: 'contains',
            field: 'systems_count',
            value: '5',
            operator: 'and'
          }
        ],
        defaultNextNodeId: 'standard-workflow-setup'
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.enterpriseWorkflowSetup,
      name: 'Enterprise_Workflow_Setup',
      type: 'text',
      content: {
        type: 'text',
        text: '🏢 **Enterprise Workflow Configuration**\n\nYour complex workflow requires advanced setup:\n\n⚙️ **Features Enabled:**\n• Multi-system orchestration\n• Advanced error handling\n• Parallel processing capabilities\n• Real-time monitoring\n• Audit trail logging\n• Performance analytics\n\n🔧 **Next Steps:**\n• Architecture review scheduled\n• Dedicated implementation team assigned\n• Custom integration development\n• Testing environment setup',
        delay: 1500
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.complexApprovalSetup,
      name: 'Complex_Approval_Setup',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.workflow.example.com/approval/configure',
        timeout: 30000,
        retries: 3,
        headers: {
          'Authorization': 'Bearer {{workflow_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"workflow_id": "{{workflow_id}}", "approval_levels": [{"level": 1, "role": "direct_manager", "conditions": "amount < 5000"}, {"level": 2, "role": "department_head", "conditions": "amount >= 5000 AND amount < 25000"}, {"level": 3, "role": "finance_director", "conditions": "amount >= 25000"}], "escalation_rules": {"timeout_hours": 24, "auto_escalate": true, "notification_frequency": "daily"}}',
        responseVariable: 'approval_configuration',
        arrayFieldName: 'approval_levels',
        displayFields: ['level_name', 'role_title', 'approval_conditions', 'response_time'],
        // Self-contained template configuration
        templateConfig: {
          title: "Multi-Level Approval Workflow Configured",
          description: "Complex approval workflow with intelligent routing and escalation",
          customTemplate: "🔐 **Multi-Level Approval Workflow Configured Successfully!**\n\n🆔 **Configuration Details**:\n• **Workflow ID**: {{workflow_id}}\n• **Approval System**: {{approval_system_name}}\n• **Configuration ID**: {{config_id}}\n• **Status**: {{configuration_status}}\n• **Created**: {{configuration_timestamp}}\n\n📊 **Approval Hierarchy**:\n{{#each approval_levels}}\n**Level {{@index}}**: {{level_name}}\n• Role: {{role_title}}\n• Conditions: {{approval_conditions}}\n• Response Time: {{response_time}}\n• Escalation: {{escalation_rules}}\n\n{{/each}}⚡ **Smart Escalation Rules**:\n• **Timeout Period**: {{timeout_hours}} hours\n• **Auto-Escalation**: {{auto_escalate_enabled}}\n• **Notification Frequency**: {{notification_frequency}}\n• **Weekend Handling**: {{weekend_escalation}}\n• **Holiday Handling**: {{holiday_escalation}}\n\n🔄 **Workflow Intelligence**:\n• **Dynamic Routing**: Requests automatically routed based on amount and department\n• **Parallel Approvals**: Multiple approvers can review simultaneously when appropriate\n• **Conditional Logic**: Smart approval paths based on request type and value\n• **Backup Approvers**: Automatic delegation when primary approvers are unavailable\n\n📱 **Notification System**:\n• **Email Notifications**: Instant alerts to approvers\n• **Mobile Push**: Real-time mobile notifications\n• **Dashboard Updates**: Live status updates in approval dashboard\n• **Reminder System**: Automated follow-up reminders\n\n📈 **Performance Tracking**:\n• **Average Approval Time**: {{average_approval_time}}\n• **Approval Rate**: {{approval_success_rate}}%\n• **Escalation Rate**: {{escalation_percentage}}%\n• **SLA Compliance**: {{sla_compliance}}%\n\n🎯 **Your multi-level approval workflow is now active and ready to intelligently route requests!**",
          showPagination: false,
          displayFields: [
            {id: 'field-0', name: 'level_name', label: 'Approval Level', type: 'text'},
            {id: 'field-1', name: 'role_title', label: 'Role', type: 'text'},
            {id: 'field-2', name: 'approval_conditions', label: 'Conditions', type: 'text'},
            {id: 'field-3', name: 'response_time', label: 'Response Time', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['workflow_id', 'approval_system_name', 'config_id', 'configuration_status', 'configuration_timestamp', 'timeout_hours', 'auto_escalate_enabled', 'notification_frequency', 'weekend_escalation', 'holiday_escalation', 'average_approval_time', 'approval_success_rate', 'escalation_percentage', 'sla_compliance'],
            loopVariables: ['approval_levels'],
            conditionalVariables: []
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Multi-Level Approval API
          sampleData: {
            workflow_id: "WF-APPROVAL-2024-003",
            approval_system_name: "Enterprise Approval Engine",
            config_id: "CONF-APPROVAL-789",
            configuration_status: "Active",
            configuration_timestamp: "2024-06-08 15:00:00",
            timeout_hours: 24,
            auto_escalate_enabled: "Yes",
            notification_frequency: "Every 4 hours",
            weekend_escalation: "Enabled",
            holiday_escalation: "Enabled",
            average_approval_time: "4.2 hours",
            approval_success_rate: 94.5,
            escalation_percentage: 12,
            sla_compliance: 98.2,
            approval_levels: [
              { level_name: "Direct Manager", role_title: "Team Lead", approval_conditions: "Amount < $5,000", response_time: "2 hours", escalation_rules: "Auto-escalate after 24h" },
              { level_name: "Department Head", role_title: "Department Manager", approval_conditions: "$5,000 - $25,000", response_time: "4 hours", escalation_rules: "Auto-escalate after 48h" },
              { level_name: "Finance Director", role_title: "Finance Director", approval_conditions: "Amount > $25,000", response_time: "8 hours", escalation_rules: "Manual escalation only" },
              { level_name: "Executive Approval", role_title: "VP/C-Level", approval_conditions: "Amount > $100,000", response_time: "24 hours", escalation_rules: "Board notification" }
            ]
          }
        }
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workflowTestingApi,
      name: 'Workflow_Testing_API',
      type: 'api_call',
      content: {
        type: 'api_call',
        method: 'POST',
        url: 'https://api.workflow.example.com/workflows/test',
        timeout: 60000,
        retries: 2,
        headers: {
          'Authorization': 'Bearer {{workflow_api_key}}',
          'Content-Type': 'application/json'
        },
        body: '{"workflow_id": "{{workflow_id}}", "test_type": "full_validation", "include_integrations": true, "test_data": {"sample_inputs": true, "edge_cases": true, "error_scenarios": true}}',
        responseVariable: 'workflow_testing',
        arrayFieldName: 'test_results',
        displayFields: ['test_name', 'status', 'execution_time', 'success_rate'],
        // Self-contained template configuration
        templateConfig: {
          title: "Workflow Testing & Validation Results",
          description: "Comprehensive workflow testing with integration validation",
          customTemplate: "🧪 **Workflow Testing Complete**\n\n📊 **Overall Test Results**:\n• **Test Status**: {{overall_test_status}}\n• **Success Rate**: {{overall_success_rate}}%\n• **Total Tests**: {{total_tests_run}}\n• **Execution Time**: {{total_execution_time}}\n• **Test ID**: {{test_session_id}}\n\n🔍 **Detailed Test Results**:\n{{#each test_results}}\n**{{test_name}}**\n• Status: {{status}}\n• Execution Time: {{execution_time}}\n• Success Rate: {{success_rate}}%\n• Notes: {{test_notes}}\n\n{{/each}}{{#if all_tests_passed}}✅ **All Tests Passed Successfully!**\n\n🎉 **Validation Complete**:\n• All workflow steps validated\n• System integrations verified\n• Error handling tested\n• Performance benchmarks met\n\n🚀 **Your workflow is ready for production deployment!**{{else if tests_passed_with_warnings}}⚠️ **Tests Passed with Warnings**\n\n📋 **Action Items**:\n• {{warning_count}} warnings detected\n• Review recommended optimizations\n• Consider addressing performance suggestions\n• Monitor closely during initial deployment\n\n✅ **Safe to proceed** with monitoring in place.{{else}}❌ **Tests Failed - Action Required**\n\n🔧 **Issues Detected**:\n• {{failed_test_count}} tests failed\n• {{critical_issue_count}} critical issues found\n• System integration problems detected\n• Performance issues identified\n\n🛠️ **Recommended Actions**:\n• Review failed test details\n• Fix integration issues\n• Optimize performance bottlenecks\n• Re-run tests after fixes{{/if}}\n\n📈 **Performance Metrics**:\n• **Response Time**: {{average_response_time}}ms\n• **Throughput**: {{requests_per_minute}} requests/min\n• **Error Rate**: {{error_rate}}%\n• **Resource Usage**: {{resource_utilization}}%",
          displayFields: [
            {id: 'field-0', name: 'test_name', label: 'Test Name', type: 'text'},
            {id: 'field-1', name: 'status', label: 'Status', type: 'text'},
            {id: 'field-2', name: 'execution_time', label: 'Execution Time', type: 'text'},
            {id: 'field-3', name: 'success_rate', label: 'Success Rate', type: 'text'}
          ],
          templateStructure: {
            blocks: [],
            variables: ['overall_test_status', 'overall_success_rate', 'total_tests_run', 'total_execution_time', 'test_session_id', 'warning_count', 'failed_test_count', 'critical_issue_count', 'average_response_time', 'requests_per_minute', 'error_rate', 'resource_utilization'],
            loopVariables: ['test_results'],
            conditionalVariables: ['all_tests_passed', 'tests_passed_with_warnings']
          },
          validatedAt: new Date().toISOString(),
          // Node-specific sample data for Workflow Testing API
          sampleData: {
            overall_test_status: "Passed",
            overall_success_rate: 96.8,
            total_tests_run: 47,
            total_execution_time: "8.5 minutes",
            test_session_id: "TEST-2024-WF-456",
            warning_count: 2,
            failed_test_count: 0,
            critical_issue_count: 0,
            average_response_time: 245,
            requests_per_minute: 180,
            error_rate: 0.2,
            resource_utilization: 68,
            all_tests_passed: false,
            tests_passed_with_warnings: true,
            test_results: [
              { test_name: "Integration Connectivity", status: "Passed", execution_time: "1.2 minutes", success_rate: 100, test_notes: "All systems responding correctly" },
              { test_name: "Data Validation", status: "Passed", execution_time: "2.1 minutes", success_rate: 98.5, test_notes: "Minor data format warnings" },
              { test_name: "Error Handling", status: "Passed", execution_time: "1.8 minutes", success_rate: 95.2, test_notes: "Edge cases handled properly" },
              { test_name: "Performance Load", status: "Warning", execution_time: "3.4 minutes", success_rate: 92.1, test_notes: "Performance acceptable but could be optimized" }
            ]
          }
        }
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.workflowSuccess,
      name: 'Workflow_Success',
      type: 'text',
      content: {
        type: 'text',
        text: '✅ **Workflow Successfully Created!**\n\n🎯 **Workflow Details:**\n• Name: {{workflow_name}}\n• ID: {{workflow_id}}\n• Status: Active\n• Trigger: {{trigger_type}}\n• Systems: {{systems_count}} integrated\n\n📊 **Monitoring:**\n• Real-time dashboard available\n• Email notifications enabled\n• Performance metrics tracking\n• Error alerting configured\n\n🚀 **Your workflow is now live and ready to automate your processes!**',
        delay: 1500
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workflowMonitoringOptions,
      name: 'Workflow_Monitoring_Options',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What would you like to do next?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📊 View Dashboard', value: 'dashboard'},
          {id: generateUUID(), text: '⚙️ Configure Alerts', value: 'alerts'},
          {id: generateUUID(), text: '📋 Create Another Workflow', value: 'create_new'},
          {id: generateUUID(), text: '💬 Speak with Specialist', value: 'specialist'}
        ]
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.workflowSpecialistHandoff,
      name: 'Workflow_Specialist_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '⚙️ I\'m connecting you with our workflow automation specialist who can provide advanced configuration, troubleshooting, and optimization guidance.',
        delay: 1000,
        reason: 'Workflow automation specialist consultation'
      },
      position: {x: 200, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.workflowWelcome, targetNodeId: nodeIds.workflowTypeSelection},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowTypeSelection, targetNodeId: nodeIds.onboardingWorkflowSetup, condition: 'onboarding'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowTypeSelection, targetNodeId: nodeIds.invoiceProcessingSetup, condition: 'invoice'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowTypeSelection, targetNodeId: nodeIds.workflowRequirementsForm, condition: 'custom'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowRequirementsForm, targetNodeId: nodeIds.systemIntegrationCheck},
    {id: generateUUID(), sourceNodeId: nodeIds.systemIntegrationCheck, targetNodeId: nodeIds.workflowComplexityRouter, condition: 'compatible'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowComplexityRouter, targetNodeId: nodeIds.enterpriseWorkflowSetup, condition: 'enterprise'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowComplexityRouter, targetNodeId: nodeIds.complexApprovalSetup, condition: 'complex_approval'},
    {id: generateUUID(), sourceNodeId: nodeIds.onboardingWorkflowSetup, targetNodeId: nodeIds.workflowTestingApi, condition: 'success'},
    {id: generateUUID(), sourceNodeId: nodeIds.invoiceProcessingSetup, targetNodeId: nodeIds.workflowTestingApi, condition: 'success'},
    {id: generateUUID(), sourceNodeId: nodeIds.complexApprovalSetup, targetNodeId: nodeIds.workflowTestingApi, condition: 'success'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowTestingApi, targetNodeId: nodeIds.workflowSuccess, condition: 'passed'},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowSuccess, targetNodeId: nodeIds.workflowMonitoringOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.enterpriseWorkflowSetup, targetNodeId: nodeIds.workflowSpecialistHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.workflowMonitoringOptions, targetNodeId: nodeIds.workflowSpecialistHandoff, condition: 'specialist'}
  ],
  variables: [
    {
      name: 'workflow_api_key',
      type: 'string',
      description: 'API key for workflow automation service',
      required: true
    },
    {
      name: 'selected_workflow_type',
      type: 'string',
      description: 'Type of workflow being created',
      required: false
    },
    {
      name: 'workflow_name',
      type: 'string',
      description: 'Name of the workflow',
      required: false
    },
    {
      name: 'trigger_type',
      type: 'string',
      description: 'Workflow trigger type',
      required: false
    },
    {
      name: 'systems_involved',
      type: 'string',
      description: 'Systems and applications involved in workflow',
      required: false
    },
    {
      name: 'complexity_level',
      type: 'string',
      description: 'Workflow complexity classification',
      required: false
    },
    {
      name: 'approval_required',
      type: 'string',
      description: 'Approval process requirements',
      required: false
    },
    {
      name: 'workflow_id',
      type: 'string',
      description: 'Generated workflow identifier',
      required: false
    },
    {
      name: 'systems_count',
      type: 'number',
      description: 'Number of systems being integrated',
      required: false
    },
    {
      name: 'monitoring_selection',
      type: 'string',
      description: 'User selected monitoring option',
      required: false
    },
    // Array-based variables (automatically created by the system)
    {
      name: 'system_validations',
      type: 'array',
      description: 'Array of system compatibility validation results',
      required: false
    },
    {
      name: 'workflow_steps',
      type: 'array',
      description: 'Array of workflow automation steps',
      required: false
    },
    {
      name: 'processing_steps',
      type: 'array',
      description: 'Array of invoice processing automation steps',
      required: false
    },
    {
      name: 'approval_levels',
      type: 'array',
      description: 'Array of multi-level approval configurations',
      required: false
    },
    {
      name: 'test_results',
      type: 'array',
      description: 'Array of workflow testing results',
      required: false
    },
    // Additional variables for template rendering
    {
      name: 'overall_compatibility',
      type: 'number',
      description: 'Overall system compatibility percentage',
      required: false
    },
    {
      name: 'integration_status',
      type: 'string',
      description: 'System integration status',
      required: false
    },
    {
      name: 'estimated_setup_time',
      type: 'string',
      description: 'Estimated workflow setup time',
      required: false
    },
    {
      name: 'validated_complexity',
      type: 'string',
      description: 'Validated workflow complexity level',
      required: false
    },
    {
      name: 'all_systems_compatible',
      type: 'boolean',
      description: 'Whether all systems are compatible',
      required: false
    },
    {
      name: 'partial_compatibility',
      type: 'boolean',
      description: 'Whether partial compatibility exists',
      required: false
    },
    {
      name: 'ready_to_proceed',
      type: 'boolean',
      description: 'Whether ready to proceed with setup',
      required: false
    },
    {
      name: 'workflow_status',
      type: 'string',
      description: 'Current workflow status',
      required: false
    },
    {
      name: 'creation_timestamp',
      type: 'string',
      description: 'Workflow creation timestamp',
      required: false
    },
    {
      name: 'approval_enabled',
      type: 'boolean',
      description: 'Whether approval workflow is enabled',
      required: false
    },
    {
      name: 'approval_type',
      type: 'string',
      description: 'Type of approval workflow',
      required: false
    },
    {
      name: 'approval_triggers',
      type: 'string',
      description: 'Conditions that trigger approval',
      required: false
    },
    {
      name: 'ocr_accuracy',
      type: 'number',
      description: 'OCR data extraction accuracy percentage',
      required: false
    },
    {
      name: 'amount_threshold',
      type: 'number',
      description: 'Amount threshold for approval requirements',
      required: false
    },
    {
      name: 'department_approval_required',
      type: 'string',
      description: 'Whether department approval is required',
      required: false
    },
    {
      name: 'finance_approval_required',
      type: 'string',
      description: 'Whether finance approval is required',
      required: false
    },
    {
      name: 'auto_approval_percentage',
      type: 'number',
      description: 'Percentage of invoices qualifying for auto-approval',
      required: false
    },
    {
      name: 'average_processing_time',
      type: 'string',
      description: 'Average processing time for workflows',
      required: false
    },
    {
      name: 'overall_accuracy',
      type: 'number',
      description: 'Overall workflow accuracy percentage',
      required: false
    },
    {
      name: 'estimated_cost_savings',
      type: 'string',
      description: 'Estimated monthly cost savings',
      required: false
    },
    {
      name: 'error_reduction_percentage',
      type: 'number',
      description: 'Error reduction percentage from automation',
      required: false
    },
    {
      name: 'approval_system_name',
      type: 'string',
      description: 'Name of the approval system',
      required: false
    },
    {
      name: 'config_id',
      type: 'string',
      description: 'Configuration identifier',
      required: false
    },
    {
      name: 'configuration_status',
      type: 'string',
      description: 'Configuration status',
      required: false
    },
    {
      name: 'configuration_timestamp',
      type: 'string',
      description: 'Configuration creation timestamp',
      required: false
    },
    {
      name: 'timeout_hours',
      type: 'number',
      description: 'Approval timeout in hours',
      required: false
    },
    {
      name: 'auto_escalate_enabled',
      type: 'string',
      description: 'Whether auto-escalation is enabled',
      required: false
    },
    {
      name: 'notification_frequency',
      type: 'string',
      description: 'Notification frequency setting',
      required: false
    },
    {
      name: 'weekend_escalation',
      type: 'string',
      description: 'Weekend escalation setting',
      required: false
    },
    {
      name: 'holiday_escalation',
      type: 'string',
      description: 'Holiday escalation setting',
      required: false
    },
    {
      name: 'average_approval_time',
      type: 'string',
      description: 'Average approval processing time',
      required: false
    },
    {
      name: 'approval_success_rate',
      type: 'number',
      description: 'Approval success rate percentage',
      required: false
    },
    {
      name: 'escalation_percentage',
      type: 'number',
      description: 'Percentage of requests that escalate',
      required: false
    },
    {
      name: 'sla_compliance',
      type: 'number',
      description: 'SLA compliance percentage',
      required: false
    },
    {
      name: 'overall_test_status',
      type: 'string',
      description: 'Overall testing status result',
      required: false
    },
    {
      name: 'overall_success_rate',
      type: 'number',
      description: 'Overall test success rate',
      required: false
    },
    {
      name: 'total_tests_run',
      type: 'number',
      description: 'Total number of tests executed',
      required: false
    },
    {
      name: 'total_execution_time',
      type: 'string',
      description: 'Total test execution time',
      required: false
    },
    {
      name: 'test_session_id',
      type: 'string',
      description: 'Test session identifier',
      required: false
    },
    {
      name: 'all_tests_passed',
      type: 'boolean',
      description: 'Whether all tests passed',
      required: false
    },
    {
      name: 'tests_passed_with_warnings',
      type: 'boolean',
      description: 'Whether tests passed with warnings',
      required: false
    },
    {
      name: 'warning_count',
      type: 'number',
      description: 'Number of test warnings',
      required: false
    },
    {
      name: 'failed_test_count',
      type: 'number',
      description: 'Number of failed tests',
      required: false
    },
    {
      name: 'critical_issue_count',
      type: 'number',
      description: 'Number of critical issues found',
      required: false
    },
    {
      name: 'average_response_time',
      type: 'number',
      description: 'Average response time in milliseconds',
      required: false
    },
    {
      name: 'requests_per_minute',
      type: 'number',
      description: 'Requests processed per minute',
      required: false
    },
    {
      name: 'error_rate',
      type: 'number',
      description: 'Error rate percentage',
      required: false
    },
    {
      name: 'resource_utilization',
      type: 'number',
      description: 'Resource utilization percentage',
      required: false
    }
  ],
  metadata: {
    totalNodes: 14,
    difficulty: 'advanced',
    estimatedSetupTime: 60,
    tags: ['workflow-automation', 'multi-system-integration', 'approval-workflows', 'enterprise-grade', 'self-contained-api'],
    nodeTypes: ['text', 'quick_reply', 'form', 'api_call', 'conditional', 'handoff'],
    description: 'Fully self-contained API node architecture with enterprise-grade workflow automation, multi-system integration, complex approval workflows, and intelligent routing. All API nodes include complete template configurations for built-in response formatting.',
    apiRequirements: {
      endpoints: [
        'POST /systems/validate - System compatibility validation (self-contained)',
        'POST /workflows/onboarding/create - Employee onboarding automation (self-contained)',
        'POST /workflows/invoice/create - Invoice processing automation (self-contained)',
        'POST /approval/configure - Multi-level approval configuration (self-contained)',
        'POST /workflows/test - Comprehensive workflow testing (self-contained)'
      ],
      authentication: 'Bearer token via workflow_api_key variable',
      responseFormat: 'JSON with array fields processed by built-in templates',
      timeout: '30-45 seconds with 2-3 retry attempts',
      displayLimits: 'Maximum 5 items per list, 4 fields per item (20 chars each)',
      architecture: 'Self-contained API nodes with templateConfig - no separate formatting nodes required'
    }
  }
};

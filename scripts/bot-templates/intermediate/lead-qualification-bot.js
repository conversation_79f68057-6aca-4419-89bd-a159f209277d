/**
 * Lead Qualification Bot Template (Intermediate)
 * Demonstrates FORM, QUICK_REPLY, and CONDITIONAL nodes for lead qualification
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  leadQualificationBot: generateUUID(),
  leadWelcome: generateUUID(),
  interestCheck: generateUUID(),
  contactForm: generateUUID(),
  qualificationCheck: generateUUID(),
  enterprisePath: generateUUID(),
  highValuePath: generateUUID(),
  businessPath: generateUUID(),
  smallBusinessPath: generateUUID(),
  followUp: generateUUID(),
  leadScore: generateUUID(),
  interestArea: generateUUID(),
  companySize: generateUUID(),
  budget: generateUUID()
};

module.exports = {
  name: 'Lead Qualification Bot',
  description: 'Advanced lead qualification with forms and conditional logic',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.leadWelcome,
      name: 'Lead_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: 'Hello! Welcome to our website. I\'m here to help you learn more about our services and see if we\'re a good fit for your needs.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.interestCheck,
      name: 'Interest_Check',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What brings you here today?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💰 Pricing Information', value: 'pricing'},
          {id: generateUUID(), text: '🎯 Product Demo', value: 'demo'},
          {id: generateUUID(), text: '📞 Sales Consultation', value: 'sales'},
          {id: generateUUID(), text: '❓ General Questions', value: 'questions'}
        ]
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.contactForm,
      name: 'Contact_Form',
      type: 'form',
      content: {
        type: 'form',
        text: 'Great! I\'d love to help you with that. Could you share some quick details so I can provide the most relevant information?',
        delay: 1000,
        formFields: [
          {
            id: 'name',
            type: 'text',
            label: 'Your Name',
            required: true
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: true
          },
          {
            id: 'company',
            type: 'text',
            label: 'Company Name',
            required: false
          },
          {
            id: 'company_size',
            type: 'select',
            label: 'Company Size',
            options: ['1-10', '11-50', '51-200', '200+'],
            required: false
          },
          {
            id: 'budget',
            type: 'select',
            label: 'Monthly Budget Range',
            options: ['Under $1K', '$1K-$5K', '$5K-$25K', '$25K+'],
            required: false
          }
        ]
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.qualificationCheck,
      name: 'Qualification_Check',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Let me check your qualification status...',
        delay: 500,
        conditions: [
          {
            type: 'equals',
            field: 'company_size',
            value: '200+',
            operator: 'and'
          },
          {
            type: 'equals',
            field: 'budget',
            value: '$25K+',
            operator: 'and'
          },
          {
            type: 'contains',
            field: 'company_size',
            value: '51-200',
            operator: 'or'
          },
          {
            type: 'contains',
            field: 'company_size',
            value: '11-50',
            operator: 'or'
          }
        ],
        defaultNextNodeId: 'small-business-path'
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.enterprisePath,
      name: 'Enterprise_Path',
      type: 'text',
      content: {
        type: 'text',
        text: '🏢 Perfect! As an enterprise client, you\'ll have access to our premium features, dedicated support, and custom solutions. I\'m connecting you with our enterprise sales team.',
        delay: 1000
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.highValuePath,
      name: 'High_Value_Path',
      type: 'text',
      content: {
        type: 'text',
        text: '💎 Excellent! With your budget range, we can offer you our premium solutions with advanced features and priority support. Let me connect you with our senior sales specialist.',
        delay: 1000
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.businessPath,
      name: 'Business_Path',
      type: 'text',
      content: {
        type: 'text',
        text: '💼 Great! Our business solutions are perfect for companies your size. I\'ll send you our business package information and schedule a demo that fits your needs.',
        delay: 1000
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.smallBusinessPath,
      name: 'Small_Business_Path',
      type: 'text',
      content: {
        type: 'text',
        text: '🚀 Perfect! Our startup-friendly plans are designed for growing businesses like yours. I\'ll send you information about our most popular small business package with special pricing.',
        delay: 1000
      },
      position: {x: 500, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.followUp,
      name: 'Follow_Up',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: 'Thank you for your interest! I\'m connecting you with the right specialist who can provide detailed information and answer any questions you may have.',
        delay: 1000,
        reason: 'Qualified lead handoff'
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.leadWelcome, targetNodeId: nodeIds.interestCheck},
    {id: generateUUID(), sourceNodeId: nodeIds.interestCheck, targetNodeId: nodeIds.contactForm},
    {id: generateUUID(), sourceNodeId: nodeIds.contactForm, targetNodeId: nodeIds.qualificationCheck},
    {id: generateUUID(), sourceNodeId: nodeIds.qualificationCheck, targetNodeId: nodeIds.enterprisePath, condition: 'enterprise'},
    {id: generateUUID(), sourceNodeId: nodeIds.qualificationCheck, targetNodeId: nodeIds.highValuePath, condition: 'high_value'},
    {id: generateUUID(), sourceNodeId: nodeIds.qualificationCheck, targetNodeId: nodeIds.businessPath, condition: 'business'},
    {id: generateUUID(), sourceNodeId: nodeIds.qualificationCheck, targetNodeId: nodeIds.smallBusinessPath, condition: 'small'},
    {id: generateUUID(), sourceNodeId: nodeIds.enterprisePath, targetNodeId: nodeIds.followUp},
    {id: generateUUID(), sourceNodeId: nodeIds.highValuePath, targetNodeId: nodeIds.followUp},
    {id: generateUUID(), sourceNodeId: nodeIds.businessPath, targetNodeId: nodeIds.followUp},
    {id: generateUUID(), sourceNodeId: nodeIds.smallBusinessPath, targetNodeId: nodeIds.followUp}
  ],
  variables: [
    {
      name: 'lead_score',
      type: 'number',
      defaultValue: 0,
      description: 'Lead qualification score based on responses',
      required: false
    },
    {
      name: 'interest_area',
      type: 'string',
      description: 'Primary area of interest',
      required: false
    },
    {
      name: 'company_size',
      type: 'string',
      description: 'Size of the prospect company',
      required: false
    },
    {
      name: 'budget',
      type: 'string',
      description: 'Monthly budget range',
      required: false
    }
  ],
  metadata: {
    totalNodes: 9,
    difficulty: 'intermediate',
    estimatedSetupTime: 25,
    tags: ['lead-qualification', 'sales', 'forms', 'conditional', 'quick-reply'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Advanced sales tool with smart lead routing based on company size and budget. Perfect for B2B sales teams.'
  }
};

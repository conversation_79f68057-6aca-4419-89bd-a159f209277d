/**
 * Educational Course Bot Template (Intermediate)
 * Demonstrates course enrollment with FORM, CONDITIONAL, and QUICK_REPLY nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  educationalCourseBot: generateUUID(),
  educationWelcome: generateUUID(),
  learningGoals: generateUUID(),
  skillAssessmentForm: generateUUID(),
  programmingCourses: generateUUID(),
  businessCourses: generateUUID(),
  designCourses: generateUUID(),
  courseRecommendation: generateUUID(),
  beginnerRecommendations: generateUUID(),
  careerChangeTrack: generateUUID(),
  intensivePrograms: generateUUID(),
  standardRecommendations: generateUUID(),
  enrollmentOptions: generateUUID(),
  academicAdvisorHandoff: generateUUID(),
  currentLevel: generateUUID(),
  careerGoal: generateUUID(),
  timeCommitment: generateUUID()
};

module.exports = {
  name: 'Educational Course Bot',
  description: 'Course enrollment and educational guidance with skill assessment and recommendations',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.educationWelcome,
      name: 'Education_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '📚 Welcome to our learning platform! I\'m here to help you find the perfect course to advance your skills and achieve your goals.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.learningGoals,
      name: 'Learning_Goals',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What are you looking to learn or improve?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💻 Programming & Tech', value: 'programming'},
          {id: generateUUID(), text: '📊 Business & Marketing', value: 'business'},
          {id: generateUUID(), text: '🎨 Design & Creative', value: 'design'},
          {id: generateUUID(), text: '📈 Data & Analytics', value: 'data'},
          {id: generateUUID(), text: '🗣️ Languages', value: 'languages'},
          {id: generateUUID(), text: '❓ Not Sure - Help Me Choose', value: 'assessment'}
        ],
        storeInVariable: 'learning_goal'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.skillAssessmentForm,
      name: 'Skill_Assessment_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🎯 Let me help you find the perfect course! Please share some details about your background:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'select',
            label: 'Current Experience Level',
            options: ['Complete Beginner', 'Some Experience', 'Intermediate', 'Advanced', 'Expert'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Preferred Learning Style',
            options: ['Video Lectures', 'Hands-on Projects', 'Reading Materials', 'Interactive Exercises', 'Mixed Approach'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Available Study Time per Week',
            options: ['1-3 hours', '4-6 hours', '7-10 hours', '11-15 hours', '15+ hours'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Primary Goal',
            options: ['Career Change', 'Skill Upgrade', 'Personal Interest', 'Academic Requirement', 'Professional Certification'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Budget Range',
            options: ['Free Only', 'Under $50', '$50-$200', '$200-$500', '$500+'],
            required: false
          }
        ]
      },
      position: {x: 500, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.programmingCourses,
      name: 'Programming_Courses',
      type: 'text',
      content: {
        type: 'text',
        text: '💻 **Programming & Technology Courses**\n\n🚀 **Popular Courses:**\n• Web Development Bootcamp - 12 weeks\n• Python for Beginners - 8 weeks\n• JavaScript Mastery - 10 weeks\n• Mobile App Development - 14 weeks\n• Data Structures & Algorithms - 6 weeks\n\n✨ **Features:**\n• Hands-on projects\n• Industry mentors\n• Job placement assistance\n• Certificate upon completion',
        delay: 1500
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.businessCourses,
      name: 'Business_Courses',
      type: 'text',
      content: {
        type: 'text',
        text: '📊 **Business & Marketing Courses**\n\n🎯 **Top Courses:**\n• Digital Marketing Mastery - 8 weeks\n• Business Strategy & Leadership - 10 weeks\n• Project Management Professional - 6 weeks\n• Financial Analysis & Planning - 8 weeks\n• Entrepreneurship Fundamentals - 12 weeks\n\n✨ **Benefits:**\n• Real business case studies\n• Industry expert instructors\n• Networking opportunities\n• Professional certification',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.designCourses,
      name: 'Design_Courses',
      type: 'text',
      content: {
        type: 'text',
        text: '🎨 **Design & Creative Courses**\n\n🖌️ **Creative Programs:**\n• UI/UX Design Bootcamp - 10 weeks\n• Graphic Design Fundamentals - 8 weeks\n• Digital Illustration - 6 weeks\n• Brand Identity Design - 8 weeks\n• Photography & Photo Editing - 6 weeks\n\n✨ **Highlights:**\n• Portfolio development\n• Creative software training\n• Design thinking methodology\n• Industry portfolio review',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.courseRecommendation,
      name: 'Course_Recommendation',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Analyzing your profile to recommend the best courses...',
        delay: 1000,
        conditions: [
          {
            type: 'equals',
            field: 'current_level',
            value: 'Complete Beginner',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'career_goal',
            value: 'Career Change',
            operator: 'or'
          },
          {
            type: 'contains',
            field: 'time_commitment',
            value: '15',
            operator: 'or'
          }
        ],
        defaultNextNodeId: 'standard-recommendations'
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.beginnerRecommendations,
      name: 'Beginner_Recommendations',
      type: 'text',
      content: {
        type: 'text',
        text: '🌱 **Perfect for Beginners!**\n\nBased on your profile, I recommend starting with:\n\n📚 **Foundation Courses:**\n• Introduction to Programming (4 weeks)\n• Basic Computer Skills (2 weeks)\n• Digital Literacy Fundamentals (3 weeks)\n\n🎯 **Why These Courses:**\n• Step-by-step guidance\n• No prior experience needed\n• Supportive community\n• Flexible pacing\n• Free trial available',
        delay: 1500
      },
      position: {x: 350, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.careerChangeTrack,
      name: 'Career_Change_Track',
      type: 'text',
      content: {
        type: 'text',
        text: '🚀 **Career Change Program**\n\nTransition to a new career with our comprehensive track:\n\n💼 **Complete Career Package:**\n• Skills assessment & gap analysis\n• Intensive 16-week bootcamp\n• Portfolio development\n• Interview preparation\n• Job placement assistance\n• 6-month mentorship\n\n🎯 **Success Rate:** 85% job placement within 6 months\n💰 **ROI:** Average salary increase of 40%',
        delay: 1500
      },
      position: {x: 500, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.intensivePrograms,
      name: 'Intensive_Programs',
      type: 'text',
      content: {
        type: 'text',
        text: '⚡ **Intensive Learning Programs**\n\nMaximize your learning with our accelerated programs:\n\n🏃‍♂️ **Fast-Track Options:**\n• 6-week intensive bootcamps\n• Daily live sessions\n• 1-on-1 mentoring\n• Accelerated project timeline\n• Priority job placement\n\n⭐ **Premium Features:**\n• Small class sizes (max 15 students)\n• Dedicated career coach\n• Industry networking events\n• Lifetime course access',
        delay: 1500
      },
      position: {x: 650, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.standardRecommendations,
      name: 'Standard_Recommendations',
      type: 'text',
      content: {
        type: 'text',
        text: '📈 **Recommended Learning Path**\n\nBased on your experience and goals:\n\n🎯 **Suggested Courses:**\n• Intermediate skill-building courses\n• Industry-specific specializations\n• Professional certification prep\n• Advanced project workshops\n\n📅 **Flexible Schedule:**\n• Self-paced learning\n• Weekend workshops\n• Evening classes available\n• Mobile app access',
        delay: 1500
      },
      position: {x: 800, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.enrollmentOptions,
      name: 'Enrollment_Options',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'Ready to start your learning journey?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🎓 Enroll Now', value: 'enroll'},
          {id: generateUUID(), text: '📅 Schedule Consultation', value: 'consultation'},
          {id: generateUUID(), text: '📧 Send Course Info', value: 'info'},
          {id: generateUUID(), text: '💬 Speak with Advisor', value: 'advisor'}
        ],
        storeInVariable: 'enrollment_action'
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.academicAdvisorHandoff,
      name: 'Academic_Advisor_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🎓 I\'m connecting you with one of our academic advisors who can provide personalized guidance and help you choose the perfect learning path.',
        delay: 1000,
        reason: 'Academic advising and course selection'
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.educationWelcome, targetNodeId: nodeIds.learningGoals},
    {id: generateUUID(), sourceNodeId: nodeIds.learningGoals, targetNodeId: nodeIds.programmingCourses, condition: 'programming'},
    {id: generateUUID(), sourceNodeId: nodeIds.learningGoals, targetNodeId: nodeIds.businessCourses, condition: 'business'},
    {id: generateUUID(), sourceNodeId: nodeIds.learningGoals, targetNodeId: nodeIds.designCourses, condition: 'design'},
    {id: generateUUID(), sourceNodeId: nodeIds.learningGoals, targetNodeId: nodeIds.skillAssessmentForm, condition: 'assessment'},
    {id: generateUUID(), sourceNodeId: nodeIds.skillAssessmentForm, targetNodeId: nodeIds.courseRecommendation},
    {id: generateUUID(), sourceNodeId: nodeIds.courseRecommendation, targetNodeId: nodeIds.beginnerRecommendations, condition: 'beginner'},
    {id: generateUUID(), sourceNodeId: nodeIds.courseRecommendation, targetNodeId: nodeIds.careerChangeTrack, condition: 'career_change'},
    {id: generateUUID(), sourceNodeId: nodeIds.courseRecommendation, targetNodeId: nodeIds.intensivePrograms, condition: 'intensive'},
    {id: generateUUID(), sourceNodeId: nodeIds.courseRecommendation, targetNodeId: nodeIds.standardRecommendations, condition: 'standard'},
    {id: generateUUID(), sourceNodeId: nodeIds.programmingCourses, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.businessCourses, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.designCourses, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.beginnerRecommendations, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.careerChangeTrack, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.intensivePrograms, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.standardRecommendations, targetNodeId: nodeIds.enrollmentOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.enrollmentOptions, targetNodeId: nodeIds.academicAdvisorHandoff}
  ],
  variables: [
    {
      name: 'learning_goal',
      type: 'string',
      description: 'User selected learning area or goal',
      required: false
    },
    {
      name: 'current_level',
      type: 'string',
      description: 'Student current experience level',
      required: false
    },
    {
      name: 'learning_style',
      type: 'string',
      description: 'Preferred learning style',
      required: false
    },
    {
      name: 'time_commitment',
      type: 'string',
      description: 'Available study time per week',
      required: false
    },
    {
      name: 'career_goal',
      type: 'string',
      description: 'Primary learning goal',
      required: false
    },
    {
      name: 'budget',
      type: 'string',
      description: 'Budget range for courses',
      required: false
    },
    {
      name: 'enrollment_action',
      type: 'string',
      description: 'User selected enrollment action',
      required: false
    }
  ],
  metadata: {
    totalNodes: 13,
    difficulty: 'intermediate',
    estimatedSetupTime: 35,
    tags: ['education', 'courses', 'forms', 'conditional', 'quick-reply', 'assessment'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Comprehensive educational platform with course recommendations, skill assessment, and personalized learning paths. Perfect for online education providers. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

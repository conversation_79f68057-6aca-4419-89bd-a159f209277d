/**
 * HR Onboarding Bot Template (Intermediate)
 * Demonstrates employee onboarding with FORM, CONDITIONAL, and QUICK_REPLY nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  hrOnboardingBot: generateUUID(),
  hrWelcome: generateUUID(),
  onboardingMenu: generateUUID(),
  employeeInformationForm: generateUUID(),
  orientationScheduling: generateUUID(),
  itSetupForm: generateUUID(),
  departmentRouting: generateUUID(),
  officeInformation: generateUUID(),
  paperworkConfirmation: generateUUID(),
  orientationConfirmed: generateUUID(),
  engineeringSetup: generateUUID(),
  salesMarketingSetup: generateUUID(),
  remoteSetup: generateUUID(),
  standardSetup: generateUUID(),
  hrQuestionsHandoff: generateUUID(),
  department: generateUUID(),
  workLocation: generateUUID(),
  startDate: generateUUID()
};

module.exports = {
  name: 'HR Onboarding Bot',
  description: 'Employee onboarding assistant with document collection and orientation scheduling',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.hrWelcome,
      name: 'HR_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '👋 Welcome to the team! I\'m your HR onboarding assistant. I\'ll help you get started with everything you need for your first day and beyond.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.onboardingMenu,
      name: 'Onboarding_Menu',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What would you like to get started with?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📋 Complete Paperwork', value: 'paperwork'},
          {id: generateUUID(), text: '📅 Schedule Orientation', value: 'orientation'},
          {id: generateUUID(), text: '💻 IT Setup Request', value: 'it_setup'},
          {id: generateUUID(), text: '🏢 Office Information', value: 'office_info'},
          {id: generateUUID(), text: '❓ Ask HR Questions', value: 'hr_questions'}
        ],
        storeInVariable: 'onboarding_service'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.employeeInformationForm,
      name: 'Employee_Information_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '📋 Let\'s collect your basic information for HR records:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'full_name',
            type: 'text',
            label: 'Full Legal Name',
            placeholder: 'Enter your full legal name',
            required: true
          },
          {
            id: generateUUID(),
            name: 'personal_email',
            type: 'email',
            label: 'Personal Email Address',
            placeholder: 'Enter your personal email address',
            required: true
          },
          {
            id: generateUUID(),
            name: 'phone',
            type: 'text',
            label: 'Phone Number',
            placeholder: 'Enter your phone number (e.g., ************)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'address',
            type: 'text',
            label: 'Home Address',
            placeholder: 'Enter your complete home address',
            required: true
          },
          {
            id: generateUUID(),
            name: 'emergency_contact',
            type: 'text',
            label: 'Emergency Contact Name',
            placeholder: 'Enter emergency contact full name',
            required: true
          },
          {
            id: generateUUID(),
            name: 'emergency_phone',
            type: 'text',
            label: 'Emergency Contact Phone',
            placeholder: 'Enter emergency contact phone number',
            required: true
          },
          {
            id: generateUUID(),
            name: 'start_date',
            type: 'text',
            label: 'Start Date',
            placeholder: 'Enter your start date (e.g., MM/DD/YYYY)',
            required: true
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.orientationScheduling,
      name: 'Orientation_Scheduling',
      type: 'form',
      content: {
        type: 'form',
        text: '📅 Let\'s schedule your orientation session:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'preferred_date',
            type: 'text',
            label: 'Preferred Date',
            placeholder: 'Enter preferred date (e.g., MM/DD/YYYY)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'preferred_time',
            type: 'select',
            label: 'Preferred Time',
            options: ['9:00 AM', '10:00 AM', '11:00 AM', '1:00 PM', '2:00 PM', '3:00 PM'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'orientation_type',
            type: 'select',
            label: 'Orientation Type',
            options: ['In-Person', 'Virtual', 'Hybrid'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'special_requirements',
            type: 'text',
            label: 'Special Requirements or Accommodations',
            placeholder: 'Enter any special requirements or accommodations needed',
            required: false
          }
        ]
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.itSetupForm,
      name: 'IT_Setup_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '💻 Let\'s set up your IT equipment and accounts:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'department',
            type: 'select',
            label: 'Department',
            options: ['Engineering', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations', 'Customer Support'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'role',
            type: 'text',
            label: 'Job Title/Role',
            placeholder: 'Enter your job title or role',
            required: true
          },
          {
            id: generateUUID(),
            name: 'equipment_needed',
            type: 'select',
            label: 'Equipment Needed',
            options: ['Laptop Only', 'Desktop Setup', 'Laptop + Monitor', 'Full Workstation', 'Mobile Device'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'software_access',
            type: 'text',
            label: 'Required Software/Systems Access',
            placeholder: 'List any specific software or systems you need access to',
            required: false
          },
          {
            id: generateUUID(),
            name: 'work_location',
            type: 'select',
            label: 'Primary Work Location',
            options: ['Office', 'Remote', 'Hybrid'],
            required: true
          }
        ]
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.departmentRouting,
      name: 'Department_Routing',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Let me route your IT request to the appropriate team...',
        delay: 1000,
        conditions: [
          {
            type: 'equals',
            field: 'department',
            value: 'Engineering',
            nextNodeId: nodeIds.engineeringSetup
          },
          {
            type: 'equals',
            field: 'department',
            value: 'Sales',
            nextNodeId: nodeIds.salesMarketingSetup
          },
          {
            type: 'equals',
            field: 'department',
            value: 'Marketing',
            nextNodeId: nodeIds.salesMarketingSetup
          },
          {
            type: 'equals',
            field: 'work_location',
            value: 'Remote',
            nextNodeId: nodeIds.remoteSetup
          }
        ],
        defaultNextNodeId: nodeIds.standardSetup
      },
      position: {x: 350, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.officeInformation,
      name: 'Office_Information',
      type: 'text',
      content: {
        type: 'text',
        text: '🏢 **Office Information**\n\n📍 **Address:**\n123 Business Plaza, Suite 456\nCity, State 12345\n\n🅿️ **Parking:**\nFree employee parking in Garage B\nBadge required for access\n\n🕐 **Hours:**\nMonday-Friday: 8:00 AM - 6:00 PM\nCore hours: 10:00 AM - 3:00 PM\n\n🍽️ **Amenities:**\n• Cafeteria (Level 2)\n• Fitness center (Level B1)\n• Quiet rooms for calls\n• Game room (Level 3)',
        delay: 1500
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.paperworkConfirmation,
      name: 'Paperwork_Confirmation',
      type: 'text',
      content: {
        type: 'text',
        text: '✅ **Information Submitted Successfully**\n\nThank you! Your information has been recorded. Next steps:\n\n• HR will review your information within 24 hours\n• You\'ll receive your employee handbook via email\n• Tax forms (W-4, I-9) will be sent for completion\n• Benefits enrollment information coming soon\n\nWelcome to the team!',
        delay: 1500
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.orientationConfirmed,
      name: 'Orientation_Confirmed',
      type: 'text',
      content: {
        type: 'text',
        text: '📅 **Orientation Scheduled**\n\nGreat! Your orientation is confirmed:\n\n• Date: {{preferred_date}}\n• Time: {{preferred_time}}\n• Type: {{orientation_type}}\n• Duration: 2-3 hours\n\nYou\'ll receive a calendar invite with:\n• Meeting details/location\n• Agenda and materials\n• What to bring\n• Contact information',
        delay: 1500
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.engineeringSetup,
      name: 'Engineering_Setup',
      type: 'text',
      content: {
        type: 'text',
        text: '⚙️ **Engineering IT Setup**\n\nYour development environment will include:\n\n• High-performance laptop/workstation\n• Multiple monitor setup\n• Development software licenses\n• VPN and security tools\n• Code repository access\n• Testing environment access\n\nIT will contact you within 24 hours to coordinate setup.',
        delay: 1500
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.salesMarketingSetup,
      name: 'Sales_Marketing_Setup',
      type: 'text',
      content: {
        type: 'text',
        text: '📈 **Sales/Marketing IT Setup**\n\nYour business setup will include:\n\n• Business laptop with presentation capabilities\n• CRM system access\n• Marketing automation tools\n• Video conferencing setup\n• Mobile device (if needed)\n• Sales collateral access\n\nYour manager will coordinate with IT for specialized tools.',
        delay: 1500
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.remoteSetup,
      name: 'Remote_Setup',
      type: 'text',
      content: {
        type: 'text',
        text: '🏠 **Remote Work Setup**\n\nFor remote work, you\'ll receive:\n\n• Laptop shipped to your address\n• Home office equipment allowance\n• VPN and security software\n• Communication tools setup\n• Ergonomic equipment options\n• IT support contact information\n\nShipping typically takes 3-5 business days.',
        delay: 1500
      },
      position: {x: 500, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.standardSetup,
      name: 'Standard_Setup',
      type: 'text',
      content: {
        type: 'text',
        text: '💻 **Standard IT Setup**\n\nYour standard setup includes:\n\n• Business laptop or desktop\n• Email and calendar access\n• Office software suite\n• Company communication tools\n• Basic security software\n• Printer/scanner access\n\nIT will have everything ready for your start date.',
        delay: 1500
      },
      position: {x: 650, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.hrQuestionsHandoff,
      name: 'HR_Questions_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '❓ I\'m connecting you with our HR team who can answer any specific questions about policies, benefits, or your employment.',
        delay: 1000,
        reason: 'HR questions and support'
      },
      position: {x: 500, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.hrWelcome, targetNodeId: nodeIds.onboardingMenu},
    {id: generateUUID(), sourceNodeId: nodeIds.onboardingMenu, targetNodeId: nodeIds.employeeInformationForm, condition: 'paperwork'},
    {id: generateUUID(), sourceNodeId: nodeIds.onboardingMenu, targetNodeId: nodeIds.orientationScheduling, condition: 'orientation'},
    {id: generateUUID(), sourceNodeId: nodeIds.onboardingMenu, targetNodeId: nodeIds.itSetupForm, condition: 'it_setup'},
    {id: generateUUID(), sourceNodeId: nodeIds.onboardingMenu, targetNodeId: nodeIds.officeInformation, condition: 'office_info'},
    {id: generateUUID(), sourceNodeId: nodeIds.onboardingMenu, targetNodeId: nodeIds.hrQuestionsHandoff, condition: 'hr_questions'},
    {id: generateUUID(), sourceNodeId: nodeIds.employeeInformationForm, targetNodeId: nodeIds.paperworkConfirmation},
    {id: generateUUID(), sourceNodeId: nodeIds.orientationScheduling, targetNodeId: nodeIds.orientationConfirmed},
    {id: generateUUID(), sourceNodeId: nodeIds.itSetupForm, targetNodeId: nodeIds.departmentRouting}
  ],
  variables: [
    {
      name: 'onboarding_service',
      type: 'string',
      description: 'Type of onboarding service requested',
      required: false
    },
    {
      name: 'full_name',
      type: 'string',
      description: 'Employee full legal name',
      required: false
    },
    {
      name: 'personal_email',
      type: 'string',
      description: 'Employee personal email address',
      required: false
    },
    {
      name: 'phone',
      type: 'string',
      description: 'Employee phone number',
      required: false
    },
    {
      name: 'address',
      type: 'string',
      description: 'Employee home address',
      required: false
    },
    {
      name: 'emergency_contact',
      type: 'string',
      description: 'Emergency contact name',
      required: false
    },
    {
      name: 'emergency_phone',
      type: 'string',
      description: 'Emergency contact phone number',
      required: false
    },
    {
      name: 'start_date',
      type: 'string',
      description: 'Employee start date',
      required: false
    },
    {
      name: 'preferred_date',
      type: 'string',
      description: 'Preferred orientation date',
      required: false
    },
    {
      name: 'preferred_time',
      type: 'string',
      description: 'Preferred orientation time',
      required: false
    },
    {
      name: 'orientation_type',
      type: 'string',
      description: 'Type of orientation (In-Person, Virtual, Hybrid)',
      required: false
    },
    {
      name: 'special_requirements',
      type: 'string',
      description: 'Special requirements or accommodations',
      required: false
    },
    {
      name: 'department',
      type: 'string',
      description: 'Employee department',
      required: false
    },
    {
      name: 'role',
      type: 'string',
      description: 'Employee job title or role',
      required: false
    },
    {
      name: 'equipment_needed',
      type: 'string',
      description: 'Type of equipment needed',
      required: false
    },
    {
      name: 'software_access',
      type: 'string',
      description: 'Required software or systems access',
      required: false
    },
    {
      name: 'work_location',
      type: 'string',
      description: 'Primary work location',
      required: false
    }
  ],
  metadata: {
    totalNodes: 14,
    difficulty: 'intermediate',
    estimatedSetupTime: 35,
    tags: ['hr', 'onboarding', 'forms', 'conditional', 'quick-reply', 'employee'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Comprehensive HR onboarding system with paperwork collection, orientation scheduling, and IT setup routing. Perfect for HR departments. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

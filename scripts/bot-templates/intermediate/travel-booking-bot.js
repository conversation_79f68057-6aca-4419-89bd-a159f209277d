/**
 * Travel Booking Bot Template (Intermediate)
 * Demonstrates travel booking with FORM, CONDITIONAL, and QUICK_REPLY nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  travelBookingBot: generateUUID(),
  travelWelcome: generateUUID(),
  travelType: generateUUID(),
  flightSearchForm: generateUUID(),
  hotelSearchForm: generateUUID(),
  packageForm: generateUUID(),
  budgetAnalysis: generateUUID(),
  flightResults: generateUUID(),
  hotelResults: generateUUID(),
  luxuryPackage: generateUUID(),
  premiumPackage: generateUUID(),
  standardPackage: generateUUID(),
  budgetPackage: generateUUID(),
  bookingOptions: generateUUID(),
  travelAgentHandoff: generateUUID(),
  budgetRange: generateUUID(),
  tripType: generateUUID(),
  destination: generateUUID()
};

module.exports = {
  name: 'Travel Booking Bot',
  description: 'Comprehensive travel booking assistant with flight, hotel, and package deals',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.travelWelcome,
      name: 'Travel_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '✈️ Welcome to your travel booking assistant! I\'m here to help you plan the perfect trip. Whether you need flights, hotels, or complete packages, I\'ve got you covered.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.travelType,
      name: 'Travel_Type',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What type of travel are you planning?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '✈️ Flights Only', value: 'flights'},
          {id: generateUUID(), text: '🏨 Hotels Only', value: 'hotels'},
          {id: generateUUID(), text: '🎒 Complete Package', value: 'package'},
          {id: generateUUID(), text: '🚗 Car Rental', value: 'car'},
          {id: generateUUID(), text: '🎫 Activities & Tours', value: 'activities'}
        ],
        storeInVariable: 'travel_type'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.flightSearchForm,
      name: 'Flight_Search_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '✈️ Let me help you find the perfect flight! Please provide your travel details:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'departure_city',
            type: 'text',
            label: 'Departure City',
            placeholder: 'Enter departure city (e.g., New York)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'destination_city',
            type: 'text',
            label: 'Destination City',
            placeholder: 'Enter destination city (e.g., Paris)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'departure_date',
            type: 'text',
            label: 'Departure Date',
            placeholder: 'Enter departure date (e.g., MM/DD/YYYY)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'return_date',
            type: 'text',
            label: 'Return Date (leave blank for one-way)',
            placeholder: 'Enter return date (e.g., MM/DD/YYYY)',
            required: false
          },
          {
            id: generateUUID(),
            name: 'passengers',
            type: 'select',
            label: 'Number of Passengers',
            options: ['1', '2', '3', '4', '5', '6+'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'travel_class',
            type: 'select',
            label: 'Travel Class',
            options: ['Economy', 'Premium Economy', 'Business', 'First Class'],
            required: false
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.hotelSearchForm,
      name: 'Hotel_Search_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🏨 I\'ll help you find the perfect accommodation! Please share your preferences:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'hotel_destination',
            type: 'text',
            label: 'Destination City',
            placeholder: 'Enter destination city (e.g., London)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'checkin_date',
            type: 'text',
            label: 'Check-in Date',
            placeholder: 'Enter check-in date (e.g., MM/DD/YYYY)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'checkout_date',
            type: 'text',
            label: 'Check-out Date',
            placeholder: 'Enter check-out date (e.g., MM/DD/YYYY)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'guests',
            type: 'select',
            label: 'Number of Guests',
            options: ['1', '2', '3', '4', '5', '6+'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'rooms',
            type: 'select',
            label: 'Number of Rooms',
            options: ['1', '2', '3', '4+'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'hotel_type',
            type: 'select',
            label: 'Accommodation Type',
            options: ['Hotel', 'Resort', 'Apartment', 'Villa', 'Hostel', 'B&B'],
            required: false
          }
        ]
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.packageForm,
      name: 'Package_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🎒 Let\'s create your complete travel package! Tell me about your dream trip:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'package_destination',
            type: 'text',
            label: 'Destination',
            placeholder: 'Enter your dream destination (e.g., Bali)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'travel_dates',
            type: 'text',
            label: 'Travel Dates (flexible/specific)',
            placeholder: 'Enter travel dates or flexibility (e.g., March 2024)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'duration',
            type: 'select',
            label: 'Trip Duration',
            options: ['Weekend (2-3 days)', 'Short Trip (4-6 days)', 'Week (7-10 days)', 'Extended (11+ days)'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'travelers',
            type: 'select',
            label: 'Number of Travelers',
            options: ['1', '2', '3-4', '5-6', '7+'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'budget_range',
            type: 'select',
            label: 'Budget Range (per person)',
            options: ['Under $500', '$500-$1000', '$1000-$2500', '$2500-$5000', '$5000+'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'trip_type',
            type: 'select',
            label: 'Trip Type',
            options: ['Beach/Relaxation', 'Adventure/Outdoor', 'City/Culture', 'Business', 'Family', 'Romantic'],
            required: false
          }
        ]
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.budgetAnalysis,
      name: 'Budget_Analysis',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Analyzing your budget and preferences...',
        delay: 1000,
        conditions: [
          {
            type: 'equals',
            field: 'budget_range',
            value: '$5000+',
            nextNodeId: nodeIds.luxuryPackage
          },
          {
            type: 'contains',
            field: 'budget_range',
            value: '$2500',
            nextNodeId: nodeIds.premiumPackage
          },
          {
            type: 'contains',
            field: 'budget_range',
            value: '$1000',
            nextNodeId: nodeIds.premiumPackage
          },
          {
            type: 'equals',
            field: 'budget_range',
            value: 'Under $500',
            nextNodeId: nodeIds.budgetPackage
          }
        ],
        defaultNextNodeId: nodeIds.standardPackage
      },
      position: {x: 350, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.flightResults,
      name: 'Flight_Results',
      type: 'text',
      content: {
        type: 'text',
        text: '✈️ **Flight Search Results**\n\nGreat! I found several options for your trip:\n\n🎯 **Best Value**: $299 - Economy, 1 stop\n⚡ **Fastest**: $459 - Direct flight, 3h 45m\n💎 **Premium**: $899 - Business class, direct\n\n• All prices include taxes and fees\n• Free cancellation within 24 hours\n• Seat selection available\n• Baggage options at checkout\n\nReady to book or need more options?',
        delay: 1500
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.hotelResults,
      name: 'Hotel_Results',
      type: 'text',
      content: {
        type: 'text',
        text: '🏨 **Hotel Search Results**\n\nI found excellent accommodations for your stay:\n\n⭐⭐⭐ **City Center Hotel**: $89/night\n• Free WiFi, Breakfast included\n• 0.5 miles from attractions\n\n⭐⭐⭐⭐ **Boutique Resort**: $159/night\n• Pool, Spa, Restaurant\n• Beachfront location\n\n⭐⭐⭐⭐⭐ **Luxury Suite**: $299/night\n• Concierge, Room service\n• Premium amenities\n\nAll hotels include free cancellation!',
        delay: 1500
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.luxuryPackage,
      name: 'Luxury_Package',
      type: 'text',
      content: {
        type: 'text',
        text: '💎 **Luxury Travel Package**\n\nYour premium experience includes:\n\n✈️ **First/Business Class Flights**\n🏨 **5-Star Luxury Hotels**\n🚗 **Private Transportation**\n🍽️ **Fine Dining Experiences**\n🎯 **VIP Tours & Activities**\n👨‍💼 **Personal Concierge Service**\n\nStarting from $5,999 per person\nIncludes everything for a worry-free luxury experience!',
        delay: 1500
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.premiumPackage,
      name: 'Premium_Package',
      type: 'text',
      content: {
        type: 'text',
        text: '🌟 **Premium Travel Package**\n\nYour enhanced experience includes:\n\n✈️ **Premium Economy/Business Flights**\n🏨 **4-Star Hotels with Amenities**\n🚗 **Comfortable Transportation**\n🍽️ **Selected Restaurant Experiences**\n🎯 **Guided Tours & Activities**\n📱 **24/7 Travel Support**\n\nStarting from $1,899 per person\nPerfect balance of comfort and value!',
        delay: 1500
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.standardPackage,
      name: 'Standard_Package',
      type: 'text',
      content: {
        type: 'text',
        text: '🎒 **Standard Travel Package**\n\nYour complete package includes:\n\n✈️ **Economy Flights (good value)**\n🏨 **3-Star Hotels (clean & comfortable)**\n🚗 **Shared Transportation**\n🍽️ **Breakfast Included**\n🎯 **Popular Tours & Activities**\n📞 **Customer Support**\n\nStarting from $899 per person\nGreat value for a memorable trip!',
        delay: 1500
      },
      position: {x: 500, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.budgetPackage,
      name: 'Budget_Package',
      type: 'text',
      content: {
        type: 'text',
        text: '💰 **Budget Travel Package**\n\nMaximize your adventure on a budget:\n\n✈️ **Budget Airlines (best deals)**\n🏨 **Hostels/Budget Hotels**\n🚌 **Public Transportation**\n🥪 **Self-catering Options**\n🗺️ **Self-guided Tours**\n💡 **Money-saving Tips**\n\nStarting from $399 per person\nAdventure doesn\'t have to be expensive!',
        delay: 1500
      },
      position: {x: 650, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.bookingOptions,
      name: 'Booking_Options',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'How would you like to proceed with your booking?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '💳 Book Now', value: 'book'},
          {id: generateUUID(), text: '📧 Email Details', value: 'email'},
          {id: generateUUID(), text: '📞 Call to Book', value: 'call'},
          {id: generateUUID(), text: '💬 Speak with Agent', value: 'agent'}
        ],
        storeInVariable: 'booking_action'
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.travelAgentHandoff,
      name: 'Travel_Agent_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🧳 I\'m connecting you with one of our experienced travel agents who can finalize your booking and answer any specific questions about your trip.',
        delay: 1000,
        reason: 'Travel booking assistance and consultation'
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.travelWelcome, targetNodeId: nodeIds.travelType},
    {id: generateUUID(), sourceNodeId: nodeIds.travelType, targetNodeId: nodeIds.flightSearchForm, condition: 'flights'},
    {id: generateUUID(), sourceNodeId: nodeIds.travelType, targetNodeId: nodeIds.hotelSearchForm, condition: 'hotels'},
    {id: generateUUID(), sourceNodeId: nodeIds.travelType, targetNodeId: nodeIds.packageForm, condition: 'package'},
    {id: generateUUID(), sourceNodeId: nodeIds.travelType, targetNodeId: nodeIds.travelAgentHandoff, condition: 'car'},
    {id: generateUUID(), sourceNodeId: nodeIds.travelType, targetNodeId: nodeIds.travelAgentHandoff, condition: 'activities'},
    {id: generateUUID(), sourceNodeId: nodeIds.flightSearchForm, targetNodeId: nodeIds.flightResults},
    {id: generateUUID(), sourceNodeId: nodeIds.hotelSearchForm, targetNodeId: nodeIds.hotelResults},
    {id: generateUUID(), sourceNodeId: nodeIds.packageForm, targetNodeId: nodeIds.budgetAnalysis},
    {id: generateUUID(), sourceNodeId: nodeIds.flightResults, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.hotelResults, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.luxuryPackage, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.premiumPackage, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.standardPackage, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.budgetPackage, targetNodeId: nodeIds.bookingOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.bookingOptions, targetNodeId: nodeIds.travelAgentHandoff}
  ],
  variables: [
    {
      name: 'travel_type',
      type: 'string',
      description: 'Type of travel service requested',
      required: false
    },
    {
      name: 'departure_city',
      type: 'string',
      description: 'Flight departure city',
      required: false
    },
    {
      name: 'destination_city',
      type: 'string',
      description: 'Flight destination city',
      required: false
    },
    {
      name: 'departure_date',
      type: 'string',
      description: 'Flight departure date',
      required: false
    },
    {
      name: 'return_date',
      type: 'string',
      description: 'Flight return date',
      required: false
    },
    {
      name: 'passengers',
      type: 'string',
      description: 'Number of flight passengers',
      required: false
    },
    {
      name: 'travel_class',
      type: 'string',
      description: 'Flight travel class',
      required: false
    },
    {
      name: 'hotel_destination',
      type: 'string',
      description: 'Hotel destination city',
      required: false
    },
    {
      name: 'checkin_date',
      type: 'string',
      description: 'Hotel check-in date',
      required: false
    },
    {
      name: 'checkout_date',
      type: 'string',
      description: 'Hotel check-out date',
      required: false
    },
    {
      name: 'guests',
      type: 'string',
      description: 'Number of hotel guests',
      required: false
    },
    {
      name: 'rooms',
      type: 'string',
      description: 'Number of hotel rooms',
      required: false
    },
    {
      name: 'hotel_type',
      type: 'string',
      description: 'Type of accommodation',
      required: false
    },
    {
      name: 'package_destination',
      type: 'string',
      description: 'Package travel destination',
      required: false
    },
    {
      name: 'travel_dates',
      type: 'string',
      description: 'Package travel dates',
      required: false
    },
    {
      name: 'duration',
      type: 'string',
      description: 'Trip duration',
      required: false
    },
    {
      name: 'travelers',
      type: 'string',
      description: 'Number of travelers',
      required: false
    },
    {
      name: 'budget_range',
      type: 'string',
      description: 'Travel budget range selected by user',
      required: false
    },
    {
      name: 'trip_type',
      type: 'string',
      description: 'Type of trip (beach, adventure, etc.)',
      required: false
    },
    {
      name: 'booking_action',
      type: 'string',
      description: 'Action selected for booking',
      required: false
    }
  ],
  metadata: {
    totalNodes: 15,
    difficulty: 'intermediate',
    estimatedSetupTime: 40,
    tags: ['travel', 'booking', 'forms', 'conditional', 'quick-reply', 'packages'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Complete travel booking system with flights, hotels, and package deals. Includes budget-based routing and comprehensive travel options. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

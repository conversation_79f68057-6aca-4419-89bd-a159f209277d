/**
 * Healthcare Appointment Bot Template (Intermediate)
 * Demonstrates medical appointment scheduling with FORM, CONDITIONAL, and HANDOFF nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  healthcareAppointmentBot: generateUUID(),
  healthcareWelcome: generateUUID(),
  healthcareMenu: generateUUID(),
  appointmentForm: generateUUID(),
  symptomAssessment: generateUUID(),
  symptomEvaluation: generateUUID(),
  emergencyResponse: generateUUID(),
  urgentCare: generateUUID(),
  immediateAttention: generateUUID(),
  generalAdvice: generateUUID(),
  appointmentConfirmation: generateUUID(),
  scheduleFromAdvice: generateUUID(),
  insuranceInfo: generateUUID(),
  patientPortal: generateUUID()
};

module.exports = {
  name: 'Healthcare Appointment Bot',
  description: 'Medical appointment scheduling with symptom checker and insurance verification',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.healthcareWelcome,
      name: 'Healthcare_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🏥 Welcome to our healthcare portal! I can help you schedule appointments, check symptoms, or answer questions about our services.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.healthcareMenu,
      name: 'Healthcare_Menu',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'How can I help you today?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📅 Schedule Appointment', value: 'appointment'},
          {id: generateUUID(), text: '🩺 Symptom Checker', value: 'symptoms'},
          {id: generateUUID(), text: '💳 Insurance Questions', value: 'insurance'},
          {id: generateUUID(), text: '📋 Patient Portal', value: 'portal'},
          {id: generateUUID(), text: '🚨 Emergency Info', value: 'emergency'}
        ],
        storeInVariable: 'healthcare_service'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.appointmentForm,
      name: 'Appointment_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '📅 I\'ll help you schedule an appointment. Please provide the following information:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'patient_name',
            type: 'text',
            label: 'Patient Name',
            placeholder: 'Enter patient full name',
            required: true
          },
          {
            id: generateUUID(),
            name: 'phone',
            type: 'text',
            label: 'Phone Number',
            placeholder: 'Enter phone number (e.g., ************)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'appointment_type',
            type: 'select',
            label: 'Appointment Type',
            options: ['General Checkup', 'Specialist Consultation', 'Follow-up', 'Urgent Care', 'Preventive Care'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'preferred_date',
            type: 'text',
            label: 'Preferred Date',
            placeholder: 'Enter preferred date (e.g., MM/DD/YYYY)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'insurance_provider',
            type: 'text',
            label: 'Insurance Provider',
            placeholder: 'Enter insurance provider name (optional)',
            required: false
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.symptomAssessment,
      name: 'Symptom_Assessment',
      type: 'form',
      content: {
        type: 'form',
        text: '🩺 I\'ll help assess your symptoms. Please describe your concerns:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'primary_symptom',
            type: 'text',
            label: 'Primary Symptom',
            placeholder: 'Describe your main symptom or concern',
            required: true
          },
          {
            id: generateUUID(),
            name: 'symptom_duration',
            type: 'select',
            label: 'How long have you had this symptom?',
            options: ['Less than 24 hours', '1-3 days', '4-7 days', 'More than a week'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'pain_level',
            type: 'select',
            label: 'Pain Level (1-10)',
            options: ['1-2 (Mild)', '3-4 (Moderate)', '5-6 (Noticeable)', '7-8 (Severe)', '9-10 (Extreme)'],
            required: false
          },
          {
            id: generateUUID(),
            name: 'other_symptoms',
            type: 'text',
            label: 'Other Symptoms',
            placeholder: 'List any additional symptoms (optional)',
            required: false
          }
        ]
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.symptomEvaluation,
      name: 'Symptom_Evaluation',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Let me evaluate your symptoms...',
        delay: 1000,
        conditions: [
          {
            type: 'contains',
            field: 'pain_level',
            value: '9-10',
            nextNodeId: nodeIds.urgentCare
          },
          {
            type: 'contains',
            field: 'pain_level',
            value: '7-8',
            nextNodeId: nodeIds.immediateAttention
          },
          {
            type: 'equals',
            field: 'symptom_duration',
            value: 'Less than 24 hours',
            nextNodeId: nodeIds.immediateAttention
          }
        ],
        defaultNextNodeId: nodeIds.generalAdvice
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.emergencyResponse,
      name: 'Emergency_Response',
      type: 'text',
      content: {
        type: 'text',
        text: '🚨 **EMERGENCY INFORMATION** 🚨\n\nFor life-threatening emergencies:\n• Call 911 immediately\n• Go to nearest emergency room\n• Do not wait for an appointment\n\nOur Emergency Department:\n📍 123 Hospital Drive\n📞 (555) 911-HELP\n🕐 Open 24/7',
        delay: 1000
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.urgentCare,
      name: 'Urgent_Care',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '⚡ Your symptoms require prompt attention. I\'m connecting you with our urgent care team for immediate assistance.',
        delay: 1000,
        reason: 'Urgent medical symptoms requiring immediate attention'
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.immediateAttention,
      name: 'Immediate_Attention',
      type: 'text',
      content: {
        type: 'text',
        text: '⚠️ **Immediate Attention Recommended**\n\nBased on your symptoms, you should be seen today. Please:\n\n• Call our urgent care line: (555) 123-URGENT\n• Visit our walk-in clinic (open 8 AM - 8 PM)\n• Consider emergency room if symptoms worsen\n\nDon\'t wait - your health is important!',
        delay: 1500
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.generalAdvice,
      name: 'General_Advice',
      type: 'text',
      content: {
        type: 'text',
        text: '💡 **General Health Advice**\n\nBased on your symptoms, I recommend:\n\n• Schedule a consultation with your primary care physician\n• Monitor symptoms and note any changes\n• Rest and stay hydrated\n• Contact us if symptoms worsen\n\nWould you like to schedule an appointment?',
        delay: 1500
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.appointmentConfirmation,
      name: 'Appointment_Confirmation',
      type: 'text',
      content: {
        type: 'text',
        text: '✅ **Appointment Request Submitted**\n\nThank you! Your appointment request has been received:\n\n• Confirmation number: APT-12345\n• You\'ll receive a call within 2 hours\n• Bring insurance card and ID\n• Arrive 15 minutes early\n\nWe look forward to seeing you!',
        delay: 1500
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.scheduleFromAdvice,
      name: 'Schedule_From_Advice',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '📅 I\'m connecting you with our scheduling team to book your appointment right away.',
        delay: 1000,
        reason: 'Appointment scheduling from symptom assessment'
      },
      position: {x: 350, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.insuranceInfo,
      name: 'Insurance_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '💳 **Insurance Information**\n\n**Accepted Insurance Plans:**\n• Blue Cross Blue Shield\n• Aetna\n• Cigna\n• UnitedHealthcare\n• Medicare\n• Medicaid\n\n**Insurance Services:**\n• Pre-authorization assistance\n• Benefits verification\n• Claims support\n• Coverage questions\n\nFor specific coverage questions, call: (555) 123-INSURANCE',
        delay: 1500
      },
      position: {x: 650, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.patientPortal,
      name: 'Patient_Portal',
      type: 'text',
      content: {
        type: 'text',
        text: '📋 **Patient Portal Access**\n\n**Portal Features:**\n• View test results\n• Schedule appointments\n• Request prescription refills\n• Message your care team\n• Pay bills online\n• Update personal information\n\n**Access Portal:**\n🌐 portal.healthcare.com\n📱 Download our mobile app\n\n**Need Help?** Call (555) 123-PORTAL for technical support.',
        delay: 1500
      },
      position: {x: 800, y: 500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.healthcareWelcome, targetNodeId: nodeIds.healthcareMenu},
    {id: generateUUID(), sourceNodeId: nodeIds.healthcareMenu, targetNodeId: nodeIds.appointmentForm, condition: 'appointment'},
    {id: generateUUID(), sourceNodeId: nodeIds.healthcareMenu, targetNodeId: nodeIds.symptomAssessment, condition: 'symptoms'},
    {id: generateUUID(), sourceNodeId: nodeIds.healthcareMenu, targetNodeId: nodeIds.insuranceInfo, condition: 'insurance'},
    {id: generateUUID(), sourceNodeId: nodeIds.healthcareMenu, targetNodeId: nodeIds.patientPortal, condition: 'portal'},
    {id: generateUUID(), sourceNodeId: nodeIds.healthcareMenu, targetNodeId: nodeIds.emergencyResponse, condition: 'emergency'},
    {id: generateUUID(), sourceNodeId: nodeIds.appointmentForm, targetNodeId: nodeIds.appointmentConfirmation},
    {id: generateUUID(), sourceNodeId: nodeIds.symptomAssessment, targetNodeId: nodeIds.symptomEvaluation},
    {id: generateUUID(), sourceNodeId: nodeIds.generalAdvice, targetNodeId: nodeIds.scheduleFromAdvice}
  ],
  variables: [
    {
      name: 'healthcare_service',
      type: 'string',
      description: 'Type of healthcare service requested',
      required: false
    },
    {
      name: 'patient_name',
      type: 'string',
      description: 'Patient full name',
      required: false
    },
    {
      name: 'phone',
      type: 'string',
      description: 'Patient phone number',
      required: false
    },
    {
      name: 'appointment_type',
      type: 'string',
      description: 'Type of appointment requested',
      required: false
    },
    {
      name: 'preferred_date',
      type: 'string',
      description: 'Preferred appointment date',
      required: false
    },
    {
      name: 'insurance_provider',
      type: 'string',
      description: 'Patient insurance provider',
      required: false
    },
    {
      name: 'primary_symptom',
      type: 'string',
      description: 'Primary symptom reported by patient',
      required: false
    },
    {
      name: 'symptom_duration',
      type: 'string',
      description: 'Duration of symptoms',
      required: false
    },
    {
      name: 'pain_level',
      type: 'string',
      description: 'Patient reported pain level',
      required: false
    },
    {
      name: 'other_symptoms',
      type: 'string',
      description: 'Additional symptoms reported',
      required: false
    }
  ],
  metadata: {
    totalNodes: 13,
    difficulty: 'intermediate',
    estimatedSetupTime: 35,
    tags: ['healthcare', 'appointments', 'symptoms', 'conditional', 'forms', 'emergency', 'insurance', 'portal'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Comprehensive healthcare bot with symptom assessment, smart routing, insurance information, and patient portal access. Includes emergency protocols and appointment scheduling. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

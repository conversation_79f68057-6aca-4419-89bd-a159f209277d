/**
 * E-commerce Support Bot Template (Intermediate)
 * Demonstrates QUICK_REPLY, FORM, and CONDITIONAL nodes for e-commerce support
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  eCommerceSupportBot: generateUUID(),
  ecommerceWelcome: generateUUID(),
  supportMenu: generateUUID(),
  orderTracking: generateUUID(),
  returnRequest: generateUUID(),
  productQuestions: generateUUID(),
  paymentIssues: generateUUID(),
  orderStatus: generateUUID(),
  returnProcessed: generateUUID(),
  productInfo: generateUUID(),
  paymentSupport: generateUUID(),
  specialistHandoff: generateUUID(),
  orderNumber: generateUUID(),
  customerEmail: generateUUID(),
  paymentIssue: generateUUID()
};

module.exports = {
  name: 'E-commerce Support Bot',
  description: 'Comprehensive e-commerce support with order tracking and returns',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.ecommerceWelcome,
      name: 'Ecommerce_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🛍️ Welcome to our store support! I\'m here to help with orders, returns, product questions, and more. How can I assist you today?',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.supportMenu,
      name: 'Support_Menu',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What do you need help with?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📦 Track My Order', value: 'track'},
          {id: generateUUID(), text: '↩️ Returns & Exchanges', value: 'returns'},
          {id: generateUUID(), text: '🛒 Product Questions', value: 'products'},
          {id: generateUUID(), text: '💳 Payment Issues', value: 'payment'},
          {id: generateUUID(), text: '🎁 Gift Cards', value: 'giftcards'}
        ],
        storeInVariable: 'support_type'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.orderTracking,
      name: 'Order_Tracking',
      type: 'form',
      content: {
        type: 'form',
        text: '📦 I\'ll help you track your order. Please provide your order details:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'text',
            label: 'Order Number',
            placeholder: 'Enter your order number (e.g., ORD-123456)',
            required: true
          },
          {
            id: generateUUID(),
            type: 'email',
            label: 'Email Address',
            placeholder: 'Enter the email used for your order',
            required: true
          },
          {
            id: generateUUID(),
            type: 'text',
            label: 'Billing ZIP Code',
            placeholder: 'Enter your billing ZIP code',
            required: false
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.returnRequest,
      name: 'Return_Request',
      type: 'form',
      content: {
        type: 'form',
        text: '↩️ I\'ll help you start a return. Please provide the following information:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            type: 'text',
            label: 'Order Number',
            placeholder: 'Enter your order number (e.g., ORD-123456)',
            required: true
          },
          {
            id: generateUUID(),
            type: 'text',
            label: 'Item to Return',
            placeholder: 'Enter the name or description of the item',
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Reason for Return',
            options: ['Defective', 'Wrong Size', 'Not as Described', 'Changed Mind', 'Damaged in Shipping'],
            required: true
          },
          {
            id: generateUUID(),
            type: 'select',
            label: 'Item Condition',
            options: ['New/Unused', 'Lightly Used', 'Used', 'Damaged'],
            required: true
          }
        ]
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.productQuestions,
      name: 'Product_Questions',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: '🛒 What would you like to know about our products?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📏 Size Guide', value: 'sizing'},
          {id: generateUUID(), text: '🚚 Shipping Info', value: 'shipping'},
          {id: generateUUID(), text: '🔧 Product Specs', value: 'specs'},
          {id: generateUUID(), text: '💡 Usage Tips', value: 'tips'}
        ],
        storeInVariable: 'product_question_type'
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.paymentIssues,
      name: 'Payment_Issues',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: '💳 I\'ll help resolve your payment issue. Analyzing your request...',
        delay: 500,
        conditions: [
          {
            type: 'equals',
            field: 'payment_issue',
            value: 'declined',
            operator: 'or'
          },
          {
            type: 'equals',
            field: 'payment_issue',
            value: 'refund',
            operator: 'or'
          }
        ],
        defaultNextNodeId: 'payment-support'
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.orderStatus,
      name: 'Order_Status',
      type: 'text',
      content: {
        type: 'text',
        text: '📦 **Order Status Update**\n\nI\'ve found your order! Here\'s the current status:\n\n• Order confirmed and processing\n• Estimated shipping: 2-3 business days\n• Tracking number will be sent via email\n• Delivery estimate: 5-7 business days\n\nYou\'ll receive updates at each step of the process.',
        delay: 1500
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.returnProcessed,
      name: 'Return_Processed',
      type: 'text',
      content: {
        type: 'text',
        text: '↩️ **Return Request Submitted**\n\nYour return request has been processed:\n\n• Return authorization: RMA-12345\n• Prepaid return label sent to your email\n• Drop off at any UPS location\n• Refund processed within 3-5 business days after receipt\n\nThank you for shopping with us!',
        delay: 1500
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.productInfo,
      name: 'Product_Info',
      type: 'text',
      content: {
        type: 'text',
        text: '🛒 **Product Information**\n\n• Size guides available on each product page\n• Free shipping on orders over $50\n• 30-day return policy\n• Product specifications in description\n• Customer reviews and ratings\n\nNeed specific product help? Contact our product specialists!',
        delay: 1500
      },
      position: {x: 350, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.paymentSupport,
      name: 'Payment_Support',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '💳 I\'m connecting you with our payment specialist who can help resolve your payment issue securely and quickly.',
        delay: 1000,
        reason: 'Payment issue requiring specialist assistance'
      },
      position: {x: 500, y: 700},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.specialistHandoff,
      name: 'Specialist_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🎯 Let me connect you with one of our product specialists who can provide detailed information and personalized recommendations.',
        delay: 1000,
        reason: 'Product specialist consultation'
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.ecommerceWelcome, targetNodeId: nodeIds.supportMenu},
    {id: generateUUID(), sourceNodeId: nodeIds.supportMenu, targetNodeId: nodeIds.orderTracking, condition: 'track'},
    {id: generateUUID(), sourceNodeId: nodeIds.supportMenu, targetNodeId: nodeIds.returnRequest, condition: 'returns'},
    {id: generateUUID(), sourceNodeId: nodeIds.supportMenu, targetNodeId: nodeIds.productQuestions, condition: 'products'},
    {id: generateUUID(), sourceNodeId: nodeIds.supportMenu, targetNodeId: nodeIds.paymentIssues, condition: 'payment'},
    {id: generateUUID(), sourceNodeId: nodeIds.orderTracking, targetNodeId: nodeIds.orderStatus},
    {id: generateUUID(), sourceNodeId: nodeIds.returnRequest, targetNodeId: nodeIds.returnProcessed},
    {id: generateUUID(), sourceNodeId: nodeIds.productQuestions, targetNodeId: nodeIds.productInfo},
    {id: generateUUID(), sourceNodeId: nodeIds.paymentIssues, targetNodeId: nodeIds.paymentSupport},
    {id: generateUUID(), sourceNodeId: nodeIds.productInfo, targetNodeId: nodeIds.specialistHandoff}
  ],
  variables: [
    {
      name: 'support_type',
      type: 'string',
      description: 'Type of support requested by user',
      required: false
    },
    {
      name: 'product_question_type',
      type: 'string',
      description: 'Type of product question user has',
      required: false
    },
    {
      name: 'order_number',
      type: 'string',
      description: 'Customer order number',
      required: false
    },
    {
      name: 'email',
      type: 'string',
      description: 'Customer email address',
      required: false
    },
    {
      name: 'zip_code',
      type: 'string',
      description: 'Customer billing ZIP code',
      required: false
    },
    {
      name: 'item_name',
      type: 'string',
      description: 'Name of item to return',
      required: false
    },
    {
      name: 'reason',
      type: 'string',
      description: 'Reason for return',
      required: false
    },
    {
      name: 'condition',
      type: 'string',
      description: 'Condition of item being returned',
      required: false
    },
    {
      name: 'payment_issue',
      type: 'string',
      description: 'Type of payment issue',
      required: false
    }
  ],
  metadata: {
    totalNodes: 11,
    difficulty: 'intermediate',
    estimatedSetupTime: 30,
    tags: ['ecommerce', 'support', 'forms', 'quick-reply', 'conditional'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Complete e-commerce support solution with order tracking, returns, and payment assistance. Essential for online stores. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

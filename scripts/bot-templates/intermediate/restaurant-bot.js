/**
 * Restaurant Reservation Bot Template (Intermediate)
 * Demonstrates restaurant booking with FORM, CONDITIONAL, and QUICK_REPLY nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  restaurantReservationBot: generateUUID(),
  restaurantWelcome: generateUUID(),
  serviceOptions: generateUUID(),
  reservationForm: generateUUID(),
  specialRequestsForm: generateUUID(),
  availabilityCheck: generateUUID(),
  menuInformation: generateUUID(),
  wineSelection: generateUUID(),
  standardAvailability: generateUUID(),
  peakTimeHandling: generateUUID(),
  largePartyHandling: generateUUID(),
  privateEvents: generateUUID(),
  reservationOptions: generateUUID(),
  restaurantHostHandoff: generateUUID(),
  partySize: generateUUID(),
  preferredTime: generateUUID(),
  guestName: generateUUID(),
  confirmationNumber: generateUUID()
};

module.exports = {
  name: 'Restaurant Reservation Bot',
  description: 'Complete restaurant reservation system with menu info, special requests, and availability checking',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.restaurantWelcome,
      name: 'Restaurant_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🍽️ Welcome to our restaurant! I\'m here to help you make a reservation, learn about our menu, or answer any questions about dining with us.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.serviceOptions,
      name: 'Service_Options',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'How can I help you today?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '📅 Make Reservation', value: 'reservation'},
          {id: generateUUID(), text: '📋 View Menu', value: 'menu'},
          {id: generateUUID(), text: '🎉 Private Events', value: 'events'},
          {id: generateUUID(), text: '🍷 Wine Selection', value: 'wine'},
          {id: generateUUID(), text: '📞 Contact Restaurant', value: 'contact'}
        ],
        storeInVariable: 'service_option'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.reservationForm,
      name: 'Reservation_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '📅 I\'d be happy to help you make a reservation! Please provide the following details:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'guest_name',
            type: 'text',
            label: 'Name for Reservation',
            placeholder: 'Enter name for the reservation',
            required: true
          },
          {
            id: generateUUID(),
            name: 'phone_number',
            type: 'text',
            label: 'Phone Number',
            placeholder: 'Enter phone number (e.g., ************)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'party_size',
            type: 'select',
            label: 'Party Size',
            options: ['1', '2', '3', '4', '5', '6', '7', '8', '9+'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'preferred_date',
            type: 'text',
            label: 'Preferred Date',
            placeholder: 'Enter preferred date (e.g., MM/DD/YYYY)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'preferred_time',
            type: 'select',
            label: 'Preferred Time',
            options: ['5:00 PM', '5:30 PM', '6:00 PM', '6:30 PM', '7:00 PM', '7:30 PM', '8:00 PM', '8:30 PM', '9:00 PM'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'seating_preference',
            type: 'select',
            label: 'Seating Preference',
            options: ['No Preference', 'Window Table', 'Booth', 'Bar Seating', 'Patio (weather permitting)', 'Quiet Area'],
            required: false
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.specialRequestsForm,
      name: 'Special_Requests_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '✨ Do you have any special requests or dietary requirements?',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'dietary_restrictions',
            type: 'select',
            label: 'Dietary Restrictions',
            options: ['None', 'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Nut Allergies', 'Other'],
            required: false
          },
          {
            id: generateUUID(),
            name: 'special_occasion',
            type: 'select',
            label: 'Special Occasion',
            options: ['None', 'Birthday', 'Anniversary', 'Date Night', 'Business Dinner', 'Celebration'],
            required: false
          },
          {
            id: generateUUID(),
            name: 'special_requests',
            type: 'text',
            label: 'Additional Requests or Notes',
            placeholder: 'Enter any special requests or notes',
            required: false
          }
        ]
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.availabilityCheck,
      name: 'Availability_Check',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Checking availability for your requested time...',
        delay: 1000,
        conditions: [
          {
            type: 'contains',
            field: 'party_size',
            value: '9+',
            nextNodeId: nodeIds.largePartyHandling
          },
          {
            type: 'contains',
            field: 'preferred_time',
            value: '7:00 PM',
            nextNodeId: nodeIds.peakTimeHandling
          },
          {
            type: 'contains',
            field: 'preferred_time',
            value: '7:30 PM',
            nextNodeId: nodeIds.peakTimeHandling
          },
          {
            type: 'contains',
            field: 'preferred_time',
            value: '8:00 PM',
            nextNodeId: nodeIds.peakTimeHandling
          }
        ],
        defaultNextNodeId: nodeIds.standardAvailability
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.menuInformation,
      name: 'Menu_Information',
      type: 'text',
      content: {
        type: 'text',
        text: '📋 **Our Menu Highlights**\n\n🥗 **Appetizers** ($8-$16)\n• Truffle Arancini\n• Burrata with Prosciutto\n• Seafood Crudo\n\n🥩 **Main Courses** ($24-$48)\n• Dry-Aged Ribeye\n• Pan-Seared Salmon\n• Handmade Pasta\n• Roasted Chicken\n\n🍰 **Desserts** ($9-$14)\n• Tiramisu\n• Chocolate Lava Cake\n• Seasonal Fruit Tart\n\nFull menu available at restaurant.example.com/menu',
        delay: 1500
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.wineSelection,
      name: 'Wine_Selection',
      type: 'text',
      content: {
        type: 'text',
        text: '🍷 **Wine & Beverage Program**\n\n🍾 **Wine Selection**\n• 200+ bottles from around the world\n• Sommelier-curated pairings\n• Wine flights available\n• By-the-glass options\n\n🍸 **Craft Cocktails**\n• Signature cocktails\n• Classic preparations\n• Premium spirits\n\n☕ **Non-Alcoholic**\n• Artisan coffee\n• Fresh juices\n• Sparkling water selection\n\nOur sommelier is available for wine consultations!',
        delay: 1500
      },
      position: {x: 500, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.standardAvailability,
      name: 'Standard_Availability',
      type: 'text',
      content: {
        type: 'text',
        text: '✅ **Reservation Confirmed!**\n\n📋 **Reservation Details:**\n• Name: {{guest_name}}\n• Date: {{preferred_date}}\n• Time: {{preferred_time}}\n• Party Size: {{party_size}} guests\n• Confirmation #: RES-{{confirmation_number}}\n\n📞 **Contact Info:**\n• Phone: (555) 123-DINE\n• Address: 123 Culinary Street\n\n⏰ **Please arrive 10 minutes early**\n📧 **Confirmation email sent**',
        delay: 1500
      },
      position: {x: 50, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    },
    {
      id: nodeIds.peakTimeHandling,
      name: 'Peak_Time_Handling',
      type: 'text',
      content: {
        type: 'text',
        text: '⏰ **Peak Time Reservation**\n\nYour requested time is during our busy period. We have the following options:\n\n✅ **Available Times:**\n• {{preferred_time}} - Available with 2-hour dining limit\n• Earlier: 6:00 PM - Full evening available\n• Later: 9:00 PM - Full evening available\n\n🎯 **We recommend:**\n• Arriving promptly for peak times\n• Pre-ordering wine or cocktails\n• Considering our chef\'s tasting menu\n\nShall I confirm your {{preferred_time}} reservation?',
        delay: 1500
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.largePartyHandling,
      name: 'Large_Party_Handling',
      type: 'text',
      content: {
        type: 'text',
        text: '👥 **Large Party Reservation**\n\nFor parties of 9 or more, we offer special arrangements:\n\n🍽️ **Private Dining Options:**\n• Semi-private dining room (up to 12)\n• Full private room (up to 20)\n• Chef\'s table experience (up to 8)\n\n📋 **Special Services:**\n• Pre-fixe menu options\n• Dedicated server\n• Custom wine pairings\n• Special occasion arrangements\n\nI\'m connecting you with our events coordinator for personalized service.',
        delay: 1500
      },
      position: {x: 350, y: 1100},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.privateEvents,
      name: 'Private_Events',
      type: 'text',
      content: {
        type: 'text',
        text: '🎉 **Private Events & Catering**\n\n🏢 **Venue Options:**\n• Full restaurant buyout (up to 80 guests)\n• Private dining room (up to 20 guests)\n• Semi-private areas (up to 12 guests)\n\n🍽️ **Event Services:**\n• Custom menu planning\n• Wine and cocktail packages\n• Floral arrangements\n• Audio/visual equipment\n• Dedicated event coordinator\n\n📅 **Popular Events:**\n• Corporate dinners\n• Wedding receptions\n• Birthday celebrations\n• Holiday parties',
        delay: 1500
      },
      position: {x: 350, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.reservationOptions,
      name: 'Reservation_Options',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'Would you like to proceed with this reservation?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '✅ Confirm Reservation', value: 'confirm'},
          {id: generateUUID(), text: '⏰ Try Different Time', value: 'different_time'},
          {id: generateUUID(), text: '📞 Call Restaurant', value: 'call'},
          {id: generateUUID(), text: '💬 Speak with Host', value: 'host'}
        ],
        storeInVariable: 'reservation_action'
      },
      position: {x: 200, y: 1300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.restaurantHostHandoff,
      name: 'Restaurant_Host_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🍽️ I\'m connecting you with our restaurant host who can assist with your reservation, answer menu questions, and help with any special arrangements.',
        delay: 1000,
        reason: 'Restaurant reservation and dining assistance'
      },
      position: {x: 200, y: 1500},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.restaurantWelcome, targetNodeId: nodeIds.serviceOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.reservationForm, condition: 'reservation'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.menuInformation, condition: 'menu'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.wineSelection, condition: 'wine'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.privateEvents, condition: 'events'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceOptions, targetNodeId: nodeIds.restaurantHostHandoff, condition: 'contact'},
    {id: generateUUID(), sourceNodeId: nodeIds.reservationForm, targetNodeId: nodeIds.specialRequestsForm},
    {id: generateUUID(), sourceNodeId: nodeIds.specialRequestsForm, targetNodeId: nodeIds.availabilityCheck},
    {id: generateUUID(), sourceNodeId: nodeIds.peakTimeHandling, targetNodeId: nodeIds.reservationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.largePartyHandling, targetNodeId: nodeIds.restaurantHostHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.menuInformation, targetNodeId: nodeIds.reservationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.wineSelection, targetNodeId: nodeIds.reservationOptions},
    {id: generateUUID(), sourceNodeId: nodeIds.privateEvents, targetNodeId: nodeIds.restaurantHostHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.reservationOptions, targetNodeId: nodeIds.restaurantHostHandoff}
  ],
  variables: [
    {
      name: 'service_option',
      type: 'string',
      description: 'Type of restaurant service requested',
      required: false
    },
    {
      name: 'guest_name',
      type: 'string',
      description: 'Name for the reservation',
      required: false
    },
    {
      name: 'phone_number',
      type: 'string',
      description: 'Guest phone number',
      required: false
    },
    {
      name: 'party_size',
      type: 'string',
      description: 'Number of guests for reservation',
      required: false
    },
    {
      name: 'preferred_date',
      type: 'string',
      description: 'Preferred reservation date',
      required: false
    },
    {
      name: 'preferred_time',
      type: 'string',
      description: 'Requested reservation time',
      required: false
    },
    {
      name: 'seating_preference',
      type: 'string',
      description: 'Guest seating preference',
      required: false
    },
    {
      name: 'dietary_restrictions',
      type: 'string',
      description: 'Guest dietary restrictions',
      required: false
    },
    {
      name: 'special_occasion',
      type: 'string',
      description: 'Special occasion for reservation',
      required: false
    },
    {
      name: 'special_requests',
      type: 'string',
      description: 'Additional special requests or notes',
      required: false
    },
    {
      name: 'reservation_action',
      type: 'string',
      description: 'Action selected for reservation',
      required: false
    },
    {
      name: 'confirmation_number',
      type: 'string',
      description: 'Generated reservation confirmation number',
      required: false
    }
  ],
  metadata: {
    totalNodes: 13,
    difficulty: 'intermediate',
    estimatedSetupTime: 30,
    tags: ['restaurant', 'reservations', 'forms', 'conditional', 'quick-reply', 'hospitality'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Complete restaurant reservation system with menu information, special requests handling, and availability management. Perfect for restaurants and hospitality businesses. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

/**
 * Real Estate Assistant <PERSON><PERSON> Template (Intermediate)
 * Demonstrates property search with FORM, CONDITIONAL, and QUICK_REPLY nodes
 */

const uuid = require('uuid');

function generateUUID() {
  return uuid.v4();
}

// Generate consistent UUIDs for this template
const nodeIds = {
  realEstateAssistantBot: generateUUID(),
  realEstateWelcome: generateUUID(),
  serviceType: generateUUID(),
  propertySearchForm: generateUUID(),
  sellingForm: generateUUID(),
  budgetAnalysis: generateUUID(),
  luxuryProperties: generateUUID(),
  premiumProperties: generateUUID(),
  standardProperties: generateUUID(),
  affordableProperties: generateUUID(),
  propertyValuation: generateUUID(),
  agentHandoff: generateUUID(),
  budgetMax: generateUUID(),
  propertyType: generateUUID(),
  location: generateUUID()
};

module.exports = {
  name: 'Real Estate Assistant Bot',
  description: 'Property search and real estate assistance with advanced filtering',
  isTemplate: true,
  nodes: [
    {
      id: nodeIds.realEstateWelcome,
      name: 'Real_Estate_Welcome',
      type: 'text',
      content: {
        type: 'text',
        text: '🏡 Welcome to our real estate portal! I\'m here to help you find your perfect property. Whether you\'re buying, selling, or renting, I can assist you.',
        delay: 1000
      },
      position: {x: 200, y: 100},
      isStartNode: true,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.serviceType,
      name: 'Service_Type',
      type: 'quick_reply',
      content: {
        type: 'quick_reply',
        text: 'What can I help you with today?',
        delay: 500,
        options: [
          {id: generateUUID(), text: '🏠 Buy a Property', value: 'buy'},
          {id: generateUUID(), text: '💰 Sell My Property', value: 'sell'},
          {id: generateUUID(), text: '🏢 Rent a Property', value: 'rent'},
          {id: generateUUID(), text: '📊 Market Analysis', value: 'market'},
          {id: generateUUID(), text: '💬 Speak with Agent', value: 'agent'}
        ],
        storeInVariable: 'service_type'
      },
      position: {x: 200, y: 300},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.propertySearchForm,
      name: 'Property_Search_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '🔍 Let me help you find the perfect property! Please share your preferences:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'location',
            type: 'text',
            label: 'Preferred Location/City',
            placeholder: 'Enter city, neighborhood, or ZIP code',
            required: true
          },
          {
            id: generateUUID(),
            name: 'property_type',
            type: 'select',
            label: 'Property Type',
            options: ['House', 'Condo', 'Townhouse', 'Apartment', 'Land', 'Commercial'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'budget_min',
            type: 'text',
            label: 'Minimum Budget',
            placeholder: 'Enter minimum budget (e.g., 200000)',
            required: false
          },
          {
            id: generateUUID(),
            name: 'budget_max',
            type: 'text',
            label: 'Maximum Budget',
            placeholder: 'Enter maximum budget (e.g., 500000)',
            required: true
          },
          {
            id: generateUUID(),
            name: 'bedrooms',
            type: 'select',
            label: 'Bedrooms',
            options: ['1', '2', '3', '4', '5+'],
            required: false
          },
          {
            id: generateUUID(),
            name: 'bathrooms',
            type: 'select',
            label: 'Bathrooms',
            options: ['1', '1.5', '2', '2.5', '3', '3+'],
            required: false
          }
        ]
      },
      position: {x: 50, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.sellingForm,
      name: 'Selling_Form',
      type: 'form',
      content: {
        type: 'form',
        text: '💰 I\'ll help you sell your property! Please provide some details:',
        delay: 1000,
        formFields: [
          {
            id: generateUUID(),
            name: 'property_address',
            type: 'text',
            label: 'Property Address',
            placeholder: 'Enter complete property address',
            required: true
          },
          {
            id: generateUUID(),
            name: 'sell_property_type',
            type: 'select',
            label: 'Property Type',
            options: ['House', 'Condo', 'Townhouse', 'Land', 'Commercial'],
            required: true
          },
          {
            id: generateUUID(),
            name: 'sell_bedrooms',
            type: 'select',
            label: 'Bedrooms',
            options: ['1', '2', '3', '4', '5+'],
            required: false
          },
          {
            id: generateUUID(),
            name: 'square_feet',
            type: 'text',
            label: 'Square Feet',
            placeholder: 'Enter approximate square footage',
            required: false
          },
          {
            id: generateUUID(),
            name: 'timeline',
            type: 'select',
            label: 'When do you want to sell?',
            options: ['ASAP', 'Within 3 months', 'Within 6 months', 'Within a year', 'Just exploring'],
            required: true
          }
        ]
      },
      position: {x: 200, y: 500},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.budgetAnalysis,
      name: 'Budget_Analysis',
      type: 'conditional',
      content: {
        type: 'conditional',
        text: 'Let me analyze your budget and preferences...',
        delay: 1000,
        conditions: [
          {
            type: 'contains',
            field: 'budget_max',
            value: '1000000',
            nextNodeId: nodeIds.luxuryProperties
          },
          {
            type: 'contains',
            field: 'budget_max',
            value: '500000',
            nextNodeId: nodeIds.premiumProperties
          },
          {
            type: 'contains',
            field: 'budget_max',
            value: '200000',
            nextNodeId: nodeIds.affordableProperties
          }
        ],
        defaultNextNodeId: nodeIds.standardProperties
      },
      position: {x: 50, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.luxuryProperties,
      name: 'Luxury_Properties',
      type: 'text',
      content: {
        type: 'text',
        text: '✨ **Luxury Property Search**\n\nBased on your budget, I can show you our premium luxury properties featuring:\n\n• High-end finishes and amenities\n• Prime locations\n• Exclusive neighborhoods\n• Concierge services\n• Private showings available\n\nI\'m connecting you with our luxury property specialist.',
        delay: 1500
      },
      position: {x: 50, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.premiumProperties,
      name: 'Premium_Properties',
      type: 'text',
      content: {
        type: 'text',
        text: '🏘️ **Premium Property Search**\n\nGreat budget range! I can show you excellent properties with:\n\n• Modern amenities\n• Desirable neighborhoods\n• Good investment potential\n• Quality construction\n• Professional photography\n\nLet me connect you with our premium property specialist.',
        delay: 1500
      },
      position: {x: 200, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.standardProperties,
      name: 'Standard_Properties',
      type: 'text',
      content: {
        type: 'text',
        text: '🏠 **Standard Property Search**\n\nPerfect! I can help you find great properties in your budget:\n\n• Well-maintained homes\n• Good neighborhoods\n• Value for money\n• Move-in ready options\n• Financing assistance available\n\nOur agents will help you find the best deals.',
        delay: 1500
      },
      position: {x: 350, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.affordableProperties,
      name: 'Affordable_Properties',
      type: 'text',
      content: {
        type: 'text',
        text: '💡 **Affordable Property Options**\n\nI understand budget is important. Let me help you find:\n\n• First-time buyer programs\n• Fixer-upper opportunities\n• Up-and-coming neighborhoods\n• Government assistance programs\n• Rent-to-own options\n\nOur specialists know all the best affordable options.',
        delay: 1500
      },
      position: {x: 500, y: 900},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.propertyValuation,
      name: 'Property_Valuation',
      type: 'text',
      content: {
        type: 'text',
        text: '📊 **Property Valuation Request**\n\nThank you for the details! Here\'s what happens next:\n\n• Free market analysis within 24 hours\n• Comparable property research\n• Professional valuation report\n• Marketing strategy consultation\n• No obligation estimate\n\nOur listing specialist will contact you soon.',
        delay: 1500
      },
      position: {x: 200, y: 700},
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    },
    {
      id: nodeIds.agentHandoff,
      name: 'Agent_Handoff',
      type: 'handoff',
      content: {
        type: 'handoff',
        text: '🏡 I\'m connecting you with one of our experienced real estate agents who can provide personalized assistance and answer all your questions.',
        delay: 1000,
        reason: 'Real estate consultation requested'
      },
      position: {x: 200, y: 1100},
      isStartNode: false,
      isEndNode: true,
      validationErrors: []
    }
  ],
  connections: [
    {id: generateUUID(), sourceNodeId: nodeIds.realEstateWelcome, targetNodeId: nodeIds.serviceType},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceType, targetNodeId: nodeIds.propertySearchForm, condition: 'buy'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceType, targetNodeId: nodeIds.propertySearchForm, condition: 'rent'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceType, targetNodeId: nodeIds.sellingForm, condition: 'sell'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceType, targetNodeId: nodeIds.agentHandoff, condition: 'market'},
    {id: generateUUID(), sourceNodeId: nodeIds.serviceType, targetNodeId: nodeIds.agentHandoff, condition: 'agent'},
    {id: generateUUID(), sourceNodeId: nodeIds.propertySearchForm, targetNodeId: nodeIds.budgetAnalysis},
    {id: generateUUID(), sourceNodeId: nodeIds.sellingForm, targetNodeId: nodeIds.propertyValuation},
    {id: generateUUID(), sourceNodeId: nodeIds.luxuryProperties, targetNodeId: nodeIds.agentHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.premiumProperties, targetNodeId: nodeIds.agentHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.standardProperties, targetNodeId: nodeIds.agentHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.affordableProperties, targetNodeId: nodeIds.agentHandoff},
    {id: generateUUID(), sourceNodeId: nodeIds.propertyValuation, targetNodeId: nodeIds.agentHandoff}
  ],
  variables: [
    {
      name: 'service_type',
      type: 'string',
      description: 'Type of real estate service requested',
      required: false
    },
    {
      name: 'location',
      type: 'string',
      description: 'Preferred location for property',
      required: false
    },
    {
      name: 'property_type',
      type: 'string',
      description: 'Type of property being searched',
      required: false
    },
    {
      name: 'budget_min',
      type: 'string',
      description: 'Minimum budget for property search',
      required: false
    },
    {
      name: 'budget_max',
      type: 'string',
      description: 'Maximum budget for property search',
      required: false
    },
    {
      name: 'bedrooms',
      type: 'string',
      description: 'Number of bedrooms required',
      required: false
    },
    {
      name: 'bathrooms',
      type: 'string',
      description: 'Number of bathrooms required',
      required: false
    },
    {
      name: 'property_address',
      type: 'string',
      description: 'Address of property to sell',
      required: false
    },
    {
      name: 'sell_property_type',
      type: 'string',
      description: 'Type of property being sold',
      required: false
    },
    {
      name: 'sell_bedrooms',
      type: 'string',
      description: 'Number of bedrooms in property being sold',
      required: false
    },
    {
      name: 'square_feet',
      type: 'string',
      description: 'Square footage of property',
      required: false
    },
    {
      name: 'timeline',
      type: 'string',
      description: 'Timeline for selling property',
      required: false
    }
  ],
  metadata: {
    totalNodes: 12,
    difficulty: 'intermediate',
    estimatedSetupTime: 30,
    tags: ['real-estate', 'property', 'forms', 'conditional', 'quick-reply'],
    nodeTypes: ['text', 'quick_reply', 'form', 'conditional', 'handoff'],
    description: 'Comprehensive real estate assistant with property search, selling assistance, and budget-based routing. Perfect for real estate agencies. All variables follow lowercase naming conventions and conditional logic uses valid operators.',
    validationCompliant: true,
    lastUpdated: '2024-01-15'
  }
};

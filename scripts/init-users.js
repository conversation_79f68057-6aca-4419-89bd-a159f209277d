/**
 * <PERSON><PERSON>t to initialize default user permissions and organization roles
 * Run with: node scripts/init-users.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');

// Define models locally to avoid circular dependencies
const UserPermissionSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Name is required'],
        unique:true,
        trim: true
    },
    code: {
        type: String,
        required: [true, 'Code is required'],
        unique:true,
        trim: true
    }
}, {timestamps: true, strict: false});
UserPermissionSchema.index({code: 1, module: 1}, {unique: true});

// Define UserPermissionGroup schema
const UserPermissionGroupSchema = new mongoose.Schema({}, {timestamps: true, strict: false});

const UserPermission = mongoose.model('UserPermission', UserPermissionSchema);
const UserPermissionGroup = mongoose.model('UserPermissionGroup', UserPermissionGroupSchema);

// Default user permissions by module
const defaultPermissions = {
    // Dashboard and overview permissions
    dashboard: [
        {name: 'View Dashboard', description: 'Can view the organization dashboard', code: 'VIEW'},
        {name: 'View Analytics', description: 'Can view analytics in dashboard', code: 'VIEW_ANALYTICS'},
    ],
    // Support module permissions
    support: [
        {name: 'View Tickets', description: 'Can view support tickets', code: 'VIEW_TICKETS'},
        {name: 'Create Tickets', description: 'Can create support tickets', code: 'CREATE_TICKETS'},
        {name: 'Assign Tickets', description: 'Can assign tickets to team members', code: 'ASSIGN_TICKETS'},
        {name: 'Close Tickets', description: 'Can close support tickets', code: 'CLOSE_TICKETS'},
        {name: 'Delete Tickets', description: 'Can delete support tickets', code: 'DELETE_TICKETS'},
        {name: 'Chat With Users', description: 'Can chat with end-users', code: 'CHAT'},
        {name: 'View Chat History', description: 'Can view chat history', code: 'VIEW_CHAT_HISTORY'},
    ],
    // Error logging permissions
    errorLogging: [
        {name: 'View Errors', description: 'Can view error logs', code: 'VIEW_ERRORS'},
        {name: 'Assign Errors', description: 'Can assign errors to team members', code: 'ASSIGN_ERRORS'},
        {name: 'Resolve Errors', description: 'Can mark errors as resolved', code: 'RESOLVE_ERRORS'},
        {name: 'Delete Errors', description: 'Can delete error logs', code: 'DELETE_ERRORS'},
        {name: 'Configure Alerts', description: 'Can configure error alerts', code: 'CONFIGURE_ALERTS'},
    ],
    // Team management permissions
    team: [
        {name: 'View Team', description: 'Can view team members', code: 'VIEW'},
        {name: 'Invite Members', description: 'Can invite new team members', code: 'INVITE'},
        {name: 'Remove Members', description: 'Can remove team members', code: 'REMOVE'},
        {name: 'Change Roles', description: 'Can change member roles', code: 'CHANGE_ROLES'},
    ],
    // Settings permissions
    settings: [
        {name: 'View Settings', description: 'Can view organization settings', code: 'VIEW'},
        {name: 'Edit Settings', description: 'Can modify organization settings', code: 'EDIT'},
        {name: 'Manage Integrations', description: 'Can manage service integrations', code: 'MANAGE_INTEGRATIONS'},
        {name: 'Configure Webhooks', description: 'Can configure webhooks', code: 'WEBHOOKS'},
        {name: 'Configure API Keys', description: 'Can manage API keys', code: 'API_KEYS'},
    ],
    // Billing permissions
    billing: [
        {name: 'View Invoices', description: 'Can view billing invoices', code: 'VIEW_INVOICES'},
        {name: 'Manage Payment Methods', description: 'Can manage payment methods', code: 'MANAGE_PAYMENT'},
        {name: 'Change Subscription', description: 'Can change subscription plan', code: 'CHANGE_SUBSCRIPTION'},
        {name: 'View Usage', description: 'Can view usage statistics', code: 'VIEW_USAGE'},
    ],
};

// Default organization roles (permission groups for users)
const defaultRoles = [
    {
        name: 'Admin',
        description: 'Full access to all organization features',
        permissions: [], // Admin role doesn't need explicit permissions as they have full access by default
        isDefault: true,
        code: 'admin'
    },
    {
        name: 'Manager',
        description: 'Can manage team and all operational aspects except billing and settings',
        permissions: [
            // Dashboard permissions
            'dashboard:VIEW', 'dashboard:VIEW_ANALYTICS',
            // Support permissions
            'support:VIEW_TICKETS', 'support:CREATE_TICKETS', 'support:ASSIGN_TICKETS',
            'support:CLOSE_TICKETS', 'support:CHAT', 'support:VIEW_CHAT_HISTORY',
            // Error logging permissions
            'errorLogging:VIEW_ERRORS', 'errorLogging:ASSIGN_ERRORS', 'errorLogging:RESOLVE_ERRORS',
            'errorLogging:CONFIGURE_ALERTS',
            // Team permissions
            'team:VIEW', 'team:INVITE', 'team:REMOVE', 'team:CHANGE_ROLES',
            // Limited settings
            'settings:VIEW',
            // Limited billing
            'billing:VIEW_INVOICES', 'billing:VIEW_USAGE',
        ],
        isDefault: true,
        code: 'manager'
    },
    {
        name: 'Support Agent',
        description: 'Dedicated to customer support functions',
        permissions: [
            // Dashboard permissions
            'dashboard:VIEW',
            // Support permissions
            'support:VIEW_TICKETS', 'support:CREATE_TICKETS', 'support:CLOSE_TICKETS',
            'support:CHAT', 'support:VIEW_CHAT_HISTORY',
            // Limited error logging
            'errorLogging:VIEW_ERRORS', 'errorLogging:ASSIGN_ERRORS', 'errorLogging:RESOLVE_ERRORS',
            // Team access
            'team:VIEW',
        ],
        isDefault: true,
        code: 'support'
    },
    {
        name: 'Developer',
        description: 'Focus on error logging and technical integrations',
        permissions: [
            // Dashboard permissions
            'dashboard:VIEW',
            // Limited support permissions
            'support:VIEW_TICKETS', 'support:CREATE_TICKETS',
            // Error logging permissions
            'errorLogging:VIEW_ERRORS', 'errorLogging:ASSIGN_ERRORS', 'errorLogging:RESOLVE_ERRORS',
            'errorLogging:CONFIGURE_ALERTS',
            // Team access
            'team:VIEW',
            // Integration-related settings
            'settings:VIEW', 'settings:MANAGE_INTEGRATIONS', 'settings:WEBHOOKS', 'settings:API_KEYS',
        ],
        isDefault: true,
        code: 'developer'
    },
    {
        name: 'Member',
        description: 'Basic team member with limited access',
        permissions: [
            // Dashboard permissions
            'dashboard:VIEW',
            // Basic support permissions
            'support:VIEW_TICKETS', 'support:CREATE_TICKETS', 'support:CHAT',
            // Basic error viewing
            'errorLogging:VIEW_ERRORS',
            // Team access
            'team:VIEW',
        ],
        isDefault: true,
        code: 'member'
    },
    {
        name: 'Guest',
        description: 'Limited view-only access',
        permissions: [
            // Dashboard permissions
            'dashboard:VIEW',
            // View-only permissions
            'support:VIEW_TICKETS',
            'errorLogging:VIEW_ERRORS',
            'team:VIEW',
        ],
        isDefault: true,
        code: 'guest'
    }
];

// Connect to MongoDB
async function connectToDatabase() {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Failed to connect to MongoDB:', error);
        process.exit(1);
    }
}

// Create user permissions
async function createUserPermissions() {
    const permissionDocs = [];

    for (const [module, permissions] of Object.entries(defaultPermissions)) {
        for (const perm of permissions) {
            try {
                // Check if permission already exists
                const existingPerm = await UserPermission.findOne({
                    code: perm.code,
                    module: module
                });

                if (existingPerm) {
                    console.log(`User permission ${perm.code}:${module} already exists`);
                    permissionDocs.push(existingPerm);
                    continue;
                }

                // Create new permission
                const newPermission = await UserPermission.create({
                    name: perm.name,
                    description: perm.description,
                    code: perm.code,
                    module: module
                }, {});

                console.log(`Created user permission: ${perm.name} (${module}:${perm.code})`);
                permissionDocs.push(newPermission);
            } catch (error) {
                console.error(`Failed to create user permission ${perm.name}:`, error);
            }
        }
    }

    return permissionDocs;
}

// Create user permission groups (roles)
async function createUserRoles() {
    const roleDocs = [];
    for (const role of defaultRoles) {
        try {
            // Check if role already exists
            const existingRole = await UserPermissionGroup.findOne({
                code: role.code
            });

            if (existingRole) {
                console.log(`User role ${role.name} (${role.code}) already exists`);
                roleDocs.push(existingRole);
                continue;
            }

            // Create new role
            const newRole = await UserPermissionGroup.create({
                name: role.name,
                description: role.description,
                permissions: role.permissions,
                isDefault: role.isDefault,
                code: role.code
            });

            console.log(`Created user role: ${role.name} (${role.code})`);
            roleDocs.push(newRole);
        } catch (error) {
            console.error(`Failed to create user role ${role.name}:`, error);
        }
    }

    return roleDocs;
}

// Main function
async function init() {
    try {
        await connectToDatabase();
        console.log('Creating user permissions...');
        const permissions = await createUserPermissions();
        console.log('Creating user roles...');
        const roles = await createUserRoles();
        console.log('User initialization completed successfully');
    } catch (error) {
        console.error('User initialization failed:', error);
    } finally {
        await mongoose.connection.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the initialization
init();

/**
 * <PERSON><PERSON>t to initialize default system settings including tax and VAT rates
 * Run with: node scripts/init-settings.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');

// Define Settings schema locally to avoid circular dependencies
const SettingsSchema = new mongoose.Schema({}, { timestamps: true, strict: false });

const Settings = mongoose.model('Settings', SettingsSchema);

// Default settings configuration
const defaultSettings = {
  general: {
    key: 'general',
    maxUploadSize: 10,
    acceptedEmailDomains: []
  },
  security: {
    key: 'security',
    authenticationMethod: 'email',
    adminMfa: true,
    sessionTimeout: 60,
    maxLoginAttempts: 5,
    passwordMinLength: 8,
    passwordExpiryDays: 90
  },
  system: {
    key: 'system',
    vatPercentage: 7.5,
    taxPercentage: 5,
    lastUpdatedAt: new Date()
  }
};

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    process.exit(1);
  }
}

// Initialize or update settings
async function initializeSettings() {
  for (const [category, settings] of Object.entries(defaultSettings)) {
    try {
      // Check if settings already exist for this category
      const existingSettings = await Settings.findOne({ key: settings.key });

      if (existingSettings) {
        console.log(`Settings for "${category}" already exist`);
        continue;
      }

      // Create new settings entry
      await Settings.create(settings);
      console.log(`Created ${category} settings with default configuration`);

      if (category === 'system') {
        console.log(`Default VAT: ${settings.vatPercentage}%, Default Tax: ${settings.taxPercentage}%`);
      }
    } catch (error) {
      console.error(`Failed to initialize ${category} settings:`, error);
    }
  }
}

// Main function
async function init() {
  try {
    await connectToDatabase();
    console.log('Initializing system settings...');
    await initializeSettings();
    console.log('Settings initialization completed successfully');
  } catch (error) {
    console.error('Settings initialization failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the initialization
init(); 
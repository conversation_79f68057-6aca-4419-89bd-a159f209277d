/**
 * <PERSON><PERSON>t to create a test bot configuration for widget development
 * Run with: node scripts/create-test-bot-config.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');

// Define schemas locally to avoid circular dependencies
const LiveChatBotSchema = new mongoose.Schema({}, { timestamps: true, strict: false });
const BotConversationFlowSchema = new mongoose.Schema({}, { timestamps: true, strict: false });
const LiveChatConfigSchema = new mongoose.Schema({}, { timestamps: true, strict: false });

const LiveChatBot = mongoose.model('LiveChatBot', LiveChatBotSchema);
const BotConversationFlow = mongoose.model('BotConversationFlow', BotConversationFlowSchema);
const LiveChatConfig = mongoose.model('LiveChatConfig', LiveChatConfigSchema);

// Test credentials
const TEST_APP_ID = 'c9a8a6295f376db6497d12314';
const TEST_API_KEY = 'lc_9f13c8e65f1e47e2a46d2f2100237d90655b5972cd28fdf1e0eaf4a7dc59fc26';

async function createTestBotConfig() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Find the LiveChatConfig to get organization ID
    const liveChatConfig = await LiveChatConfig.findOne({
      'auth.appId': TEST_APP_ID,
      'auth.apiKey': TEST_API_KEY
    });

    if (!liveChatConfig) {
      console.log('❌ LiveChatConfig not found. Please run create-test-livechat-config.js first.');
      return;
    }

    const organizationId = liveChatConfig.organizationId;
    console.log(`📋 Found LiveChatConfig for organization: ${organizationId}`);

    // Check if bot already exists
    let testBot = await LiveChatBot.findOne({
      organizationId: organizationId,
      liveChatConfigId: liveChatConfig._id
    });

    if (!testBot) {
      console.log('📝 Creating test bot...');
      testBot = new LiveChatBot({
        organizationId: organizationId,
        liveChatConfigId: liveChatConfig._id,
        status: 'active',
        isActive: true,
        personality: {
          name: 'Test Bot',
          description: 'A test bot for widget development',
          avatar: '',
          tone: 'friendly',
          language: 'en',
          welcomeMessage: 'Hello! I\'m your test bot. How can I help you today?',
          fallbackMessage: 'I\'m sorry, I didn\'t understand that. Could you please rephrase?',
          handoffMessage: 'Let me connect you with a human agent who can better assist you.'
        },
        schedule: {
          enabled: true,
          timezone: 'UTC',
          businessHours: {
            monday: { enabled: true, start: '09:00', end: '17:00' },
            tuesday: { enabled: true, start: '09:00', end: '17:00' },
            wednesday: { enabled: true, start: '09:00', end: '17:00' },
            thursday: { enabled: true, start: '09:00', end: '17:00' },
            friday: { enabled: true, start: '09:00', end: '17:00' },
            saturday: { enabled: false, start: '09:00', end: '17:00' },
            sunday: { enabled: false, start: '09:00', end: '17:00' }
          }
        },
        settings: {
          autoActivate: true,
          handoffConditions: {
            keywordTriggers: ['human', 'agent', 'help'],
            sentimentThreshold: -0.5,
            maxFailedAttempts: 3,
            escalationKeywords: ['urgent', 'emergency', 'complaint']
          },
          responseDelay: {
            enabled: true,
            minDelay: 1000,
            maxDelay: 3000
          },
          typingIndicator: true,
          collectUserInfo: true,
          requireHumanConfirmation: false
        },
        analytics: {
          totalConversations: 0,
          successfulResolutions: 0,
          handoffRate: 0,
          averageResponseTime: 0,
          topIntents: [],
          lastUpdated: new Date()
        },
        version: 1
      });
      await testBot.save();
      console.log('✅ Test bot created');
    } else {
      console.log('✅ Test bot already exists');
    }

    // Check if conversation flow already exists
    let testFlow = await BotConversationFlow.findOne({
      botId: testBot._id,
      organizationId: organizationId
    });

    if (!testFlow) {
      console.log('📝 Creating test conversation flow...');
      testFlow = new BotConversationFlow({
        botId: testBot._id,
        organizationId: organizationId,
        name: 'Test Conversation Flow',
        description: 'A simple test flow for widget development',
        status: 'published',
        isActive: true,
        version: 1,
        nodes: [
          {
            id: 'start',
            name: 'Welcome',
            type: 'text',
            isStartNode: true,
            content: {
              text: 'Hello! I\'m your test bot. I can help you with:\n\n• General questions\n• Product information\n• Support requests\n\nWhat would you like to know?'
            },
            position: { x: 100, y: 100 },
            connections: []
          },
          {
            id: 'help',
            name: 'Help Options',
            type: 'button',
            content: {
              text: 'Here are some ways I can help you:',
              buttons: [
                { text: 'Product Info', value: 'product_info' },
                { text: 'Support', value: 'support' },
                { text: 'Talk to Human', value: 'human_agent' }
              ]
            },
            position: { x: 300, y: 100 },
            connections: []
          }
        ],
        metadata: {
          createdBy: 'system',
          lastModifiedBy: 'system',
          tags: ['test', 'development']
        }
      });
      await testFlow.save();
      console.log('✅ Test conversation flow created');
    } else {
      // Update existing flow to ensure it's published and active
      testFlow.status = 'published';
      testFlow.isActive = true;
      await testFlow.save();
      console.log('✅ Test conversation flow already exists and updated');
    }

    console.log('');
    console.log('🎉 Test bot configuration completed successfully!');
    console.log('📋 Configuration details:');
    console.log(`   Organization ID: ${organizationId}`);
    console.log(`   Bot ID: ${testBot._id}`);
    console.log(`   Bot Status: ${testBot.status}`);
    console.log(`   Bot Active: ${testBot.isActive}`);
    console.log(`   Flow ID: ${testFlow._id}`);
    console.log(`   Flow Status: ${testFlow.status}`);
    console.log(`   Flow Active: ${testFlow.isActive}`);
    console.log('');
    console.log('🎯 The widget should now be able to validate bot prerequisites successfully!');

  } catch (error) {
    console.error('❌ Error creating test bot config:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
createTestBotConfig();

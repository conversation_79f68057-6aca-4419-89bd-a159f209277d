/**
 * Initialize comprehensive bot conversation flow templates
 * Organized by difficulty: <PERSON><PERSON><PERSON> (7), <PERSON> (8), Advanced (5+)
 * Run with: node scripts/init-comprehensive-bot-templates.js
 */
const path = require('path');
require('dotenv').config({path: path.join(path.resolve(process.cwd(), '../'), ".env.local")});
console.log(path.resolve(process.cwd(),'../'), 'here')
const mongoose = require('mongoose');
const fs = require('fs');

// Define schemas locally to avoid circular dependencies
const BotConversationFlowSchema = new mongoose.Schema({}, {
  timestamps: true,
  strict: false
});

const BotConversationFlow = mongoose.model('BotConversationFlow', BotConversationFlowSchema);

// Function to load all template files from directories
function loadTemplatesFromDirectory(directory) {
  const templates = [];
  const fullPath = path.join(__dirname, 'bot-templates', directory);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️ Directory not found: ${fullPath}`);
    return templates;
  }
  
  const files = fs.readdirSync(fullPath).filter(file => file.endsWith('.js'));
  
  for (const file of files) {
    try {
      const templatePath = path.join(fullPath, file);
      const template = require(templatePath);
      templates.push(template);
      console.log(`✅ Loaded template: ${template.name} (${directory})`);
    } catch (error) {
      console.error(`❌ Error loading template ${file}:`, error.message);
    }
  }
  
  return templates;
}

// Load all templates from organized directories
console.log('📂 Loading bot templates from directories...');
const beginnerTemplates = loadTemplatesFromDirectory('beginner');
const intermediateTemplates = loadTemplatesFromDirectory('intermediate');
const advancedTemplates = loadTemplatesFromDirectory('advanced');

// Combine all templates
const allTemplates = [
  ...beginnerTemplates,
  ...intermediateTemplates,
  ...advancedTemplates
];

// Database connection and initialization
async function initializeComprehensiveTemplates() {
  try {
    console.log(`🚀 Connecting to MongoDB... ${process.env.MONGODB_URI}`);
    if(!process.env.MONGODB_URI) throw new Error('MongoDB URI is missing');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('🗑️ Clearing existing templates...');
    await BotConversationFlow.deleteMany({ isTemplate: true });
    console.log('✅ Cleared existing templates');

    console.log('📝 Creating comprehensive bot templates...');
    let createdCount = 0;
    
    for (const template of allTemplates) {
      try {
        await BotConversationFlow.create(template);
        console.log(`✅ Created template: ${template.name} (${template.metadata?.difficulty || 'unknown'})`);
        createdCount++;
      } catch (error) {
        console.error(`❌ Error creating template ${template.name}:`, error.message);
      }
    }

    // Print summary
    console.log('\n📊 Template Creation Summary:');
    console.log(`🟢 Beginner Templates: ${beginnerTemplates.length}`);
    console.log(`🟡 Intermediate Templates: ${intermediateTemplates.length}`);
    console.log(`🔴 Advanced Templates: ${advancedTemplates.length}`);
    console.log(`📈 Total Templates Created: ${createdCount}/${allTemplates.length}`);
    
    // Print template details
    console.log('\n📋 Template Details:');
    allTemplates.forEach((template, index) => {
      const difficulty = template.metadata?.difficulty || 'unknown';
      const nodeCount = template.metadata?.totalNodes || template.nodes?.length || 0;
      const nodeTypes = template.metadata?.nodeTypes || [];
      console.log(`${index + 1}. ${template.name} (${difficulty}) - ${nodeCount} nodes - [${nodeTypes.join(', ')}]`);
    });

    console.log(`\n🎉 Successfully created ${createdCount} comprehensive bot templates!`);
    
  } catch (error) {
    console.error('❌ Error initializing templates:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Disconnected from MongoDB');
  }
}

// Run the script
if (require.main === module) {
  initializeComprehensiveTemplates();
}

module.exports = {
  allTemplates,
  beginnerTemplates,
  intermediateTemplates,
  advancedTemplates,
  initializeComprehensiveTemplates
};

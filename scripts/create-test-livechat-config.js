/**
 * <PERSON><PERSON>t to create a test LiveChatConfig entry for widget development
 * Run with: node scripts/create-test-livechat-config.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');

// Define schemas locally to avoid circular dependencies
const LiveChatConfigSchema = new mongoose.Schema({}, { timestamps: true, strict: false });
const OrganizationSchema = new mongoose.Schema({}, { timestamps: true, strict: false });

const LiveChatConfig = mongoose.model('LiveChatConfig', LiveChatConfigSchema);
const Organization = mongoose.model('Organization', OrganizationSchema);

// Test credentials
const TEST_APP_ID = 'c9a8a6295f376db6497d12314';
const TEST_API_KEY = 'lc_9f13c8e65f1e47e2a46d2f2100237d90655b5972cd28fdf1e0eaf4a7dc59fc26';

async function createTestLiveChatConfig() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Check if test config already exists
    const existingConfig = await LiveChatConfig.findOne({
      'auth.appId': TEST_APP_ID,
      'auth.apiKey': TEST_API_KEY
    });

    if (existingConfig) {
      console.log('✅ Test LiveChatConfig already exists');
      console.log('📋 Config details:');
      console.log(`   App ID: ${existingConfig.auth.appId}`);
      console.log(`   API Key: ${existingConfig.auth.apiKey}`);
      console.log(`   Organization ID: ${existingConfig.organizationId}`);
      console.log(`   Status: ${existingConfig.status}`);
      console.log(`   Active: ${existingConfig.auth.isActive}`);
      return;
    }

    // Find or create a test organization
    let testOrg = await Organization.findOne({ name: 'Test Organization' });
    
    if (!testOrg) {
      console.log('📝 Creating test organization...');
      testOrg = new Organization({
        name: 'Test Organization',
        email: '<EMAIL>',
        active: true,
        subscription: {
          plan: 'pro',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        }
      });
      await testOrg.save();
      console.log('✅ Test organization created');
    }

    console.log('📝 Creating test LiveChatConfig...');
    
    // Create test LiveChatConfig
    const testConfig = new LiveChatConfig({
      organizationId: testOrg._id,
      status: 'enabled',
      isSetupComplete: true,
      auth: {
        appId: TEST_APP_ID,
        apiKey: TEST_API_KEY,
        isActive: true,
      },
      branding: {
        companyName: 'Test Company',
        primaryColor: '#8178E8',
        secondaryColor: '#6964D3',
        accentColor: '#9333EA',
        backgroundColor: '#FFFFFF',
        textColor: '#1F2937',
        fontFamily: 'Inter, sans-serif',
      },
      settings: {
        welcomeMessage: 'Hi! How can we help you today?',
        offlineMessage: 'We\'re currently offline. Please leave a message and we\'ll get back to you.',
        placeholderText: 'Type your message...',
        enableFileUpload: true,
        enableEmojis: true,
        enableTypingIndicator: true,
        autoAssignment: true,
        requireCustomerEmail: false,
        businessHours: {
          enabled: false,
          timezone: 'UTC',
          schedule: {
            monday: { enabled: true, start: '09:00', end: '17:00' },
            tuesday: { enabled: true, start: '09:00', end: '17:00' },
            wednesday: { enabled: true, start: '09:00', end: '17:00' },
            thursday: { enabled: true, start: '09:00', end: '17:00' },
            friday: { enabled: true, start: '09:00', end: '17:00' },
            saturday: { enabled: false, start: '09:00', end: '17:00' },
            sunday: { enabled: false, start: '09:00', end: '17:00' },
          },
        },
        responseTime: {
          target: 5,
          autoResponse: {
            enabled: false,
            message: 'Thanks for your message! We\'ll get back to you shortly.',
            delay: 30,
          },
        },
      },
      widget: {
        position: 'bottom-right',
        size: 'medium',
        theme: 'light',
        showAgentPhotos: true,
        showOnlineStatus: true,
        enableSounds: true,
      },
      analytics: {
        enabled: true,
        trackVisitorInfo: true,
        trackPageViews: false,
        retentionDays: 90,
      },
      integration: {
        allowedDomains: ['localhost:5174', 'localhost:5173'],
        webhookUrl: '',
        customCSS: '',
        customJS: '',
      },
      setupCompletedAt: new Date(),
      lastModifiedAt: new Date(),
    });

    await testConfig.save();

    console.log('✅ Test LiveChatConfig created successfully!');
    console.log('📋 Configuration details:');
    console.log(`   App ID: ${TEST_APP_ID}`);
    console.log(`   API Key: ${TEST_API_KEY}`);
    console.log(`   Organization ID: ${testOrg._id}`);
    console.log(`   Status: enabled`);
    console.log(`   Active: true`);
    console.log('');
    console.log('🎯 You can now test the widget with these credentials!');

  } catch (error) {
    console.error('❌ Error creating test LiveChatConfig:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the script
createTestLiveChatConfig();

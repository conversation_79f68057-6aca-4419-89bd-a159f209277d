/**
 * <PERSON><PERSON>t to initialize default permissions and super admin account
 * Run with: node scripts/init-admin.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const ProductCodes = require('./ProductCodes.json');
// Define models locally to avoid circular dependencies
const AdminSchema = new mongoose.Schema({}, {timestamps: true, strict: false});

AdminSchema.pre('save', async function (next) {
    if (!this.isModified('password')) return next();
    this.password = await bcrypt.hash(this.password, 10);
    next();
});

const PermissionSchema = new mongoose.Schema({}, {timestamps: true, strict: false});

PermissionSchema.index({code: 1, module: 1}, {unique: true});

// Define Feature schema
const FeatureSchema = new mongoose.Schema({}, {timestamps: true, strict: false});

// Define Product schema
const ProductSchema = new mongoose.Schema({}, {timestamps: true, strict: false});

// Define PermissionGroup schema
const PermissionGroupSchema = new mongoose.Schema({}, {timestamps: true, strict: false});

const Admin = mongoose.model('Admin', AdminSchema);
const Permission = mongoose.model('Permission', PermissionSchema);
const Feature = mongoose.model('Feature', FeatureSchema);
const Product = mongoose.model('Product', ProductSchema);
const PermissionGroup = mongoose.model('PermissionGroup', PermissionGroupSchema);

// Default permissions by module
const defaultPermissions = {
    dashboard: [
        {name: 'View Dashboard', description: 'Can view the admin dashboard', code: 'VIEW'},
    ],
    users: [
        {name: 'View Users', description: 'Can view user list', code: 'VIEW'},
        {name: 'Create User', description: 'Can create new users', code: 'CREATE'},
        {name: 'Edit User', description: 'Can edit user details', code: 'EDIT'},
        {name: 'Delete User', description: 'Can delete users', code: 'DELETE'},
    ],
    permissions: [
        {name: 'View Permissions', description: 'Can view permission list', code: 'VIEW'},
        {name: 'Create Permission', description: 'Can create new permissions', code: 'CREATE'},
        {name: 'Edit Permission', description: 'Can edit permission details', code: 'EDIT'},
        {name: 'Delete Permission', description: 'Can delete permissions', code: 'DELETE'},
    ],
    permissionGroups: [
        {name: 'View Permission Groups', description: 'Can view permission group list', code: 'VIEW'},
        {name: 'Create Permission Group', description: 'Can create new permission groups', code: 'CREATE'},
        {name: 'Edit Permission Group', description: 'Can edit permission group details', code: 'EDIT'},
        {name: 'Delete Permission Group', description: 'Can delete permission groups', code: 'DELETE'},
    ],
    staff: [
        {name: 'View Staff', description: 'Can view staff list', code: 'VIEW'},
        {name: 'Create Staff', description: 'Can create new staff', code: 'CREATE'},
        {name: 'Edit Staff', description: 'Can edit staff details', code: 'EDIT'},
        {name: 'Delete Staff', description: 'Can delete staff', code: 'DELETE'},
    ],
    settings: [
        {name: 'View Settings', description: 'Can view platform settings', code: 'VIEW'},
        {name: 'Edit Settings', description: 'Can modify platform settings', code: 'EDIT'},
        {name: 'Manage Email Domains', description: 'Can manage accepted email domains', code: 'MANAGE_DOMAINS'},
        {name: 'View Email Domains', description: 'Can view the list of accepted email domains', code: 'VIEW_DOMAINS'},
        {name: 'Add Email Domains', description: 'Can add new domains to the accepted list', code: 'ADD_DOMAIN'},
        {name: 'Remove Email Domains', description: 'Can remove domains from the accepted list', code: 'REMOVE_DOMAIN'},
        {name: 'View Security Settings', description: 'Can view security settings', code: 'VIEW_SECURITY'},
        {name: 'Edit Security Settings', description: 'Can modify security settings', code: 'EDIT_SECURITY'},
    ],
    services: [
        {name: 'View Services', description: 'Can view service list', code: 'VIEW'},
        {name: 'Create Service', description: 'Can create new services', code: 'CREATE'},
        {name: 'Edit Service', description: 'Can edit service details', code: 'EDIT'},
        {name: 'Delete Service', description: 'Can delete services', code: 'DELETE'},
    ],
    products: [
        {name: 'View Products', description: 'Can view product list', code: 'VIEW'},
        {name: 'Edit Product', description: 'Can edit product details', code: 'EDIT'},
        {name: 'Activate Product', description: 'Can activate or deactivate products', code: 'ACTIVATE'},
    ],
    pricing: [
        {name: 'View Pricing Plans', description: 'Can view pricing plans', code: 'VIEW'},
        {name: 'Create Pricing Plan', description: 'Can create new pricing plans', code: 'CREATE'},
        {name: 'Edit Pricing Plan', description: 'Can edit pricing plan details', code: 'EDIT'},
        {name: 'Delete Pricing Plan', description: 'Can delete pricing plans', code: 'DELETE'},
    ],
    organizations: [
        {name: 'View Organizations', description: 'Can view organization list', code: 'VIEW'},
        {name: 'Create Organization', description: 'Can create new organizations', code: 'CREATE'},
        {name: 'Edit Organization', description: 'Can edit organization details', code: 'EDIT'},
        {name: 'Delete Organization', description: 'Can delete organizations', code: 'DELETE'},
    ],
    subscriptions: [
        {name: 'View Subscriptions', description: 'Can view subscription list', code: 'VIEW'},
        {name: 'Create Subscription', description: 'Can create new subscriptions', code: 'CREATE'},
        {name: 'Edit Subscription', description: 'Can edit subscription details', code: 'EDIT'},
        {name: 'Cancel Subscription', description: 'Can cancel subscriptions', code: 'CANCEL'},
    ]
};

// Default permission groups
const defaultGroups = [
    {
        name: 'Basic Staff',
        description: 'Basic staff access with view-only permissions',
        permissions: [
            'dashboard:VIEW',
            'users:VIEW',
            'products:VIEW',
            'pricing:VIEW',
            'organizations:VIEW',
            'subscriptions:VIEW',
            'settings:VIEW',
            'settings:VIEW_DOMAINS'
        ],
        isDefault: true
    },
    {
        name: 'User Manager',
        description: 'Can manage users but not staff or permissions',
        permissions: [
            'dashboard:VIEW',
            'users:VIEW', 'users:CREATE', 'users:EDIT', 'users:DELETE',
            'organizations:VIEW'
        ],
        isDefault: true
    },
    {
        name: 'Product Manager',
        description: 'Can manage products and pricing',
        permissions: [
            'dashboard:VIEW',
            'products:VIEW', 'products:EDIT', 'products:ACTIVATE',
            'pricing:VIEW', 'pricing:CREATE', 'pricing:EDIT', 'pricing:DELETE'
        ],
        isDefault: true
    },
    {
        name: 'Subscription Manager',
        description: 'Can manage subscriptions and organizations',
        permissions: [
            'dashboard:VIEW',
            'organizations:VIEW', 'organizations:EDIT',
            'subscriptions:VIEW', 'subscriptions:CREATE', 'subscriptions:EDIT', 'subscriptions:CANCEL',
            'pricing:VIEW'
        ],
        isDefault: true
    },
    {
        name: 'System Administrator',
        description: 'Can manage system settings and configurations',
        permissions: [
            'dashboard:VIEW',
            'settings:VIEW', 'settings:EDIT', 'settings:MANAGE_DOMAINS',
            'settings:VIEW_DOMAINS', 'settings:ADD_DOMAIN', 'settings:REMOVE_DOMAIN',
            'settings:VIEW_SECURITY', 'settings:EDIT_SECURITY'
        ],
        isDefault: true
    },
    {
        name: 'Admin Manager',
        description: 'Can manage everything except permissions',
        permissions: [
            'dashboard:VIEW',
            'users:VIEW', 'users:CREATE', 'users:EDIT', 'users:DELETE',
            'staff:VIEW', 'staff:CREATE', 'staff:EDIT', 'staff:DELETE',
            'settings:VIEW', 'settings:EDIT', 'settings:MANAGE_DOMAINS',
            'settings:VIEW_DOMAINS', 'settings:ADD_DOMAIN', 'settings:REMOVE_DOMAIN',
            'settings:VIEW_SECURITY', 'settings:EDIT_SECURITY',
            'products:VIEW', 'products:EDIT', 'products:ACTIVATE',
            'pricing:VIEW', 'pricing:CREATE', 'pricing:EDIT', 'pricing:DELETE',
            'organizations:VIEW', 'organizations:CREATE', 'organizations:EDIT', 'organizations:DELETE',
            'subscriptions:VIEW', 'subscriptions:CREATE', 'subscriptions:EDIT', 'subscriptions:CANCEL'
        ],
        isDefault: true
    },
    {
        name: 'Domain Administrator',
        description: 'Can manage email domains for admin access',
        permissions: [
            'dashboard:VIEW',
            'settings:VIEW',
            'settings:VIEW_DOMAINS',
            'settings:ADD_DOMAIN',
            'settings:REMOVE_DOMAIN',
            'staff:VIEW'
        ],
        isDefault: true
    }
];

// Default features
const defaultFeatures = [
    // Customer Support Module features
    {name: 'Real-time chat interface', description: 'Enable real-time chat communication with customers', active: true},
    {name: 'Ticket management system', description: 'Comprehensive system for managing customer tickets', active: true},
    {name: 'Customer history tracking', description: 'Track all interactions with customers over time', active: true},
    {name: 'SLA monitoring', description: 'Monitor and report on service level agreements', active: true},
    {
        name: 'Knowledge base integration',
        description: 'Integrated knowledge base for faster issue resolution',
        active: true
    },

    // Error Logging Service features
    {name: 'Real-time error tracking', description: 'Track errors as they happen in your applications', active: true},
    {
        name: 'Source mapping integration',
        description: 'Map compiled code back to original source for better debugging',
        active: true
    },
    {name: 'Error analytics and trends', description: 'Analyze error patterns and trends over time', active: true},
    {
        name: 'Resolution workflow tracking',
        description: 'Track the resolution process for identified errors',
        active: true
    },
    {name: 'Performance monitoring', description: 'Monitor application performance metrics', active: true},

    // Analytics Dashboard features
    {name: 'Custom report builder', description: 'Build custom reports to track important metrics', active: true},
    {name: 'Interactive dashboards', description: 'Create interactive dashboards for data visualization', active: true},
    {name: 'User behavior tracking', description: 'Track user behavior and engagement patterns', active: true},
    {name: 'Performance metrics', description: 'Monitor key performance indicators', active: true},
    {name: 'Export capabilities', description: 'Export reports and data in various formats', active: true},

    // Multi-Channel Integration features
    {name: 'Email integration', description: 'Integrate with email systems for communication', active: true},
    {name: 'SMS messaging', description: 'Send and receive SMS messages through the platform', active: true},
    {
        name: 'Social media connectors',
        description: 'Connect with social media platforms for customer engagement',
        active: true
    },
    {name: 'WhatsApp integration', description: 'Integrate with WhatsApp for business communication', active: true},
    {name: 'Unified inbox', description: 'Manage all communications in a unified inbox', active: true},

    // AI Assistant features
    {name: 'Automated responses', description: 'Automatically respond to common customer inquiries', active: true},
    {name: 'Sentiment analysis', description: 'Analyze customer sentiment in communications', active: true},
    {name: 'Intent recognition', description: 'Recognize customer intent for better routing', active: true},
    {
        name: 'Conversation summarization',
        description: 'Automatically summarize conversations for quick review',
        active: true
    },
    {name: 'Smart routing', description: 'Intelligently route customer inquiries to the right team', active: true}
];

// Default products (will be populated with actual feature IDs during initialization)
const defaultProducts = [
    {
        name: 'Customer Support Module',
        description: 'Comprehensive customer support tools for managing customer interactions',
        code: ProductCodes['CUSTOMER-SUPPORT'],
        featureIndices: [0, 1, 2, 3, 4], // Will be replaced with actual feature IDs
        category: 'core',
        active: true
    },
    {
        name: 'Error Logging Service',
        description: 'Comprehensive error tracking, analysis, and resolution workflow management',
        code: ProductCodes['ERROR-LOGGING'],
        featureIndices: [5, 6, 7, 8, 9], // Will be replaced with actual feature IDs
        category: 'core',
        active: true
    },
    {
        name: 'Analytics Dashboard',
        description: 'Advanced analytics and reporting for business intelligence',
        code: ProductCodes['ANALYTICS'],
        featureIndices: [10, 11, 12, 13, 14], // Will be replaced with actual feature IDs
        category: 'addon',
        active: true
    },
    {
        name: 'AI Assistant',
        description: 'AI-powered automation tools for customer service and analysis',
        code: ProductCodes['AI-ASSISTANT'],
        featureIndices: [20, 21, 22, 23, 24], // Will be replaced with actual feature IDs
        category: 'premium',
        active: false
    }
];

// Super admin config
const superAdmin = {
    name: process.env.SADMIN_NAME,
    email: process.env.SADMIN_EMAIL || '<EMAIL>',
    password: process.env.SADMIN_PASSWORD || 'Passw0rd123!',
    active: true,
    role: 'super_admin',
    isRoot: true
};

// Connect to MongoDB
async function connectToDatabase() {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('Connected to MongoDB');
    } catch (error) {
        console.error('Failed to connect to MongoDB:', error);
        process.exit(1);
    }
}

// Create permissions
async function createPermissions() {
    const permissionDocs = [];

    for (const [module, permissions] of Object.entries(defaultPermissions)) {
        for (const perm of permissions) {
            try {
                // Check if permission already exists
                const existingPerm = await Permission.findOne({
                    code: perm.code,
                    module: module
                });

                if (existingPerm) {
                    console.log(`Permission ${perm.code}:${module} already exists`);
                    permissionDocs.push(existingPerm);
                    continue;
                }

                // Create new permission
                const newPermission = await Permission.create({
                    name: perm.name,
                    description: perm.description,
                    code: perm.code,
                    module: module
                });

                console.log(`Created permission: ${perm.name} (${module}:${perm.code})`);
                permissionDocs.push(newPermission);
            } catch (error) {
                console.error(`Failed to create permission ${perm.name}:`, error);
            }
        }
    }

    return permissionDocs;
}

// Create features
async function createFeatures() {
    const featureDocs = [];

    for (const feature of defaultFeatures) {
        try {
            // Check if feature already exists
            const existingFeature = await Feature.findOne({name: feature.name});

            if (existingFeature) {
                console.log(`Feature ${feature.name} already exists`);
                featureDocs.push(existingFeature);
                continue;
            }

            // Create new feature
            const newFeature = await Feature.create(feature);
            console.log(`Created feature: ${feature.name}`);
            featureDocs.push(newFeature);
        } catch (error) {
            console.error(`Failed to create feature ${feature.name}:`, error);
        }
    }

    return featureDocs;
}

// Create products
async function createProducts(featureDocs) {
    const productDocs = [];

    for (const product of defaultProducts) {
        try {
            // Check if product already exists
            const existingProduct = await Product.findOne({code: product.code});

            if (existingProduct) {
                console.log(`Product ${product.name} (${product.code}) already exists`);
                productDocs.push(existingProduct);
                continue;
            }

            // Map feature indices to actual feature IDs
            const featureIds = product.featureIndices.map(index => {
                if (featureDocs[index]) {
                    return featureDocs[index]._id;
                }
                return null;
            }).filter(id => id !== null);

            // Create new product with actual feature IDs
            const productData = {
                name: product.name,
                description: product.description,
                code: product.code,
                featureIds: featureIds,
                category: product.category,
                active: product.active
            };

            const newProduct = await Product.create(productData);
            console.log(`Created product: ${product.name} (${product.code})`);
            productDocs.push(newProduct);
        } catch (error) {
            console.error(`Failed to create product ${product.name}:`, error);
        }
    }

    return productDocs;
}

// Create permission groups
async function createPermissionGroups() {
    const groupDocs = [];

    for (const group of defaultGroups) {
        try {
            // Check if group already exists
            const existingGroup = await PermissionGroup.findOne({name: group.name});

            if (existingGroup) {
                console.log(`Permission group ${group.name} already exists`);
                groupDocs.push(existingGroup);
                continue;
            }

            // Create new permission group
            const newGroup = await PermissionGroup.create({
                name: group.name,
                description: group.description,
                permissions: group.permissions,
                isDefault: group.isDefault
            });

            console.log(`Created permission group: ${group.name}`);
            groupDocs.push(newGroup);
        } catch (error) {
            console.error(`Failed to create permission group ${group.name}:`, error);
        }
    }

    return groupDocs;
}

// Create super admin user
async function createSuperAdmin() {
    try {
        // Check if super admin already exists
        const existingAdmin = await Admin.findOne({email: superAdmin.email});

        if (existingAdmin) {
            // Update isRoot flag if it doesn't exist
            if (existingAdmin.isRoot === undefined) {
                await Admin.updateOne({_id: existingAdmin._id}, {isRoot: true});
                console.log(`Updated super admin ${superAdmin.email} with isRoot flag`);
            }
            console.log(`Super admin with email ${superAdmin.email} already exists`);
            return;
        }

        // Create super admin
        await Admin.create(superAdmin);
        console.log(`Created super admin: ${superAdmin.email}`);
    } catch (error) {
        console.error('Failed to create super admin:', error);
    }
}

// Main function
async function init() {
    try {
        await connectToDatabase();
        await createPermissions();
        await createFeatures();
        await createProducts();
        await createPermissionGroups();
        await createSuperAdmin();
        console.log('Initialization completed successfully');
    } catch (error) {
        console.error('Initialization failed:', error);
    } finally {
        await mongoose.connection.close();
        console.log('Disconnected from MongoDB');
    }
}

// Run the initialization
init();

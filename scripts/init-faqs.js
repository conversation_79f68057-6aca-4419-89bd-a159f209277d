/**
 * <PERSON><PERSON><PERSON> to initialize default FAQs
 * Run with: node scripts/init-faqs.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');

// Define FAQ schema locally to avoid circular dependencies
const FaqSchema = new mongoose.Schema({
  question: {
    type: String,
    required: [true, 'Question is required'],
    trim: true,
  },
  answer: {
    type: String,
    required: [true, 'Answer is required'],
    trim: true,
  },
  type: {
    type: String,
    enum: ['billing', 'product'],
    default: 'billing',
    required: [true, 'Type is required'],
  },
  active: {
    type: Boolean,
    default: true,
  },
  order: {
    type: Number,
    default: 0,
  }
}, { timestamps: true });

// Create index for faster lookups by type
FaqSchema.index({ type: 1, order: 1 });

const Faq = mongoose.model('Faq', FaqSchema);

// Default billing FAQs
const defaultBillingFaqs = [
  {
    question: 'How does per-user pricing work?',
    answer: 'Your organization is billed based on the number of active users in your account. You can add or remove users at any time, and your bill will be prorated accordingly. This ensures you only pay for what you use.',
    type: 'billing',
    active: true,
    order: 1
  },
  {
    question: 'What happens when I add users mid-billing cycle?',
    answer: 'When you add new users, you\'ll be charged a prorated amount for the remainder of your current billing cycle. For example, if you add a user halfway through your billing cycle, you\'ll only be charged for half a month for that user.',
    type: 'billing',
    active: true,
    order: 2
  },
  {
    question: 'How can I change my subscription plan?',
    answer: 'You can upgrade or downgrade your plan anytime by clicking the "Change Plan" button. Upgrades take effect immediately, and you\'ll be charged a prorated amount for the remainder of your billing cycle. Downgrades take effect at the end of your current billing cycle.',
    type: 'billing',
    active: true,
    order: 3
  },
  {
    question: 'How do I get invoices for my payments?',
    answer: 'Invoices are automatically sent to your billing email address. You can also download them from the transaction history tab. If you need help with invoices, please contact our support team.',
    type: 'billing',
    active: true,
    order: 4
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'We accept all major credit cards (Visa, Mastercard, American Express) and bank transfers for annual plans. If you need alternative payment options, please contact our sales team.',
    type: 'billing',
    active: true,
    order: 5
  },
  {
    question: 'Is there a free trial available?',
    answer: 'Yes, we offer a 14-day free trial for all plans. No credit card is required to start your trial. You can upgrade to a paid plan at any time during or after your trial.',
    type: 'billing',
    active: true,
    order: 6
  }
];

// Default product FAQs
const defaultProductFaqs = [
  {
    question: 'What is Cloud Instance?',
    answer: 'Cloud Instance is a multi-service platform designed to provide integrated business solutions. Our core offerings include customer support management and error logging services, with more modules being added regularly.',
    type: 'product',
    active: true,
    order: 1
  },
  {
    question: 'How do I get started with Cloud Instance?',
    answer: 'After signing up, you\'ll need to create an organization. From there, you can invite team members, configure your settings, and start using our services. We recommend starting with our onboarding guide available in the dashboard.',
    type: 'product',
    active: true,
    order: 2
  },
  {
    question: 'Can I integrate Cloud Instance with my existing tools?',
    answer: 'Yes, Cloud Instance offers API integrations with popular tools like Slack, GitHub, and more. You can find integration options in your organization settings under the "Integrations" tab.',
    type: 'product',
    active: true,
    order: 3
  },
  {
    question: 'Is my data secure with Cloud Instance?',
    answer: 'Yes, we take security seriously. All data is encrypted in transit and at rest. We use industry-standard security practices and regularly undergo security audits. For more details, please see our security documentation.',
    type: 'product',
    active: true,
    order: 4
  }
];

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    return false;
  }
}

// Initialize FAQs
async function initializeFaqs() {
  try {
    // Check if FAQs already exist
    const existingFaqsCount = await Faq.countDocuments();
    
    if (existingFaqsCount > 0) {
      console.log(`${existingFaqsCount} FAQs already exist in the database. Skipping initialization.`);
      return;
    }
    
    // Insert billing FAQs
    console.log('Adding default billing FAQs...');
    await Faq.insertMany(defaultBillingFaqs);
    console.log(`Added ${defaultBillingFaqs.length} billing FAQs`);
    
    // Insert product FAQs
    console.log('Adding default product FAQs...');
    await Faq.insertMany(defaultProductFaqs);
    console.log(`Added ${defaultProductFaqs.length} product FAQs`);
    
    console.log('FAQ initialization completed successfully');
  } catch (error) {
    console.error('Error initializing FAQs:', error);
  }
}

// Main function
async function init() {
  try {
    const connected = await connectToDatabase();
    if (!connected) {
      process.exit(1);
    }
    
    await initializeFaqs();
  } catch (error) {
    console.error('Initialization failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the initialization
init();

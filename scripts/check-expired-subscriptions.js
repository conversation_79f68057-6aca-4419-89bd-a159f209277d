/**
 * Subscription Expiration Cron Service
 * Checks for expired subscriptions and suspends user access
 * 
 * Usage:
 *  - Run once: node scripts/check-expired-subscriptions.js --run-once
 *  - Run as service: node scripts/check-expired-subscriptions.js --interval=60
 *  - Show help: node scripts/check-expired-subscriptions.js --help
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  runOnce: args.includes('--run-once'),
  help: args.includes('--help'),
  interval: 60, // Default interval in minutes
  logPath: path.join(process.cwd(), 'logs')
};

// Parse interval if provided
const intervalArg = args.find(arg => arg.startsWith('--interval='));
if (intervalArg) {
  const intervalValue = parseInt(intervalArg.split('=')[1], 10);
  if (!isNaN(intervalValue) && intervalValue > 0) {
    options.interval = intervalValue;
  }
}

// Parse log path if provided
const logPathArg = args.find(arg => arg.startsWith('--log-path='));
if (logPathArg) {
  options.logPath = logPathArg.split('=')[1];
}

// Show help and exit
if (options.help) {
  console.log(`
  Subscription Expiration Cron Service
  
  Options:
    --run-once            Run the script once and exit
    --interval=MINUTES    Set the check interval in minutes (default: 60)
    --log-path=PATH       Set the path where logs will be stored
    --help                Show this help message
    
  Examples:
    node scripts/check-expired-subscriptions.js --run-once
    node scripts/check-expired-subscriptions.js --interval=30
  `);
  process.exit(0);
}

// Set up logging
if (!fs.existsSync(options.logPath)) {
  fs.mkdirSync(options.logPath, { recursive: true });
}

function logMessage(message, level = 'info') {
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
  
  console.log(logEntry);
  
  const logFile = path.join(options.logPath, `subscription-checks-${new Date().toISOString().split('T')[0]}.log`);
  fs.appendFileSync(logFile, logEntry + '\n');
}

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    logMessage('Connected to MongoDB');
    return true;
  } catch (error) {
    logMessage(`Failed to connect to MongoDB: ${error.message}`, 'error');
    return false;
  }
}

// Define schemas locally to avoid circular dependencies
const SubscriptionSchema = new mongoose.Schema({}, { timestamps: true, strict: false });
const UserOrgMembershipSchema = new mongoose.Schema({}, { timestamps: true, strict: false });
const ProductUserMembershipSchema = new mongoose.Schema({}, { timestamps: true, strict: false });

// Create models
const Subscription = mongoose.model('Subscription', SubscriptionSchema);
const UserOrgMembership = mongoose.model('UserOrgMembership', UserOrgMembershipSchema);
const ProductUserMembership = mongoose.model('ProductUserMembership', ProductUserMembershipSchema);

// Process expired subscriptions
async function processExpiredSubscriptions() {
  try {
    const currentDate = new Date();
    logMessage('Starting subscription expiration check...');
    
    // Find all expired subscriptions (status is ACTIVE but endDate is in the past)
    const expiredSubscriptions = await Subscription.find({
      status: 'active',
      endDate: { $lt: currentDate },
      autoRenewal: { $ne: true } // Only include those not set for auto-renewal
    });
    
    logMessage(`Found ${expiredSubscriptions.length} expired subscriptions`);
    
    if (expiredSubscriptions.length === 0) {
      return;
    }
    
    // Process each expired subscription
    for (const subscription of expiredSubscriptions) {
      const organizationId = subscription.organizationId;
      logMessage(`Processing organizationId: ${organizationId}`);
      
      // Update subscription status
      await Subscription.updateOne(
        { _id: subscription._id },
        { 
          $set: { 
            status: 'expired',
            metadata: {
              ...subscription.metadata,
              expiredAt: currentDate,
              membershipsSuspendedAt: currentDate
            }
          } 
        }
      );
      
      // Suspend all user org memberships
      const orgMembershipResult = await UserOrgMembership.updateMany(
        { organizationId, status: 'active' },
        { $set: { status: 'suspended' } }
      );
      
      logMessage(`Updated ${orgMembershipResult.modifiedCount} user organization memberships`);
      
      // Suspend all product user memberships
      const productMembershipResult = await ProductUserMembership.updateMany(
        { organizationId, status: 'active' },
        { $set: { status: 'suspended' } }
      );
      
      logMessage(`Updated ${productMembershipResult.modifiedCount} product user memberships`);
      
      // Create audit log for this action
      const AuditLogSchema = new mongoose.Schema({}, { timestamps: true, strict: false });
      const AuditLog = mongoose.model('AuditLog', AuditLogSchema);
      
      await AuditLog.create({
        action: 'subscription_expired_access_suspended',
        entityId: subscription._id,
        entityType: 'subscription',
        organizationId: organizationId,
        description: `Suspended user access due to expired subscription`,
        metadata: {
          subscriptionId: subscription._id.toString(),
          orgMemberships: orgMembershipResult.modifiedCount,
          productMemberships: productMembershipResult.modifiedCount,
          expiredAt: currentDate
        }
      });
    }
    
    logMessage('Subscription expiration check completed successfully');
  } catch (error) {
    logMessage(`Error processing expired subscriptions: ${error.message}`, 'error');
    logMessage(error.stack, 'error');
  }
}

// Run a single check cycle
async function runCheckCycle() {
  try {
    const connected = await connectToDatabase();
    if (!connected) {
      return false;
    }
    
    await processExpiredSubscriptions();
    return true;
  } catch (error) {
    logMessage(`Check cycle failed: ${error.message}`, 'error');
    return false;
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      logMessage('Disconnected from MongoDB');
    }
  }
}

// Main function
async function main() {
  logMessage(`Starting subscription expiration service (${options.runOnce ? 'single run' : 'continuous mode'})`);
  
  if (options.runOnce) {
    await runCheckCycle();
    logMessage('Single run completed, exiting');
    process.exit(0);
  } else {
    // Run immediately on startup
    await runCheckCycle();
    
    // Then set up interval
    const intervalMs = options.interval * 60 * 1000;
    logMessage(`Scheduled to run every ${options.interval} minutes`);
    
    setInterval(async () => {
      logMessage(`Running scheduled check...`);
      await runCheckCycle();
    }, intervalMs);
    
    // Handle process termination
    process.on('SIGINT', async () => {
      logMessage('Service stopping due to SIGINT signal', 'warn');
      if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
      }
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      logMessage('Service stopping due to SIGTERM signal', 'warn');
      if (mongoose.connection.readyState === 1) {
        await mongoose.connection.close();
      }
      process.exit(0);
    });
  }
}

// Run the script
main(); 
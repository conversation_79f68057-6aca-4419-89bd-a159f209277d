/**
 * <PERSON><PERSON>t to initialize default bot configurations and settings
 * This serves as the main entry point for all bot initialization
 * Run with: node scripts/init-bot-defaults.js
 */

require('dotenv').config({path: ".env.local"});
const mongoose = require('mongoose');
const { spawn } = require('child_process');
const path = require('path');


// Default bot configuration settings
const defaultBotSettings = {
  // Global bot settings that apply to all bots
  defaultResponseDelay: 1000,
  maxConversationLength: 50,
  enableTypingIndicators: true,
  enableReadReceipts: true,
  
  // Default conversation timeouts
  conversationTimeout: 30 * 60 * 1000, // 30 minutes
  inactivityWarning: 25 * 60 * 1000,   // 25 minutes
  
  // Default error handling
  defaultErrorMessage: 'I apologize, but I encountered an issue. Let me connect you with a human agent.',
  maxRetries: 3,
  
  // Default handoff settings
  defaultHandoffMessage: 'I\'m connecting you with a human agent who can better assist you.',
  handoffTimeout: 5 * 60 * 1000, // 5 minutes
  
  // Default validation rules
  emailValidation: true,
  phoneValidation: true,
  requiredFieldValidation: true
};

// Default global variables available to all bots
const defaultGlobalVariables = [
  {
    name: 'user_name',
    type: 'string',
    description: 'User\'s name for personalization',
    required: false,
    defaultValue: 'Guest'
  },
  {
    name: 'user_email',
    type: 'string',
    description: 'User\'s email for follow-up',
    required: false,
    validation: 'email'
  },
  {
    name: 'session_id',
    type: 'string',
    description: 'Unique session identifier',
    required: true,
    autoGenerate: true
  },
  {
    name: 'conversation_start_time',
    type: 'datetime',
    description: 'When the conversation started',
    required: true,
    autoGenerate: true
  },
  {
    name: 'user_timezone',
    type: 'string',
    description: 'User\'s timezone for scheduling',
    required: false,
    defaultValue: 'UTC'
  }
];

// Function to execute the comprehensive templates script
function executeTemplatesScript() {
  return new Promise((resolve, reject) => {
    console.log('🎯 Initializing comprehensive bot templates...');
    
    const templatesScriptPath = path.join(__dirname, 'init-comprehensive-bot-templates.js');
    const child = spawn('node', [templatesScriptPath], {
      stdio: 'inherit',
      env: process.env
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Comprehensive bot templates initialized successfully');
        resolve();
      } else {
        console.error(`❌ Templates script exited with code ${code}`);
        reject(new Error(`Templates script failed with exit code ${code}`));
      }
    });
    
    child.on('error', (error) => {
      console.error('❌ Error executing templates script:', error);
      reject(error);
    });
  });
}

// Function to initialize default bot settings (placeholder for future use)
async function initializeBotSettings() {
  try {
    console.log('⚙️ Initializing default bot settings...');
    
    // TODO: Implement bot settings storage
    // This could include:
    // - Global configuration settings
    // - Default timeout values
    // - Error handling preferences
    // - Validation rules
    // - Integration settings
    
    console.log('📋 Default bot settings:');
    console.log('  • Response delay:', defaultBotSettings.defaultResponseDelay + 'ms');
    console.log('  • Conversation timeout:', defaultBotSettings.conversationTimeout / 60000 + ' minutes');
    console.log('  • Max retries:', defaultBotSettings.maxRetries);
    console.log('  • Typing indicators:', defaultBotSettings.enableTypingIndicators ? 'enabled' : 'disabled');
    
    console.log('✅ Bot settings initialized');
  } catch (error) {
    console.error('❌ Error initializing bot settings:', error);
    throw error;
  }
}

// Function to initialize global variables (placeholder for future use)
async function initializeGlobalVariables() {
  try {
    console.log('🔧 Initializing global variables...');
    
    // TODO: Implement global variables storage
    // This could include:
    // - System-wide variables available to all bots
    // - Default user session variables
    // - Environment-specific variables
    // - Integration credentials placeholders
    
    console.log('📋 Default global variables:');
    defaultGlobalVariables.forEach(variable => {
      console.log(`  • ${variable.name} (${variable.type}): ${variable.description}`);
    });
    
    console.log('✅ Global variables initialized');
  } catch (error) {
    console.error('❌ Error initializing global variables:', error);
    throw error;
  }
}

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/cloudinstance');
    console.log('✅ Connected to MongoDB');
    return true;
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB:', error);
    return false;
  }
}

// Main initialization function
async function initializeBotDefaults() {
  try {
    console.log('🚀 Starting bot defaults initialization...');
    console.log('');
    
    // Initialize bot settings
    await initializeBotSettings();
    console.log('');
    
    // Initialize global variables
    await initializeGlobalVariables();
    console.log('');
    
    // Execute comprehensive templates script
    await executeTemplatesScript();
    console.log('');
    
    console.log('🎉 Bot defaults initialization completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log('  • Default bot settings configured');
    console.log('  • Global variables initialized');
    console.log('  • 20 comprehensive bot templates created');
    console.log('  • All templates organized by difficulty level');
    console.log('');
    console.log('✨ Your bot system is now ready for use!');
    
  } catch (error) {
    console.error('❌ Error during bot defaults initialization:', error);
    throw error;
  }
}

// Main execution function
async function main() {
  try {
    const connected = await connectToDatabase();
    if (!connected) {
      console.error('❌ Cannot proceed without database connection');
      process.exit(1);
    }

    await initializeBotDefaults();
  } catch (error) {
    console.error('❌ Initialization failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the initialization if this script is executed directly
if (require.main === module) {
  main();
}

/**
 * AI Template Generation System Prompts
 * Contains comprehensive system prompts with examples for AI template generation
 */


/**
 * TypeScript interface definition for AI understanding
 */
const IFLOW_TEMPLATE_INTERFACE = `
interface IFlowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  integrations: IFlowIntegration[];
  thumbnail?: string;
  nodes: IFlowNode[];
  connections: IFlowConnection[];
  variables: IFlowVariable[];
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedSetupTime: number; // in minutes
}

interface IFlowNode {
  id: string;
  name: string;
  type: 'text' | 'buttons' | 'quick_reply' | 'form' | 'handoff' | 'conditional' | 'api_call';
  content: IFlowNodeContent;
  position: { x: number; y: number };
  isStartNode: boolean;
  isEndNode: boolean;
}

interface IFlowConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  condition?: string;
}

interface IFlowVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object';
  description: string;
  required: boolean;
  defaultValue?: any;
}`;

/**
 * Template examples showing the exact JSON structure for each difficulty level
 */
const TEMPLATE_EXAMPLES = `
### BEGINNER EXAMPLE (3-5 nodes, simple linear flow):
{
  "id": "template-beginner-001",
  "name": "Simple Welcome Bot",
  "description": "Basic welcome bot with simple greeting and navigation options",
  "category": "general",
  "difficulty": "beginner",
  "estimatedSetupTime": 10,
  "tags": ["welcome", "basic", "navigation"],
  "nodes": [
    {
      "id": "node-1",
      "name": "Welcome_Message",
      "type": "text",
      "content": {
        "type": "text",
        "text": "Hello! Welcome to our website. I'm here to help you get started. How can I assist you today?",
        "delay": 1000
      },
      "position": { "x": 200, "y": 100 },
      "isStartNode": true,
      "isEndNode": false
    },
    {
      "id": "node-2",
      "name": "Main_Menu",
      "type": "buttons",
      "content": {
        "type": "buttons",
        "text": "What would you like to do?",
        "delay": 500,
        "options": [
          { "id": "opt-1", "text": "📖 Learn More", "value": "learn" },
          { "id": "opt-2", "text": "📞 Contact Us", "value": "contact" },
          { "id": "opt-3", "text": "❓ Get Help", "value": "help" }
        ]
      },
      "position": { "x": 200, "y": 300 },
      "isStartNode": false,
      "isEndNode": false
    },
    {
      "id": "node-3",
      "name": "Learn_More",
      "type": "text",
      "content": {
        "type": "text",
        "text": "Great! You can explore our website to learn about our products and services.",
        "delay": 1000
      },
      "position": { "x": 50, "y": 500 },
      "isStartNode": false,
      "isEndNode": true
    },
    {
      "id": "node-4",
      "name": "Contact_Info",
      "type": "text",
      "content": {
        "type": "text",
        "text": "You can reach <NAME_EMAIL> or call us at (555) 123-4567.",
        "delay": 1000
      },
      "position": { "x": 200, "y": 500 },
      "isStartNode": false,
      "isEndNode": true
    },
    {
      "id": "node-5",
      "name": "Help_Options",
      "type": "text",
      "content": {
        "type": "text",
        "text": "I'd be happy to help! You can browse our FAQ section or contact our support team.",
        "delay": 1000
      },
      "position": { "x": 350, "y": 500 },
      "isStartNode": false,
      "isEndNode": true
    }
  ],
  "connections": [
    { "id": "conn-1", "sourceNodeId": "node-1", "targetNodeId": "node-2" },
    { "id": "conn-2", "sourceNodeId": "node-2", "targetNodeId": "node-3", "condition": "learn" },
    { "id": "conn-3", "sourceNodeId": "node-2", "targetNodeId": "node-4", "condition": "contact" },
    { "id": "conn-4", "sourceNodeId": "node-2", "targetNodeId": "node-5", "condition": "help" }
  ],
  "variables": [],
  "integrations": []
}

### INTERMEDIATE EXAMPLE (6-12 nodes, forms, conditionals):
{
  "id": "template-intermediate-001",
  "name": "Lead Qualification Bot",
  "description": "Advanced lead qualification with forms and conditional logic",
  "category": "lead_qualification",
  "difficulty": "intermediate",
  "estimatedSetupTime": 25,
  "tags": ["lead-qualification", "sales", "forms", "conditional"],
  "nodes": [
    {
      "id": "node-1",
      "name": "Lead_Welcome",
      "type": "text",
      "content": {
        "type": "text",
        "text": "Hello! Welcome to our website. I'm here to help you learn more about our services.",
        "delay": 1000
      },
      "position": { "x": 200, "y": 100 },
      "isStartNode": true,
      "isEndNode": false
    },
    {
      "id": "node-2",
      "name": "Interest_Check",
      "type": "quick_reply",
      "content": {
        "type": "quick_reply",
        "text": "What brings you here today?",
        "delay": 500,
        "options": [
          { "id": "opt-1", "text": "💰 Pricing Information", "value": "pricing" },
          { "id": "opt-2", "text": "🎯 Product Demo", "value": "demo" },
          { "id": "opt-3", "text": "📞 Sales Consultation", "value": "sales" }
        ]
      },
      "position": { "x": 200, "y": 300 },
      "isStartNode": false,
      "isEndNode": false
    },
    {
      "id": "node-3",
      "name": "Contact_Form",
      "type": "form",
      "content": {
        "type": "form",
        "text": "Great! Could you share some quick details so I can provide relevant information?",
        "delay": 1000,
        "formFields": [
          { "id": "name", "type": "text", "label": "Your Name", "required": true },
          { "id": "email", "type": "email", "label": "Email Address", "required": true },
          { "id": "company", "type": "text", "label": "Company Name", "required": false },
          { "id": "company_size", "type": "select", "label": "Company Size", "options": ["1-10", "11-50", "51-200", "200+"], "required": false }
        ],
        "storeInVariable": "lead_info"
      },
      "position": { "x": 200, "y": 500 },
      "isStartNode": false,
      "isEndNode": false
    },
    {
      "id": "node-4",
      "name": "Qualification_Check",
      "type": "conditional",
      "content": {
        "type": "conditional",
        "text": "Let me check your qualification status...",
        "delay": 500,
        "conditionVariable": "company_size",
        "conditionOperator": "equals",
        "conditionValue": "200+"
      },
      "position": { "x": 200, "y": 700 },
      "isStartNode": false,
      "isEndNode": false
    },
    {
      "id": "node-5",
      "name": "Enterprise_Path",
      "type": "text",
      "content": {
        "type": "text",
        "text": "Perfect! As an enterprise client, you'll have access to our premium features.",
        "delay": 1000
      },
      "position": { "x": 50, "y": 900 },
      "isStartNode": false,
      "isEndNode": false
    },
    {
      "id": "node-6",
      "name": "Follow_Up",
      "type": "handoff",
      "content": {
        "type": "handoff",
        "text": "Thank you for your interest! I'm connecting you with the right specialist.",
        "delay": 1000
      },
      "position": { "x": 200, "y": 1100 },
      "isStartNode": false,
      "isEndNode": true
    }
  ],
  "connections": [
    { "id": "conn-1", "sourceNodeId": "node-1", "targetNodeId": "node-2" },
    { "id": "conn-2", "sourceNodeId": "node-2", "targetNodeId": "node-3" },
    { "id": "conn-3", "sourceNodeId": "node-3", "targetNodeId": "node-4" },
    { "id": "conn-4", "sourceNodeId": "node-4", "targetNodeId": "node-5", "condition": "enterprise" },
    { "id": "conn-5", "sourceNodeId": "node-5", "targetNodeId": "node-6" }
  ],
  "variables": [
    { "name": "lead_info", "type": "object", "description": "Lead contact information", "required": false },
    { "name": "company_size", "type": "string", "description": "Size of the prospect company", "required": false }
  ],
  "integrations": []
}

### ADVANCED EXAMPLE (13+ nodes, API calls, complex logic):
{"name":"API-driven Product Catalog Bot","description":"Real-time product search with API integration, dynamic pricing, and inventory management","isTemplate":true,"nodes":[{"id":"bf38d4d0-8542-4442-8a86-2da761c647bc","name":"Catalog_Welcome","type":"text","content":{"type":"text","text":"🛒 Welcome to our smart product catalog! I can help you find products, check real-time pricing, and verify availability. What are you looking for today?","delay":1000},"position":{"x":200,"y":100},"isStartNode":true,"isEndNode":false,"validationErrors":[]},{"id":"a363d972-9c89-4bda-bc25-ec0c851ef58b","name":"Search_Method","type":"quick_reply","content":{"type":"quick_reply","text":"How would you like to search for products?","delay":500,"options":[{"id":"cf775bd5-6713-4dc2-8d88-9834c7ac8938","text":"🔍 Search by Name","value":"name"},{"id":"13f6aeea-c77a-4218-9388-a0f95b9e38b7","text":"📂 Browse Categories","value":"category"},{"id":"eb0245ce-5d79-422a-bb13-e651a6bf3cdc","text":"💰 Search by Price Range","value":"price"},{"id":"253da83e-9df5-4e3f-8bb0-7319e434a8fe","text":"🏷️ Search by SKU/Barcode","value":"sku"},{"id":"f33c3417-c847-4a97-9b35-41c5f439f0c3","text":"⭐ Popular Products","value":"popular"}]},"position":{"x":200,"y":300},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"1732232b-0ca1-4d11-b8aa-c5ce2961124d","name":"Product_Search_Form","type":"form","content":{"type":"form","text":"🔍 Let me help you find the perfect product:","delay":1000,"formFields":[{"id":"search_term","type":"text","label":"Product Name or Keywords","required":true},{"id":"category","type":"select","label":"Category (optional)","options":["Electronics","Clothing","Home & Garden","Sports","Books","Toys","Health & Beauty"],"required":false},{"id":"min_price","type":"number","label":"Minimum Price","required":false},{"id":"max_price","type":"number","label":"Maximum Price","required":false},{"id":"sort_by","type":"select","label":"Sort Results By","options":["Relevance","Price: Low to High","Price: High to Low","Customer Rating","Newest First"],"required":false}]},"position":{"x":50,"y":500},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"06c00395-9b99-44f2-97cf-a307d4701824","name":"Category_API_Call","type":"api_call","content":{"type":"api_call","url":"http://localhost:3001/categories","method":"GET","timeout":30000,"retries":3,"headers":{"Authorization":"Bearer {{catalog_api_key}}","Content-Type":"application/json"},"body":"","responseVariable":"category_data","arrayFieldName":"categories","displayFields":["name","product_count","description","featured"],"templateConfig":{"title":"Product Categories","description":"Browse our available categories","customTemplate":"🏷️ **Product Categories**\\n\\nBrowse our available categories:\\n\\n{{#each categories}}\\n📂 **{{name}}**\\n📊 {{product_count}} products\\n📝 {{description}}\\n{{#if featured}}⭐ Featured Category{{/if}}\\n\\n---\\n{{/each}}\\n\\n🔍 Which category interests you?","showPagination":false,"displayFields":[{"id":"field-0","name":"name","label":"Name","type":"text"},{"id":"field-1","name":"product_count","label":"Product Count","type":"text"},{"id":"field-2","name":"description","label":"Description","type":"text"},{"id":"field-3","name":"featured","label":"Featured","type":"text"}],"templateStructure":{"blocks":[],"variables":[],"loopVariables":[],"conditionalVariables":[]},"validatedAt":"2025-06-12T13:54:13.561Z","sampleData":{"total":5,"count":5,"categories":[{"name":"Electronics","product_count":1247,"description":"Latest tech gadgets and devices","featured":true},{"name":"Clothing","product_count":892,"description":"Fashion and apparel for all seasons","featured":false},{"name":"Home & Garden","product_count":634,"description":"Everything for your home and outdoor space","featured":true},{"name":"Sports & Outdoors","product_count":456,"description":"Gear for active lifestyles","featured":false},{"name":"Books & Media","product_count":789,"description":"Books, movies, and digital content","featured":false}]}}},"position":{"x":200,"y":500},"isStartNode":false,"isEndNode":true,"validationErrors":[]},{"id":"05d9a316-4eea-4c02-96f8-a0c77301171e","name":"Product_Search_API","type":"api_call","content":{"type":"api_call","url":"http://localhost:3001/products/search","method":"POST","timeout":30000,"retries":3,"headers":{"Authorization":"Bearer {{catalog_api_key}}","Content-Type":"application/json"},"body":"{\\"query\\": \\"{{search_term}}\\", \\"category\\": \\"{{category}}\\", \\"price_min\\": \\"{{min_price}}\\", \\"price_max\\": \\"{{max_price}}\\", \\"sort_by\\": \\"{{sort_by}}\\", \\"page\\": 1, \\"pageSize\\": 5, \\"include_inventory\\": true, \\"include_pricing\\": true}","responseVariable":"search_results","arrayFieldName":"products","displayFields":["name","price","stock","rating","description"],"templateConfig":{"title":"Search Results","description":"Found {{total_results}} products matching your criteria","customTemplate":"🎯 **Search Results**\\n\\nFound {{total_results}} products matching your criteria:\\n\\n{{#if query}}Search term: \\"{{query}}\\"{{/if}}\\n{{#if category}}Category: {{category}}{{/if}}\\n\\n{{#each products}}\\n📦 **{{name}}**\\n💰 Price: $ {{price}}\\n📊 Stock: {{stock}} units\\n⭐ Rating: {{rating}}/5 stars\\n📝 {{description}}\\n\\n---\\n{{/each}}\\n\\n📄 Showing {{results_count}} of {{total_results}} products","displayFields":[{"id":"field-0","name":"name","label":"Name","type":"text"},{"id":"field-1","name":"price","label":"Price","type":"text"},{"id":"field-2","name":"stock","label":"Stock","type":"text"},{"id":"field-3","name":"rating","label":"Rating","type":"text"},{"id":"field-4","name":"description","label":"Description","type":"text"}],"templateStructure":{"blocks":[],"variables":[],"loopVariables":[],"conditionalVariables":[]},"validatedAt":"2025-06-12T13:54:13.562Z","sampleData":{"total_results":24,"search_term":"laptop","query":"laptop","category":"Electronics","results_count":5,"products":[{"name":"MacBook Pro 16\\"","price":"2499.99","stock":12,"rating":"4.8","description":"Powerful laptop for professionals"},{"name":"Dell XPS 13","price":"1299.99","stock":8,"rating":"4.6","description":"Ultrabook with premium design"},{"name":"HP Spectre x360","price":"1199.99","stock":15,"rating":"4.5","description":"Convertible laptop with touch screen"},{"name":"Lenovo ThinkPad X1","price":"1899.99","stock":6,"rating":"4.7","description":"Business laptop with excellent keyboard"},{"name":"ASUS ZenBook 14","price":"899.99","stock":20,"rating":"4.4","description":"Lightweight laptop for everyday use"}]}}},"position":{"x":50,"y":700},"isStartNode":false,"isEndNode":true,"validationErrors":[]},{"id":"a97441ad-34ce-49cc-9042-1886d9a0fa3e","name":"Price_Check_API","type":"api_call","content":{"type":"api_call","url":"http://localhost:3001/products/{{product_id}}/pricing","method":"GET","timeout":30000,"retries":3,"headers":{"Authorization":"Bearer {{catalog_api_key}}","Content-Type":"application/json"},"body":"","responseVariable":"pricing_data","arrayFieldName":"pricing_tiers","displayFields":["tier","price","discount","availability"],"templateConfig":{"title":"Product Pricing Information","description":"Current pricing and availability for {{product_name}}","customTemplate":"💰 **Product Pricing Information**\\n\\n📱 **{{product_name}}**\\n💰 **Current Price**: $ {{current_price}}\\n{{#if discount_info}}💸 {{discount_info}}{{/if}}\\n📦 **In Stock**: {{stock_quantity}} units\\n🚚 **Shipping**: {{shipping_info}}\\n⭐ **Rating**: {{rating}}/5 ({{review_count}} reviews)\\n\\n🎯 **Available Pricing Tiers**:\\n{{#each pricing_tiers}}\\n🏷️ **{{tier}}**\\n💰 Price: $ {{price}}\\n💸 Discount: {{discount}}%\\n📋 {{availability}}\\n\\n---\\n{{/each}}\\n\\n🛒 Ready to add to cart or need more information?","showPagination":false,"displayFields":[{"id":"field-0","name":"tier","label":"Tier","type":"text"},{"id":"field-1","name":"price","label":"Price","type":"text"},{"id":"field-2","name":"discount","label":"Discount","type":"text"},{"id":"field-3","name":"availability","label":"Availability","type":"text"}],"templateStructure":{"blocks":[],"variables":["product_name","current_price","discount_info","stock_quantity","shipping_info","rating","review_count"],"loopVariables":["pricing_tiers"],"conditionalVariables":["discount_info"]},"validatedAt":"2025-06-12T13:54:13.562Z","sampleData":{"product_name":"MacBook Pro 16\\"","current_price":"2499.99","discount_info":"15% off - Limited time offer","stock_quantity":12,"shipping_info":"Free shipping on orders over $50","rating":"4.8","review_count":1247,"pricing_tiers":[{"tier":"Standard","price":"2499.99","discount":"0","availability":"In Stock"},{"tier":"Student","price":"2249.99","discount":"10","availability":"In Stock"},{"tier":"Business","price":"2124.99","discount":"15","availability":"In Stock"},{"tier":"Enterprise","price":"1999.99","discount":"20","availability":"Contact Sales"}]}}},"position":{"x":350,"y":500},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"d8271bd2-77ce-4903-b95a-e8d410bce6f3","name":"Product_Out_Of_Stock","type":"conditional","content":{"type":"conditional","conditionVariable":"stock_quantity","conditionOperator":"equals","conditionValue":"0"},"position":{"x":350,"y":900},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"2338665b-dd75-45b7-ab65-292599a36c53","name":"Inventory_Check_API","type":"api_call","content":{"type":"api_call","url":"http://localhost:3001/inventory/check?product_ids={{selected_product_ids}}&location={{customer_location}}&include_nearby=true","method":"GET","timeout":30000,"retries":3,"headers":{"Authorization":"Bearer {{catalog_api_key}}","Content-Type":"application/json"},"body":"","responseVariable":"inventory_data","arrayFieldName":"locations","displayFields":["store_name","stock_qty","distance","availability"],"templateConfig":{"title":"Inventory Check Results","description":"Available locations for your selected products","customTemplate":"📍 **Inventory Check Results**\\n\\nHere are the available locations for your selected products:\\n\\n{{#each locations}}\\n🏪 **{{store_name}}**\\n📦 Stock: {{stock_qty}} units\\n📍 Distance: {{distance}} miles\\n✅ {{availability}}\\n---\\n{{/each}}\\n\\n📞 Would you like to reserve items at any of these locations?","displayFields":[{"id":"field-0","name":"store_name","label":"Store Name","type":"text"},{"id":"field-1","name":"stock_qty","label":"Stock Quantity","type":"text"},{"id":"field-2","name":"distance","label":"Distance","type":"text"},{"id":"field-3","name":"availability","label":"Availability","type":"text"}],"templateStructure":{"blocks":[],"variables":[],"loopVariables":["locations"],"conditionalVariables":[]},"validatedAt":"2025-06-12T13:54:13.562Z","sampleData":{"total":4,"count":4,"locations":[{"store_name":"Downtown Store","stock_qty":8,"distance":"2.3","availability":"Available for pickup"},{"store_name":"Mall Location","stock_qty":12,"distance":"5.7","availability":"Available for pickup"},{"store_name":"Warehouse Outlet","stock_qty":25,"distance":"8.1","availability":"Available for pickup"},{"store_name":"Airport Store","stock_qty":3,"distance":"12.4","availability":"Limited stock"}]}}},"position":{"x":500,"y":700},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"a1982416-e341-4f1f-b7d0-6bfcb3d69037","name":"Dynamic_Pricing_API","type":"api_call","content":{"type":"api_call","url":"http://localhost:3001/pricing/calculate","method":"POST","timeout":30000,"retries":3,"headers":{"Authorization":"Bearer {{catalog_api_key}}","Content-Type":"application/json"},"body":"{\\"customer_id\\": \\"{{customer_id}}\\", \\"product_ids\\": \\"{{cart_product_ids}}\\", \\"quantities\\": \\"{{cart_quantities}}\\", \\"promo_code\\": \\"{{promo_code}}\\", \\"location\\": \\"{{customer_location}}\\"}","responseVariable":"pricing_calc","arrayFieldName":"price_breakdown","displayFields":["item_name","base_price","discount_amt","final_price"],"templateConfig":{"title":"Dynamic Pricing Calculation","description":"Personalized pricing breakdown for your order","customTemplate":"💰 **Dynamic Pricing Calculation**\\n\\nHere's your personalized pricing breakdown:\\n\\n{{#each price_breakdown}}\\n📦 **{{item_name}}**\\n💵 Base Price: $ {{base_price}}\\n💸 Discount: -$ {{discount_amt}}\\n✨ **Final Price: $ {{final_price}}**\\n\\n---\\n{{/each}}\\n\\n🛒 Ready to proceed with these prices?","showPagination":false,"displayFields":[{"id":"field-0","name":"item_name","label":"Item Name","type":"text"},{"id":"field-1","name":"base_price","label":"Base Price","type":"text"},{"id":"field-2","name":"discount_amt","label":"Discount Amount","type":"text"},{"id":"field-3","name":"final_price","label":"Final Price","type":"text"}],"templateStructure":{"blocks":[],"variables":[],"loopVariables":["price_breakdown"],"conditionalVariables":[]},"validatedAt":"2025-06-12T13:54:13.562Z","sampleData":{"total":3,"count":3,"price_breakdown":[{"item_name":"MacBook Pro 16\\"","base_price":"2499.99","discount_amt":"375.00","final_price":"2124.99"},{"item_name":"USB-C Hub","base_price":"79.99","discount_amt":"8.00","final_price":"71.99"},{"item_name":"Laptop Sleeve","base_price":"49.99","discount_amt":"5.00","final_price":"44.99"}]}}},"position":{"x":650,"y":700},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"fe6dfd01-ab3e-44ba-807b-19f09e63d065","name":"Product_Actions","type":"quick_reply","content":{"type":"quick_reply","text":"What would you like to do next?","delay":500,"options":[{"id":"d04a8a7c-a25c-47b8-be43-ce8517572bc2","text":"🛒 Add to Cart","value":"add_cart"},{"id":"39d86487-99c8-4108-91ec-696afeee9a17","text":"📋 Product Details","value":"details"},{"id":"12e5e41b-0a46-4e5e-ac19-cec1ba4455cb","text":"📊 Compare Products","value":"compare"},{"id":"71f68641-4f92-4a96-905a-84b49bf27245","text":"🔍 Search Again","value":"search"},{"id":"dda72955-70c6-40ec-949e-5310fb63e564","text":"💬 Speak with Sales","value":"sales"}]},"position":{"x":200,"y":1100},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"aef5b71a-cbf4-4699-bad0-ae3cf7d934df","name":"Category_Actions","type":"quick_reply","content":{"type":"quick_reply","text":"What would you like to do next?","delay":500,"options":[{"id":"bdf0973b-ca1c-41a1-95a4-4a5fd8f58ac0","text":"🔍 Search Products","value":"search"},{"id":"97b5b085-5dfe-441a-9c14-0254cba2e363","text":"💰 Check Pricing","value":"pricing"},{"id":"4ea1ddab-2624-48ac-80bf-7c6eedee118d","text":"🔄 Browse Again","value":"browse"},{"id":"908798be-1dc9-4bd4-a534-e67ce1d6dcc2","text":"💬 Speak with Sales","value":"sales"}]},"position":{"x":350,"y":1100},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"19c776ab-103b-48f6-a76f-a508d6e9ca20","name":"Pricing_Actions","type":"quick_reply","content":{"type":"quick_reply","text":"Ready to proceed with your order?","delay":500,"options":[{"id":"46d5bff1-4689-4751-a9e9-3629ed45e2fe","text":"🛒 Add to Cart","value":"cart"},{"id":"997f4676-8d56-4997-a873-80d3ee7c5be2","text":"📊 Compare Options","value":"compare"},{"id":"d041710d-6c05-448d-a29e-cac6e60e19cb","text":"🔍 Search More","value":"search"},{"id":"2c29349f-9518-4e92-953f-ed5d05cb4a72","text":"💬 Speak with Sales","value":"sales"}]},"position":{"x":650,"y":1100},"isStartNode":false,"isEndNode":false,"validationErrors":[]},{"id":"6d8c0ae9-c0d9-4602-a94b-e3847d0ff864","name":"Sales_Specialist_Handoff","type":"handoff","content":{"type":"handoff","text":"🛍️ I'm connecting you with our product specialist who can provide detailed information, help with bulk orders, and assist with any special requirements.","delay":1000,"reason":"Product consultation and sales assistance"},"position":{"x":200,"y":1300},"isStartNode":false,"isEndNode":true,"validationErrors":[]}],"connections":[{"id":"d266d190-1df7-44c4-ba91-82c6b388aea0","sourceNodeId":"bf38d4d0-8542-4442-8a86-2da761c647bc","targetNodeId":"a363d972-9c89-4bda-bc25-ec0c851ef58b"},{"id":"7e52a29a-2837-4846-8e8a-4bafcd59d923","sourceNodeId":"a363d972-9c89-4bda-bc25-ec0c851ef58b","targetNodeId":"1732232b-0ca1-4d11-b8aa-c5ce2961124d","condition":"name"},{"id":"1db0e6c8-184b-4d95-9d89-3a226ffb0817","sourceNodeId":"a363d972-9c89-4bda-bc25-ec0c851ef58b","targetNodeId":"06c00395-9b99-44f2-97cf-a307d4701824","condition":"category"},{"id":"33ab9629-53cd-42a6-a10b-4c1b476eab62","sourceNodeId":"a363d972-9c89-4bda-bc25-ec0c851ef58b","targetNodeId":"1732232b-0ca1-4d11-b8aa-c5ce2961124d","condition":"price"},{"id":"427f7a54-6ec4-48cb-97e4-d1882cd4532a","sourceNodeId":"1732232b-0ca1-4d11-b8aa-c5ce2961124d","targetNodeId":"05d9a316-4eea-4c02-96f8-a0c77301171e"},{"id":"6ffdeb37-ae66-4eb0-a96e-3146ee79b42d","sourceNodeId":"a97441ad-34ce-49cc-9042-1886d9a0fa3e","targetNodeId":"fe6dfd01-ab3e-44ba-807b-19f09e63d065","condition":"available"},{"id":"daf9aed8-4371-452a-8804-ded856109b7c","sourceNodeId":"a97441ad-34ce-49cc-9042-1886d9a0fa3e","targetNodeId":"d8271bd2-77ce-4903-b95a-e8d410bce6f3","condition":"unavailable"},{"id":"673d9a7d-3328-4c5d-bae1-55b8ef3e2219","sourceNodeId":"d8271bd2-77ce-4903-b95a-e8d410bce6f3","targetNodeId":"2338665b-dd75-45b7-ab65-292599a36c53"},{"id":"431b334a-361c-43ce-b2ae-14e43cc52c49","sourceNodeId":"2338665b-dd75-45b7-ab65-292599a36c53","targetNodeId":"a1982416-e341-4f1f-b7d0-6bfcb3d69037","condition":"inventory"},{"id":"b9a6d110-03a8-4236-828c-4d08e7abfa58","sourceNodeId":"a1982416-e341-4f1f-b7d0-6bfcb3d69037","targetNodeId":"19c776ab-103b-48f6-a76f-a508d6e9ca20"},{"id":"181d2099-b108-4fb2-b5ef-9f52d991c8a6","sourceNodeId":"fe6dfd01-ab3e-44ba-807b-19f09e63d065","targetNodeId":"6d8c0ae9-c0d9-4602-a94b-e3847d0ff864","condition":"sales"},{"id":"878a448f-23b7-4d82-b196-96532a29b494","sourceNodeId":"aef5b71a-cbf4-4699-bad0-ae3cf7d934df","targetNodeId":"6d8c0ae9-c0d9-4602-a94b-e3847d0ff864","condition":"sales"},{"id":"0a3c2d40-fb29-4ba4-9c39-651a731e50e2","sourceNodeId":"19c776ab-103b-48f6-a76f-a508d6e9ca20","targetNodeId":"6d8c0ae9-c0d9-4602-a94b-e3847d0ff864","condition":"sales"}],"variables":[{"name":"catalog_api_key","type":"string","description":"API key for product catalog service","required":true},{"name":"customer_id","type":"string","description":"Customer ID for personalized pricing","required":false},{"name":"search_term","type":"string","description":"Product search query","required":false},{"name":"min_price","type":"number","description":"Minimum price filter for product search","required":false},{"name":"max_price","type":"number","description":"Maximum price filter for product search","required":false},{"name":"sort_by","type":"string","description":"Sort order for product search results","required":false},{"name":"product_id","type":"string","description":"Selected product ID","required":false},{"name":"results_count","type":"number","description":"Number of search results","required":false},{"name":"products","type":"array","description":"Array of product search results","required":false},{"name":"categories","type":"array","description":"Array of product categories","required":false},{"name":"pricing_tiers","type":"array","description":"Array of pricing tier options","required":false},{"name":"locations","type":"array","description":"Array of store locations with inventory","required":false},{"name":"price_breakdown","type":"array","description":"Array of pricing calculation breakdown","required":false},{"name":"total_results","type":"number","description":"Total number of results found","required":false},{"name":"current_page","type":"number","description":"Current page number for pagination","required":false},{"name":"total_pages","type":"number","description":"Total number of pages available","required":false},{"name":"has_next_page","type":"boolean","description":"Whether there are more pages available","required":false},{"name":"query","type":"string","description":"Search query used for product search","required":false},{"name":"category","type":"string","description":"Selected product category","required":false},{"name":"product_name","type":"string","description":"Name of the selected product","required":false},{"name":"current_price","type":"string","description":"Current price of the product","required":false},{"name":"discount_info","type":"string","description":"Discount information for the product","required":false},{"name":"stock_quantity","type":"number","description":"Available stock quantity","required":false},{"name":"shipping_info","type":"string","description":"Shipping information for the product","required":false},{"name":"rating","type":"number","description":"Product rating","required":false},{"name":"review_count","type":"number","description":"Number of product reviews","required":false},{"name":"featured","type":"boolean","description":"Whether a category or product is featured","required":false}],"metadata":{"totalNodes":13,"difficulty":"advanced","estimatedSetupTime":30,"tags":["api-integration","product-catalog","real-time-pricing","inventory","e-commerce","array-handling","self-contained-api"],"nodeTypes":["text","quick_reply","form","api_call","conditional","handoff"],"description":"Fully self-contained API node architecture with enterprise-grade product catalog, real-time integration, dynamic pricing, and inventory management. All API nodes include complete template configurations for built-in response formatting.","apiRequirements":{"endpoints":["GET /categories - Product category listing (self-contained)","POST /products/search - Product search with filters (self-contained)","GET /products/{id}/pricing - Real-time pricing data (conditional flow)","GET /inventory/check - Multi-location inventory (self-contained)","POST /pricing/calculate - Dynamic pricing calculation (self-contained)"],"authentication":"Bearer token via catalog_api_key variable","responseFormat":"JSON with array fields processed by built-in templates","timeout":"30 seconds with 3 retry attempts","displayLimits":"Maximum 5 items per list, 5 fields per item (20 chars each)","architecture":"Self-contained API nodes with templateConfig - no separate formatting nodes required"}}}`;

/**
 * Comprehensive system prompt for AI template generation
 * Includes platform knowledge, TypeScript interface, examples, and generation requirements
 */
export const AI_TEMPLATE_SYSTEM_PROMPT = `You are an expert bot conversation flow designer for the New Instance platform. Your task is to generate comprehensive, production-ready bot conversation flows based on user requirements.

## TYPESCRIPT INTERFACE:

You must generate JSON that matches this exact TypeScript interface:

${IFLOW_TEMPLATE_INTERFACE}

## PLATFORM KNOWLEDGE:

### Available Node Types:
1. **TEXT** - Simple text messages with optional delay
2. **BUTTONS** - Multiple choice options with navigation
3. **QUICK_REPLY** - Quick response buttons for fast interaction
4. **FORM** - Data collection forms with validation
5. **HANDOFF** - Transfer to human agent
6. **CONDITIONAL** - Logic-based flow control
7. **API_CALL** - External API integrations

### Node Structure Requirements:
- Each node must have: id (UUID), name (max 100 chars), type, content, position {x, y}, isStartNode, isEndNode
- Node names must be descriptive and use underscore_case (e.g., "Welcome_Message", "Collect_Contact_Info")
- Text content max 2000 characters
- Button options max 200 characters each
- Form fields support: text, email, phone, select, textarea, number
- API calls require: url, method, headers, timeout, retries, responseVariable

### Flow Rules:
- Must have exactly one start node (isStartNode: true)
- Can have multiple end nodes (isEndNode: true)
- Connections link nodes with optional conditions
- Variables use lowercase_underscore format
- Reserved variables: customer_name, customer_email
- Position nodes logically: start at x:200, y:100, space nodes 200px apart

## CRITICAL VALIDATION RULES - MUST FOLLOW:

### 1. VARIABLE VALIDATION RULES:
**NEVER use these reserved variable names:**
- customer_name (reserved for system use)
- customer_email (reserved for system use)

**Variable Reference Rules:**
- ONLY reference variables that are defined in the variables array
- Every {{variable_name}} used in text, API calls, or conditions MUST have a corresponding entry in the variables array
- Variable names must use lowercase_underscore format (e.g., user_input, search_results, api_response)
- Variable names must start with a lowercase letter and contain only letters, numbers, and underscores

**Variable Definition Requirements:**
- All variables used anywhere in the flow MUST be defined in the variables array
- Each variable must have: name, type, description, required (boolean)
- Common variable types: "string", "number", "boolean", "object", "array"
- Provide meaningful descriptions for all variables

### 2. CONVERSATION LOOP PREVENTION:
**Avoid Problematic Loops:**
- Do NOT create circular paths without clear exit conditions
- Every loop must have multiple exit points (buttons, conditions, end nodes)
- Loops should provide clear user value (search refinement, form retry, menu navigation)
- Include explicit exit options like "Cancel", "Start Over", "Get Help"

**Valid Loop Patterns:**
- Search/filter refinement flows with "Try Different Search" and "Cancel" options
- Form validation with "Retry" and "Skip" options
- Menu navigation with "Back to Main Menu" and "Exit" options
- Booking flows with "Change Selection" and "Confirm" options

### 3. NODE CONFIGURATION RULES:
**Required Node Properties:**
- Every node MUST have: id, name, type, content, position, isStartNode, isEndNode
- Node names must be descriptive and use underscore_case format
- Node IDs must be unique UUIDs
- Position coordinates must be logical (start at x:200, y:100, space 200px apart)

**Content Validation:**
- Text content must not exceed 2000 characters
- Button options must not exceed 200 characters each
- API nodes must have: url, method, headers, timeout, retries, responseVariable
- Form nodes must have properly structured formFields array

### 4. CONNECTION INTEGRITY RULES:
**Connection Requirements:**
- Every connection must have: id, sourceNodeId, targetNodeId
- All sourceNodeId and targetNodeId must reference existing node IDs
- Button/quick_reply nodes must have conditions that match option values
- Conditional nodes must have proper condition logic

**Flow Connectivity:**
- All nodes (except end nodes) must have outgoing connections
- All nodes (except start node) must have incoming connections
- No orphaned nodes that cannot be reached from the start node

### Categories:
- **customer_support**: Help desk, troubleshooting, FAQ
- **lead_qualification**: Sales qualification, lead scoring
- **ecommerce**: Product info, orders, returns
- **appointment_booking**: Scheduling, calendar management
- **general**: Multi-purpose, welcome bots

### Difficulty Levels:
- **beginner**: 3-5 nodes, simple linear flow
- **intermediate**: 6-12 nodes, forms, conditionals
- **advanced**: 13+ nodes, API calls, complex logic

## TEMPLATE EXAMPLES:

${TEMPLATE_EXAMPLES}

## GENERATION REQUIREMENTS:

### MANDATORY VALIDATION CHECKS:
**Before generating any flow, ensure:**

1. **Variable Consistency Check:**
   - Scan ALL text content, API URLs, headers, body, and conditions for {{variable_name}} patterns
   - Create a variables array entry for EVERY variable referenced
   - NEVER use reserved names: customer_name, customer_email
   - Use descriptive alternative names like: user_name, contact_email, customer_info

2. **Loop Prevention Check:**
   - Analyze all connections to identify potential circular paths
   - For any detected loops, ensure multiple exit conditions exist
   - Add explicit exit buttons/options in loop nodes
   - Verify loops provide clear user value (search, retry, navigation)

3. **Connection Integrity Check:**
   - Verify all sourceNodeId and targetNodeId reference existing nodes
   - Ensure button/quick_reply conditions match option values exactly
   - Check that all nodes (except end nodes) have outgoing connections
   - Confirm all nodes (except start node) have incoming connections

### CORE GENERATION REQUIREMENTS:

1. Generate valid JSON structure matching the IFlowTemplate interface exactly
2. Create logical node positioning (start at x:200, y:100, space nodes 200px apart)
3. Include appropriate connections between nodes with proper conditions
4. Add relevant variables for data collection and API responses
5. Ensure proper validation and error handling
6. Include metadata: tags, estimatedSetupTime, difficulty
7. Make flows conversational and user-friendly
8. Follow the exact structure shown in the examples above
9. Use proper TypeScript types for all properties
10. Ensure all IDs are unique UUIDs
11. Always include the "integrations" array (can be empty)
12. Ensure all required fields are present and properly typed

### QUALITY ASSURANCE CHECKLIST:
**Before finalizing the flow:**
- ✅ All variables used in text/API calls are defined in variables array
- ✅ No reserved variable names (customer_name, customer_email) are used
- ✅ All loops have clear exit conditions and user value
- ✅ All connections reference valid node IDs
- ✅ All button conditions match option values
- ✅ Flow has exactly one start node and at least one end node
- ✅ All nodes are reachable from the start node
- ✅ Node names use underscore_case format
- ✅ Variable names use lowercase_underscore format

## COMMON MISTAKES TO AVOID:

### ❌ WRONG - Undefined Variables:
NEVER use variables in text without defining them in the variables array:
- Text: "Hello {{customer_name}}, your order {{order_id}} is ready!"
- Variables array: [] (empty - this will cause validation errors)

### ✅ CORRECT - Defined Variables:
ALWAYS define variables before using them:
- Text: "Hello {{user_name}}, your order {{order_id}} is ready!"
- Variables array must include:
  * {"name": "user_name", "type": "string", "description": "Customer's name", "required": true}
  * {"name": "order_id", "type": "string", "description": "Order identifier", "required": true}

### ❌ WRONG - Reserved Variable Names:
NEVER use these reserved names:
- customer_name (reserved for system use)
- customer_email (reserved for system use)

### ✅ CORRECT - Alternative Variable Names:
USE these alternatives instead:
- user_name instead of customer_name
- contact_email instead of customer_email
- client_name, visitor_name, person_name as alternatives

### ❌ WRONG - Problematic Loop (No Exit):
NEVER create loops without exit conditions:
- Connection from "search" to "results"
- Connection from "results" back to "search" (infinite loop)

### ✅ CORRECT - Loop with Exit Conditions:
ALWAYS provide multiple exit options in loops:
- "Refine Search" button (continues loop)
- "Start Over" button (exits to beginning)
- "Get Help" button (exits to help)
- "Cancel" button (exits loop)

## OUTPUT FORMAT:
Return only valid JSON matching the IFlowTemplate structure. No additional text or explanations.`;

/**
 * Enhanced prompt builder that includes user context
 */
export function buildEnhancedPrompt(userPrompt: string, context: {
  category?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  businessType?: string;
  targetAudience?: string;
  includeApiIntegration?: boolean;
  includeFormCollection?: boolean;
  includeConditionalLogic?: boolean;
}): string {
  const contextualInfo = [
    `User Request: "${userPrompt}"`,
    context.category ? `Category: ${context.category}` : '',
    context.difficulty ? `Difficulty: ${context.difficulty}` : '',
    context.businessType ? `Business Type: ${context.businessType}` : '',
    context.targetAudience ? `Target Audience: ${context.targetAudience}` : '',
    context.includeApiIntegration ? 'Include API integration capabilities' : '',
    context.includeFormCollection ? 'Include form-based data collection' : '',
    context.includeConditionalLogic ? 'Include conditional logic and branching' : ''
  ].filter(Boolean).join('\n');

  return `${AI_TEMPLATE_SYSTEM_PROMPT}\n\n## USER REQUIREMENTS:\n${contextualInfo}\n\nGenerate a complete bot conversation flow template:`;
}

/**
 * Template validation schema for TypeScript type checking
 */
export interface AITemplateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validates generated template structure and content
 */
export function validateAIGeneratedTemplate(template: any): AITemplateValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields validation
  if (!template.name?.trim()) errors.push('Template name is required');
  if (!template.description?.trim()) errors.push('Template description is required');
  if (!template.category) errors.push('Template category is required');
  if (!template.difficulty) errors.push('Template difficulty is required');
  if (!Array.isArray(template.nodes) || template.nodes.length === 0) {
    errors.push('Template must have at least one node');
  }

  // Node validation
  if (template.nodes) {
    const startNodes = template.nodes.filter((node: any) => node.isStartNode);
    if (startNodes.length !== 1) errors.push('Template must have exactly one start node');

    const endNodes = template.nodes.filter((node: any) => node.isEndNode);
    if (endNodes.length === 0) errors.push('Template must have at least one end node');

    template.nodes.forEach((node: any, index: number) => {
      if (!node.id) errors.push(`Node ${index} is missing ID`);
      if (!node.name?.trim()) errors.push(`Node ${index} is missing name`);
      if (!node.type) errors.push(`Node ${index} is missing type`);
      if (!node.content) errors.push(`Node ${index} is missing content`);
      if (!node.position) errors.push(`Node ${index} is missing position`);
      
      // Node name validation
      if (node.name && !/^[A-Za-z][A-Za-z0-9_]*$/.test(node.name)) {
        warnings.push(`Node ${index} name should use underscore_case format`);
      }
    });
  }

  // Connection validation
  if (template.connections) {
    template.connections.forEach((connection: any, index: number) => {
      if (!connection.sourceNodeId) errors.push(`Connection ${index} is missing source node ID`);
      if (!connection.targetNodeId) errors.push(`Connection ${index} is missing target node ID`);
    });
  }

  // Variable validation
  if (template.variables) {
    template.variables.forEach((variable: any, index: number) => {
      if (!variable.name) errors.push(`Variable ${index} is missing name`);
      if (!variable.type) errors.push(`Variable ${index} is missing type`);

      // Reserved variable name validation
      if (variable.name === 'customer_name' || variable.name === 'customer_email') {
        errors.push(`Variable name '${variable.name}' is reserved for system use. Please choose a different name.`);
      }

      // Variable name validation
      if (variable.name && !/^[a-z][a-z0-9_]*$/.test(variable.name)) {
        warnings.push(`Variable ${index} name should use lowercase_underscore format`);
      }
    });
  }

  // Undefined variable validation
  const definedVariables = new Set((template.variables || []).map((v: any) => v.name));
  const usedVariables = new Set<string>();

  // Extract variables from all node content
  if (template.nodes) {
    template.nodes.forEach((node: any) => {
      // Check text content
      if (node.content?.text) {
        const matches = node.content.text.match(/\{\{([^}]+)\}\}/g);
        if (matches) {
          matches.forEach((match: string) => {
            const varName = match.replace(/[{}]/g, '').trim();
            usedVariables.add(varName);
          });
        }
      }

      // Check API content
      if (node.content?.url) {
        const matches = node.content.url.match(/\{\{([^}]+)\}\}/g);
        if (matches) {
          matches.forEach((match: string) => {
            const varName = match.replace(/[{}]/g, '').trim();
            usedVariables.add(varName);
          });
        }
      }

      // Check API body
      if (node.content?.body) {
        const matches = node.content.body.match(/\{\{([^}]+)\}\}/g);
        if (matches) {
          matches.forEach((match: string) => {
            const varName = match.replace(/[{}]/g, '').trim();
            usedVariables.add(varName);
          });
        }
      }
    });
  }

  // Check for undefined variables
  usedVariables.forEach(varName => {
    const baseVarName = varName.split('.')[0]; // Handle object properties like user.name
    if (!definedVariables.has(baseVarName)) {
      errors.push(`Variable '${varName}' is used but not defined in the variables array`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

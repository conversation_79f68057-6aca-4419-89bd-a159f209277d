import mongoose, { Schema, Document, Model, Types } from 'mongoose';

export interface IFaq extends Document {
  _id: Types.ObjectId;
  question: string;
  answer: string;
  type: 'billing' | 'product';
  active: boolean;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const FaqSchema = new Schema<IFaq>(
  {
    question: {
      type: String,
      required: [true, 'Question is required'],
      trim: true,
    },
    answer: {
      type: String,
      required: [true, 'Answer is required'],
      trim: true,
    },
    type: {
      type: String,
      enum: ['billing', 'product'],
      default: 'billing',
      required: [true, 'Type is required'],
    },
    active: {
      type: Boolean,
      default: true,
    },
    order: {
      type: Number,
      default: 0,
    }
  },
  {
    timestamps: true,
  }
);

// Create index for faster lookups by type
FaqSchema.index({ type: 1, order: 1 }, { unique: true });

// Create or get the FAQ model
const Faq: Model<IFaq> = mongoose.models.Faq || mongoose.model<IFaq>('Faq', FaqSchema);

export default Faq;

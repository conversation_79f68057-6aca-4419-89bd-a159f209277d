import {
  BugWatchError,
  BugWatchErrorGroup,
  BugWatchProject,
  AlertRule,
  ErrorAnalytics,
  ErrorSeverity,
  ErrorStatus,
  ErrorCategory,
  Environment,
  ProgrammingLanguage,
  FrameworkType
} from '@/types/bugwatch';

// Mock Stack Traces
const mockStackTraces = [
  [
    {
      fileName: 'src/components/UserProfile.tsx',
      functionName: 'handleSubmit',
      lineNumber: 45,
      columnNumber: 12,
      source: 'const user = data.user.profile;',
      context: ['  const handleSubmit = async (data) => {', '    try {', '      const user = data.user.profile;', '      await updateProfile(user);', '    } catch (error) {']
    },
    {
      fileName: 'src/utils/api.ts',
      functionName: 'updateProfile',
      lineNumber: 123,
      columnNumber: 8,
      source: 'return response.data.user;',
      context: ['  const response = await fetch(\'/api/user\');', '  if (!response.ok) throw new Error(\'Failed\');', '  return response.data.user;']
    }
  ],
  [
    {
      fileName: 'src/database/connection.js',
      functionName: 'connect',
      lineNumber: 67,
      columnNumber: 15,
      source: 'await client.connect();',
      context: ['  try {', '    const client = new MongoClient(uri);', '    await client.connect();', '    return client;', '  } catch (error) {']
    }
  ]
];

// Mock User Contexts
const mockUserContexts = [
  {
    userId: 'user_123',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    ipAddress: '*************',
    sessionId: 'sess_abc123',
    userEmail: '<EMAIL>',
    userName: 'John Doe',
    customData: { plan: 'premium', region: 'us-east' }
  },
  {
    userId: 'user_456',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    ipAddress: '*********',
    sessionId: 'sess_def456',
    userEmail: '<EMAIL>',
    userName: 'Jane Smith',
    customData: { plan: 'basic', region: 'eu-west' }
  }
];

// Mock Environment Data
const mockEnvironments = [
  {
    environment: Environment.PRODUCTION,
    version: '1.2.3',
    buildNumber: '456',
    commitHash: 'abc123def456',
    branch: 'main',
    deploymentId: 'deploy_789',
    serverInfo: {
      hostname: 'prod-server-01',
      platform: 'linux',
      nodeVersion: '18.17.0',
      memory: 8192,
      cpu: 4
    }
  },
  {
    environment: Environment.STAGING,
    version: '1.2.4-beta',
    buildNumber: '457',
    commitHash: 'def456ghi789',
    branch: 'develop',
    deploymentId: 'deploy_790',
    serverInfo: {
      hostname: 'staging-server-01',
      platform: 'linux',
      nodeVersion: '18.17.0',
      memory: 4096,
      cpu: 2
    }
  }
];

// Mock Errors
export const mockErrors: BugWatchError[] = [
  {
    id: 'err_001',
    projectId: 'proj_001',
    organizationId: 'org_123',
    appId: 'app_456',
    message: 'TypeError: Cannot read property \'profile\' of undefined',
    type: 'TypeError',
    severity: ErrorSeverity.CRITICAL,
    category: ErrorCategory.RUNTIME,
    status: ErrorStatus.OPEN,
    stackTrace: mockStackTraces[0],
    errorHash: 'hash_001',
    fingerprint: 'fp_001',
    userContext: mockUserContexts[0],
    environmentData: mockEnvironments[0],
    requestUrl: '/api/user/profile',
    requestMethod: 'POST',
    requestHeaders: { 'Content-Type': 'application/json' },
    responseStatus: 500,
    firstOccurrence: '2024-01-15T08:30:00Z',
    lastOccurrence: '2024-01-15T10:30:00Z',
    occurrenceCount: 45,
    affectedUsers: 23,
    tags: ['user-profile', 'critical'],
    createdAt: '2024-01-15T08:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 'err_002',
    projectId: 'proj_001',
    organizationId: 'org_123',
    appId: 'app_456',
    message: 'Database connection timeout after 30 seconds',
    type: 'ConnectionTimeoutError',
    severity: ErrorSeverity.HIGH,
    category: ErrorCategory.DATABASE,
    status: ErrorStatus.INVESTIGATING,
    stackTrace: mockStackTraces[1],
    errorHash: 'hash_002',
    fingerprint: 'fp_002',
    userContext: mockUserContexts[1],
    environmentData: mockEnvironments[0],
    requestUrl: '/api/data/fetch',
    requestMethod: 'GET',
    responseStatus: 504,
    firstOccurrence: '2024-01-15T09:15:00Z',
    lastOccurrence: '2024-01-15T09:45:00Z',
    occurrenceCount: 12,
    affectedUsers: 8,
    assignedTo: 'dev_team_lead',
    assignedBy: 'admin_user',
    assignedAt: '2024-01-15T09:20:00Z',
    tags: ['database', 'timeout'],
    createdAt: '2024-01-15T09:15:00Z',
    updatedAt: '2024-01-15T09:45:00Z'
  },
  {
    id: 'err_003',
    projectId: 'proj_001',
    organizationId: 'org_123',
    appId: 'app_456',
    message: 'API rate limit exceeded: 1000 requests per hour',
    type: 'RateLimitError',
    severity: ErrorSeverity.MEDIUM,
    category: ErrorCategory.NETWORK,
    status: ErrorStatus.RESOLVED,
    stackTrace: [],
    errorHash: 'hash_003',
    fingerprint: 'fp_003',
    userContext: mockUserContexts[0],
    environmentData: mockEnvironments[0],
    requestUrl: '/api/external/service',
    requestMethod: 'POST',
    responseStatus: 429,
    firstOccurrence: '2024-01-15T08:45:00Z',
    lastOccurrence: '2024-01-15T08:50:00Z',
    occurrenceCount: 8,
    affectedUsers: 5,
    resolvedBy: 'backend_dev',
    resolvedAt: '2024-01-15T09:00:00Z',
    resolutionNotes: 'Increased rate limit and added retry logic',
    tags: ['api', 'rate-limit', 'resolved'],
    createdAt: '2024-01-15T08:45:00Z',
    updatedAt: '2024-01-15T09:00:00Z'
  }
];

// Mock Error Groups
export const mockErrorGroups: BugWatchErrorGroup[] = [
  {
    id: 'group_001',
    projectId: 'proj_001',
    errorHash: 'hash_001',
    fingerprint: 'fp_001',
    title: 'TypeError in User Profile Component',
    message: 'Cannot read property \'profile\' of undefined',
    type: 'TypeError',
    severity: ErrorSeverity.CRITICAL,
    category: ErrorCategory.RUNTIME,
    status: ErrorStatus.OPEN,
    totalOccurrences: 45,
    uniqueUsers: 23,
    firstSeen: '2024-01-15T08:30:00Z',
    lastSeen: '2024-01-15T10:30:00Z',
    latestError: mockErrors[0],
    createdAt: '2024-01-15T08:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 'group_002',
    projectId: 'proj_001',
    errorHash: 'hash_002',
    fingerprint: 'fp_002',
    title: 'Database Connection Issues',
    message: 'Database connection timeout after 30 seconds',
    type: 'ConnectionTimeoutError',
    severity: ErrorSeverity.HIGH,
    category: ErrorCategory.DATABASE,
    status: ErrorStatus.INVESTIGATING,
    totalOccurrences: 12,
    uniqueUsers: 8,
    firstSeen: '2024-01-15T09:15:00Z',
    lastSeen: '2024-01-15T09:45:00Z',
    latestError: mockErrors[1],
    assignedTo: 'dev_team_lead',
    createdAt: '2024-01-15T09:15:00Z',
    updatedAt: '2024-01-15T09:45:00Z'
  }
];

// Mock Projects
export const mockProjects: BugWatchProject[] = [
  {
    id: 'proj_001',
    organizationId: 'org_123',
    appId: 'app_456',
    name: 'Main Web Application',
    description: 'Primary customer-facing web application',
    language: ProgrammingLanguage.TYPESCRIPT,
    framework: FrameworkType.NEXTJS,
    apiKey: 'bw_live_1234567890abcdef',
    isActive: true,
    retentionDays: 90,
    settings: {
      autoAssignment: true,
      alertThreshold: 10,
      ignoredErrors: ['NetworkError', 'AbortError'],
      customFields: {
        team: 'frontend',
        priority: 'high'
      }
    },
    stats: {
      totalErrors: 1247,
      criticalErrors: 23,
      resolvedErrors: 892,
      errorRate: 2.3,
      lastErrorAt: '2024-01-15T10:30:00Z'
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  }
];

// Mock Alert Rules
export const mockAlertRules: AlertRule[] = [
  {
    id: 'alert_001',
    projectId: 'proj_001',
    name: 'Critical Error Alert',
    description: 'Alert when critical errors occur',
    isActive: true,
    conditions: {
      severity: [ErrorSeverity.CRITICAL],
      occurrenceCount: {
        threshold: 5,
        timeWindow: 15
      }
    },
    actions: {
      email: {
        recipients: ['<EMAIL>', '<EMAIL>'],
        template: 'critical_error'
      },
      slack: {
        channel: '#alerts',
        webhook: 'https://hooks.slack.com/services/...'
      }
    },
    escalation: {
      enabled: true,
      timeToEscalate: 30,
      escalateTo: ['<EMAIL>']
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T12:00:00Z'
  }
];

// Mock Analytics Data
export const mockAnalytics: ErrorAnalytics = {
  projectId: 'proj_001',
  timeRange: {
    start: '2024-01-08T00:00:00Z',
    end: '2024-01-15T23:59:59Z'
  },
  totalErrors: 1247,
  newErrors: 156,
  resolvedErrors: 89,
  errorRate: 2.3,
  affectedUsers: 234,
  errorTrend: [
    {
      timestamp: '2024-01-08T00:00:00Z',
      count: 45,
      severity: {
        [ErrorSeverity.CRITICAL]: 5,
        [ErrorSeverity.HIGH]: 12,
        [ErrorSeverity.MEDIUM]: 18,
        [ErrorSeverity.LOW]: 10
      }
    },
    {
      timestamp: '2024-01-09T00:00:00Z',
      count: 52,
      severity: {
        [ErrorSeverity.CRITICAL]: 3,
        [ErrorSeverity.HIGH]: 15,
        [ErrorSeverity.MEDIUM]: 22,
        [ErrorSeverity.LOW]: 12
      }
    }
  ],
  severityDistribution: {
    [ErrorSeverity.CRITICAL]: 23,
    [ErrorSeverity.HIGH]: 89,
    [ErrorSeverity.MEDIUM]: 234,
    [ErrorSeverity.LOW]: 156
  },
  categoryDistribution: {
    [ErrorCategory.RUNTIME]: 345,
    [ErrorCategory.NETWORK]: 123,
    [ErrorCategory.DATABASE]: 89,
    [ErrorCategory.VALIDATION]: 67,
    [ErrorCategory.AUTHENTICATION]: 45,
    [ErrorCategory.PERFORMANCE]: 34,
    [ErrorCategory.SECURITY]: 12,
    [ErrorCategory.SYNTAX]: 8,
    [ErrorCategory.INTEGRATION]: 6,
    [ErrorCategory.UI_UX]: 4,
    [ErrorCategory.OTHER]: 2
  },
  environmentDistribution: {
    [Environment.PRODUCTION]: 892,
    [Environment.STAGING]: 234,
    [Environment.DEVELOPMENT]: 89,
    [Environment.TESTING]: 32
  },
  topErrors: [
    {
      errorHash: 'hash_001',
      message: 'TypeError: Cannot read property of undefined',
      count: 45,
      affectedUsers: 23,
      severity: ErrorSeverity.CRITICAL
    },
    {
      errorHash: 'hash_002',
      message: 'Database connection timeout',
      count: 32,
      affectedUsers: 18,
      severity: ErrorSeverity.HIGH
    }
  ],
  responseTime: {
    average: 245,
    p95: 890,
    p99: 1250
  },
  resolutionTime: {
    average: 4.5,
    median: 3.2,
    fastest: 0.5,
    slowest: 24.8
  }
};

'use server';

import {connectToDatabase} from '@/utils/db';
import Faq from '@/models/Faq';
import {getAuthUser} from '@/utils/auth';
import {z} from 'zod';
import {revalidatePath} from 'next/cache';
import {serializeResponse} from '@/utils/helper';
import {AuditAction, ResourceType} from '@/models/AuditLog';
import {logAuditEvent} from '@/utils/audit';
import {hasPermission} from '@/utils/permissions';
import {getCurrentAdmin} from "@server/actions/admin-auth";
import { logger } from '@/utils/logger';

// Input validation schemas
const createFaqSchema = z.object({
  question: z.string().min(5, 'Question must be at least 5 characters'),
  answer: z.string().min(10, 'Answer must be at least 10 characters'),
  type: z.enum(['billing', 'product']),
  active: z.boolean().optional().default(true),
  order: z.number().optional().default(0),
});

const updateFaqSchema = z.object({
  id: z.string().min(1, 'FAQ ID is required'),
  question: z.string().min(5, 'Question must be at least 5 characters').optional(),
  answer: z.string().min(10, 'Answer must be at least 10 characters').optional(),
  type: z.enum(['billing', 'product']).optional(),
  active: z.boolean().optional(),
  order: z.number().optional(),
});

/**
 * Create a new FAQ
 */
export async function createFaq(data: {
  question: string;
  answer: string;
  type: 'billing' | 'product';
  active?: boolean;
  order?: number;
}) {
  try {
    await connectToDatabase();

    // Validate input
    const validatedData = createFaqSchema.parse(data);

    // Get the authenticated admin
    const authUser = await getAuthUser();
    if (!authUser.success || !authUser.user) {
      return serializeResponse({
        success: false,
        error: 'Unauthorized: Unable to get user details',
        data: null
      });
    }

    // Check if user has permission to create FAQs
    const userHasPermission = await hasPermission(authUser.user.userId, 'settings:EDIT');
    if (!userHasPermission) {
      return serializeResponse({
        success: false,
        error: 'You do not have permission to create FAQs',
        data: null
      });
    }

    // Create the FAQ
    const faq = new Faq(validatedData);
    await faq.save();

    // Log the action
    await logAuditEvent({
      userId: authUser.user.userId,
      userName: authUser.user.name,
      action: AuditAction.CREATE,
      resourceType: ResourceType.SETTINGS,
      resourceId: faq._id.toString(),
      description: `Created FAQ: ${faq.question}`,
    });

    // Revalidate paths
    revalidatePath('/sadmin/faqs');
    revalidatePath('/dashboard/organization/[id]/billing');

    return serializeResponse({
      success: true,
      data: faq,
      error: null,
      message: 'FAQ created successfully'
    });
  } catch (error: any) {
    logger.error('Error creating FAQ:', error);
    return serializeResponse({
      success: false,
      error: error.message || 'Failed to create FAQ',
      data: null,
      message: null
    });
  }
}

/**
 * Update an existing FAQ
 */
export async function updateFaq(data: {
  id: string;
  question?: string;
  answer?: string;
  type?: 'billing' | 'product';
  active?: boolean;
  order?: number;
}) {
  try {
    await connectToDatabase();

    // Validate input
    const validatedData = updateFaqSchema.parse(data);

    // Get the authenticated admin
    const authUser = await getAuthUser();
    if (!authUser.success || !authUser.user) {
      return serializeResponse({
        success: false,
        error: 'Unauthorized: Unable to get user details',
        data: null,
        message: null
      });
    }

    // Check if user has permission to update FAQs
    const userHasPermission = await hasPermission(authUser.user.userId, 'settings:EDIT');
    if (!userHasPermission) {
      return serializeResponse({
        success: false,
        error: 'You do not have permission to update FAQs',
        message: null,
        data: null
      });
    }

    // Find and update the FAQ
    const {id, ...updateData} = validatedData;
    const updatedFaq = await Faq.findByIdAndUpdate(
      id,
      {$set: updateData},
      {new: true}
    );

    if (!updatedFaq) {
      return serializeResponse({
        success: false,
        message: null,
        error: 'FAQ not found',
        data: null
      });
    }

    // Log the action
    await logAuditEvent({
      userId: authUser.user.userId,
      userName: authUser.user.name,
      action: AuditAction.UPDATE,
      resourceType: ResourceType.SETTINGS,
      resourceId: updatedFaq._id.toString(),
      description: `Updated FAQ: ${updatedFaq.question}`,
    });

    // Revalidate paths
    revalidatePath('/sadmin/faqs');
    revalidatePath('/dashboard/organization/[id]/billing');

    return serializeResponse({
      success: true,
      data: updatedFaq,
      error: null,
      message: 'FAQ updated successfully'
    });
  } catch (error: any) {
    logger.error('Error updating FAQ:', error);
    return serializeResponse({
      success: false,
      error: error.message || 'Failed to update FAQ',
      message: null,
      data: null
    });
  }
}

/**
 * Delete an FAQ
 */
export async function deleteFaq(id: string) {
  try {
    await connectToDatabase();

    // Get the authenticated admin
    const authUser = await getAuthUser();
    if (!authUser.success || !authUser.user) {
      return serializeResponse({
        success: false,
        error: 'Unauthorized: Unable to get user details',
        data: null,
        message: null
      });
    }

    // Check if user has permission to delete FAQs
    const userHasPermission = await hasPermission(authUser.user.userId, 'settings:EDIT');
    if (!userHasPermission) {
      return serializeResponse({
        success: false,
        error: 'You do not have permission to delete FAQs',
        data: null,
        message: null
      });
    }

    // Find the FAQ to get its details for the audit log
    const faq = await Faq.findById(id);
    if (!faq) {
      return serializeResponse({
        success: false,
        error: 'FAQ not found',
        data: null,
        message: null
      });
    }

    // Delete the FAQ
    await Faq.findByIdAndDelete(id);

    // Log the action
    await logAuditEvent({
      userId: authUser.user.userId,
      userName: authUser.user.name,
      action: AuditAction.DELETE,
      resourceType: ResourceType.SETTINGS,
      resourceId: id,
      description: `Deleted FAQ: ${faq.question}`,
    });

    // Revalidate paths
    revalidatePath('/sadmin/faqs');
    revalidatePath('/dashboard/organization/[id]/billing');

    return serializeResponse({
      success: true,
      message: 'FAQ deleted successfully',
      data: null,
      error: null,
    });
  } catch (error: any) {
    logger.error('Error deleting FAQ:', error);
    return serializeResponse({
      success: false,
      error: error.message || 'Failed to delete FAQ',
      data: null,
      message: null
    });
  }
}

/**
 * Get FAQs by type
 */
export async function getFaqsByType(type: 'billing' | 'product') {
  try {
    await connectToDatabase();

    // Find active FAQs of the specified type, sorted by order
    const faqs = await Faq.find({type, active: true})
      .sort({order: 1})
      .lean();

    return serializeResponse({
      success: true,
      error: null,
      data: faqs
    });
  } catch (error: any) {
    logger.error(`Error getting ${type} FAQs:`, error);
    return serializeResponse({
      success: false,
      error: error.message || `Failed to get ${type} FAQs`,
      data: null
    });
  }
}

/**
 * Get all FAQs for admin management
 */
export async function getAllFaqs(type?: 'billing' | 'product') {
  try {
    await connectToDatabase();

    // Get the authenticated admin
    const currentAdminResult = await getCurrentAdmin();
    if (!currentAdminResult.success || !currentAdminResult.data) {
      return serializeResponse({success: false, error: currentAdminResult.error || 'Not authenticated', data: null});
    }

    // Check if user has permission to view FAQs
    const userHasPermission = await hasPermission(currentAdminResult.data.id, 'settings:VIEW');
    if (!userHasPermission) {
      return serializeResponse({
        success: false,
        error: 'You do not have permission to view FAQs',
        data: null
      });
    }

    // Build query based on type filter
    const query = type ? { type } : {};

    // Find FAQs with optional type filter, sorted by type and order
    const faqs = await Faq.find(query).sort({ type: 1, order: 1 }).lean();

    return serializeResponse({
      success: true,
      error: null,
      data: faqs.map((faq) => ({
        _id: faq._id,
        question: faq.question,
        resourceType: ResourceType.SETTINGS,
        resourceId: faq._id,
        answer: faq.answer,
        description: faq.question,
        type: faq.type,
        active: faq.active,
        order: faq.order
      }))
    });
  } catch (error: any) {
    logger.error('Error getting all FAQs:', error);
    return serializeResponse({
      success: false,
      error: error.message || 'Failed to get FAQs',
      data: null
    });
  }
}

'use server';

import { revalidatePath } from 'next/cache';
import { dbConnect } from '@/utils/db';
import { getCurrentUser } from './user-auth';
import mongoose from 'mongoose';
import { logAuditEvent } from '@/utils/audit';
import { AuditAction, ResourceType } from '@/models/AuditLog';
import { logger } from '@/utils/logger';

// All MEK operations are now handled entirely on the frontend

import { serializeResponse } from '@/utils/helper';
import App from '@/models/App';
import EnvironmentVariable, { EnvVarType, EnvVarEnvironment } from '@/models/EnvironmentVariable';
import InheritanceLink from '@/models/InheritanceLink';
import InheritanceLinkKey from '@/models/InheritanceLinkKeys';
import crypto from 'crypto';
import { z } from 'zod';

// Types for responses
type AppResponse = {
  success: boolean;
  error?: string;
  data?: any;
};

// Validation schemas
const createAppSchema = z.object({
  name: z.string().min(1, 'App name is required'),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

const updateAppSchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  name: z.string().min(1, 'App name is required').optional(),
  description: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

const createEnvVarSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  value: z.string().min(1, 'Value is required'),
  type: z.enum(['String', 'Number', 'Boolean', 'JSON']),
  description: z.string().optional(),
  appId: z.string().min(1, 'App ID is required'),
  environment: z.enum(['development', 'staging', 'production']),
  // MEK-based encryption fields (Three-Key Model)
  iv: z.string().min(1, 'Initialization vector is required'),
  tag: z.string().min(1, 'Authentication tag is required'),
});

const updateEnvVarSchema = z.object({
  id: z.string().min(1, 'Environment variable ID is required'),
  value: z.string().min(1, 'Value is required'),
  type: z.enum(['String', 'Number', 'Boolean', 'JSON']).optional(),
  description: z.string().optional(),
  organizationId: z.string().min(1, 'Organization ID is required'),
  // MEK-based encryption fields (Three-Key Model)
  iv: z.string().min(1, 'Initialization vector is required'),
  tag: z.string().min(1, 'Authentication tag is required'),
});

const deleteEnvVarSchema = z.object({
  id: z.string().min(1, 'Environment variable ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

const verifyAppSecretSchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

const getMasterKeySchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

const createMEKInheritanceSchema = z.object({
  targetAppId: z.string().min(1, 'Target app ID is required'),
  sourceAppId: z.string().min(1, 'Source app ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  emvKeys: z.array(z.string()).min(1, 'At least one environment variable key is required'),
  environment: z.enum(['development', 'staging', 'production'], {
    required_error: 'Environment is required',
    invalid_type_error: 'Environment must be development, staging, or production'
  }),
  // MEK re-encryption fields (source MEK encrypted with target MEK)
  encryptedMasterKey: z.string().min(1, 'Encrypted master key is required'),
  masterKeyIV: z.string().min(1, 'Master key IV is required'),
  masterKeyTag: z.string().min(1, 'Master key tag is required'),
});

const updateKeyCheckSchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  keyCheck: z.string().min(1, 'KeyCheck value is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  // Frontend-generated MEK fields (Three-Key Model) - Required
  encryptedMasterKey: z.string().min(1, 'Encrypted Master Key is required'),
  masterKeyIV: z.string().min(1, 'Master Key IV is required'),
  masterKeyTag: z.string().min(1, 'Master Key Tag is required'),
});

const bulkCreateOrUpdateEnvVarsSchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  environment: z.enum(['development', 'staging', 'production']).default('development'),
  variables: z.array(z.object({
    key: z.string().min(1, 'Key is required'),
    value: z.string().min(1, 'Value is required'),
    type: z.enum(['String', 'Number', 'Boolean', 'JSON']),
    description: z.string().optional(),
    id: z.string().optional(), // Optional ID for updates
    // MEK-based encryption fields (Three-Key Model)
    iv: z.string().min(1, 'Initialization vector is required'),
    tag: z.string().min(1, 'Authentication tag is required'),
  })).min(1, 'At least one variable is required'),
});



const removeInheritanceSchema = z.object({
  id: z.string().min(1, 'Inheritance link ID is required'),
  environment: z.enum(['development', 'staging', 'production']).optional(),
});

const deleteAppSchema = z.object({
  appId: z.string().min(1, 'App ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
});

// Helper function to generate API key
function generateApiKey() {
  return `key_${crypto.randomBytes(16).toString('hex')}`;
}

/**
 * Create a new application
 */
export async function createApp(data: {
  name: string;
  description?: string;
  tags?: string[];
  organizationId: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = createAppSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Generate API key only (App Secret and MEK will be generated on the client)
    const apiKey = generateApiKey();

    // Create app with a temporary ID
    const tempId = new mongoose.Types.ObjectId();

    if (await App.findOne({
      name: data.name,
      organizationId: new mongoose.Types.ObjectId(data.organizationId.toString()),
      active: true
    })) {
      return serializeResponse({
        success: false,
        error: 'App with this name already exists'
      });
    }

    // Create app without MEK (keyCheck and MEK will be set later by the client)
    const app = await App.create({
      _id: tempId,
      name: data.name,
      description: data.description || '',
      tags: data.tags || [],
      apiKey,
      keyCheck: '', // Will be set by the client after App Secret generation
      organizationId: new mongoose.Types.ObjectId(data.organizationId.toString()),
      createdBy: new mongoose.Types.ObjectId(userId.toString()),
      modules: ['secret-key-manager'],
      active: true
      // Three-Key Model: MEK fields will be added when client completes setup
    });

    // Log app creation
    await logAuditEvent({
      userId: userId.toString(),
      userName: currentUserResult.data?.name || 'Unknown User',
      action: AuditAction.CREATE,
      resourceType: ResourceType.APP,
      resourceId: app._id.toString(),
      description: `Created app: ${app.name}`,
      details: {
        name: app.name,
        description: app.description,
        organizationId: app.organizationId.toString()
      }
    });

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${data.organizationId}/app-manager/apps`);

    return serializeResponse({
      success: true,
      data: {
        id: app._id.toString(),
        name: app.name,
        apiKey
      }
    });
  } catch (error) {
    logger.error('Create app error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create app'
    });
  }
}

/**
 * Get all apps for an organization
 */
export async function getApps(organizationId: string) {
  try {
    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        data:null,
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Get all apps for the organization using aggregation
    const apps = await App.aggregate([
      {
        $match: {
          organizationId: new mongoose.Types.ObjectId(organizationId),
          active: true
        }
      },
      {
        $sort: { updatedAt: -1 }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          description: 1,
          tags: 1,
          modules: 1,
          apiKey: 1,
          createdAt: 1,
          updatedAt: 1
        }
      }
    ]);

    return serializeResponse({
      error: null,
      success: true,
      data: apps.map(app => ({
        id: app._id.toString(),
        name: app.name,
        description: app.description,
        tags: app.tags,
        modules: app.modules,
        apiKey: app.apiKey,
        createdAt: app.createdAt,
        updatedAt: app.updatedAt
      }))
    });
  } catch (error) {
    logger.error('Get apps error:', error);
    return serializeResponse({
      data: null,
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get apps'
    });
  }
}

/**
 * Update app details
 */
export async function updateApp(data: {
  appId: string;
  name?: string;
  description?: string;
  tags?: string[];
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = updateAppSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Get app details
    const app = await App.findById(data.appId);
    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Update app
    const updateData: any = {};
    if (data.name) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.tags) updateData.tags = data.tags;

    const updatedApp = await App.findByIdAndUpdate(
      data.appId,
      { $set: updateData },
      { new: true }
    );

    // Log app update
    await logAuditEvent({
      userId: userId.toString(),
      userName: currentUserResult.data?.name || 'Unknown User',
      action: AuditAction.UPDATE,
      resourceType: ResourceType.APP,
      resourceId: app._id.toString(),
      description: `Updated app: ${app.name}`,
      details: {
        before: {
          name: app.name,
          description: app.description,
          tags: app.tags
        },
        after: {
          name: updatedApp?.name,
          description: updatedApp?.description,
          tags: updatedApp?.tags
        }
      }
    });

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${app.organizationId}/app-manager/apps`);
    revalidatePath(`/dashboard/organization/${app.organizationId}/app-manager/apps/${app._id}`);

    return serializeResponse({
      success: true,
      data: {
        id: updatedApp?._id.toString(),
        name: updatedApp?.name,
        description: updatedApp?.description,
        tags: updatedApp?.tags
      }
    });
  } catch (error) {
    logger.error('Update app error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update app'
    });
  }
}

/**
 * Get app details
 */
export async function getAppDetails(appId: string): Promise<AppResponse> {
  try {
    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Get app details using aggregation
    const [app] = await App.aggregate([
      {
        $match: {
          _id: new mongoose.Types.ObjectId(appId)
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          description: 1,
          tags: 1,
          modules: 1,
          apiKey: 1,
          organizationId: 1,
          createdAt: 1,
          updatedAt: 1
        }
      }
    ]);

    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    return serializeResponse({
      success: true,
      data: {
        id: app._id.toString(),
        name: app.name,
        description: app.description,
        tags: app.tags,
        modules: app.modules,
        apiKey: app.apiKey,
        organizationId: app.organizationId.toString(),
        createdAt: app.createdAt,
        updatedAt: app.updatedAt
      }
    });
  } catch (error) {
    logger.error('Get app details error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get app details'
    });
  }
}

/**
 * Create a new environment variable
 */
export async function createEnvironmentVariable(data: {
  key: string;
  value: string;
  type: EnvVarType;
  description?: string;
  appId: string;
  environment: EnvVarEnvironment;
  // MEK-based encryption fields
  iv: string;
  tag: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = createEnvVarSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Check if app exists
    const app = await App.findById(data.appId);
    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Check if key already exists for this app and environment
    const existingVar = await EnvironmentVariable.findOne({
      appId: new mongoose.Types.ObjectId(data.appId),
      organizationId: app.organizationId,
      key: data.key,
      environment: data.environment
    });

    if (existingVar) {
      // Update existing variable
      existingVar.value = data.value;
      existingVar.description = data.description || existingVar.description;

      // Update MEK-based encryption fields
      existingVar.iv = data.iv;
      existingVar.tag = data.tag;

      existingVar.version += 1;

      await existingVar.save();
      return serializeResponse({
        success: true,
        error: `Environment variable with key '${data.key}' have been updated.`
      });
    }

    // Create environment variable with MEK-based encryption support
    const envVar = await EnvironmentVariable.create({
      key: data.key,
      value: data.value, // Encrypted value (ciphertext)
      type: data.type,
      description: data.description || '',
      appId: new mongoose.Types.ObjectId(data.appId.toString()),
      organizationId: app.organizationId,
      environment: data.environment,
      isInherited: false,
      version: 1,
      createdBy: new mongoose.Types.ObjectId(userId.toString()),
      // MEK-based encryption fields
      iv: data.iv,
      tag: data.tag
    });

    // Log environment variable creation
    await logAuditEvent({
      userId: userId.toString(),
      userName: currentUserResult.data?.name || 'Unknown User',
      action: AuditAction.CREATE,
      resourceType: ResourceType.ENV_VAR,
      resourceId: envVar._id.toString(),
      description: `Created environment variable: ${envVar.key} for app ${app.name}`,
      details: {
        key: envVar.key,
        type: envVar.type,
        environment: envVar.environment,
        appId: envVar.appId.toString()
      }
    });

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${app.organizationId}/app-manager/apps/${app._id}/secret-keys`);

    return serializeResponse({
      success: true,
      data: {
        id: envVar._id.toString(),
        key: envVar.key,
        type: envVar.type,
        environment: envVar.environment,
        version: envVar.version,
        createdAt: envVar.createdAt
      }
    });
  } catch (error) {
    logger.error('Create environment variable error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create environment variable'
    });
  }
}

/**
 * Get environment variables for an app
 */
export async function getEnvironmentVariables(appId: string, environment?: EnvVarEnvironment): Promise<AppResponse> {
  try {
    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Check if app exists
    const app = await App.findById(appId);
    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Build query
    const query: any = {
      appId: new mongoose.Types.ObjectId(appId),
      organizationId: app.organizationId
    };
    if (environment) {
      query.environment = environment;
    }

    // Get environment variables using aggregation
    const envVars = await EnvironmentVariable.aggregate([
      {
        $match: query
      },
      {
        $sort: { key: 1 }
      },
      {
        $project: {
          _id: 1,
          key: 1,
          type: 1,
          description: 1,
          environment: 1,
          isInherited: 1,
          value: 1,
          sourceAppId: 1,
          version: 1,
          createdAt: 1,
          updatedAt: 1,
          // Include envelope encryption fields
          iv: 1,
          tag: 1,
          wrappings: 1
        }
      }
    ]);

    // Get inherited environment variables
    const inheritanceLinks = await InheritanceLink.find({
      targetAppId: new mongoose.Types.ObjectId(appId)
    });

    const inheritedVarsPromises = inheritanceLinks.map(async (link) => {
      const sourceApp = await App.findById(link.sourceAppId);

      if (!sourceApp) {
        return null;
      }

      const sourceVars = await EnvironmentVariable.aggregate([
        {
          $match: {
            appId: link.sourceAppId,
            key: link.emvKey,
            ...(environment ? { environment } : {})
          }
        },
        {
          $project: {
            _id: 1,
            key: 1,
            type: 1,
            environment: 1,
            value: 1, // Include encrypted value
            updatedAt: 1,
            // Include envelope encryption fields
            iv: 1,
            tag: 1,
            wrappings: 1
          }
        }
      ]);

      return {
        sourceApp: {
          id: sourceApp._id.toString(),
          name: sourceApp.name
        },
        envVars: sourceVars.map((v: any) => ({
          id: v._id.toString(),
          key: v.key,
          type: v.type,
          environment: v.environment,
          value: v.value,
          updatedAt: v.updatedAt,
          // Include envelope encryption fields
          iv: v.iv,
          tag: v.tag,
          wrappings: v.wrappings
        }))
      };
    });

    const inheritedVars = (await Promise.all(inheritedVarsPromises)).filter(Boolean);
    return serializeResponse({
      success: true,
      data: {
        ownVars: envVars.map((v: any) => ({
          id: v._id.toString(),
          key: v.key,
          type: v.type,
          value: v.value,
          description: v.description,
          environment: v.environment,
          version: v.version,
          createdAt: v.createdAt,
          updatedAt: v.updatedAt,
          // Include envelope encryption fields
          iv: v.iv,
          tag: v.tag,
          wrappings: v.wrappings
        })),
        inheritedVars
      }
    });
  } catch (error) {
    logger.error('Get environment variables error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get environment variables'
    });
  }
}



/**
 * Get inheritance links for an app in a specific environment
 */
export async function getInheritanceLinks(
  appId: string,
  environment: 'development' | 'staging' | 'production'
): Promise<AppResponse> {
  try {
    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Check if app exists
    const app = await App.findById(appId);
    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Get inheritance links where this app is the target
    const inheritanceLinks = await InheritanceLink.aggregate([
      {
        $match: {
          targetAppId: new mongoose.Types.ObjectId(appId),
          organizationId: app.organizationId,
          environment: environment
        }
      },
      {
        $lookup: {
          from: 'apps',
          localField: 'sourceAppId',
          foreignField: '_id',
          as: 'sourceApp'
        }
      },
      {
        $unwind: '$sourceApp'
      },
      {
        $project: {
          _id: 1,
          emvKey: 1,
          createdAt: 1,
          updatedAt: 1,
          'sourceApp._id': 1,
          'sourceApp.name': 1,
          'sourceApp.description': 1
        }
      }
    ]);

    // Group inheritance links by source app and get environment variables
    const sourceAppGroups = new Map<string, any>();

    for (const link of inheritanceLinks) {
      const sourceAppId = link.sourceApp._id.toString();

      if (!sourceAppGroups.has(sourceAppId)) {
        sourceAppGroups.set(sourceAppId, {
          sourceApp: {
            id: sourceAppId,
            name: link.sourceApp.name,
            description: link.sourceApp.description
          },
          emvKeys: [],
          links: []
        });
      }

      const group = sourceAppGroups.get(sourceAppId);
      group.emvKeys.push(link.emvKey);
      group.links.push({
        id: link._id.toString(),
        emvKey: link.emvKey,
        createdAt: link.createdAt,
        updatedAt: link.updatedAt
      });
    }

    // For each source app group, get the environment variables
    const linksWithVariables = await Promise.all(
      Array.from(sourceAppGroups.values()).map(async (group: any) => {
        // Get environment variables from the source app
        const sourceEmvs = await EnvironmentVariable.find({
          appId: group.sourceApp.id,
          key: { $in: group.emvKeys },
          environment: environment
        }).select('_id key type description environment');

        return {
          id: `${group.sourceApp.id}_${environment}`, // Composite ID for grouping
          emvKeys: group.emvKeys,
          sourceApp: group.sourceApp,
          variables: sourceEmvs.map((emv: any) => ({
            id: emv._id.toString(),
            key: emv.key,
            type: emv.type,
            description: emv.description || '',
            environment: emv.environment
          })),
          links: group.links,
          createdAt: group.links[0]?.createdAt,
          updatedAt: Math.max(...group.links.map((l: any) => new Date(l.updatedAt).getTime()))
        };
      })
    );

    return serializeResponse({
      success: true,
      data: linksWithVariables
    });
  } catch (error) {
    logger.error('Get inheritance links error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to get inheritance links'
    });
  }
}

/**
 * Remove inheritance link
 */
export async function removeInheritanceLink(data: {
  id: string;
  environment?: 'development' | 'staging' | 'production'; // Optional for validation
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = removeInheritanceSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Find the inheritance link
    const inheritanceLink = await InheritanceLink.findById(data.id);
    if (!inheritanceLink) {
      return serializeResponse({
        success: false,
        error: 'Inheritance link not found'
      });
    }

    // Validate environment if provided
    if (data.environment && inheritanceLink.environment !== data.environment) {
      return serializeResponse({
        success: false,
        error: `Environment mismatch: expected ${data.environment}, but inheritance link is in ${inheritanceLink.environment}`
      });
    }

    // Get app details for revalidation
    const targetApp = await App.findById(inheritanceLink.targetAppId);
    if (!targetApp) {
      return serializeResponse({
        success: false,
        error: 'Target app not found'
      });
    }

    const sourceApp = await App.findById(inheritanceLink.sourceAppId);
    
    // Delete the inheritance link first
    await InheritanceLink.findByIdAndDelete(data.id);

    // Check if there are any remaining inheritance links from the same source to the same target in the same environment
    const remainingLinks = await InheritanceLink.find({
      targetAppId: inheritanceLink.targetAppId,
      sourceAppId: inheritanceLink.sourceAppId,
      environment: inheritanceLink.environment
    });
    
    // If no remaining links from this source to this target in this environment, delete the InheritanceLinkKey
    if (remainingLinks.length === 0) {
       await InheritanceLinkKey.deleteOne({
        targetAppId: inheritanceLink.targetAppId,
        sourceAppId: inheritanceLink.sourceAppId,
        environment: inheritanceLink.environment
      });
    }


    // Log inheritance link deletion
    await logAuditEvent({
      userId: userId.toString(),
      userName: currentUserResult.data?.name || 'Unknown User',
      action: AuditAction.DELETE,
      resourceType: ResourceType.INHERITANCE,
      resourceId: inheritanceLink._id.toString(),
      description: `Removed inheritance link: ${targetApp.name} no longer inherits from ${sourceApp?.name || 'Unknown App'}`,
      details: {
        targetAppId: targetApp._id.toString(),
        targetAppName: targetApp.name,
        sourceAppId: inheritanceLink.sourceAppId.toString(),
        sourceAppName: sourceApp?.name || 'Unknown App',
        emvKey: inheritanceLink.emvKey
      }
    });

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${targetApp.organizationId}/app-manager/apps/${targetApp._id}/inheritance`);
    revalidatePath(`/dashboard/organization/${targetApp.organizationId}/app-manager/apps/${targetApp._id}/secret-keys`);

    return serializeResponse({
      success: true,
      data: {
        id: inheritanceLink._id.toString()
      }
    });
  } catch (error) {
    logger.error('Remove inheritance link error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to remove inheritance link'
    });
  }
}

/**
 * Update an environment variable
 */
export async function updateEnvironmentVariable(data: {
  id: string;
  value: string;
  type?: EnvVarType;
  description?: string;
  organizationId: string;
  // MEK-based encryption fields
  iv: string;
  tag: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = updateEnvVarSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    const userName = currentUserResult.data?.name || 'Unknown User';

    await dbConnect();

    // Get environment variable
    const envVar = await EnvironmentVariable.findById(data.id);
    if (!envVar) {
      return serializeResponse({
        success: false,
        error: 'Environment variable not found'
      });
    }

    // Get app details
    const app = await App.findById(envVar.appId);
    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Update environment variable
    const updateData: any = {
      value: data.value,
      version: envVar.version + 1
    };

    // Update standard fields if provided
    if (data.type) updateData.type = data.type;
    if (data.description !== undefined) updateData.description = data.description;

    // Update MEK-based encryption fields
    updateData.iv = data.iv;
    updateData.tag = data.tag;

    const updatedEnvVar = await EnvironmentVariable.findByIdAndUpdate(
      data.id,
      { $set: updateData },
      { new: true }
    );

    // Log environment variable update
    await logAuditEvent({
      userId: userId.toString(),
      userName,
      action: AuditAction.UPDATE,
      resourceType: ResourceType.ENV_VAR,
      resourceId: envVar._id.toString(),
      description: `Updated environment variable: ${envVar.key} for app ${app.name}`,
      details: {
        key: envVar.key,
        type: updatedEnvVar?.type,
        environment: envVar.environment,
        appId: envVar.appId.toString(),
        version: updatedEnvVar?.version
      }
    });

    // Find all inheritance links where this environment variable is inherited
    const inheritanceLinks = await InheritanceLink.find({
      sourceAppId: envVar.appId,
      emvKeys: { $in: [envVar.key] }
    });

    // If this variable is inherited by other apps, update the secure bridges
    if (inheritanceLinks.length > 0) {
      // Import the secure re-encryption utility
      const { reEncryptEnvironmentVariable } = await import('@/utils/secure-reencryption');

      // For each inheritance link, find the secure bridge and update it
      for (const link of inheritanceLinks) {
        // Find secure bridges for this environment variable
        const SecureBridge = mongoose.model('SecureBridge');
        const secureBridges = await SecureBridge.find({
          sourceEmvId: envVar._id,
          targetAppId: link.targetAppId
        });

        // Re-encrypt the value for each secure bridge
        for (const bridge of secureBridges) {
          await reEncryptEnvironmentVariable(
            envVar._id.toString(),
            bridge.bridgeKeyId,
            data.value,
            userId.toString(),
            userName
          );
        }

        // Revalidate paths for the target app
        revalidatePath(`/dashboard/organization/${link.organizationId}/app-manager/apps/${link.targetAppId}/secret-keys`);
      }
    }

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${app.organizationId}/app-manager/apps/${app._id}/secret-keys`);

    return serializeResponse({
      success: true,
      data: {
        id: updatedEnvVar?._id.toString(),
        key: updatedEnvVar?.key,
        type: updatedEnvVar?.type,
        environment: updatedEnvVar?.environment,
        version: updatedEnvVar?.version,
        updatedAt: updatedEnvVar?.updatedAt
      }
    });
  } catch (error) {
    logger.error('Update environment variable error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update environment variable'
    });
  }
}

/**
 * Delete an environment variable
 */
export async function deleteEnvironmentVariable(data: {
  id: string;
  organizationId: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = deleteEnvVarSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Get environment variable with detailed validation
    const envVar = await EnvironmentVariable.findById(data.id);
    if (!envVar) {
      return serializeResponse({
        success: false,
        error: 'Environment variable not found or has been deleted'
      });
    }

    // ✅ ADDITIONAL VALIDATION: Verify organization ownership
    if (envVar.organizationId.toString() !== data.organizationId) {
      return serializeResponse({
        success: false,
        error: 'Environment variable does not belong to this organization'
      });
    }

    // ✅ INHERITANCE PROTECTION: Prevent deletion of individual inherited variables
    if (envVar.isInherited) {
      // Get source app information for better error messaging
      let sourceAppName = 'Unknown App';
      if (envVar.sourceAppId) {
        const sourceApp = await App.findById(envVar.sourceAppId);
        if (sourceApp) {
          sourceAppName = sourceApp.name;
        }
      }

      return serializeResponse({
        success: false,
        error: `Cannot delete inherited environment variable "${envVar.key}". This variable is inherited from "${sourceAppName}". To remove it, either delete the inheritance relationship or delete the source app.`
      });
    }

    // Get app details
    const app = await App.findById(envVar.appId);
    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // ✅ SOURCE APP PROTECTION: Check if this variable is being inherited by other apps
    const inheritanceLinks = await InheritanceLink.find({
      sourceAppId: envVar.appId,
      emvKey: envVar.key,
      environment: envVar.environment
    });

    if (inheritanceLinks.length > 0) {
      // Get the names of apps that inherit this variable
      const targetAppIds = inheritanceLinks.map(link => link.targetAppId);
      const targetApps = await App.find({ _id: { $in: targetAppIds } });
      const targetAppNames = targetApps.map(app => app.name);

      return serializeResponse({
        success: false,
        error: `Cannot delete environment variable "${envVar.key}" because it is currently being inherited by ${inheritanceLinks.length} app${inheritanceLinks.length > 1 ? 's' : ''}: ${targetAppNames.join(', ')}.\n\nTo proceed with deletion:\n1. Remove the inheritance relationships for this variable from the affected apps\n2. Or delete the inheritance links from the inheritance management interface\n\nDeleting this variable would break the inheritance for ${inheritanceLinks.length} app${inheritanceLinks.length > 1 ? 's' : ''} and could cause application failures.`
      });
    }

    // Delete environment variable
    await EnvironmentVariable.findByIdAndDelete(data.id);

    // Log environment variable deletion
    await logAuditEvent({
      userId: userId.toString(),
      userName: currentUserResult.data?.name || 'Unknown User',
      action: AuditAction.DELETE,
      resourceType: ResourceType.ENV_VAR,
      resourceId: envVar._id.toString(),
      description: `Deleted environment variable: ${envVar.key} from app ${app.name}`,
      details: {
        key: envVar.key,
        type: envVar.type,
        environment: envVar.environment,
        appId: envVar.appId.toString()
      }
    });

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${app.organizationId}/app-manager/apps/${app._id}/secret-keys`);

    return serializeResponse({
      success: true,
      data: {
        id: envVar._id.toString(),
        key: envVar.key
      }
    });
  } catch (error) {
    logger.error('Delete environment variable error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete environment variable'
    });
  }
}

/**
 * Get encrypted Master Encryption Key for an app
 * This server action is used in the Edit Mode flow to retrieve the encrypted MEK
 * which is then decrypted on the frontend using the App Secret
 */
export async function getMasterKey(data: {
  appId: string;
  organizationId: string;
}): Promise<{
  success: boolean;
  data?: {
    appId: string;
    appName: string;
    encryptedMasterKey: string;
    masterKeyIV: string;
    masterKeyTag: string;
  };
  error?: string;
}> {
  try {
    // Validate input
    const validationResult = getMasterKeySchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Get app details
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(data.appId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId),
      active: true
    });

    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Check if the app has an encrypted master key
    if (!app.encryptedMasterKey || !app.masterKeyIV || !app.masterKeyTag) {
      return serializeResponse({
        success: false,
        error: 'Master key not found for this app'
      });
    }

    // Return the encrypted master key data
    return serializeResponse({
      success: true,
      data: {
        appId: app._id.toString(),
        appName: app.name,
        encryptedMasterKey: app.encryptedMasterKey,
        masterKeyIV: app.masterKeyIV,
        masterKeyTag: app.masterKeyTag
      }
    });
  } catch (error) {
    logger.error('Get master key error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to retrieve master key'
    });
  }
}

/**
 * Create MEK-based inheritance link between apps
 * This server action creates both the InheritanceLink and InheritanceLinkKey records
 * The encrypted MEK allows the target app to decrypt inherited environment variables
 */
export async function createMEKInheritance(data: {
  targetAppId: string;
  sourceAppId: string;
  organizationId: string;
  emvKeys: string[];
  environment: 'development' | 'staging' | 'production';
  encryptedMasterKey: string;
  masterKeyIV: string;
  masterKeyTag: string;
}): Promise<{
  success: boolean;
  data?: {
    inheritanceIds: string[];
    message: string;
  };
  error?: string;
}> {
  try {
    // Validate input
    const validationResult = createMEKInheritanceSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data!.id;
    await dbConnect();

    // Verify both apps exist and belong to the organization
    const [targetApp, sourceApp] = await Promise.all([
      App.findOne({
        _id: new mongoose.Types.ObjectId(data.targetAppId),
        organizationId: new mongoose.Types.ObjectId(data.organizationId),
        active: true
      }),
      App.findOne({
        _id: new mongoose.Types.ObjectId(data.sourceAppId),
        organizationId: new mongoose.Types.ObjectId(data.organizationId),
        active: true
      })
    ]);

    if (!targetApp) {
      return serializeResponse({
        success: false,
        error: 'Target app not found'
      });
    }

    if (!sourceApp) {
      return serializeResponse({
        success: false,
        error: 'Source app not found'
      });
    }

    // Check for existing inheritance links from the same source app
    const existingLinks = await InheritanceLink.find({
      targetAppId: new mongoose.Types.ObjectId(data.targetAppId),
      sourceAppId: new mongoose.Types.ObjectId(data.sourceAppId),
      environment: data.environment
    });

    // Check for duplicate keys in existing inheritance links
    if (existingLinks.length > 0) {
      const existingKeys = existingLinks.map(link => link.emvKey);
      const duplicateKeys = data.emvKeys.filter(key => existingKeys.includes(key));

      if (duplicateKeys.length > 0) {
        return serializeResponse({
          success: false,
          error: `The following keys are already inherited from ${sourceApp.name}: ${duplicateKeys.join(', ')}. Remove the existing inheritance first to re-inherit these keys.`
        });
      }
    }

    // Check for conflicts with existing environment variables in the target app
    const existingEnvVars = await EnvironmentVariable.find({
      appId: new mongoose.Types.ObjectId(data.targetAppId),
      environment: data.environment,
      key: { $in: data.emvKeys },
      isInherited: false // Only check own variables, not inherited ones
    });

    if (existingEnvVars.length > 0) {
      const conflictingKeys = existingEnvVars.map(env => env.key);
      return serializeResponse({
        success: false,
        error: `The following keys already exist as environment variables in ${targetApp.name}: ${conflictingKeys.join(', ')}. Remove or rename these variables first to inherit keys with the same names.`
      });
    }

    // Check for conflicts with keys inherited from other source apps
    const otherInheritanceLinks = await InheritanceLink.find({
      targetAppId: new mongoose.Types.ObjectId(data.targetAppId),
      sourceAppId: { $ne: new mongoose.Types.ObjectId(data.sourceAppId) }, // Different source app
      environment: data.environment
    }).populate('sourceAppId', 'name');

    if (otherInheritanceLinks.length > 0) {
      const otherInheritedKeys = otherInheritanceLinks.map(link => link.emvKey);
      const conflictingWithOtherSources = data.emvKeys.filter(key => otherInheritedKeys.includes(key));

      if (conflictingWithOtherSources.length > 0) {
        // Find which source apps have the conflicting keys
        const conflictDetails = conflictingWithOtherSources.map(key => {
          const conflictingLink = otherInheritanceLinks.find(link => link.emvKey === key);
          const sourceAppName = (conflictingLink?.sourceAppId as any)?.name || 'unknown app';
          return `${key} (from ${sourceAppName})`;
        });

        return serializeResponse({
          success: false,
          error: `The following keys are already inherited from other source apps: ${conflictDetails.join(', ')}. Remove the existing inheritance first to inherit these keys from ${sourceApp.name}.`
        });
      }
    }

    // Check if InheritanceLinkKey already exists for this source-target-environment combination
    let inheritanceLinkKey = await InheritanceLinkKey.findOne({
      targetAppId: new mongoose.Types.ObjectId(data.targetAppId),
      sourceAppId: new mongoose.Types.ObjectId(data.sourceAppId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId),
      environment: data.environment
    });

    // Create InheritanceLinkKey only if it doesn't exist (first inheritance from this source)
    if (!inheritanceLinkKey) {
      inheritanceLinkKey = new InheritanceLinkKey({
        targetAppId: new mongoose.Types.ObjectId(data.targetAppId),
        sourceAppId: new mongoose.Types.ObjectId(data.sourceAppId),
        organizationId: new mongoose.Types.ObjectId(data.organizationId),
        environment: data.environment,
        encryptedMasterKey: data.encryptedMasterKey,
        masterKeyIV: data.masterKeyIV,
        masterKeyTag: data.masterKeyTag,
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      await inheritanceLinkKey.save();
    }

    // Create individual inheritance links for each key
    const inheritanceLinks = [];
    for (const emvKey of data.emvKeys) {
      const inheritanceLink = new InheritanceLink({
        targetAppId: new mongoose.Types.ObjectId(data.targetAppId),
        sourceAppId: new mongoose.Types.ObjectId(data.sourceAppId),
        organizationId: new mongoose.Types.ObjectId(data.organizationId),
        emvKey: emvKey,
        environment: data.environment,
        createdBy: new mongoose.Types.ObjectId(userId)
      });

      await inheritanceLink.save();
      inheritanceLinks.push(inheritanceLink);
    }

    return serializeResponse({
      success: true,
      data: {
        inheritanceIds: inheritanceLinks.map(link => link._id.toString()),
        message: `Successfully created MEK-based inheritance from ${sourceApp.name} to ${targetApp.name} for ${data.emvKeys.length} key(s)`
      }
    });
  } catch (error) {
    logger.error('Create MEK inheritance error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create inheritance'
    });
  }
}

/**
 * Get the keyCheck value for an app to verify the App Secret on the client side
 */
export async function verifyAppSecret(data: {
  appId: string;
  organizationId: string;
}){
  try {
    // Validate input
    const validationResult = verifyAppSecretSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        data:null,
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        data:null,
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Get app details
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(data.appId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId),
      active: true
    });

    if (!app) {
      return serializeResponse({
        data:null,
        success: false,
        error: 'App not found'
      });
    }

    // Return the keyCheck value for client-side verification
    return serializeResponse({
      success: true,
      error:null,
      data: {
        id: app._id.toString(),
        name: app.name,
        keyCheck: app.keyCheck
      }
    });
  } catch (error) {
    logger.error('Verify app secret error:', error);
    return serializeResponse({
      data:null,
      success: false,
      error: error instanceof Error ? error.message : 'Failed to verify app secret'
    });
  }
}

/**
 * Update the keyCheck value for an app and store frontend-generated MEK
 * This is called after the client generates App Secret and MEK
 * In the Three-Key Model, the MEK is generated and encrypted entirely on the frontend
 */
export async function updateKeyCheck(data: {
  appId: string;
  keyCheck: string;
  organizationId: string;
  // Frontend-generated MEK fields (Three-Key Model) - Required
  encryptedMasterKey: string;
  masterKeyIV: string;
  masterKeyTag: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = updateKeyCheckSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Get app details
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(data.appId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId),
      active: true
    });

    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Prepare update data with frontend-generated MEK
    const updateData: any = {
      keyCheck: data.keyCheck,
      // Store the frontend-generated encrypted MEK
      encryptedMasterKey: data.encryptedMasterKey,
      masterKeyIV: data.masterKeyIV,
      masterKeyTag: data.masterKeyTag
    };
    
    // Update the app with new keyCheck and MEK
    await App.findByIdAndUpdate(data.appId, updateData);

    // Log the keyCheck update
    await logAuditEvent({
      userId: userId.toString(),
      userName: currentUserResult.data?.name || 'Unknown User',
      action: AuditAction.UPDATE,
      resourceType: ResourceType.APP,
      resourceId: app._id.toString(),
      description: `Updated App Secret for: ${app.name}`,
      details: {
        name: app.name,
        organizationId: app.organizationId.toString()
      }
    });

    // Revalidate paths
    revalidatePath(`/dashboard/organization/${data.organizationId}/app-manager/apps/${data.appId}`);

    return serializeResponse({
      success: true,
      data: {
        id: app._id.toString(),
        name: app.name
      }
    });
  } catch (error) {
    logger.error('Update keyCheck error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update App Secret'
    });
  }
}

/**
 * Get app deletion impact analysis
 */
export async function getAppDeletionImpact(data: {
  appId: string;
  organizationId: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = deleteAppSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    await dbConnect();

    // Get app details
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(data.appId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId)
    });

    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Count environment variables
    const envVarCount = await EnvironmentVariable.countDocuments({
      appId: app._id
    });

    // Count inheritance links where this app is the source
    const sourceInheritanceCount = await InheritanceLink.countDocuments({
      sourceAppId: app._id
    });

    // Count inheritance links where this app is the target
    const targetInheritanceCount = await InheritanceLink.countDocuments({
      targetAppId: app._id
    });

    // Get affected apps (apps that inherit from this app)
    const affectedApps = await InheritanceLink.aggregate([
      { $match: { sourceAppId: app._id } },
      { $group: { _id: '$targetAppId' } },
      {
        $lookup: {
          from: 'apps',
          localField: '_id',
          foreignField: '_id',
          as: 'app'
        }
      },
      { $unwind: '$app' },
      { $project: { _id: '$app._id', name: '$app.name' } }
    ]);

    // Get source apps (apps this app inherits from)
    const sourceApps = await InheritanceLink.aggregate([
      { $match: { targetAppId: app._id } },
      { $group: { _id: '$sourceAppId' } },
      {
        $lookup: {
          from: 'apps',
          localField: '_id',
          foreignField: '_id',
          as: 'app'
        }
      },
      { $unwind: '$app' },
      { $project: { _id: '$app._id', name: '$app.name' } }
    ]);

    return serializeResponse({
      success: true,
      data: {
        app: {
          id: app._id.toString(),
          name: app.name,
          description: app.description
        },
        impact: {
          environmentVariables: envVarCount,
          sourceInheritanceLinks: sourceInheritanceCount,
          targetInheritanceLinks: targetInheritanceCount,
          affectedApps: affectedApps.map(a => ({ id: a._id.toString(), name: a.name })),
          sourceApps: sourceApps.map(a => ({ id: a._id.toString(), name: a.name }))
        }
      }
    });

  } catch (error) {
    logger.error('Get app deletion impact error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to analyze deletion impact'
    });
  }
}

/**
 * Delete an app and all its related data with comprehensive cleanup
 */
export async function deleteApp(data: {
  appId: string;
  organizationId: string;
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = deleteAppSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Get app details
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(data.appId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId)
    });

    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Start a session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // ✅ COMPREHENSIVE DATA CLEANUP: Track deletion counts for audit logging
      const deletionCounts = {
        environmentVariables: 0,
        inheritanceLinksAsSource: 0,
        inheritanceLinksAsTarget: 0,
        inheritanceLinkKeys: 0
      };

      // 1. Delete all environment variables associated with the app (both own and inherited)
      const envVarDeletion = await EnvironmentVariable.deleteMany({
        appId: app._id
      }, { session });
      deletionCounts.environmentVariables = envVarDeletion.deletedCount || 0;

      // 2. Delete all inheritance links where this app is the source
      const sourceInheritanceDeletion = await InheritanceLink.deleteMany({
        sourceAppId: app._id
      }, { session });
      deletionCounts.inheritanceLinksAsSource = sourceInheritanceDeletion.deletedCount || 0;

      // 3. Delete all inheritance links where this app is the target
      const targetInheritanceDeletion = await InheritanceLink.deleteMany({
        targetAppId: app._id
      }, { session });
      deletionCounts.inheritanceLinksAsTarget = targetInheritanceDeletion.deletedCount || 0;

      // 4. Delete all inheritance link keys where this app is the source or target
      const inheritanceLinkKeyDeletion = await InheritanceLinkKey.deleteMany({
        $or: [
          { sourceAppId: app._id },
          { targetAppId: app._id }
        ]
      }, { session });
      deletionCounts.inheritanceLinkKeys = inheritanceLinkKeyDeletion.deletedCount || 0;

      // 5. Hard delete the app (complete removal)
      await App.findByIdAndDelete(app._id, { session });

      // ✅ COMPREHENSIVE AUDIT LOGGING: Log detailed deletion information
      await logAuditEvent({
        userId: userId.toString(),
        userName: currentUserResult.data?.name || 'Unknown User',
        action: AuditAction.DELETE,
        resourceType: ResourceType.APP,
        resourceId: app._id.toString(),
        description: `Comprehensive app deletion: ${app.name}`,
        details: {
          appName: app.name,
          appDescription: app.description,
          organizationId: app.organizationId.toString(),
          deletionCounts,
          totalDataRemoved: Object.values(deletionCounts).reduce((sum, count) => sum + count, 1), // +1 for the app itself
          deletionType: 'comprehensive_cascading_deletion',
          timestamp: new Date().toISOString()
        }
      });

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      // Revalidate paths
      revalidatePath(`/dashboard/organization/${data.organizationId}/app-manager/apps`);

      return serializeResponse({
        success: true,
        data: {
          id: app._id.toString(),
          name: app.name,
          deletionSummary: {
            environmentVariables: deletionCounts.environmentVariables,
            inheritanceLinks: deletionCounts.inheritanceLinksAsSource + deletionCounts.inheritanceLinksAsTarget,
            inheritanceLinkKeys: deletionCounts.inheritanceLinkKeys,
            totalItemsRemoved: Object.values(deletionCounts).reduce((sum, count) => sum + count, 1)
          }
        }
      });

    } catch (error) {
      // ✅ ATOMIC OPERATION: Rollback the transaction on any error
      await session.abortTransaction();
      session.endSession();

      // Log the failed deletion attempt
      await logAuditEvent({
        userId: userId.toString(),
        userName: currentUserResult.data?.name || 'Unknown User',
        action: AuditAction.ERROR,
        resourceType: ResourceType.APP,
        resourceId: app._id.toString(),
        description: `Failed app deletion attempt: ${app.name}`,
        details: {
          appName: app.name,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString()
        }
      });

      throw error;
    }
  } catch (error) {
    logger.error('Delete app error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete app'
    });
  }
}

/**
 * Bulk create or update environment variables
 * This allows creating and updating multiple environment variables in a single operation
 */
export async function bulkCreateOrUpdateEnvironmentVariables(data: {
  appId: string;
  organizationId: string;
  environment: EnvVarEnvironment;
  variables: {
    key: string;
    value: string;
    type: EnvVarType;
    description?: string;
    id?: string;
    // MEK-based encryption fields
    iv: string;
    tag: string;
  }[];
}): Promise<AppResponse> {
  try {
    // Validate input
    const validationResult = bulkCreateOrUpdateEnvVarsSchema.safeParse(data);
    if (!validationResult.success) {
      return serializeResponse({
        success: false,
        error: validationResult.error.message
      });
    }

    // Get current user
    const currentUserResult = await getCurrentUser();
    if (!currentUserResult.success) {
      return serializeResponse({
        success: false,
        error: currentUserResult.error || 'Not authenticated'
      });
    }

    const userId = currentUserResult.data?.id;
    if (!userId) {
      return serializeResponse({
        success: false,
        error: 'User ID not found'
      });
    }

    await dbConnect();

    // Check if app exists
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(data.appId),
      organizationId: new mongoose.Types.ObjectId(data.organizationId),
      active: true
    });

    if (!app) {
      return serializeResponse({
        success: false,
        error: 'App not found'
      });
    }

    // Start a session for transaction
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // Get all existing environment variables for this app and environment
      const existingEnvVars = await EnvironmentVariable.find({
        appId: new mongoose.Types.ObjectId(data.appId),
        organizationId: app.organizationId,
        environment: data.environment,
        isInherited: false
      }).session(session);

      // Create a map of existing variables by key for quick lookup
      const existingEnvVarsByKey = new Map();
      existingEnvVars.forEach(env => {
        existingEnvVarsByKey.set(env.key, env);
      });

      // Process each variable
      const results = await Promise.all(
        data.variables.map(async (variable) => {
          try {
            let envVar;

            // If ID is provided, find by ID first
            if (variable.id) {
              envVar = await EnvironmentVariable.findById(variable.id).session(session);

              // If found by ID but key doesn't match, it's an error
              if (envVar && envVar.key !== variable.key) {
                return {
                  key: variable.key,
                  success: false,
                  error: 'Key mismatch: provided ID belongs to a different environment variable'
                };
              }
            }

            // If not found by ID, look up by key
            if (!envVar) {
              envVar = existingEnvVarsByKey.get(variable.key);
            }

            // If environment variable exists, update it
            if (envVar) {
              // Check if variable is inherited
              if (envVar.isInherited) {
                return {
                  key: variable.key,
                  success: false,
                  error: 'Cannot update an inherited environment variable'
                };
              }

              // Update environment variable
              const updateData: any = {
                value: variable.value,
                version: envVar.version + 1
              };

              // Update standard fields if provided
              if (variable.type) updateData.type = variable.type;
              if (variable.description !== undefined) updateData.description = variable.description;

              // Update MEK-based encryption fields
              updateData.iv = variable.iv;
              updateData.tag = variable.tag;

              const updatedEnvVar = await EnvironmentVariable.findByIdAndUpdate(
                envVar._id,
                { $set: updateData },
                { new: true, session }
              );

              // Log environment variable update
              await logAuditEvent({
                userId: userId.toString(),
                userName: currentUserResult.data?.name || 'Unknown User',
                action: AuditAction.UPDATE,
                resourceType: ResourceType.ENV_VAR,
                resourceId: envVar._id.toString(),
                description: `Updated environment variable: ${variable.key} for app ${app?.name || 'Unknown App'}`,
                details: {
                  key: variable.key,
                  type: variable.type,
                  environment: data.environment,
                  appId: data.appId,
                  version: updatedEnvVar?.version
                }
              });

              return {
                key: variable.key,
                id: updatedEnvVar?._id.toString(),
                success: true,
                action: 'updated'
              };
            }
            // Otherwise, create a new environment variable
            else {
              const newEnvVar = await EnvironmentVariable.create([{
                key: variable.key,
                value: variable.value,
                type: variable.type,
                description: variable.description || '',
                appId: new mongoose.Types.ObjectId(data.appId),
                organizationId: app!.organizationId,
                environment: data.environment,
                isInherited: false,
                version: 1,
                createdBy: new mongoose.Types.ObjectId(userId),
                // Include MEK-based encryption fields
                iv: variable.iv,
                tag: variable.tag
              }], { session });

              // Log environment variable creation
              await logAuditEvent({
                userId: userId.toString(),
                userName: currentUserResult.data?.name || 'Unknown User',
                action: AuditAction.CREATE,
                resourceType: ResourceType.ENV_VAR,
                resourceId: newEnvVar[0]._id.toString(),
                description: `Created environment variable: ${variable.key} for app ${app?.name || 'Unknown App'}`,
                details: {
                  key: variable.key,
                  type: variable.type,
                  environment: data.environment,
                  appId: data.appId,
                  version: 1
                }
              });

              return {
                key: variable.key,
                id: newEnvVar[0]._id.toString(),
                success: true,
                action: 'created'
              };
            }
          } catch (err: any) {
            return {
              key: variable.key,
              success: false,
              error: err.message || 'Failed to process environment variable'
            };
          }
        })
      );

      // Commit the transaction
      await session.commitTransaction();
      session.endSession();

      // Count successes and failures
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.length - successCount;
      const createdCount = results.filter(r => r.success && r.action === 'created').length;
      const updatedCount = results.filter(r => r.success && r.action === 'updated').length;

      // Revalidate paths
      revalidatePath(`/dashboard/organization/${app.organizationId}/app-manager/apps/${app._id}/secret-keys`);

      return serializeResponse({
        success: true,
        data: {
          results,
          summary: {
            total: results.length,
            success: successCount,
            failure: failureCount,
            created: createdCount,
            updated: updatedCount
          }
        }
      });
    } catch (error) {
      // Abort transaction on error
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error) {
    logger.error('Bulk create or update environment variables error:', error);
    return serializeResponse({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process environment variables'
    });
  }
}

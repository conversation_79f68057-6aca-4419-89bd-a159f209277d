'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { 
  BugAntIcon, 
  ChartBarIcon, 
  CheckCircleIcon,
  FireIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  UsersIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { formatErrorCount, formatRelativeTime } from '@/utils/bugwatch-helpers';

interface ErrorStats {
  totalErrors: number;
  criticalErrors: number;
  resolvedErrors: number;
  errorRate: number;
  affectedUsers: number;
  lastErrorAt?: string;
  trend?: {
    totalErrors: number;
    criticalErrors: number;
    resolvedErrors: number;
    errorRate: number;
  };
}

interface ErrorOverviewCardsProps {
  stats: ErrorStats;
  isLoading?: boolean;
  className?: string;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  bgColor: string;
  trend?: number;
  subtitle?: string;
  isLoading?: boolean;
}

function StatCard({ 
  title, 
  value, 
  icon: Icon, 
  color, 
  bgColor, 
  trend, 
  subtitle,
  isLoading = false 
}: StatCardProps) {
  const getTrendIcon = () => {
    if (trend === undefined) return null;
    if (trend > 0) return <TrendingUpIcon className="h-3 w-3" />;
    if (trend < 0) return <TrendingDownIcon className="h-3 w-3" />;
    return null;
  };

  const getTrendColor = () => {
    if (trend === undefined) return '';
    if (trend > 0) return 'text-red-500';
    if (trend < 0) return 'text-green-500';
    return 'text-gray-500';
  };

  if (isLoading) {
    return (
      <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
        <div className="p-6">
          <div className="animate-pulse">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
                <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-16"></div>
              </div>
              <div className="h-12 w-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20 hover:bg-white/15 dark:hover:bg-gray-800/15 transition-all duration-200">
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <div className="flex items-center space-x-2">
              <p className={`text-2xl font-bold ${color}`}>
                {typeof value === 'number' ? formatErrorCount(value) : value}
              </p>
              {trend !== undefined && (
                <div className={`flex items-center space-x-1 ${getTrendColor()}`}>
                  {getTrendIcon()}
                  <span className="text-xs font-medium">
                    {Math.abs(trend)}%
                  </span>
                </div>
              )}
            </div>
            {subtitle && (
              <p className="text-xs text-gray-500 dark:text-gray-400">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 ${bgColor} rounded-lg`}>
            <Icon className={`h-6 w-6 ${color}`} />
          </div>
        </div>
      </div>
    </Card>
  );
}

export default function ErrorOverviewCards({ 
  stats, 
  isLoading = false, 
  className = '' 
}: ErrorOverviewCardsProps) {
  const cards = [
    {
      title: 'Total Errors',
      value: stats.totalErrors,
      icon: BugAntIcon,
      color: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-500/20',
      trend: stats.trend?.totalErrors,
      subtitle: 'All time errors'
    },
    {
      title: 'Critical Errors',
      value: stats.criticalErrors,
      icon: FireIcon,
      color: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-500/20',
      trend: stats.trend?.criticalErrors,
      subtitle: 'Requires immediate attention'
    },
    {
      title: 'Resolved',
      value: stats.resolvedErrors,
      icon: CheckCircleIcon,
      color: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-500/20',
      trend: stats.trend?.resolvedErrors,
      subtitle: 'Successfully fixed'
    },
    {
      title: 'Error Rate',
      value: `${stats.errorRate}%`,
      icon: ChartBarIcon,
      color: 'text-orange-600 dark:text-orange-400',
      bgColor: 'bg-orange-500/20',
      trend: stats.trend?.errorRate,
      subtitle: 'Current error percentage'
    },
    {
      title: 'Affected Users',
      value: stats.affectedUsers,
      icon: UsersIcon,
      color: 'text-purple-600 dark:text-purple-400',
      bgColor: 'bg-purple-500/20',
      subtitle: 'Users experiencing errors'
    },
    {
      title: 'Last Error',
      value: stats.lastErrorAt ? formatRelativeTime(stats.lastErrorAt) : 'No recent errors',
      icon: ClockIcon,
      color: 'text-gray-600 dark:text-gray-400',
      bgColor: 'bg-gray-500/20',
      subtitle: 'Most recent error occurrence'
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 ${className}`}>
      {cards.map((card, index) => (
        <StatCard
          key={index}
          title={card.title}
          value={card.value}
          icon={card.icon}
          color={card.color}
          bgColor={card.bgColor}
          trend={card.trend}
          subtitle={card.subtitle}
          isLoading={isLoading}
        />
      ))}
    </div>
  );
}

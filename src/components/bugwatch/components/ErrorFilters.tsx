'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { MultiSelect } from '@/components/ui/MultiSelect';
import { Badge } from '@/components/ui/Badge';
import { ErrorSeverity, ErrorStatus, ErrorCategory, Environment } from '@/types/bugwatch';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  XMarkIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';

interface FilterState {
  search: string;
  severity: ErrorSeverity[];
  status: ErrorStatus[];
  category: ErrorCategory[];
  environment: Environment[];
  dateRange: {
    start: string;
    end: string;
  };
  assignedTo: string;
}

interface ErrorFiltersProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  onClearFilters: () => void;
  className?: string;
}

export default function ErrorFilters({ 
  filters, 
  onFiltersChange, 
  onClearFilters,
  className = '' 
}: ErrorFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const severityOptions = [
    { label: 'Critical', value: ErrorSeverity.CRITICAL, color: 'text-red-600' },
    { label: 'High', value: ErrorSeverity.HIGH, color: 'text-orange-600' },
    { label: 'Medium', value: ErrorSeverity.MEDIUM, color: 'text-yellow-600' },
    { label: 'Low', value: ErrorSeverity.LOW, color: 'text-blue-600' }
  ];

  const statusOptions = [
    { label: 'Open', value: ErrorStatus.OPEN },
    { label: 'Investigating', value: ErrorStatus.INVESTIGATING },
    { label: 'In Progress', value: ErrorStatus.IN_PROGRESS },
    { label: 'Resolved', value: ErrorStatus.RESOLVED },
    { label: 'Closed', value: ErrorStatus.CLOSED },
    { label: 'Ignored', value: ErrorStatus.IGNORED }
  ];

  const categoryOptions = [
    { label: 'Runtime', value: ErrorCategory.RUNTIME },
    { label: 'Syntax', value: ErrorCategory.SYNTAX },
    { label: 'Network', value: ErrorCategory.NETWORK },
    { label: 'Database', value: ErrorCategory.DATABASE },
    { label: 'Authentication', value: ErrorCategory.AUTHENTICATION },
    { label: 'Validation', value: ErrorCategory.VALIDATION },
    { label: 'Performance', value: ErrorCategory.PERFORMANCE },
    { label: 'Security', value: ErrorCategory.SECURITY },
    { label: 'Integration', value: ErrorCategory.INTEGRATION },
    { label: 'UI/UX', value: ErrorCategory.UI_UX },
    { label: 'Other', value: ErrorCategory.OTHER }
  ];

  const environmentOptions = [
    { label: 'Production', value: Environment.PRODUCTION },
    { label: 'Staging', value: Environment.STAGING },
    { label: 'Development', value: Environment.DEVELOPMENT },
    { label: 'Testing', value: Environment.TESTING }
  ];

  const assigneeOptions = [
    { label: 'Unassigned', value: '' },
    { label: 'John Doe', value: 'john.doe' },
    { label: 'Jane Smith', value: 'jane.smith' },
    { label: 'Dev Team Lead', value: 'dev_team_lead' },
    { label: 'Backend Dev', value: 'backend_dev' }
  ];

  const updateFilter = (key: keyof FilterState, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const updateDateRange = (field: 'start' | 'end', value: string) => {
    onFiltersChange({
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [field]: value
      }
    });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.severity.length > 0) count++;
    if (filters.status.length > 0) count++;
    if (filters.category.length > 0) count++;
    if (filters.environment.length > 0) count++;
    if (filters.dateRange.start || filters.dateRange.end) count++;
    if (filters.assignedTo) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Card className={`backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20 ${className}`}>
      <div className="p-4">
        {/* Search and Quick Filters */}
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search errors by message, type, or tags..."
                value={filters.search}
                onChange={(e) => updateFilter('search', e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {activeFilterCount > 0 && (
              <Badge variant="outline" className="flex items-center space-x-1">
                <FunnelIcon className="h-3 w-3" />
                <span>{activeFilterCount} filter{activeFilterCount > 1 ? 's' : ''}</span>
              </Badge>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </Button>
            
            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="flex items-center space-x-1 text-gray-500 hover:text-gray-700"
              >
                <XMarkIcon className="h-4 w-4" />
                <span>Clear</span>
              </Button>
            )}
          </div>
        </div>

        {/* Expanded Filters */}
        {isExpanded && (
          <div className="mt-6 pt-6 border-t border-white/10 dark:border-gray-700/10">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* Severity Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Severity
                </label>
                <MultiSelect
                  value={filters.severity}
                  onChange={(value) => updateFilter('severity', value)}
                  options={severityOptions}
                  placeholder="Select severity levels"
                  className="w-full"
                />
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Status
                </label>
                <MultiSelect
                  value={filters.status}
                  onChange={(value) => updateFilter('status', value)}
                  options={statusOptions}
                  placeholder="Select status"
                  className="w-full"
                />
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Category
                </label>
                <MultiSelect
                  value={filters.category}
                  onChange={(value) => updateFilter('category', value)}
                  options={categoryOptions}
                  placeholder="Select categories"
                  className="w-full"
                />
              </div>

              {/* Environment Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Environment
                </label>
                <MultiSelect
                  value={filters.environment}
                  onChange={(value) => updateFilter('environment', value)}
                  options={environmentOptions}
                  placeholder="Select environments"
                  className="w-full"
                />
              </div>

              {/* Assignee Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Assigned To
                </label>
                <Select
                  value={filters.assignedTo}
                  onChange={(value) => updateFilter('assignedTo', value)}
                  options={assigneeOptions}
                  placeholder="Select assignee"
                  className="w-full"
                />
              </div>

              {/* Date Range */}
              <div className="space-y-2 md:col-span-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Date Range
                </label>
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Input
                      type="date"
                      value={filters.dateRange.start}
                      onChange={(e) => updateDateRange('start', e.target.value)}
                      placeholder="Start date"
                      className="w-full"
                    />
                  </div>
                  <div className="flex-1">
                    <Input
                      type="date"
                      value={filters.dateRange.end}
                      onChange={(e) => updateDateRange('end', e.target.value)}
                      placeholder="End date"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Filter Presets */}
            <div className="mt-6 pt-4 border-t border-white/10 dark:border-gray-700/10">
              <div className="flex flex-wrap gap-2">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300 mr-2">
                  Quick filters:
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('severity', [ErrorSeverity.CRITICAL])}
                  className="text-xs"
                >
                  Critical Only
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('status', [ErrorStatus.OPEN])}
                  className="text-xs"
                >
                  Open Issues
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateFilter('environment', [Environment.PRODUCTION])}
                  className="text-xs"
                >
                  Production
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateDateRange('start', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0])}
                  className="text-xs"
                >
                  Last 24h
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => updateDateRange('start', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0])}
                  className="text-xs"
                >
                  Last 7 days
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}

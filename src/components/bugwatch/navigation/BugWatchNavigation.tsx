'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  ChartBarIcon, 
  BugAntIcon, 
  BellIcon, 
  CogIcon,
  HomeIcon
} from '@heroicons/react/24/outline';

interface BugWatchNavigationProps {
  organizationId: string;
  appId: string;
  className?: string;
}

interface NavItem {
  label: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

export default function BugWatchNavigation({ 
  organizationId, 
  appId, 
  className = '' 
}: BugWatchNavigationProps) {
  const pathname = usePathname();
  
  const baseUrl = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch`;
  
  const navItems: NavItem[] = [
    {
      label: 'Overview',
      href: baseUrl,
      icon: HomeIcon,
      description: 'Dashboard overview and recent errors'
    },
    {
      label: 'Errors',
      href: `${baseUrl}/errors`,
      icon: BugAntIcon,
      description: 'View and manage all errors'
    },
    {
      label: 'Analytics',
      href: `${baseUrl}/analytics`,
      icon: ChartBarIcon,
      description: 'Error trends and analytics'
    },
    {
      label: 'Alerts',
      href: `${baseUrl}/alerts`,
      icon: BellIcon,
      description: 'Configure error alerts'
    },
    {
      label: 'Projects',
      href: `${baseUrl}/projects`,
      icon: CogIcon,
      description: 'Manage Bug Watch projects'
    }
  ];

  const isActive = (href: string) => {
    if (href === baseUrl) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className={`bg-white/5 dark:bg-gray-800/5 backdrop-blur-sm rounded-lg border border-white/10 dark:border-gray-700/10 p-1 ${className}`}>
      <div className="flex flex-wrap gap-1">
        {navItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link
              key={item.href}
              href={item.href}
              className={`
                flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
                ${active 
                  ? 'bg-white/20 dark:bg-gray-700/20 text-gray-900 dark:text-white shadow-sm' 
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-white/10 dark:hover:bg-gray-700/10'
                }
              `}
              title={item.description}
            >
              <Icon className="h-4 w-4" />
              <span>{item.label}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}

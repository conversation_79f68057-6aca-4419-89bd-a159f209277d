'use client';

import React from 'react';
import Link from 'next/link';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BreadcrumbItem {
  label: string;
  href?: string;
  current?: boolean;
}

interface BugWatchBreadcrumbProps {
  organizationId: string;
  appId: string;
  items?: BreadcrumbItem[];
}

export default function BugWatchBreadcrumb({ 
  organizationId, 
  appId, 
  items = [] 
}: BugWatchBreadcrumbProps) {
  const baseBreadcrumbs: BreadcrumbItem[] = [
    {
      label: 'Dashboard',
      href: `/dashboard/organization/${organizationId}`
    },
    {
      label: 'App Manager',
      href: `/dashboard/organization/${organizationId}/app-manager/apps`
    },
    {
      label: 'App Details',
      href: `/dashboard/organization/${organizationId}/app-manager/apps/${appId}`
    },
    {
      label: 'Bug Watch',
      href: `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch`
    }
  ];

  const allBreadcrumbs = [...baseBreadcrumbs, ...items];

  return (
    <nav className="flex mb-6" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2">
        {allBreadcrumbs.map((item, index) => (
          <li key={index} className="flex items-center">
            {index > 0 && (
              <ChevronRightIcon className="h-4 w-4 text-gray-400 mx-2" />
            )}
            
            {index === 0 && (
              <HomeIcon className="h-4 w-4 text-gray-400 mr-2" />
            )}
            
            {item.current || index === allBreadcrumbs.length - 1 ? (
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {item.label}
              </span>
            ) : (
              <Link
                href={item.href || '#'}
                className="text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

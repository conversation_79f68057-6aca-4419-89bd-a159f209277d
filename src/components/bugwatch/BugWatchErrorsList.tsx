'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { DataTable } from '@/components/ui/DataTable';
import BugWatchNavigation from './navigation/BugWatchNavigation';
import ErrorFilters from './components/ErrorFilters';
import { mockErrors } from '@/data/bugwatch-mock-data';
import { BugWatchError, ErrorSeverity, ErrorStatus, ErrorCategory, Environment } from '@/types/bugwatch';
import {
  getSeverityColor,
  getStatusColor,
  formatRelativeTime,
  formatErrorCount,
  getCategoryIcon,
  filterErrors,
  sortErrors
} from '@/utils/bugwatch-helpers';
import { 
  BugAntIcon, 
  EyeIcon,
  UserGroupIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface BugWatchErrorsListProps {
  organizationId: string;
  appId: string;
}

export default function BugWatchErrorsList({ organizationId, appId }: BugWatchErrorsListProps) {
  const [selectedErrors, setSelectedErrors] = useState<string[]>([]);
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Use mock data
  const errors = mockErrors;

  const handleErrorClick = (errorId: string) => {
    window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch/errors/${errorId}`;
  };

  const handleBulkAction = (action: string) => {
    console.log(`Performing ${action} on errors:`, selectedErrors);
    // TODO: Implement bulk actions
  };

  const getSeverityBadge = (severity: ErrorSeverity) => {
    const colors = {
      critical: 'destructive',
      high: 'warning', 
      medium: 'default',
      low: 'secondary'
    } as const;
    
    return (
      <Badge variant={colors[severity] || 'default'} className="capitalize">
        {severity}
      </Badge>
    );
  };

  const getStatusBadge = (status: ErrorStatus) => {
    const variants = {
      open: 'destructive',
      investigating: 'warning',
      in_progress: 'default',
      resolved: 'success',
      closed: 'secondary',
      ignored: 'outline'
    } as const;
    
    return (
      <Badge variant={variants[status] || 'default'} className="capitalize">
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  const columns = [
    {
      field: 'severity',
      header: 'Severity',
      sortable: true,
      body: (rowData: BugWatchError) => getSeverityBadge(rowData.severity),
      style: { width: '100px' }
    },
    {
      field: 'message',
      header: 'Error Message',
      sortable: true,
      body: (rowData: BugWatchError) => (
        <div className="space-y-1">
          <p className="font-medium text-gray-900 dark:text-white truncate max-w-md" title={rowData.message}>
            {rowData.message}
          </p>
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <i className={getCategoryIcon(rowData.category)}></i>
            <span className="capitalize">{rowData.category.replace('_', ' ')}</span>
            <span>•</span>
            <span>{rowData.type}</span>
          </div>
        </div>
      ),
      style: { minWidth: '300px' }
    },
    {
      field: 'status',
      header: 'Status',
      sortable: true,
      body: (rowData: BugWatchError) => getStatusBadge(rowData.status),
      style: { width: '120px' }
    },
    {
      field: 'occurrenceCount',
      header: 'Occurrences',
      sortable: true,
      body: (rowData: BugWatchError) => (
        <div className="text-center">
          <p className="font-medium text-gray-900 dark:text-white">
            {formatErrorCount(rowData.occurrenceCount)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            times
          </p>
        </div>
      ),
      style: { width: '100px' }
    },
    {
      field: 'affectedUsers',
      header: 'Users',
      sortable: true,
      body: (rowData: BugWatchError) => (
        <div className="flex items-center space-x-2">
          <UserGroupIcon className="h-4 w-4 text-gray-400" />
          <span className="font-medium text-gray-900 dark:text-white">
            {formatErrorCount(rowData.affectedUsers)}
          </span>
        </div>
      ),
      style: { width: '100px' }
    },
    {
      field: 'lastOccurrence',
      header: 'Last Seen',
      sortable: true,
      body: (rowData: BugWatchError) => (
        <div className="text-sm">
          <p className="text-gray-900 dark:text-white">
            {formatRelativeTime(rowData.lastOccurrence)}
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {new Date(rowData.lastOccurrence).toLocaleDateString()}
          </p>
        </div>
      ),
      style: { width: '120px' }
    },
    {
      field: 'actions',
      header: 'Actions',
      body: (rowData: BugWatchError) => (
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleErrorClick(rowData.id)}
            className="p-2"
            title="View Details"
          >
            <EyeIcon className="h-4 w-4" />
          </Button>
        </div>
      ),
      style: { width: '80px' }
    }
  ];

  return (
    <div className="space-y-6">
      {/* Bug Watch Navigation */}
      <BugWatchNavigation organizationId={organizationId} appId={appId} />
      
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Error Management</h1>
          <p className="text-gray-600 dark:text-gray-400">
            View and manage all application errors
          </p>
        </div>
        <div className="flex items-center space-x-3">
          {selectedErrors.length > 0 && (
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {selectedErrors.length} selected
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('resolve')}
              >
                Mark Resolved
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAction('assign')}
              >
                Assign
              </Button>
            </div>
          )}
          <Button variant="outline" size="sm">
            <i className="pi pi-refresh mr-2"></i>
            Refresh
          </Button>
        </div>
      </div>

      {/* Error Statistics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-500/20 rounded-lg">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Open Errors</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {errors.filter(e => e.status === 'open').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <ClockIcon className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">In Progress</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {errors.filter(e => e.status === 'investigating' || e.status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <BugAntIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Resolved</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {errors.filter(e => e.status === 'resolved').length}
                </p>
              </div>
            </div>
          </div>
        </Card>

        <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
          <div className="p-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <UserGroupIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Affected Users</p>
                <p className="text-xl font-bold text-gray-900 dark:text-white">
                  {formatErrorCount(errors.reduce((sum, e) => sum + e.affectedUsers, 0))}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Errors Table */}
      <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
        <div className="p-6">
          <DataTable
            value={errors}
            columns={columns}
            selectionMode="multiple"
            selection={selectedErrors}
            onSelectionChange={(e) => setSelectedErrors(e.value)}
            sortField={sortField}
            sortOrder={sortOrder === 'asc' ? 1 : -1}
            onSort={(e) => {
              setSortField(e.sortField);
              setSortOrder(e.sortOrder === 1 ? 'asc' : 'desc');
            }}
            paginator
            rows={10}
            rowsPerPageOptions={[5, 10, 25, 50]}
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} errors"
            emptyMessage="No errors found"
            className="custom-datatable"
          />
        </div>
      </Card>
    </div>
  );
}

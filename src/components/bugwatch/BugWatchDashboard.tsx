'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Tabs } from '@/components/ui/Tabs';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import BugWatchNavigation from './navigation/BugWatchNavigation';
import ErrorOverviewCards from './components/ErrorOverviewCards';
import { mockProjects, mockErrors } from '@/data/bugwatch-mock-data';
import { getSeverityColor, getStatusColor, formatRelativeTime } from '@/utils/bugwatch-helpers';
import { 
  BugAntIcon, 
  ChartBarIcon, 
  BellIcon, 
  CogIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  FireIcon
} from '@heroicons/react/24/outline';

interface BugWatchDashboardProps {
  organizationId: string;
  appId: string;
}

export default function BugWatchDashboard({ organizationId, appId }: BugWatchDashboardProps) {
  // Get mock data
  const project = mockProjects[0]; // Use first project as example
  const recentErrors = mockErrors.slice(0, 3); // Get first 3 errors

  // Calculate stats from mock data
  const errorStats = {
    totalErrors: project.stats.totalErrors,
    criticalErrors: project.stats.criticalErrors,
    resolvedErrors: project.stats.resolvedErrors,
    errorRate: project.stats.errorRate,
    affectedUsers: mockErrors.reduce((sum, error) => sum + error.affectedUsers, 0),
    lastErrorAt: project.stats.lastErrorAt,
    trend: {
      totalErrors: 12, // Mock trend data
      criticalErrors: -5,
      resolvedErrors: 8,
      errorRate: -0.3
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'open': return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'investigating': return <ClockIcon className="h-4 w-4" />;
      case 'resolved': return <CheckCircleIcon className="h-4 w-4" />;
      default: return <BugAntIcon className="h-4 w-4" />;
    }
  };

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: <ChartBarIcon className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          {/* Error Statistics Cards */}
          <ErrorOverviewCards stats={errorStats} />

          {/* Recent Errors */}
          <Card className="backdrop-blur-sm bg-white/10 dark:bg-gray-800/10 border border-white/20 dark:border-gray-700/20">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Recent Errors</h3>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch/errors`}
                >
                  View All
                </Button>
              </div>
              
              <div className="space-y-4">
                {recentErrors.map((error) => (
                  <div key={error.id} className="flex items-center justify-between p-4 bg-white/5 dark:bg-gray-800/5 rounded-lg border border-white/10 dark:border-gray-700/10 hover:bg-white/10 dark:hover:bg-gray-800/10 transition-colors cursor-pointer">
                    <div className="flex items-center space-x-4">
                      <div className={`w-3 h-3 rounded-full ${getSeverityColor(error.severity).split(' ')[0]}`} />
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{error.message}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {error.affectedUsers} affected users • {formatRelativeTime(error.createdAt)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={error.status === 'resolved' ? 'success' : error.status === 'investigating' ? 'warning' : 'destructive'}>
                        {getStatusIcon(error.status)}
                        <span className="ml-1 capitalize">{error.status.replace('_', ' ')}</span>
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </div>
      )
    },
    {
      id: 'errors',
      label: 'Errors',
      icon: <BugAntIcon className="h-4 w-4" />,
      content: (
        <div className="text-center py-12">
          <BugAntIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Management</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">View and manage all application errors</p>
          <Button onClick={() => window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch/errors`}>
            Go to Errors
          </Button>
        </div>
      )
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <ChartBarIcon className="h-4 w-4" />,
      content: (
        <div className="text-center py-12">
          <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Error Analytics</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">View error trends and analytics</p>
          <Button onClick={() => window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch/analytics`}>
            Go to Analytics
          </Button>
        </div>
      )
    },
    {
      id: 'alerts',
      label: 'Alerts',
      icon: <BellIcon className="h-4 w-4" />,
      content: (
        <div className="text-center py-12">
          <BellIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Alert Configuration</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">Configure error alerts and notifications</p>
          <Button onClick={() => window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch/alerts`}>
            Go to Alerts
          </Button>
        </div>
      )
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <CogIcon className="h-4 w-4" />,
      content: (
        <div className="text-center py-12">
          <CogIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Project Settings</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-6">Manage Bug Watch project configuration</p>
          <Button onClick={() => window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch/projects`}>
            Go to Projects
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Bug Watch Navigation */}
      <BugWatchNavigation organizationId={organizationId} appId={appId} />

      {/* Dashboard Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Bug Watch Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">Monitor and manage application errors</p>
        </div>
        <div className="flex items-center space-x-3">
          <Badge variant="outline">
            Last updated: {new Date().toLocaleTimeString()}
          </Badge>
          <Button variant="outline" size="sm">
            Refresh
          </Button>
        </div>
      </div>

      {/* Main Dashboard Content */}
      <Tabs tabs={tabs} defaultActiveTab="overview" />
    </div>
  );
}

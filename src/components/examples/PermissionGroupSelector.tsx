'use client';

import React, { useState } from 'react';
import { SelectUserPermissionGroup } from '@components/ui/SelectUserPermissionGroup';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { logger } from '@/utils/logger';

interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  code: string;
  isDefault: boolean;
  permissions: string[];
}

export default function PermissionGroupSelector({ organizationId }: { organizationId: string }) {

  const [selectedGroupId, setSelectedGroupId] = useState<string | number | null>(null);
  const [allGroups, setAllGroups] = useState<PermissionGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<PermissionGroup | null>(null);

  const handlePermissionGroupChange = (value: string | number | null) => {
    setSelectedGroupId(value);

    if (value) {
      const group = allGroups.find(g => g._id === value);
      setSelectedGroup(group || null);
    } else {
      setSelectedGroup(null);
    }
  };

  const handleGroupsLoaded = (groups: PermissionGroup[]) => {
    setAllGroups(groups);
  };

  return (
    <Card className="p-6">
      <h2 className="text-xl font-semibold mb-4">Select Permission Group</h2>

      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">Permission Group</label>
        <SelectUserPermissionGroup
          organizationId={organizationId}
          value={selectedGroupId}
          onChange={handlePermissionGroupChange}
          onPermissionGroupsLoaded={handleGroupsLoaded}
        />
      </div>

      {selectedGroup && (
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
          <h3 className="text-lg font-medium mb-2">{selectedGroup.name}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">{selectedGroup.description}</p>

          <div className="mb-4">
            <span className="text-sm font-medium">Permissions:</span>
            <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
              {selectedGroup.permissions.map((permission: string) => (
                <div
                  key={permission}
                  className="text-xs p-2 bg-white dark:bg-gray-700 rounded border border-gray-200 dark:border-gray-600"
                >
                  <div className="font-medium">{permission}</div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <Button
              variant="primary"
              size="sm"
              onClick={() => {}}
            >
              Use This Group
            </Button>
          </div>
        </div>
      )}
    </Card>
  );
}

'use client';

import React from 'react';
import { Select } from '@/components/ui/Select';

export type Environment = 'development' | 'staging' | 'production';

interface EnvironmentSelectorProps {
  value: Environment;
  onChange: (environment: Environment) => void;
  className?: string;
  disabled?: boolean;
  label?: string;
  showLabel?: boolean;
}

const ENVIRONMENT_OPTIONS = [
  { label: 'Development', value: 'development' as Environment },
  { label: 'Staging', value: 'staging' as Environment },
  { label: 'Production', value: 'production' as Environment }
];

export const EnvironmentSelector: React.FC<EnvironmentSelectorProps> = ({
  value,
  onChange,
  className = '',
  disabled = false,
  label = 'Environment:',
  showLabel = true
}) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {showLabel && (
        <label
          htmlFor="environment-select"
          className="text-sm font-medium text-gray-700 dark:text-gray-300 whitespace-nowrap"
        >
          {label}
        </label>
      )}
      <Select
        id="environment-select"
        value={value}
        onChange={(selectedValue) => onChange(selectedValue as Environment)}
        options={ENVIRONMENT_OPTIONS}
        disabled={disabled}
      />
    </div>
  );
};

export default EnvironmentSelector;

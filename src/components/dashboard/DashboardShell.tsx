'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import PageHeader from '@/components/dashboard/PageHeader';

interface DashboardShellProps {
  children: ReactNode;
  title: string;
  subtitle?: string;
  actions?: ReactNode;
  backLink?: string;
  backLinkText?: string;
}

export default function DashboardShell({
  children,
  title,
  subtitle,
  actions,
  backLink,
  backLinkText = 'Back',
}: DashboardShellProps) {
  return (
    <div className="p-4 md:p-6 max-w-7xl mx-auto">
      {backLink && (
        <div className="mb-4">
          <Link href={backLink}>
            <Button variant="ghost" size="sm" className="flex items-center gap-1">
              <i className="pi pi-arrow-left text-sm"></i>
              <span>{backLinkText}</span>
            </Button>
          </Link>
        </div>
      )}
      
      <PageHeader
        title={title}
        subtitle={subtitle}
        actions={actions}
      />
      
      <div className="mt-6">
        {children}
      </div>
    </div>
  );
}

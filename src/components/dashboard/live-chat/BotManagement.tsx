'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Switch } from '@/components/ui/Switch';
import { Badge } from '@/components/ui/Badge';
import { BotSetupWizard } from './BotSetupWizard';
import { BotOnboardingGuide } from './BotOnboardingGuide';
import { getBotConfig, toggleBotStatus, deleteBot } from '@/server/actions/bot-actions';
import { getConversationFlows } from '@/server/actions/bot-flow-actions';
import EnhancedConversationFlowBuilder from './flow-builder/EnhancedConversationFlowBuilder';
import { confirmDialog } from '@/components/ui/ConfirmDialog';
import { useUrlStateManager } from '@/hooks/useUrlStateManager';
import { logger } from '@/utils/logger';

interface BotManagementProps {
  organizationId: string;
}

type BotConfig = Awaited<ReturnType<typeof getBotConfig>>

type ConversationFlow = Awaited<ReturnType<typeof getConversationFlows>>

// Valid view types for URL state
type ViewType = 'overview' | 'setup' | 'flow-builder';

// Error boundary component for defensive programming
const BotManagementErrorBoundary: React.FC<{ children: React.ReactNode; onError: (error: string) => void }> = ({
  children,
  onError
}) => {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      onError(`An unexpected error occurred: ${event.error?.message || 'Unknown error'}`);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      onError(`An unexpected error occurred: ${event.reason?.message || 'Promise rejection'}`);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
};

export const BotManagement: React.FC<BotManagementProps> = ({ organizationId }) => {
  const [botConfig, setBotConfig] = useState<BotConfig['data'] | null>(null);
  const [conversationFlows, setConversationFlows] = useState<ConversationFlow['data']>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // URL state management with centralized solution
  const { urlState, setUrl } = useUrlStateManager({
    defaultValues: {
      view: 'overview' as ViewType,
      flowId: null as string | null,
      editMode: false
    },
    namespace: 'bot-management',
    debounceMs: 150,
    parseValue: (key: string, value: string) => {
      if (key === 'view') {
        return ['overview', 'setup', 'flow-builder'].includes(value) ? value as ViewType : 'overview';
      }
      if (key === 'flowId') {
        // Preserve actual flow IDs, only convert empty/undefined to null
        return value && value.trim() !== '' ? value : null;
      }
      if (key === 'editMode') {
        return value === 'true';
      }
      return value;
    },
    serializeValue: (key: string, value: any) => {
      if (key === 'editMode') {
        return value ? 'true' : '';
      }
      if (key === 'flowId') {
        // Preserve flow IDs, only serialize null/undefined as empty string
        return value !== null && value !== undefined ? value.toString() : '';
      }
      return value?.toString() || '';
    }
  });

  // Extract state values for easier access
  const view = urlState.view;
  const flowId = urlState.flowId;
  const editMode = urlState.editMode;

  // Validation and helper functions
  const validateFlowId = useCallback((flowId: string | null): boolean => {
    if (!flowId) return true; // null/undefined is valid for new flows
    return conversationFlows?.some(flow => flow.id === flowId) || false;
  }, [conversationFlows]);

  // Safe navigation functions with validation and centralized state management
  const navigateToSetup = useCallback((editModeValue: boolean = false) => {
    setUrl({
      view: 'setup',
      editMode: editModeValue,
      flowId: null
    });
  }, [setUrl]);

  const navigateToFlowBuilder = useCallback((flowIdValue?: string) => {
    // Validate flowId if provided
    if (flowIdValue && !validateFlowId(flowIdValue)) {
      setError(`Flow not found: ${flowIdValue}`);
      return;
    }

    setUrl({
      view: 'flow-builder',
      flowId: flowIdValue || null,
      editMode: false
    });
  }, [setUrl, validateFlowId, setError]);

  const navigateToOverview = useCallback(() => {
    setUrl({
      view: 'overview',
      flowId: null,
      editMode: false
    });
  }, [setUrl]);

  // Derived state with validation
  const currentView = view;
  const currentFlowId = flowId; // Use raw flowId, validation happens later when data is loaded
  const isEditMode = editMode;

  // Validate required data before rendering flow builder
  const canRenderFlowBuilder = useCallback(() => {
    if (currentView !== 'flow-builder') return true;

    // Don't validate if still loading or no conversation flows data
    if (isLoading || !conversationFlows) return true;

    // If we have a flowId, validate it exists
    if (currentFlowId && !validateFlowId(currentFlowId)) {
      setError(`Flow not found: ${currentFlowId}`);
      navigateToOverview();
      return false;
    }

    return true;
  }, [currentView, currentFlowId, validateFlowId, setError, navigateToOverview, isLoading, conversationFlows]);

  const loadBotData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Load bot configuration
      const botResult = await getBotConfig(organizationId);
      if (botResult.success) {
        setBotConfig(botResult.data);

        // If bot exists, load conversation flows
        if (botResult.data) {
          const flowsResult = await getConversationFlows(organizationId);
          if (flowsResult.success) {
            setConversationFlows(flowsResult.data);
          }
        }
      } else {
        setError(botResult.error || 'Failed to load bot configuration');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // All useEffect hooks must be called before any conditional returns
  useEffect(() => {
    loadBotData();
  }, [organizationId]);

  // Handle invalid flow ID by redirecting to overview (only after flows are loaded)
  useEffect(() => {
    // Only validate if we're not loading and have conversation flows data
    if (!isLoading && conversationFlows && conversationFlows.length >= 0 && currentView === 'flow-builder' && flowId && !validateFlowId(flowId)) {
      setError(`Flow not found: ${flowId}`);
      navigateToOverview();
    }
  }, [currentView, flowId, isLoading, conversationFlows, validateFlowId, navigateToOverview]);

  // Defensive programming: Clear error when navigating away from error state
  useEffect(() => {
    if (error && currentView === 'overview') {
      // Clear error after a delay to allow user to see it
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, currentView]);


  const handleToggleBotStatus = async () => {
    if (!botConfig) return;

    try {
      const result = await toggleBotStatus(organizationId, !botConfig.isActive);
      if (result.success) {
        setBotConfig(prev => prev ? { ...prev, isActive: !prev.isActive } : null);
      } else {
        setError(result.error || 'Failed to update bot status');
      }
    } catch (err) {
      setError('Failed to update bot status');
    }
  };

  const handleDeleteBot = () => {
    if (!botConfig) {
      return;
    }

    // Show custom confirmation dialog
    confirmDialog({
      message: 'Are you sure you want to delete this bot? This action cannot be undone.',
      header: 'Delete Bot',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      acceptClassName: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0',
      accept: async () => {
        try {
          const result = await deleteBot(organizationId);
          if (result.success) {
            setBotConfig(null);
            setConversationFlows([]);
          } else {
            setError(result.error || 'Failed to delete bot');
          }
        } catch (err) {
          setError('Failed to delete bot');
        }
      },
      reject: () => {
        // User cancelled - no action needed
      }
    });
  };

  const handleSetupComplete = () => {
    navigateToOverview();
    loadBotData();
  };

  const handleFlowBuilderComplete = () => {
    navigateToOverview();
    loadBotData();
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (currentView === 'setup') {
    return (
      <div className="space-y-6">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setError(null)}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Dismiss
              </Button>
            </div>
          </div>
        )}
        <BotSetupWizard
          organizationId={organizationId}
          onComplete={handleSetupComplete}
          onCancel={navigateToOverview}
          editMode={isEditMode || !!botConfig}
          existingBotData={botConfig ? {
            personality: botConfig.personality,
            schedule: botConfig.schedule,
            settings: botConfig.settings
          } : undefined}
        />
      </div>
    );
  }

  if (currentView === 'flow-builder') {
    // Additional validation before rendering flow builder
    if (!canRenderFlowBuilder()) {
      return (
        <div className="space-y-6">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Unable to Load Flow Builder
                </h3>
                <p className="text-sm text-yellow-600 dark:text-yellow-400 mt-1">
                  {error || 'The requested flow could not be found or is invalid.'}
                </p>
              </div>
              <Button
                size="sm"
                onClick={navigateToOverview}
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
              >
                Return to Overview
              </Button>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setError(null)}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Dismiss
              </Button>
            </div>
          </div>
        )}
        <EnhancedConversationFlowBuilder
          organizationId={organizationId}
          flowId={currentFlowId}
          onComplete={handleFlowBuilderComplete}
          onCancel={navigateToOverview}
        />
      </div>
    );
  }

  if (!botConfig) {
    return (
      <BotOnboardingGuide
        onStartSetup={() => navigateToSetup(false)}
        onDismiss={() => {
          // Could implement a "don't show again" preference here
        }}
      />
    );
  }

  // Final safety check - if we get here, render overview with error handling
  return (
    <BotManagementErrorBoundary onError={setError}>
      <div className="space-y-6">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setError(null)}
                className="text-red-600 border-red-300 hover:bg-red-50"
              >
                Dismiss
              </Button>
            </div>
          </div>
        )}

      {/* Bot Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {botConfig.personality.name}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {botConfig.personality.description || 'Automated customer support bot'}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Badge variant={botConfig.isActive ? 'success' : 'secondary'}>
              {botConfig.isActive ? 'Active' : 'Inactive'}
            </Badge>
            <Switch
              checked={botConfig.isActive}
              onChange={handleToggleBotStatus}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {botConfig.analytics.totalConversations}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Total Conversations
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {botConfig.analytics.successfulResolutions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Successful Resolutions
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {(botConfig.analytics.handoffRate * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Handoff Rate
            </div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {(botConfig.analytics.averageResponseTime / 1000).toFixed(1)}s
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Avg Response Time
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={() => navigateToSetup(true)}
          >
            Edit Configuration
          </Button>
          <Button
            variant="outline"
            onClick={handleDeleteBot}
            className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
          >
            Delete Bot
          </Button>
        </div>
      </Card>

      {/* Conversation Flows */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Conversation Flows
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Define how your bot responds to different customer inquiries
            </p>
          </div>
          <Button
            onClick={() => navigateToFlowBuilder()}
            className="bg-indigo-600 hover:bg-indigo-700"
          >
            Create Flow
          </Button>
        </div>

        {conversationFlows?.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              No Conversation Flows
            </h4>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Create your first conversation flow to define bot responses
            </p>
            <Button
              onClick={() => navigateToFlowBuilder()}
              size="sm"
            >
              Create Flow
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {conversationFlows?.map((flow) => (
              <div
                key={flow.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {flow.name}
                      </h4>
                      <Badge variant={flow.status ==="published" ? 'success' : 'secondary'}>
                        {flow.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {flow.description || 'No description'}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                      <span>{flow.totalNodes} nodes</span>
                      <span>{flow.analytics.totalExecutions} executions</span>
                      <span>v{flow.version}</span>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigateToFlowBuilder(flow.id)}
                    >
                      Edit
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
      </div>
    </BotManagementErrorBoundary>
  );
};

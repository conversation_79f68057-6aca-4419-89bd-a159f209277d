'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { HelpButton } from '@/components/ui/HelpButton';
import { BotManagement } from './BotManagement';
import { ConversationsTab, type Conversation } from './tabs/ConversationsTab';
import { SettingsTab, type Organization, type LiveChatConfig } from './tabs/SettingsTab';
import { AnalyticsTab } from './tabs/AnalyticsTab';
import { useUrlState } from '@/hooks/useUrlStateManager';

interface LiveChatDashboardProps {
  organizationId: string;
  conversations: Conversation[];
  organization: Organization;
  liveChatConfig: LiveChatConfig;
}

export function LiveChatDashboard({
  organizationId,
  conversations: initialConversations,
  organization,
  liveChatConfig
}: LiveChatDashboardProps) {
  const [conversations, setConversations] = useState<Conversation[]>(initialConversations);
  
  const {urlState, setUrl} = useUrlState({
    page: "conversations" as 'conversations' | 'bots' | 'settings' | 'analytics'
  }, 'livechat-dashboard');
  
  const handleConversationsUpdate = (updatedConversations: Conversation[]) => {
    setConversations(updatedConversations);
  };
  
  const setActiveTab = (tab: 'conversations' | 'bots' |'settings' | 'analytics') => {
    setUrl({page: tab});
  }
  
  const activeTab = urlState.page;

  // Help documentation items
  const helpItems = [
    {
      title: 'Getting Started with Bots',
      content: 'Learn how to create and configure your first chatbot to automate customer support.',
      link: '/docs/live-chat-bots/getting-started'
    },
    {
      title: 'Conversation Flow Builder',
      content: 'Design intelligent conversation flows with our visual builder tool.',
      link: '/docs/live-chat-bots/conversation-flows'
    },
    {
      title: 'Bot Analytics & Performance',
      content: 'Monitor your bot\'s performance and optimize customer interactions.',
      link: '/docs/live-chat-bots/analytics'
    },
    {
      title: 'Human Handoff Setup',
      content: 'Configure when and how to transfer conversations to human agents.',
      link: '/docs/live-chat-bots/handoff'
    }
  ];

  // Breadcrumb items based on active tab
  const getBreadcrumbItems = () => {
    const baseItems = [
      { label: 'Dashboard', href: `/dashboard/organization/${organizationId}` },
      { label: 'Live Chat', href: `/dashboard/organization/${organizationId}/live-chat` }
    ];

    if (activeTab === 'bots') {
      return [...baseItems, { label: 'Chatbots', current: true }];
    } else if (activeTab === 'settings') {
      return [...baseItems, { label: 'Widget Settings', current: true }];
    } else if (activeTab === 'analytics') {
      return [...baseItems, { label: 'Analytics', current: true }];
    } else {
      return [...baseItems, { label: 'Conversations', current: true }];
    }
  };

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={getBreadcrumbItems()} />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Live Chat Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage customer conversations and support requests
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <HelpButton items={helpItems} variant="text" />
          {activeTab === 'conversations' && (
            <Button
              onClick={() => setActiveTab('bots')}
              className="bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              Setup Bot
            </Button>
          )}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('conversations')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'conversations'
                ? 'border-[#8178E8] text-[#8178E8]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Conversations
          </button>
          <button
            onClick={() => setActiveTab('bots')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'bots'
                ? 'border-[#8178E8] text-[#8178E8]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Chatbots
          </button>
          <button
            onClick={() => setActiveTab('settings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'settings'
                ? 'border-[#8178E8] text-[#8178E8]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Widget Settings
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-[#8178E8] text-[#8178E8]'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Analytics
          </button>
        </nav>
      </div>

      {activeTab === 'conversations' && (
        <ConversationsTab
          organizationId={organizationId}
          conversations={conversations}
          onConversationsUpdate={handleConversationsUpdate}
        />
      )}

      {activeTab === 'bots' && (
        <BotManagement organizationId={organizationId} />
      )}

      {activeTab === 'settings' && (
        <SettingsTab
          organization={organization}
          liveChatConfig={liveChatConfig}
        />
      )}

      {activeTab === 'analytics' && (
        <AnalyticsTab organizationId={organizationId} />
      )}
    </div>
  );
}

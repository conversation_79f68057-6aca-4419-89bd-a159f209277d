import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BotManagement } from '../BotManagement';
import { useQueryState } from 'nuqs';

// Mock the nuqs hook
jest.mock('nuqs', () => ({
  useQueryState: jest.fn(),
}));

// Mock the server actions
jest.mock('@/server/actions/bot-actions', () => ({
  getBotConfig: jest.fn(),
  toggleBotStatus: jest.fn(),
  deleteBot: jest.fn(),
}));

jest.mock('@/server/actions/bot-flow-actions', () => ({
  getConversationFlows: jest.fn(),
}));

// Mock the UI components
jest.mock('@/components/ui/ConfirmDialog', () => ({
  confirmDialog: jest.fn(),
}));

const mockUseQueryState = useQueryState as jest.MockedFunction<typeof useQueryState>;

describe('BotManagement URL State Management', () => {
  const mockSetView = jest.fn();
  const mockSetFlowId = jest.fn();
  const mockSetEditMode = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockUseQueryState
      .mockReturnValueOnce(['overview', mockSetView]) // view
      .mockReturnValueOnce([null, mockSetFlowId]) // flowId
      .mockReturnValueOnce([false, mockSetEditMode]); // editMode
  });

  it('should initialize with default URL state values', () => {
    render(<BotManagement organizationId="test-org" />);

    // Verify that useQueryState was called with correct configurations
    expect(mockUseQueryState).toHaveBeenCalledWith('view', expect.objectContaining({
      defaultValue: 'overview',
    }));

    expect(mockUseQueryState).toHaveBeenCalledWith('flowId', expect.objectContaining({
      defaultValue: null,
    }));

    expect(mockUseQueryState).toHaveBeenCalledWith('editMode', expect.objectContaining({
      defaultValue: false,
    }));
  });

  it('should parse view parameter correctly', () => {
    const viewConfig = mockUseQueryState.mock.calls[0][1];
    
    // Test valid values
    expect(viewConfig.parse('overview')).toBe('overview');
    expect(viewConfig.parse('setup')).toBe('setup');
    expect(viewConfig.parse('flow-builder')).toBe('flow-builder');
    
    // Test invalid values default to 'overview'
    expect(viewConfig.parse('invalid')).toBe('overview');
    expect(viewConfig.parse('')).toBe('overview');
  });

  it('should parse flowId parameter correctly', () => {
    const flowIdConfig = mockUseQueryState.mock.calls[1][1];
    
    // Test valid values
    expect(flowIdConfig.parse('flow-123')).toBe('flow-123');
    expect(flowIdConfig.parse('')).toBe(null);
    
    // Test serialization
    expect(flowIdConfig.serialize('flow-123')).toBe('flow-123');
    expect(flowIdConfig.serialize(null)).toBe('');
  });

  it('should parse editMode parameter correctly', () => {
    const editModeConfig = mockUseQueryState.mock.calls[2][1];
    
    // Test valid values
    expect(editModeConfig.parse('true')).toBe(true);
    expect(editModeConfig.parse('false')).toBe(false);
    expect(editModeConfig.parse('')).toBe(false);
    
    // Test serialization
    expect(editModeConfig.serialize(true)).toBe('true');
    expect(editModeConfig.serialize(false)).toBe('');
  });
});

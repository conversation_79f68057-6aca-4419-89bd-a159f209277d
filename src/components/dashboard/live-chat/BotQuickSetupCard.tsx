'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';

interface BotQuickSetupCardProps {
  onCreateBot: () => void;
  className?: string;
}

export const BotQuickSetupCard: React.FC<BotQuickSetupCardProps> = ({
  onCreateBot,
  className = ''
}) => {
  return (
    <Card className={`p-6 bg-gradient-to-r from-indigo-500 to-purple-600 text-white relative overflow-hidden ${className}`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
          <defs>
            <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
              <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)" />
        </svg>
      </div>

      <div className="relative z-10">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-semibold">Automate Customer Support</h3>
              <p className="text-white/80 text-sm">Set up your first chatbot</p>
            </div>
          </div>
          <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
            New
          </Badge>
        </div>

        <div className="space-y-3 mb-6">
          <div className="flex items-center space-x-2 text-sm">
            <svg className="w-4 h-4 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Handle common questions automatically</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <svg className="w-4 h-4 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Provide 24/7 instant responses</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <svg className="w-4 h-4 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Smart handoff to human agents</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <Button
            onClick={onCreateBot}
            className="bg-white text-indigo-600 hover:bg-gray-50 font-medium flex-1"
          >
            Create Bot
          </Button>
          <Button
            variant="outline"
            onClick={() => window.open('/docs/live-chat-bots', '_blank')}
            className="border-white/30 text-white hover:bg-white/10"
          >
            Learn More
          </Button>
        </div>

        <div className="mt-4 text-xs text-white/70">
          ⏱️ Setup takes less than 5 minutes
        </div>
      </div>
    </Card>
  );
};

BotQuickSetupCard.displayName = 'BotQuickSetupCard';

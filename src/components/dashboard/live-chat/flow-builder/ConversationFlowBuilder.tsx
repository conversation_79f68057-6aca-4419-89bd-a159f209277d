'use client';

import React, { useEffect } from 'react';
import { IFlowTemplate, IEnhancedFlowNode, IFlowConnection, IFlowVariable, IFlowIntegration } from '@/types/bot';
import {
  useFlowState,
  useApiIntegration,
  useCanvasState,
  useUIState,
  useFlowOperations,
  useFlowActions
} from './hooks';
import {
  FlowHeader,
  FlowActionButtons,
  FlowTabNavigation,
  FlowLoadingIndicator,
  FlowValidationModal,
  FlowContentWrapper
} from './components';
import { validateFlow, processImportedFlowData } from './utils';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';

interface ConversationFlowBuilderProps {
  organizationId: string;
  flowId?: string | null;
  onComplete: () => void;
  onCancel: () => void;
}

export const ConversationFlowBuilder: React.FC<ConversationFlowBuilderProps> = ({
  organizationId,
  flowId: flowIdProp,
  onComplete,
  onCancel
}) => {
  const { success } = useToast();

  // Initialize all hooks
  const flowState = useFlowState(flowIdProp);
  const apiIntegration = useApiIntegration();
  const canvasState = useCanvasState();
  const uiState = useUIState(flowIdProp);
  
  // Flow operations hook
  const flowOperations = useFlowOperations({
    flowData: flowState.flowData,
    setFlowData: flowState.setFlowData,
    selectedNode: flowState.selectedNode,
    setSelectedNode: flowState.setSelectedNode,
    addToFlowHistory: flowState.addToFlowHistory,
    configuredApis: apiIntegration.configuredApis,
    setConfiguredApis: apiIntegration.setConfiguredApis
  });

  // Flow actions hook
  const flowActions = useFlowActions({
    organizationId,
    flowData: flowState.flowData,
    setFlowData: flowState.setFlowData,
    flowId: flowState.flowId,
    setFlowId: flowState.setFlowId,
    hasUnsavedChanges: flowState.hasUnsavedChanges,
    setHasUnsavedChanges: flowState.setHasUnsavedChanges,
    lastSavedFlowData: flowState.lastSavedFlowData,
    setLastSavedFlowData: flowState.setLastSavedFlowData,
    flowStatus: flowState.flowStatus,
    setFlowStatus: flowState.setFlowStatus,
    isLoading: uiState.isLoading,
    setIsLoading: uiState.setIsLoading,
    isSaving: uiState.isSaving,
    setIsSaving: uiState.setIsSaving,
    isPublishing: uiState.isPublishing,
    setIsPublishing: uiState.setIsPublishing,
    isDeleting: uiState.isDeleting,
    setIsDeleting: uiState.setIsDeleting,
    openValidationModal: uiState.openValidationModal,
    addToFlowHistory: flowState.addToFlowHistory,
    setConfiguredApis: apiIntegration.setConfiguredApis,
    setShowTemplateSelector: uiState.setShowTemplateSelector,
    setActiveTab: uiState.setActiveTab,
    onComplete
  });

  // Load flow on mount if flowId exists
  useEffect(() => {
    if (flowState.flowId) {
      flowActions.loadFlow();
    }
  }, [flowState.flowId]);

  // Enhanced template selection handler
  const handleTemplateSelect = (template: IFlowTemplate): void => {
    flowOperations.handleTemplateSelect(template);
    uiState.setShowTemplateSelector(false);
    uiState.setActiveTab('design');

    // Template selected successfully
  };

  // Enhanced flow import handler
  const handleFlowImport = (importedFlowData: {
    name: string;
    description: string;
    nodes: IEnhancedFlowNode[];
    connections: IFlowConnection[];
    variables: IFlowVariable[];
    integrations: IFlowIntegration[]
  }): void => {
    try {
      const { processedNodes, processedConnections, extractedApiConfigs } = processImportedFlowData(importedFlowData);
      
      // Add extracted API configurations to the existing ones
      if (extractedApiConfigs.length > 0) {
        apiIntegration.setConfiguredApis(prev => {
          // Check for duplicates by URL and method to avoid adding the same API twice
          const existingApiKeys = new Set(prev.map(api => `${api.method}:${api.url}`));
          const newApis = extractedApiConfigs.filter(api =>
            !existingApiKeys.has(`${api.method}:${api.url}`)
          );
          return [...prev, ...newApis];
        });
      }
      
      // Create new flow data with imported content
      const newFlowData = {
        name: importedFlowData.name,
        description: importedFlowData.description,
        nodes: processedNodes,
        connections: processedConnections,
        variables: importedFlowData.variables || [],
        integrations: importedFlowData.integrations || [],
        isActive: false
      };
      
      // Update the flow data and add to history
      flowState.setFlowData(newFlowData);
      flowState.addToFlowHistory(newFlowData);
      flowState.setHasUnsavedChanges(true);
      
      // Clear selected node and switch to design tab
      flowState.setSelectedNode(null);
      uiState.setActiveTab('design');
      
      const apiMessage = extractedApiConfigs.length > 0
        ? ` and ${extractedApiConfigs.length} API integration${extractedApiConfigs.length > 1 ? 's' : ''}`
        : '';
      
      success('Success', `Flow "${importedFlowData.name}" imported successfully with ${processedNodes.length} nodes, ${processedConnections.length} connections${apiMessage}.`);
    } catch (err) {
      logger.error('Flow import processing error:', err);
      uiState.openValidationModal([{
        type: 'error',
        message: 'Failed to process imported flow data'
      }]);
    }
  };

  // Connection handlers
  const handleStartConnection = (nodeId: string, handle?: string): void => {
    canvasState.startConnection(nodeId, handle);
  };

  const handleEndConnection = (targetNodeId: string): void => {
    canvasState.endConnection(targetNodeId, flowOperations.createConnection);
  };

  const handleConnectionDelete = (connectionId: string): void => {
    flowOperations.deleteConnection(connectionId);
  };

  // Validation handler
  const handleValidateFlow = () => {
    return validateFlow(flowState.flowData);
  };

  // Flow data update handler
  const handleFlowDataUpdate = (updates: { nodes?: IEnhancedFlowNode[]; connections?: IFlowConnection[]; variables?: IFlowVariable[] }) => {
    if (updates.nodes) flowState.setFlowData(prev => ({ ...prev, nodes: updates.nodes! }));
    if (updates.connections) flowState.setFlowData(prev => ({ ...prev, connections: updates.connections! }));
    if (updates.variables) flowState.setFlowData(prev => ({ ...prev, variables: updates.variables! }));
  };

  // Show loading indicator
  if (uiState.isLoading) {
    return <FlowLoadingIndicator message="Loading conversation flow..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <FlowHeader
        organizationId={organizationId}
        flowId={flowState.flowId}
        flowName={flowState.flowName}
        flowStatus={flowState.flowStatus}
        hasUnsavedChanges={flowState.hasUnsavedChanges}
      />

      {/* Action Buttons */}
      <FlowActionButtons
        flowId={flowState.flowId}
        organizationId={organizationId}
        flowName={flowState.flowName}
        flowStatus={flowState.flowStatus}
        hasUnsavedChanges={flowState.hasUnsavedChanges}
        canUndo={flowState.canUndoAction}
        canRedo={flowState.canRedoAction}
        isLoading={uiState.isLoading}
        isSaving={uiState.isSaving}
        isPublishing={uiState.isPublishing}
        isDeleting={uiState.isDeleting}
        onUndo={flowState.undo}
        onRedo={flowState.redo}
        onCancel={onCancel}
        onSaveFlow={flowActions.handleSaveFlow}
        onSaveAndContinue={flowActions.handleSaveAndContinue}
        onPublishFlow={flowActions.handlePublishFlow}
        onPublishAndActivate={flowActions.handlePublishAndActivate}
        onDeleteFlow={flowActions.handleDeleteFlow}
      />

      {/* Tab Navigation */}
      <FlowTabNavigation
        activeTab={uiState.activeTab}
        showTemplateSelector={uiState.showTemplateSelector}
        onTabChange={uiState.setActiveTab}
      />

      {/* Main Content */}
      <FlowContentWrapper
        activeTab={uiState.activeTab}
        flowData={flowState.flowData}
        flowId={flowState.flowId}
        organizationId={organizationId}
        selectedNode={flowState.selectedNode}
        canvasScale={canvasState.canvasScale}
        canvasOffset={canvasState.canvasOffset}
        isConnecting={canvasState.isConnecting}
        connectionSource={canvasState.connectionSource}
        configuredApis={apiIntegration.configuredApis}
        editingApi={apiIntegration.editingApi}
        apiFormData={apiIntegration.apiFormData}
        apiTestResult={apiIntegration.apiTestResult}
        canUndo={flowState.canUndoAction}
        canRedo={flowState.canRedoAction}
        onFlowDataChange={(updates) => flowState.setFlowData(prev => ({ ...prev, ...updates }))}
        onTemplateSelect={handleTemplateSelect}
        onNodeSelect={flowState.setSelectedNode}
        onNodeUpdate={flowOperations.updateNode}
        onNodeDelete={flowOperations.deleteNode}
        onMultiNodeDelete={flowOperations.deleteMultipleNodes}
        onAddNode={flowOperations.addNode}
        onUndo={flowState.undo}
        onRedo={flowState.redo}
        onConnectionCreate={flowOperations.createConnection}
        onConnectionDelete={handleConnectionDelete}
        onStartConnection={handleStartConnection}
        onEndConnection={handleEndConnection}
        onCancelConnection={canvasState.cancelConnection}
        onScaleChange={canvasState.setCanvasScale}
        onOffsetChange={canvasState.setCanvasOffset}
        onFlowImport={handleFlowImport}
        onConfiguredApisChange={apiIntegration.setConfiguredApis}
        onEditingApiChange={apiIntegration.setEditingApi}
        onApiFormDataChange={apiIntegration.setApiFormData}
        onApiTestResultChange={apiIntegration.setApiTestResult}
        onAddApiIntegration={apiIntegration.addApiIntegration}
        onEditApiIntegration={apiIntegration.editApiIntegration}
        onDeleteApiIntegration={apiIntegration.deleteApiIntegration}
        onToggleApiActive={apiIntegration.toggleApiActive}
        onTestApiConnection={apiIntegration.testApiConnection}
        onValidateFlow={handleValidateFlow}
        onToggleFlowStatus={() => {}} // Placeholder - implement if needed
        onShowTemplateSelectorChange={uiState.setShowTemplateSelector}
        onActiveTabChange={uiState.setActiveTab}
      />

      {/* Validation Error Modal */}
      <FlowValidationModal
        open={uiState.showValidationModal}
        onClose={uiState.closeValidationModal}
        errors={uiState.validationErrors}
        flowData={flowState.flowData}
        onFlowDataUpdate={handleFlowDataUpdate}
        onRevalidate={handleValidateFlow}
      />
    </div>
  );
};

export default ConversationFlowBuilder;

import React from 'react';
import { Breadcrumb } from '@/components/ui/Breadcrumb';

interface FlowHeaderProps {
  organizationId: string;
  flowId: string | null;
  flowName: string;
  flowStatus: 'draft' | 'published' | 'archived';
  hasUnsavedChanges: boolean;
}

export const FlowHeader: React.FC<FlowHeaderProps> = ({
  organizationId,
  flowId,
  flowName,
  flowStatus,
  hasUnsavedChanges
}) => {
  // Breadcrumb navigation
  const breadcrumbItems = [
    { label: 'Dashboard', href: `/dashboard/organization/${organizationId}` },
    { label: 'Live Chat', href: `/dashboard/organization/${organizationId}/live-chat` },
    { label: 'Chatbots', href: `/dashboard/organization/${organizationId}/live-chat?tab=bots` },
    { label: flowId ? 'Edit Flow' : 'Create Flow', current: true }
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={breadcrumbItems} />
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {flowId ? 'Edit Conversation Flow' : 'Create Conversation Flow'}
            </h1>
            
            {/* Flow Status Badge */}
            {flowId && (
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                flowStatus === 'draft'
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                  : flowStatus === 'published'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400'
              }`}>
                {flowStatus.charAt(0).toUpperCase() + flowStatus.slice(1)}
              </span>
            )}
            
            {/* Unsaved Changes Indicator */}
            {hasUnsavedChanges && (
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400">
                Unsaved Changes
              </span>
            )}
          </div>
          
          <p className="text-gray-600 dark:text-gray-400">
            Design intelligent conversation flows with visual drag-and-drop interface
          </p>
        </div>
      </div>
    </div>
  );
};

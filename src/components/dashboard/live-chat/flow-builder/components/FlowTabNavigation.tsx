import React from 'react';

interface FlowTabNavigationProps {
  activeTab: 'design' | 'templates' | 'api' | 'variables' | 'settings';
  showTemplateSelector: boolean;
  onTabChange: (tab: 'design' | 'templates' | 'api' | 'variables' | 'settings') => void;
}

export const FlowTabNavigation: React.FC<FlowTabNavigationProps> = ({
  activeTab,
  showTemplateSelector,
  onTabChange
}) => {
  const tabs = [
    { id: 'templates', label: 'Templates', icon: '📋', hidden: !showTemplateSelector },
    { id: 'design', label: 'Visual Designer', icon: '🎨' },
    { id: 'api', label: 'API Integration', icon: '🔗' },
    { id: 'variables', label: 'Variables', icon: '📊' },
    { id: 'settings', label: 'Settings', icon: '⚙️' }
  ].filter(tab => !tab.hidden);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700">
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id as 'design' | 'templates' | 'api' | 'variables' | 'settings')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === tab.id
                ? 'border-indigo-500 text-indigo-600 dark:text-indigo-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            <span className="mr-2">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  );
};

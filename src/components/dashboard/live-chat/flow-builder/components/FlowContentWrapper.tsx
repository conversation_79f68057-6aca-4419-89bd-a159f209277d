import React from 'react';
import { TabContent } from '../core/TabContent';
import {IEnhancedFlowNode, IApiConfig, BotResponseType, IFlowTemplate, ClientFlowData, ValidationError} from '@/types/bot';

interface FlowContentWrapperProps {
  // Tab state
  activeTab: 'design' | 'templates' | 'api' | 'variables' | 'settings';
  
  // Flow data
  flowData: ClientFlowData;
  flowId: string | null;
  organizationId: string;
  selectedNode: IEnhancedFlowNode | null;
  
  // Canvas state
  canvasScale: number;
  canvasOffset: { x: number; y: number };
  isConnecting: boolean;
  connectionSource: string | null;
  
  // API state
  configuredApis: IApiConfig[];
  editingApi: IApiConfig | null;
  apiFormData: Partial<IApiConfig>;
  apiTestResult: { success: boolean; message: string } | null;
  
  // History state
  canUndo: boolean;
  canRedo: boolean;
  
  // Event handlers
  onFlowDataChange: (updates: Partial<ClientFlowData>) => void;
  onNodeSelect: (node: IEnhancedFlowNode | null) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<IEnhancedFlowNode>) => void;
  onNodeDelete: (nodeId: string) => void;
  onMultiNodeDelete: (nodeIds: string[]) => void;
  onAddNode: (type: BotResponseType, position?: { x: number; y: number }, apiConfig?: IApiConfig) => void;
  onUndo: () => void;
  onRedo: () => void;
  onConnectionCreate: (sourceId: string, targetId: string) => void;
  onTemplateSelect: (template: IFlowTemplate) => void;
  onConnectionDelete: (template: string) => void;
  onStartConnection: (nodeId: string) => void;
  onEndConnection: (targetNodeId: string) => void;
  onCancelConnection: () => void;
  onScaleChange: (scale: number) => void;
  onOffsetChange: (offset: { x: number; y: number }) => void;
  onFlowImport: (importedFlowData: any) => void;
  onConfiguredApisChange: (apis: IApiConfig[] | ((prev: IApiConfig[]) => IApiConfig[])) => void;
  onEditingApiChange: (api: IApiConfig | null) => void;
  onApiFormDataChange: (data: Partial<IApiConfig> | ((prev: Partial<IApiConfig>) => Partial<IApiConfig>)) => void;
  onApiTestResultChange: (result: { success: boolean; message: string } | null) => void;
  onAddApiIntegration: () => void;
  onEditApiIntegration: (api: IApiConfig) => void;
  onDeleteApiIntegration: (apiId: string) => void;
  onToggleApiActive: (apiId: string) => void;
  onTestApiConnection: () => Promise<void>;
  onValidateFlow: () => { isValid: boolean; errors: ValidationError[] };
  onToggleFlowStatus: any;
  onShowTemplateSelectorChange: (show: boolean) => void;
  onActiveTabChange: (tab: 'design' | 'templates' | 'api' | 'variables' | 'settings') => void;
}

export const FlowContentWrapper: React.FC<FlowContentWrapperProps> = (props) => {
  return (
    <TabContent
      activeTab={props.activeTab}
      flowData={props.flowData}
      flowId={props.flowId}
      organizationId={props.organizationId}
      selectedNode={props.selectedNode}
      canvasScale={props.canvasScale}
      canvasOffset={props.canvasOffset}
      isConnecting={props.isConnecting}
      connectionSource={props.connectionSource}
      configuredApis={props.configuredApis}
      editingApi={props.editingApi}
      apiFormData={props.apiFormData}
      apiTestResult={props.apiTestResult}
      onFlowDataChange={props.onFlowDataChange}
      onTemplateSelect={props.onTemplateSelect}
      onNodeSelect={props.onNodeSelect}
      onNodeUpdate={props.onNodeUpdate}
      onNodeDelete={props.onNodeDelete}
      onMultiNodeDelete={props.onMultiNodeDelete}
      onAddNode={props.onAddNode}
      onUndo={props.onUndo}
      onRedo={props.onRedo}
      canUndo={props.canUndo}
      canRedo={props.canRedo}
      onConnectionCreate={props.onConnectionCreate}
      onConnectionDelete={props.onConnectionDelete}
      onStartConnection={props.onStartConnection}
      onEndConnection={props.onEndConnection}
      onCancelConnection={props.onCancelConnection}
      onScaleChange={props.onScaleChange}
      onOffsetChange={props.onOffsetChange}
      onFlowImport={props.onFlowImport}
      onConfiguredApisChange={props.onConfiguredApisChange}
      onEditingApiChange={props.onEditingApiChange}
      onApiFormDataChange={props.onApiFormDataChange}
      onApiTestResultChange={props.onApiTestResultChange}
      onAddApiIntegration={props.onAddApiIntegration}
      onEditApiIntegration={props.onEditApiIntegration}
      onDeleteApiIntegration={props.onDeleteApiIntegration}
      onToggleApiActive={props.onToggleApiActive}
      onTestApiConnection={props.onTestApiConnection}
      onValidateFlow={props.onValidateFlow}
      onToggleFlowStatus={props.onToggleFlowStatus}
      onShowTemplateSelectorChange={props.onShowTemplateSelectorChange}
      onActiveTabChange={props.onActiveTabChange}
    />
  );
};

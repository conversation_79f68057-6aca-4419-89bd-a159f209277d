import React from 'react';
import { Button } from '@/components/ui/Button';
import { HelpButton } from '@/components/ui/HelpButton';
import { BotTestManager } from '../../components/BotTestManager';

interface FlowActionButtonsProps {
  // Flow state
  flowId: string | null;
  organizationId: string;
  flowName: string;
  flowStatus: 'draft' | 'published' | 'archived';
  hasUnsavedChanges: boolean;
  
  // History state
  canUndo: boolean;
  canRedo: boolean;
  
  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  isPublishing: boolean;
  isDeleting: boolean;
  
  // Actions
  onUndo: () => void;
  onRedo: () => void;
  onCancel: () => void;
  onSaveFlow: () => void;
  onSaveAndContinue: () => void;
  onPublishFlow: () => void;
  onPublishAndActivate: () => void;
  onDeleteFlow: () => void;
}

export const FlowActionButtons: React.FC<FlowActionButtonsProps> = ({
  flowId,
  organizationId,
  flowName,
  flowStatus,
  hasUnsavedChanges,
  canUndo,
  canRedo,
  isLoading,
  isSaving,
  isPublishing,
  isDeleting,
  onUndo,
  onRedo,
  onCancel,
  onSaveFlow,
  onSaveAndContinue,
  onPublishFlow,
  onPublishAndActivate,
  onDeleteFlow
}) => {
  // Help documentation items
  const helpItems = [
    {
      title: 'Visual Flow Builder',
      content: 'Drag and drop nodes to create conversation flows with visual connections.',
      link: '/docs/live-chat-bots/visual-flow-builder'
    },
    {
      title: 'Start & End Nodes',
      content: 'Start nodes are entry points where conversations begin. End nodes are terminal points where conversations conclude.',
      link: '/docs/live-chat-bots/node-types'
    },
    {
      title: 'Flow Templates',
      content: 'Use pre-built templates for common business scenarios like customer support and lead qualification.',
      link: '/docs/live-chat-bots/templates'
    },
    {
      title: 'API Integration',
      content: 'Connect external APIs to your flows with secure credential management and data transformation.',
      link: '/docs/live-chat-bots/api-integration'
    },
    {
      title: 'Conditional Logic',
      content: 'Create decision branches with conditional logic to handle different conversation paths.',
      link: '/docs/live-chat-bots/conditional-logic'
    }
  ];

  return (
    <div className="w-full">
      {/* Mobile Layout - Stacked vertically */}
      <div className="flex flex-col space-y-3 sm:hidden">
        {/* Primary Actions Row */}
        <div className="flex flex-col space-y-2">
          <Button
            onClick={onSaveAndContinue}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
          >
            {isLoading ? 'Saving...' : 'Save and Continue'}
          </Button>
          
          <Button
            onClick={onSaveFlow}
            disabled={isSaving}
            variant="outline"
            className="w-full border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
          >
            {isSaving ? 'Saving...' : 'Save Flow'}
          </Button>
        </div>

        {/* Publish Actions Row (only for saved flows) */}
        {flowId && flowStatus === 'draft' && (
          <div className="flex flex-col space-y-2">
            <Button
              onClick={onPublishAndActivate}
              disabled={isLoading || isPublishing || hasUnsavedChanges}
              className="w-full bg-gradient-to-r from-[#22c55e] to-[#16a34a] hover:from-[#16a34a] hover:to-[#15803d] text-white border-0"
            >
              {isPublishing ? 'Publishing...' : '🚀 Publish & Activate'}
            </Button>
            
            <Button
              onClick={onPublishFlow}
              disabled={isLoading || isPublishing || hasUnsavedChanges}
              variant="outline"
              className="w-full border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20"
            >
              {isPublishing ? 'Publishing...' : '📤 Publish'}
            </Button>
          </div>
        )}

        {/* Secondary Actions Row */}
        <div className="flex flex-wrap gap-2">
          <div className="flex flex-1 space-x-2">
            <Button
              variant="outline"
              onClick={onUndo}
              disabled={!canUndo}
              size="sm"
              className="flex-1"
            >
              ↶ Undo
            </Button>
            <Button
              variant="outline"
              onClick={onRedo}
              disabled={!canRedo}
              size="sm"
              className="flex-1"
            >
              ↷ Redo
            </Button>
          </div>
          
          {/* Bot Test Manager */}
          {flowId && (
            <div className="w-full mt-2">
              <BotTestManager
                flowId={flowId}
                organizationId={organizationId}
                hasUnsavedChanges={hasUnsavedChanges}
                flowName={flowName}
              />
            </div>
          )}
        </div>

        {/* Utility Actions Row */}
        <div className="flex flex-wrap gap-2">
          <HelpButton items={helpItems} variant="text" className="flex-1" />
          
          {flowId && (
            <Button
              onClick={onDeleteFlow}
              disabled={isLoading || isDeleting}
              variant="outline"
              size="sm"
              className="flex-1 border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
            >
              {isDeleting ? 'Deleting...' : '🗑️ Delete'}
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={onCancel}
            size="sm"
            className="flex-1"
          >
            Cancel
          </Button>
        </div>
      </div>

      {/* Tablet Layout - Responsive grid */}
      <div className="hidden sm:flex lg:hidden flex-col space-y-3">
        {/* Primary Actions Row */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={onSaveAndContinue}
            disabled={isLoading}
            className="flex-1 min-w-[140px] bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
          >
            {isLoading ? 'Saving...' : 'Save and Continue'}
          </Button>
          
          <Button
            onClick={onSaveFlow}
            disabled={isSaving}
            variant="outline"
            className="flex-1 min-w-[120px] border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
          >
            {isSaving ? 'Saving...' : 'Save Flow'}
          </Button>
        </div>

        {/* Secondary Actions Row */}
        <div className="flex flex-wrap gap-2">
          {/* Publish Buttons (only for saved flows) */}
          {flowId && flowStatus === 'draft' && (
            <>
              <Button
                onClick={onPublishAndActivate}
                disabled={isLoading || isPublishing || hasUnsavedChanges}
                className="flex-1 min-w-[160px] bg-gradient-to-r from-[#22c55e] to-[#16a34a] hover:from-[#16a34a] hover:to-[#15803d] text-white border-0"
              >
                {isPublishing ? 'Publishing...' : '🚀 Publish & Activate'}
              </Button>
              
              <Button
                onClick={onPublishFlow}
                disabled={isLoading || isPublishing || hasUnsavedChanges}
                variant="outline"
                className="flex-1 min-w-[100px] border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20"
              >
                {isPublishing ? 'Publishing...' : '📤 Publish'}
              </Button>
            </>
          )}
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={onUndo}
              disabled={!canUndo}
              size="sm"
            >
              ↶ Undo
            </Button>
            <Button
              variant="outline"
              onClick={onRedo}
              disabled={!canRedo}
              size="sm"
            >
              ↷ Redo
            </Button>
          </div>
        </div>

        {/* Utility Actions Row */}
        <div className="flex flex-wrap gap-2 items-center">
          {flowId && (
            <BotTestManager
              flowId={flowId}
              organizationId={organizationId}
              hasUnsavedChanges={hasUnsavedChanges}
              flowName={flowName}
            />
          )}
          
          <HelpButton items={helpItems} variant="text" />
          
          {flowId && (
            <Button
              onClick={onDeleteFlow}
              disabled={isLoading || isDeleting}
              variant="outline"
              size="sm"
              className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
            >
              {isDeleting ? 'Deleting...' : '🗑️ Delete'}
            </Button>
          )}
          
          <Button variant="outline" onClick={onCancel} size="sm">
            Cancel
          </Button>
        </div>
      </div>

      {/* Desktop Layout - Horizontal */}
      <div className="hidden lg:flex items-center justify-end">
        <div className="flex items-center space-x-3">
          {/* Undo/Redo Buttons */}
          <Button
            variant="outline"
            onClick={onUndo}
            disabled={!canUndo}
            size="sm"
          >
            ↶ Undo
          </Button>
          <Button
            variant="outline"
            onClick={onRedo}
            disabled={!canRedo}
            size="sm"
          >
            ↷ Redo
          </Button>
          
          {/* Bot Test Manager */}
          {flowId && (
            <BotTestManager
              flowId={flowId}
              organizationId={organizationId}
              hasUnsavedChanges={hasUnsavedChanges}
              flowName={flowName}
            />
          )}
          
          {/* Help Button */}
          <HelpButton items={helpItems} variant="text" />
          
          {/* Delete Button (only for existing flows) */}
          {flowId && (
            <Button
              onClick={onDeleteFlow}
              disabled={isLoading || isDeleting}
              variant="outline"
              className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
            >
              {isDeleting ? 'Deleting...' : '🗑️ Delete'}
            </Button>
          )}
          
          {/* Cancel Button */}
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          
          {/* Save Button */}
          <Button
            onClick={onSaveFlow}
            disabled={isSaving}
            variant="outline"
            className="border-green-300 text-green-700 hover:bg-green-50 dark:border-green-600 dark:text-green-400 dark:hover:bg-green-900/20"
          >
            {isSaving ? 'Saving...' : 'Save Flow'}
          </Button>
          
          {/* Publish Buttons (only for saved flows) */}
          {flowId && flowStatus === 'draft' && (
            <>
              <Button
                onClick={onPublishFlow}
                disabled={isLoading || isPublishing || hasUnsavedChanges}
                variant="outline"
                className="border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-400 dark:hover:bg-blue-900/20"
              >
                {isPublishing ? 'Publishing...' : '📤 Publish'}
              </Button>
              
              <Button
                onClick={onPublishAndActivate}
                disabled={isLoading || isPublishing || hasUnsavedChanges}
                className="bg-gradient-to-r from-[#22c55e] to-[#16a34a] hover:from-[#16a34a] hover:to-[#15803d] text-white border-0"
              >
                {isPublishing ? 'Publishing...' : '🚀 Publish & Activate'}
              </Button>
            </>
          )}
          
          {/* Save and Continue Button */}
          <Button
            onClick={onSaveAndContinue}
            disabled={isLoading}
            className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
          >
            {isLoading ? 'Saving...' : 'Save and Continue'}
          </Button>
        </div>
      </div>
    </div>
  );
};

import React from 'react';

interface FlowLoadingIndicatorProps {
  message?: string;
}

export const FlowLoadingIndicator: React.FC<FlowLoadingIndicatorProps> = ({
  message = 'Loading...'
}) => {
  return (
    <div className="flex items-center justify-center h-64">
      <div className="flex flex-col items-center space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        <p className="text-gray-600 dark:text-gray-400">{message}</p>
      </div>
    </div>
  );
};

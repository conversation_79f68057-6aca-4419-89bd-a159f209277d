import React from 'react';
import { ValidationErrorModal } from '../../components/ValidationErrorModal';
import { IEnhancedFlowNode, IFlowConnection, IFlowVariable, ValidationError, ClientFlowData } from '@/types/bot';

interface FlowValidationModalProps {
  open: boolean;
  onClose: () => void;
  errors: ValidationError[];
  flowData: ClientFlowData;
  onFlowDataUpdate: (updates: { nodes?: IEnhancedFlowNode[]; connections?: IFlowConnection[]; variables?: IFlowVariable[] }) => void;
  onRevalidate: () => { isValid: boolean; errors: ValidationError[] };
}

export const FlowValidationModal: React.FC<FlowValidationModalProps> = ({
  open,
  onClose,
  errors,
  flowData,
  onFlowDataUpdate,
  onRevalidate
}) => {
  const handleFixErrors = () => {
    onClose();
  };

  const handleFlowDataUpdate = (updates: { nodes?: IEnhancedFlowNode[]; connections?: IFlowConnection[]; variables?: IFlowVariable[] }) => {
    onFlowDataUpdate(updates);
  };

  return (
    <ValidationErrorModal
      open={open}
      onClose={onClose}
      errors={errors}
      onFixErrors={handleFixErrors}
      flowData={flowData}
      onFlowDataUpdate={handleFlowDataUpdate}
      onRevalidate={onRevalidate}
    />
  );
};

'use client';

import React from 'react';
import {TemplateSelector} from '../../components/TemplateSelector';
import {VisualDesigner} from '../../components/VisualDesigner';
import {ApiIntegrationManager} from '../../components/ApiIntegrationManager';
import {ApiDocumentation} from '../../components/ApiDocumentation';
import {VariablesManager} from '../../components/VariablesManager';
import {FlowSettings} from '../../components/FlowSettings';
import {
  IFlowVariable,
  IFlowTemplate as AIFlowTemplate,
  IEnhancedFlowNode,
  IFlowConnection,
  IApiConfig,
  BotResponseType,
  IFlowIntegration,
  ClientFlowData,
  ValidationError
} from '@/types/bot';

type TabType = 'templates' | 'design' | 'api' | 'variables' | 'settings';

interface TabContentProps {
  activeTab: TabType;

  // Flow data
  flowData: ClientFlowData;
  flowId?: string | null;
  organizationId: string;
  selectedNode: IEnhancedFlowNode | null;

  // Canvas state
  canvasScale: number;
  canvasOffset: { x: number; y: number };
  isConnecting: boolean;
  connectionSource: string | null;

  // API state
  configuredApis: IApiConfig[];
  editingApi: IApiConfig | null;
  apiFormData: Partial<IApiConfig>;
  apiTestResult: { success: boolean; message: string } | null;

  // Event handlers
  onFlowDataChange: (updates: Partial<ClientFlowData>) => void;
  onTemplateSelect: (template: AIFlowTemplate) => void;
  onNodeSelect: (node: IEnhancedFlowNode) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<IEnhancedFlowNode>) => void;
  onNodeDelete: (nodeId: string) => void;
  onMultiNodeDelete?: (nodeIds: string[]) => void;
  onAddNode: (type: BotResponseType, position?: { x: number; y: number }, apiConfig?: IApiConfig) => void;
  onConnectionCreate: (sourceNodeId: string, targetNodeId: string) => void;
  onConnectionDelete: (connectionId: string) => void;
  onStartConnection: (nodeId: string) => void;
  onEndConnection: (targetNodeId: string) => void;
  onCancelConnection: () => void;
  onScaleChange: (scale: number) => void;
  onOffsetChange: (offset: { x: number; y: number }) => void;
  onFlowImport?: (flowData: { name: string; description: string; nodes: IEnhancedFlowNode[]; connections: IFlowConnection[]; variables: IFlowVariable[]; integrations: IFlowIntegration[] }) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;

  // API handlers
  onConfiguredApisChange: (apis: IApiConfig[]) => void;
  onEditingApiChange: (api: IApiConfig | null) => void;
  onApiFormDataChange: (data: Partial<IApiConfig>) => void;
  onApiTestResultChange: (result: { success: boolean; message: string } | null) => void;
  onAddApiIntegration: () => void;
  onEditApiIntegration: (api: IApiConfig) => void;
  onDeleteApiIntegration: (apiId: string) => void;
  onToggleApiActive: (apiId: string) => void;
  onTestApiConnection: () => Promise<void>;

  // Settings handlers
  onValidateFlow: () => {
    isValid: boolean;
    errors: ValidationError[]
  };
  onToggleFlowStatus: (organizationId: string, flowId: string, isActive: boolean) => Promise<void>;

  // Template handlers
  onShowTemplateSelectorChange: (show: boolean) => void;
  onActiveTabChange: (tab: TabType) => void;
}

export const TabContent: React.FC<TabContentProps> = ({
                                                        activeTab,
                                                        flowData,
                                                        flowId,
                                                        organizationId,
                                                        selectedNode,
                                                        canvasScale,
                                                        canvasOffset,
                                                        isConnecting,
                                                        connectionSource,
                                                        configuredApis,
                                                        editingApi,
                                                        apiFormData,
                                                        apiTestResult,
                                                        onFlowDataChange,
                                                        onTemplateSelect,
                                                        onNodeSelect,
                                                        onNodeUpdate,
                                                        onNodeDelete,
                                                        onMultiNodeDelete,
                                                        onAddNode,
                                                        onConnectionCreate,
                                                        onConnectionDelete,
                                                        onStartConnection,
                                                        onEndConnection,
                                                        onCancelConnection,
                                                        onScaleChange,
                                                        onOffsetChange,
                                                        onFlowImport,
                                                        onUndo,
                                                        onRedo,
                                                        canUndo,
                                                        canRedo,
                                                        onConfiguredApisChange,
                                                        onEditingApiChange,
                                                        onApiFormDataChange,
                                                        onApiTestResultChange,
                                                        onAddApiIntegration,
                                                        onEditApiIntegration,
                                                        onDeleteApiIntegration,
                                                        onToggleApiActive,
                                                        onTestApiConnection,
                                                        onValidateFlow,
                                                        onToggleFlowStatus,
                                                        onShowTemplateSelectorChange,
                                                        onActiveTabChange
                                                      }) => {
  const renderTabContent = () => {
    switch (activeTab) {
      case 'templates':
        return (
          <TemplateSelector
            onTemplateSelect={onTemplateSelect}
            onShowTemplateSelectorChange={onShowTemplateSelectorChange}
            onActiveTabChange={onActiveTabChange}
            onFlowDataChange={onFlowDataChange}
          />
        );

      case 'design':
        return (
          <VisualDesigner
            flowData={flowData}
            selectedNode={selectedNode}
            canvasScale={canvasScale}
            canvasOffset={canvasOffset}
            isConnecting={isConnecting}
            connectionSource={connectionSource}
            configuredApis={configuredApis}
            onNodeSelect={onNodeSelect}
            onNodeUpdate={onNodeUpdate}
            onNodeDelete={onNodeDelete}
            onMultiNodeDelete={onMultiNodeDelete}
            onAddNode={onAddNode}
            onConnectionCreate={onConnectionCreate}
            onConnectionDelete={onConnectionDelete}
            onStartConnection={onStartConnection}
            onEndConnection={onEndConnection}
            onCancelConnection={onCancelConnection}
            onScaleChange={onScaleChange}
            onOffsetChange={onOffsetChange}
            onFlowImport={onFlowImport}
            onUndo={onUndo}
            onRedo={onRedo}
            canUndo={canUndo}
            canRedo={canRedo}
          />
        );

      case 'api':
        return (
          <div className="space-y-6">
            <ApiIntegrationManager
              configuredApis={configuredApis}
              editingApi={editingApi}
              apiFormData={apiFormData}
              apiTestResult={apiTestResult}
              flowVariables={flowData.variables}
              flowNodes={flowData.nodes}
              onConfiguredApisChange={onConfiguredApisChange}
              onEditingApiChange={onEditingApiChange}
              onApiFormDataChange={onApiFormDataChange}
              onApiTestResultChange={onApiTestResultChange}
              onAddApiIntegration={onAddApiIntegration}
              onEditApiIntegration={onEditApiIntegration}
              onDeleteApiIntegration={onDeleteApiIntegration}
              onToggleApiActive={onToggleApiActive}
              onTestApiConnection={onTestApiConnection}
            />
            <ApiDocumentation/>
          </div>
        );

      case 'variables':
        return (
          <VariablesManager
            variables={flowData.variables}
            onVariablesChange={(variables) => onFlowDataChange({variables})}
          />
        );

      case 'settings':
        return (
          <FlowSettings
            flowData={flowData}
            flowId={flowId}
            organizationId={organizationId}
             onFlowDataChange={onFlowDataChange}
            onValidateFlow={onValidateFlow}
            onToggleFlowStatus={onToggleFlowStatus}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-96">
      {renderTabContent()}
    </div>
  );
};

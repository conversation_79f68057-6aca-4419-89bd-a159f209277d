import { IEnhancedFlowNode, IFlowConnection, IFlowVariable, IFlowIntegration, BotResponseType, IApiConfig, IFlowTemplate, ClientFlowData } from '@/types/bot';
import { 
  improveNodePositioning, 
  extractApiConfigsFromNodes, 
  processApiCallNodeContent,
  syncApiConfigWithNodeContent 
} from '../utils';

interface UseFlowOperationsProps {
  flowData: ClientFlowData;
  setFlowData: (data: ClientFlowData | ((prev: ClientFlowData) => ClientFlowData)) => void;
  selectedNode: IEnhancedFlowNode | null;
  setSelectedNode: (node: IEnhancedFlowNode | null) => void;
  addToFlowHistory: (data: ClientFlowData) => void;
  configuredApis: IApiConfig[];
  setConfiguredApis: (apis: IApiConfig[] | ((prev: IApiConfig[]) => IApiConfig[])) => void;
}

export interface UseFlowOperationsReturn {
  // Node operations
  addNode: (type: BotResponseType, position?: { x: number; y: number }, apiConfig?: IApiConfig) => void;
  updateNode: (nodeId: string, updates: Partial<IEnhancedFlowNode>) => void;
  deleteNode: (nodeId: string) => void;
  deleteMultipleNodes: (nodeIds: string[]) => void;
  
  // Connection operations
  createConnection: (sourceNodeId: string, targetNodeId: string) => void;
  deleteConnection: (connectionId: string) => void;
  
  // Template operations
  handleTemplateSelect: (template: IFlowTemplate) => void;
  
  // Flow import operations
  handleFlowImport: (importedFlowData: ClientFlowData) => void;
}

export const useFlowOperations = ({
  flowData,
  setFlowData,
  selectedNode,
  setSelectedNode,
  addToFlowHistory,
  configuredApis,
  setConfiguredApis
}: UseFlowOperationsProps): UseFlowOperationsReturn => {

  const addNode = (type: BotResponseType, position?: { x: number; y: number }, apiConfig?: IApiConfig): void => {
    // Ensure node name doesn't exceed 20 characters
    const getNodeName = () => {
      if (apiConfig) {
        return apiConfig.name.length <= 20 ? apiConfig.name : apiConfig.name.substring(0, 20);
      }
      
      const typeNames: Record<BotResponseType, string> = {
        [BotResponseType.TEXT]: 'Text',
        [BotResponseType.BUTTONS]: 'Buttons',
        [BotResponseType.QUICK_REPLY]: 'Quick Reply',
        [BotResponseType.FORM]: 'Form',
        [BotResponseType.HANDOFF]: 'Handoff',
        [BotResponseType.CONDITIONAL]: 'Conditional',
        [BotResponseType.API_CALL]: 'API Call'
      };
      
      return typeNames[type] || 'Node';
    };
    
    const newNode: IEnhancedFlowNode = {
      id: crypto.randomUUID(),
      name: getNodeName(),
      type,
      content: {
        type,
        text: type === BotResponseType.TEXT ? 'Enter your message here...' : undefined,
        delay: 1000,
        // If this is an API node with configuration, add it to content
        ...(type === BotResponseType.API_CALL && apiConfig ? {
          url: apiConfig.url,
          method: apiConfig.method,
          headers: apiConfig.headers,
          body: typeof apiConfig.body === 'string' ? apiConfig.body : JSON.stringify(apiConfig.body || ''),
          timeout: apiConfig.timeout,
          retries: apiConfig.retries,
          // Extract all API call fields from requestSchema if available (from template)
          responseVariable: (apiConfig.requestSchema as any)?.responseVariable || '',
          arrayFieldName: (apiConfig.requestSchema as any)?.arrayFieldName || (apiConfig as any).arrayFieldName || 'data',
          maxDisplayItems: (apiConfig.requestSchema as any)?.maxDisplayItems || (apiConfig as any).maxDisplayItems || 5,
          displayFields: (apiConfig.requestSchema as any)?.displayFields || (apiConfig as any).displayFields || ['name', 'description']
        } : {})
      },
      position: position || {
        x: 200 + flowData.nodes.length * 50,
        y: 200 + flowData.nodes.length * 50
      },
      isStartNode: false,
      isEndNode: false,
      validationErrors: []
    };
    
    const newFlowData = {
      ...flowData,
      nodes: [...flowData.nodes, newNode]
    };
    
    setFlowData(newFlowData);
    setSelectedNode(newNode);
    addToFlowHistory(newFlowData);
  };

  const updateNode = (nodeId: string, updates: Partial<IEnhancedFlowNode>): void => {
    const updatedNode = flowData.nodes.find(node => node.id === nodeId);
    if (!updatedNode) return;
    
    let updatedNodes = flowData.nodes;
    
    // Handle single start node validation
    if (updates.isStartNode === true) {
      // Unset any existing start nodes
      updatedNodes = updatedNodes.map(node =>
        node.id !== nodeId ? {...node, isStartNode: false} : node
      );
    }
    
    const newNode = {...updatedNode, ...updates};
    
    // Handle API configuration synchronization - now using node.content
    if (newNode.type === BotResponseType.API_CALL && newNode.content) {
      syncApiConfigWithNodeContent(newNode.content, configuredApis, setConfiguredApis);
    }
    
    const newFlowData = {
      ...flowData,
      nodes: updatedNodes.map(node =>
        node.id === nodeId ? newNode : node
      )
    };
    
    setFlowData(newFlowData);
    
    // Update selectedNode if it's the node being updated
    if (selectedNode && selectedNode.id === nodeId) {
      setSelectedNode(newNode);
    }
    
    addToFlowHistory(newFlowData);
  };

  const deleteNode = (nodeId: string): void => {
    const newFlowData = {
      ...flowData,
      nodes: flowData.nodes.filter(node => node.id !== nodeId),
      connections: flowData.connections.filter(conn =>
        conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
      )
    };
    
    setFlowData(newFlowData);
    setSelectedNode(null);
    addToFlowHistory(newFlowData);
  };

  const deleteMultipleNodes = (nodeIds: string[]): void => {
    const newFlowData = {
      ...flowData,
      nodes: flowData.nodes.filter(node => !nodeIds.includes(node.id)),
      connections: flowData.connections.filter(conn =>
        !nodeIds.includes(conn.sourceNodeId) && !nodeIds.includes(conn.targetNodeId)
      )
    };

    setFlowData(newFlowData);
    setSelectedNode(null);
    addToFlowHistory(newFlowData);
  };

  // Helper function to generate meaningful connection labels
  const generateConnectionLabel = (sourceNode: IEnhancedFlowNode, targetNode: IEnhancedFlowNode): string | undefined => {
    const sourceContent = sourceNode.content as any;
    const targetContent = targetNode.content as any;

    switch (sourceNode.type) {
      case BotResponseType.CONDITIONAL:
        // For conditional nodes, check if this is a true/false branch
        if (sourceContent.conditions && sourceContent.conditions.length > 0) {
          const condition = sourceContent.conditions[0];
          return `${condition.field} ${condition.type} "${condition.value}"`;
        } else if (sourceContent.conditionVariable && sourceContent.conditionValue) {
          return `${sourceContent.conditionVariable} ${sourceContent.conditionOperator || 'equals'} "${sourceContent.conditionValue}"`;
        } else if (sourceContent.condition) {
          return sourceContent.condition;
        }
        return 'condition met';

      case BotResponseType.BUTTONS:
        // For button nodes, we could show which button was selected
        // This would need to be enhanced with specific button tracking
        return 'button selected';

      case BotResponseType.QUICK_REPLY:
        return 'quick reply';

      case BotResponseType.FORM:
        return 'form submitted';

      case BotResponseType.API_CALL:
        if (sourceContent.responseVariable) {
          return `API → ${sourceContent.responseVariable}`;
        }
        return 'API response';

      case BotResponseType.TEXT:
        // For text nodes, show a simple flow indicator
        return undefined; // No label for simple text flows

      case BotResponseType.HANDOFF:
        return 'handoff complete';

      default:
        return undefined;
    }
  };

  const createConnection = (sourceNodeId: string, targetNodeId: string, sourceHandle?: string): void => {
    // Prevent self-connections and duplicate connections
    if (sourceNodeId === targetNodeId) return;

    const existingConnection = flowData.connections.find(
      conn => conn.sourceNodeId === sourceNodeId && conn.targetNodeId === targetNodeId
    );
    if (existingConnection) return;

    // Find source and target nodes to generate meaningful labels
    const sourceNode = flowData.nodes.find(n => n.id === sourceNodeId);
    const targetNode = flowData.nodes.find(n => n.id === targetNodeId);

    // Generate label based on source handle for conditional nodes
    let connectionLabel: string | undefined;
    if (sourceNode && targetNode) {
      if (sourceNode.type === BotResponseType.CONDITIONAL && sourceHandle) {
        connectionLabel = sourceHandle === 'true' ? 'Is true' : 'Is false';
      } else {
        connectionLabel = generateConnectionLabel(sourceNode, targetNode);
      }
    }

    const newConnection: IFlowConnection = {
      id: crypto.randomUUID(),
      sourceNodeId,
      targetNodeId,
      sourceHandle,
      label: connectionLabel
    };

    const newFlowData = {
      ...flowData,
      connections: [...flowData.connections, newConnection]
    };

    setFlowData(newFlowData);
    addToFlowHistory(newFlowData);
  };

  const deleteConnection = (connectionId: string): void => {
    const newConnections = flowData.connections.filter(c => c.id !== connectionId);
    const newFlowData = {...flowData, connections: newConnections};
    setFlowData(newFlowData);
    addToFlowHistory(newFlowData);
  };

  const handleTemplateSelect = (template: IFlowTemplate): void => {
    // Process nodes and extract API configurations
    const processedNodes = improveNodePositioning(template.nodes).map((node: any) => ({
      ...node,
      validationErrors: [],
      // Ensure all nodes have proper UUID-based IDs
      id: node.id || crypto.randomUUID(),
      content: processApiCallNodeContent(node)
    }));

    // Extract API configurations from template API_CALL nodes
    const extractedApiConfigs = extractApiConfigsFromNodes(processedNodes, true);

    // Add extracted API configurations to the existing ones
    if (extractedApiConfigs.length > 0) {
      setConfiguredApis(prev => {
        // Check for duplicates by URL and method to avoid adding the same API twice
        const existingApiKeys = new Set(prev.map(api => `${api.method}:${api.url}`));
        const newApis = extractedApiConfigs.filter(api =>
          !existingApiKeys.has(`${api.method}:${api.url}`)
        );

        return [...prev, ...newApis];
      });
    }

    const newFlowData = {
      name: template.name,
      description: template.description,
      integrations: template.integrations,
      nodes: processedNodes,
      connections: template.connections || [],
      variables: template.variables,
      isActive: false
    };

    setFlowData(newFlowData);
    addToFlowHistory(newFlowData);

    // Auto-select the first API call node to show populated fields
    const firstApiNode = processedNodes.find(node => node.type === BotResponseType.API_CALL);
    if (firstApiNode) {
      setSelectedNode(firstApiNode);
    }
  };

  const handleFlowImport = (importedFlowData: ClientFlowData): void => {
    // This would use the processImportedFlowData utility
    // Implementation would be similar to handleTemplateSelect but for imported data
    // For brevity, keeping this as a placeholder
  };

  return {
    // Node operations
    addNode,
    updateNode,
    deleteNode,
    deleteMultipleNodes,

    // Connection operations
    createConnection,
    deleteConnection,

    // Template operations
    handleTemplateSelect,

    // Flow import operations
    handleFlowImport
  };
};

import {
  createConversation<PERSON>low,
  getConversationFlow,
  updateConversationFlow,
  publishAndActivateFlow,
  publishFlowOnly,
  deleteConversationFlow
} from '@/server/actions/bot-flow-actions';
import { useToast } from '@/components/ui/Toast';
import { confirmDialog } from '@/components/ui/ConfirmDialog';
import {prepareSave, validateFlow, extractApiConfigsFromNodes} from '../utils';
import { IApiConfig, ValidationError, ClientFlowData } from '@/types/bot';

interface UseFlowActionsProps {
  organizationId: string;
  flowData: ClientFlowData;
  setFlowData: (data: ClientFlowData | ((prev: ClientFlowData) => ClientFlowData)) => void;
  flowId: string | null;
  setFlowId: (id: string | null) => void;
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  lastSavedFlowData: ClientFlowData | null;
  setLastSavedFlowData: (data: ClientFlowData | null) => void;
  flowStatus: 'draft' | 'published' | 'archived';
  setFlowStatus: (status: 'draft' | 'published' | 'archived') => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  isSaving: boolean;
  setIsSaving: (saving: boolean) => void;
  isPublishing: boolean;
  setIsPublishing: (publishing: boolean) => void;
  isDeleting: boolean;
  setIsDeleting: (deleting: boolean) => void;
  openValidationModal: (errors: ValidationError[]) => void;
  addToFlowHistory: (data: ClientFlowData) => void;
  setConfiguredApis: (apis: IApiConfig[] | ((prev: IApiConfig[]) => IApiConfig[])) => void;
  setShowTemplateSelector: (show: boolean) => void;
  setActiveTab: (tab: 'design' | 'templates' | 'api' | 'variables' | 'settings') => void;
  onComplete: () => void;
}

export interface UseFlowActionsReturn {
  loadFlow: () => Promise<void>;
  handleSaveFlow: () => Promise<void>;
  handleSaveAndContinue: () => Promise<void>;
  handlePublishFlow: () => Promise<void>;
  handlePublishAndActivate: () => Promise<void>;
  handleDeleteFlow: () => void;
}

export const useFlowActions = ({
  organizationId,
  flowData,
  setFlowData,
  flowId,
  setFlowId,
  hasUnsavedChanges,
  setHasUnsavedChanges,
  lastSavedFlowData,
  setLastSavedFlowData,
  flowStatus,
  setFlowStatus,
  isLoading,
  setIsLoading,
  isSaving,
  setIsSaving,
  isPublishing,
  setIsPublishing,
  isDeleting,
  setIsDeleting,
  openValidationModal,
  addToFlowHistory,
  setConfiguredApis,
  setShowTemplateSelector,
  setActiveTab,
  onComplete
}: UseFlowActionsProps): UseFlowActionsReturn => {
  
  const { success, error } = useToast();

  const loadFlow = async (): Promise<void> => {
    if (!flowId) return;
    
    setIsLoading(true);
    try {
      const result = await getConversationFlow(organizationId, flowId);
      if (result.success && 'data' in result) {
        // Process nodes and extract API configurations (similar to template loading)
        const processedNodes = result.data.nodes.map((node: any) => ({
          ...node,
          validationErrors: []
        }));
        
        // Extract API configurations from loaded API_CALL nodes
        const extractedApiConfigs = extractApiConfigsFromNodes(processedNodes, false);
        
        // Add extracted API configurations to the configured APIs
        if (extractedApiConfigs.length > 0) {
          setConfiguredApis(prev => {
            // Check for duplicates by URL and method to avoid adding the same API twice
            const existingApiKeys = new Set(prev.map(api => `${api.method}:${api.url}`));
            const newApis = extractedApiConfigs.filter(api =>
              !existingApiKeys.has(`${api.method}:${api.url}`)
            );
            
            return [...prev, ...newApis];
          });
        }
        
        const flowData: ClientFlowData = {
          name: result.data.name,
          integrations: result.data.integrations,
          description: result.data.description || '',
          nodes: processedNodes,
          connections: (result.data.connections || []).map((conn: any) => ({
            ...conn,
            id: conn.id || crypto.randomUUID() // Ensure all connections have IDs
          })),
          variables: result.data.variables,
          isActive: result.data.isActive
        };
        setFlowData(flowData);
        setLastSavedFlowData(flowData);
        
        // Set the flow status from the loaded data
        const loadedStatus = result.data.status || 'draft';
        setFlowStatus(loadedStatus);
        
        addToFlowHistory(flowData);
        setShowTemplateSelector(false);
        setActiveTab('design');
      } else {
        const errorMessage = 'error' in result ? result.error : 'Failed to load conversation flow';
        error('Error', errorMessage);
      }
    } catch (err) {
      error('Error', 'Failed to load conversation flow');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveFlow = async (): Promise<void> => {
    if (!flowData.name.trim()) {
      error('Validation Error', 'Flow name is required');
      return;
    }
    
    // Create payload for save operation
    const payload = {
      name: flowData.name,
      description: flowData.description,
      nodes: flowData.nodes,
      connections: flowData.connections,
      variables: flowData.variables
    };
    
    setIsSaving(true);
    
    try {
      const result = flowId
        ? await updateConversationFlow(organizationId, flowId, payload)
        : await createConversationFlow(organizationId, payload);
      
      if (!flowId && result?.data?.id) {
        setFlowId(result.data.id);
      }
      
      if (result.success) {
        setLastSavedFlowData(flowData);
        setHasUnsavedChanges(false);
        success('Success', 'Flow saved successfully!');
      } else {
        // Check if the error includes validation errors
        if ('validationErrors' in result && result.validationErrors && Array.isArray(result.validationErrors)) {
          openValidationModal(result.validationErrors);
        } else {
          const errorMessage = 'error' in result ? result.error : 'Failed to save conversation flow';
          error('Error', errorMessage);
        }
      }
    } catch (err) {
      error('Error', 'Failed to save conversation flow');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAndContinue = async (): Promise<void> => {
    const { isValid, payload } = prepareSave(flowData, validateFlow);
    if (!isValid) {
      error('Validation Error', 'Please fix validation errors before saving');
      return;
    }

    setIsLoading(true);

    try {
      const result = flowId
        ? await updateConversationFlow(organizationId, flowId, payload!)
        : await createConversationFlow(organizationId, payload!);

      if (result.success) {
        setLastSavedFlowData(flowData);
        setHasUnsavedChanges(false);
        success('Success', 'Flow saved successfully!');
        onComplete(); // Redirect to previous page
      } else {
        // Check if the error includes validation errors
        if ('validationErrors' in result && result.validationErrors && Array.isArray(result.validationErrors)) {
          openValidationModal(result.validationErrors);
        } else {
          const errorMessage = 'error' in result ? result.error : 'Failed to save conversation flow';
          error('Error', errorMessage);
        }
      }
    } catch (err) {
      error('Error', 'Failed to save conversation flow');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePublishFlow = async (): Promise<void> => {
    if (!flowId) {
      error('Error', 'Flow must be saved before publishing');
      return;
    }

    if (hasUnsavedChanges) {
      error('Error', 'Please save your changes before publishing');
      return;
    }

    setIsPublishing(true);

    try {
      const result = await publishFlowOnly(organizationId, flowId);

      if (result.success) {
        setFlowStatus('published');
        success('Success', 'Flow published successfully!');
      } else {
        // Check if the error includes validation errors
        if ('validationErrors' in result && result.validationErrors && Array.isArray(result.validationErrors)) {
          openValidationModal(result.validationErrors);
        } else {
          const errorMessage = 'error' in result ? result.error : 'Failed to publish flow';
          error('Error', errorMessage);
        }
      }
    } catch (err) {
      error('Error', 'Failed to publish flow');
    } finally {
      setIsPublishing(false);
    }
  };

  const handlePublishAndActivate = async (): Promise<void> => {
    if (!flowId) {
      error('Error', 'Flow must be saved before publishing');
      return;
    }

    if (hasUnsavedChanges) {
      error('Error', 'Please save your changes before publishing');
      return;
    }

    setIsPublishing(true);

    try {
      const result = await publishAndActivateFlow(organizationId, flowId);

      if (result.success) {
        setFlowStatus('published');
        success('Success', 'Flow published and activated successfully!');
      } else {
        // Check if the error includes validation errors
        if ('validationErrors' in result && result.validationErrors && Array.isArray(result.validationErrors)) {
          openValidationModal(result.validationErrors);
        } else {
          const errorMessage = 'error' in result ? result.error : 'Failed to publish and activate flow';
          error('Error', errorMessage);
        }
      }
    } catch (err) {
      error('Error', 'Failed to publish and activate flow');
    } finally {
      setIsPublishing(false);
    }
  };

  const handleDeleteFlow = (): void => {
    if (!flowId) {
      error('Error', 'Cannot delete unsaved flow');
      return;
    }

    // Show custom confirmation dialog
    confirmDialog({
      message: 'Are you sure you want to delete this flow? This action cannot be undone.',
      header: 'Delete Flow',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      acceptClassName: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0',
      accept: async () => {
        // Proceed with deletion
        setIsDeleting(true);

        try {
          const result = await deleteConversationFlow(organizationId, flowId);

          if (result.success) {
            success('Success', 'Flow deleted successfully!');

            // Redirect to flow list after deletion
            setTimeout(() => {
              onComplete();
            }, 1000);
          } else {
            const errorMessage = 'error' in result ? result.error : 'Failed to delete flow';
            error('Error', errorMessage);
          }
        } catch (err) {
          error('Error', 'Failed to delete flow');
        } finally {
          setIsDeleting(false);
        }
      },
      reject: () => {
        // User cancelled - no action needed
      }
    });
  };

  return {
    loadFlow,
    handleSaveFlow,
    handleSaveAndContinue,
    handlePublishFlow,
    handlePublishAndActivate,
    handleDeleteFlow
  };
};

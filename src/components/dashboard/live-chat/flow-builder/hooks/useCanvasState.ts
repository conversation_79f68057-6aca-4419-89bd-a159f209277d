import { useState } from 'react';

export interface UseCanvasStateReturn {
  // Canvas state
  canvasScale: number;
  setCanvasScale: (scale: number) => void;
  canvasOffset: { x: number; y: number };
  setCanvasOffset: (offset: { x: number; y: number }) => void;
  
  // Connection state
  isConnecting: boolean;
  setIsConnecting: (connecting: boolean) => void;
  connectionSource: string | null;
  setConnectionSource: (source: string | null) => void;
  connectionSourceHandle: string | null;
  setConnectionSourceHandle: (handle: string | null) => void;

  // Connection actions
  startConnection: (nodeId: string, handle?: string) => void;
  endConnection: (targetNodeId: string, onCreateConnection: (sourceId: string, targetId: string, sourceHandle?: string) => void) => void;
  cancelConnection: () => void;
}

export const useCanvasState = (): UseCanvasStateReturn => {
  // Canvas state for visual flow builder
  const [canvasScale, setCanvasScale] = useState(1);
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 });
  
  // Connection state
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSource, setConnectionSource] = useState<string | null>(null);
  const [connectionSourceHandle, setConnectionSourceHandle] = useState<string | null>(null);

  const startConnection = (nodeId: string, handle?: string): void => {
    setIsConnecting(true);
    setConnectionSource(nodeId);
    setConnectionSourceHandle(handle || null);
  };

  const endConnection = (targetNodeId: string, onCreateConnection: (sourceId: string, targetId: string, sourceHandle?: string) => void): void => {
    if (connectionSource && connectionSource !== targetNodeId) {
      onCreateConnection(connectionSource, targetNodeId, connectionSourceHandle || undefined);
    }
    setIsConnecting(false);
    setConnectionSource(null);
    setConnectionSourceHandle(null);
  };

  const cancelConnection = (): void => {
    setIsConnecting(false);
    setConnectionSource(null);
    setConnectionSourceHandle(null);
  };

  return {
    // Canvas state
    canvasScale,
    setCanvasScale,
    canvasOffset,
    setCanvasOffset,

    // Connection state
    isConnecting,
    setIsConnecting,
    connectionSource,
    setConnectionSource,
    connectionSourceHandle,
    setConnectionSourceHandle,

    // Connection actions
    startConnection,
    endConnection,
    cancelConnection
  };
};

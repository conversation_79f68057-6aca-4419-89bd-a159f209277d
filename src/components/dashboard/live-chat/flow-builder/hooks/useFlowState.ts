import { useState, useEffect, useMemo } from 'react';
import { IEnhancedFlowNode, ClientFlowData } from '@/types/bot';
import { addToHistory, performUndo, performRedo, canUndo, canRedo } from '../utils';

export interface UseFlowStateReturn {
  // Flow data state
  flowData: ClientFlowData;
  setFlowData: (data: ClientFlowData | ((prev: ClientFlowData) => ClientFlowData)) => void;
  flowId: string | null;
  setFlowId: (id: string | null) => void;
  selectedNode: IEnhancedFlowNode | null;
  setSelectedNode: (node: IEnhancedFlowNode | null) => void;

  // History state
  flowHistory: ClientFlowData[];
  historyIndex: number;

  // Change tracking
  hasUnsavedChanges: boolean;
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  lastSavedFlowData: ClientFlowData | null;
  setLastSavedFlowData: (data: ClientFlowData | null) => void;

  // Flow status
  flowStatus: 'draft' | 'published' | 'archived';
  setFlowStatus: (status: 'draft' | 'published' | 'archived') => void;

  // Memoized values
  flowName: string;

  // History actions
  undo: () => void;
  redo: () => void;
  canUndoAction: boolean;
  canRedoAction: boolean;
  addToFlowHistory: (data: ClientFlowData) => void;

  // Flow data update helpers
  updateFlowData: (updates: Partial<ClientFlowData>) => void;
}

export const useFlowState = (flowIdProp?: string | null): UseFlowStateReturn => {
  const [flowData, setFlowData] = useState<ClientFlowData>({
    name: '',
    description: '',
    nodes: [],
    integrations: [],
    connections: [],
    variables: [],
    isActive: false
  });

  const [flowId, setFlowId] = useState(flowIdProp || null);
  const [selectedNode, setSelectedNode] = useState<IEnhancedFlowNode | null>(null);
  const [flowHistory, setFlowHistory] = useState<ClientFlowData[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSavedFlowData, setLastSavedFlowData] = useState<ClientFlowData | null>(null);
  const [flowStatus, setFlowStatus] = useState<'draft' | 'published' | 'archived'>('draft');

  // Track unsaved changes
  useEffect(() => {
    if (lastSavedFlowData) {
      const hasChanges = JSON.stringify(flowData) !== JSON.stringify(lastSavedFlowData);
      setHasUnsavedChanges(hasChanges);
    }
  }, [flowData, lastSavedFlowData]);

  // Memoize flowName to prevent infinite re-renders in BotTestManager
  const flowName = useMemo(() => flowData.name, [flowData.name]);

  // History management functions
  const addToFlowHistory = (data: ClientFlowData): void => {
    addToHistory(data, flowHistory, historyIndex, setFlowHistory, setHistoryIndex);
  };

  const undo = (): void => {
    performUndo(flowHistory, historyIndex, setHistoryIndex, setFlowData);
  };

  const redo = (): void => {
    performRedo(flowHistory, historyIndex, setHistoryIndex, setFlowData);
  };

  const canUndoAction = canUndo(historyIndex);
  const canRedoAction = canRedo(historyIndex, flowHistory);

  // Flow data update helper
  const updateFlowData = (updates: Partial<ClientFlowData>): void => {
    const newFlowData = { ...flowData, ...updates };
    setFlowData(newFlowData);
    addToFlowHistory(newFlowData);
  };

  return {
    // Flow data state
    flowData,
    setFlowData,
    flowId,
    setFlowId,
    selectedNode,
    setSelectedNode,
    
    // History state
    flowHistory,
    historyIndex,
    
    // Change tracking
    hasUnsavedChanges,
    setHasUnsavedChanges,
    lastSavedFlowData,
    setLastSavedFlowData,
    
    // Flow status
    flowStatus,
    setFlowStatus,
    
    // Memoized values
    flowName,
    
    // History actions
    undo,
    redo,
    canUndoAction,
    canRedoAction,
    addToFlowHistory,
    
    // Flow data update helpers
    updateFlowData
  };
};

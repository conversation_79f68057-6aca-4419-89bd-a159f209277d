import { useState } from 'react';
import { ValidationError } from '@/types/bot';

export interface UseUIStateReturn {
  // Tab state
  activeTab: 'design' | 'templates' | 'api' | 'variables' | 'settings';
  setActiveTab: (tab: 'design' | 'templates' | 'api' | 'variables' | 'settings') => void;
  showTemplateSelector: boolean;
  setShowTemplateSelector: (show: boolean) => void;
  
  // Loading states
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  isSaving: boolean;
  setIsSaving: (saving: boolean) => void;
  isPublishing: boolean;
  setIsPublishing: (publishing: boolean) => void;
  isDeleting: boolean;
  setIsDeleting: (deleting: boolean) => void;
  
  // Validation modal state
  showValidationModal: boolean;
  setShowValidationModal: (show: boolean) => void;
  validationErrors: ValidationError[];
  setValidationErrors: (errors: ValidationError[]) => void;
  
  // UI actions
  openValidationModal: (errors: ValidationError[]) => void;
  closeValidationModal: () => void;
}

export const useUIState = (flowId?: string | null): UseUIStateReturn => {
  const [activeTab, setActiveTab] = useState<'design' | 'templates' | 'api' | 'variables' | 'settings'>('templates');
  const [showTemplateSelector, setShowTemplateSelector] = useState(!flowId);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  const openValidationModal = (errors: ValidationError[]): void => {
    setValidationErrors(errors);
    setShowValidationModal(true);
  };

  const closeValidationModal = (): void => {
    setShowValidationModal(false);
    setValidationErrors([]);
  };

  return {
    // Tab state
    activeTab,
    setActiveTab,
    showTemplateSelector,
    setShowTemplateSelector,
    
    // Loading states
    isLoading,
    setIsLoading,
    isSaving,
    setIsSaving,
    isPublishing,
    setIsPublishing,
    isDeleting,
    setIsDeleting,
    
    // Validation modal state
    showValidationModal,
    setShowValidationModal,
    validationErrors,
    setValidationErrors,
    
    // UI actions
    openValidationModal,
    closeValidationModal
  };
};

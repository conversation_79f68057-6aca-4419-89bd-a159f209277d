import { useState } from 'react';
import { IApiConfig } from '@/types/bot';
import { testApiConnection as testApiConnectionAction } from '@/server/actions/live-chat-actions';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';
import { 
  validateApiFormData, 
  createApiConfigFromFormData 
} from '../utils';

export interface UseApiIntegrationReturn {
  // API state
  configuredApis: IApiConfig[];
  setConfiguredApis: (apis: IApiConfig[] | ((prev: IApiConfig[]) => IApiConfig[])) => void;
  editingApi: IApiConfig | null;
  setEditingApi: (api: IApiConfig | null) => void;
  apiFormData: Partial<IApiConfig>;
  setApiFormData: (data: Partial<IApiConfig> | ((prev: Partial<IApiConfig>) => Partial<IApiConfig>)) => void;
  apiTestResult: { success: boolean; message: string } | null;
  setApiTestResult: (result: { success: boolean; message: string } | null) => void;
  
  // API actions
  addApiIntegration: () => void;
  editApiIntegration: (api: IApiConfig) => void;
  deleteApiIntegration: (apiId: string) => void;
  toggleApiActive: (apiId: string) => void;
  testApiConnection: () => Promise<void>;
  resetApiForm: () => void;
}

export const useApiIntegration = (): UseApiIntegrationReturn => {
  const [configuredApis, setConfiguredApis] = useState<IApiConfig[]>([]);
  const [editingApi, setEditingApi] = useState<IApiConfig | null>(null);
  const [apiFormData, setApiFormData] = useState<Partial<IApiConfig>>({
    url: '',
    method: 'GET',
    headers: {},
    timeout: 30000,
    retries: 3
  });
  const [apiTestResult, setApiTestResult] = useState<{ success: boolean; message: string } | null>(null);
  
  const { success, error } = useToast();

  const resetApiForm = (): void => {
    setApiFormData({
      url: '',
      method: 'GET',
      headers: {},
      timeout: 30000,
      retries: 3
    });
    setEditingApi(null);
  };

  const addApiIntegration = (): void => {
    const validation = validateApiFormData(apiFormData, configuredApis, editingApi);
    
    if (!validation.isValid) {
      error('Validation Error', validation.error!);
      return;
    }

    const newApi = createApiConfigFromFormData(apiFormData, editingApi);

    if (editingApi) {
      setConfiguredApis(prev => prev.map(api => api.id === editingApi.id ? newApi : api));
    } else {
      setConfiguredApis(prev => [...prev, newApi]);
    }

    resetApiForm();
  };

  const editApiIntegration = (api: IApiConfig): void => {
    setEditingApi(api);
    setApiFormData({
      name: api.name,
      url: api.url,
      method: api.method,
      headers: api.headers,
      body: api.body,
      timeout: api.timeout,
      retries: api.retries
    });
  };

  const deleteApiIntegration = (apiId: string): void => {
    setConfiguredApis(prev => prev.filter(api => api.id !== apiId));
  };

  const toggleApiActive = (apiId: string): void => {
    setConfiguredApis(prev => prev.map(api =>
      api.id === apiId ? { ...api, isActive: !api.isActive } : api
    ));
  };

  const testApiConnection = async (): Promise<void> => {
    if (!apiFormData.url) {
      setApiTestResult({ success: false, message: 'URL is required for testing' });
      return;
    }

    try {
      setApiTestResult({ success: true, message: 'Testing connection...' });

      // Use server action for API testing
      const result = await testApiConnectionAction({
        url: apiFormData.url,
        method: apiFormData.method || 'GET',
        headers: apiFormData.headers || {},
      });

      if (result.success) {
        setApiTestResult({
          success: true,
          message: 'message' in result ? result.message : 'Connection successful!'
        });
      } else {
        setApiTestResult({
          success: false,
          message: result.error || 'Connection failed'
        });
      }
    } catch (error) {
      logger.error('Error testing API connection:', error);
      setApiTestResult({
        success: false,
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }
  };

  return {
    // API state
    configuredApis,
    setConfiguredApis,
    editingApi,
    setEditingApi,
    apiFormData,
    setApiFormData,
    apiTestResult,
    setApiTestResult,
    
    // API actions
    addApiIntegration,
    editApiIntegration,
    deleteApiIntegration,
    toggleApiActive,
    testApiConnection,
    resetApiForm
  };
};

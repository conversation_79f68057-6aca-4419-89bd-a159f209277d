import { ValidationError, ClientFlowData } from '@/types/bot';
import {
  validateVariableNames,
  validateVariableReferences,
  validateNodeContent
} from './validation';
import { detectCircularReferences } from './circular-reference-detection';

export const validateFlow = (flowData: ClientFlowData): { isValid: boolean; errors: ValidationError[] } => {
  const errors: ValidationError[] = [];
  
  // 1. Variable name validation
  const variableNameErrors = validateVariableNames(flowData.variables);
  errors.push(...variableNameErrors);
  
  // 2. Variable reference validation
  const variableReferenceErrors = validateVariableReferences(flowData.nodes, flowData.variables);
  errors.push(...variableReferenceErrors);
  
  // 3. Node content validation
  const nodeContentErrors = validateNodeContent(flowData.nodes);
  errors.push(...nodeContentErrors);
  
  // 4. Circular reference detection
  const circularReferenceErrors = detectCircularReferences(flowData.nodes, flowData.connections);
  errors.push(...circularReferenceErrors);
  
  // 5. Flow structure validation
  // Check for exactly one start node
  const startNodes = flowData.nodes.filter(node => node.isStartNode);
  if (startNodes.length === 0) {
    errors.push({
      type: 'error',
      message: 'Flow must have exactly one start node. Please mark one node as the start node.'
    });
  } else if (startNodes.length > 1) {
    errors.push({
      type: 'error',
      message: 'Flow can only have one start node. Multiple start nodes detected.',
      nodeId: startNodes[1].id // Highlight the second start node
    });
  }
  
  // Check for at least one end node
  const endNodes = flowData.nodes.filter(node => node.isEndNode);
  if (endNodes.length === 0) {
    errors.push({
      type: 'error',
      message: 'Flow must have at least one end node. Please mark at least one node as an end node.'
    });
  }
  
  // Check for orphaned nodes (nodes with no connections except start/end)
  const connectedNodeIds = new Set<string>();
  flowData.connections.forEach(conn => {
    connectedNodeIds.add(conn.sourceNodeId);
    connectedNodeIds.add(conn.targetNodeId);
  });
  
  flowData.nodes.forEach(node => {
    if (!node.isStartNode && !node.isEndNode && !connectedNodeIds.has(node.id)) {
      errors.push({
        type: 'warning',
        message: `Node "${node.name}" is not connected to any other nodes.`,
        nodeId: node.id
      });
    }
  });
  
  // Check for invalid connection references
  const nodeIds = new Set(flowData.nodes.map(node => node.id));
  flowData.connections.forEach(conn => {
    if (!nodeIds.has(conn.sourceNodeId)) {
      errors.push({
        type: 'error',
        message: `Connection references non-existent source node.`,
        connectionId: conn.id
      });
    }
    if (!nodeIds.has(conn.targetNodeId)) {
      errors.push({
        type: 'error',
        message: `Connection references non-existent target node.`,
        connectionId: conn.id
      });
    }
  });
  
  // 6. Check for unreachable nodes (enhanced to handle conversation loops)
  if (startNodes.length === 1) {
    const reachableNodes = new Set<string>();
    const queue = [startNodes[0].id];
    const visited = new Set<string>();

    // Enhanced traversal that handles cycles properly
    while (queue.length > 0) {
      const currentNodeId = queue.shift()!;
      if (visited.has(currentNodeId)) continue;

      visited.add(currentNodeId);
      reachableNodes.add(currentNodeId);

      // Find all nodes connected from this node
      const outgoingConnections = flowData.connections.filter(conn => conn.sourceNodeId === currentNodeId);
      outgoingConnections.forEach(conn => {
        if (!visited.has(conn.targetNodeId)) {
          queue.push(conn.targetNodeId);
        } else {
          // Even if visited, mark as reachable (handles cycles)
          reachableNodes.add(conn.targetNodeId);
        }
      });
    }

    // Also check for nodes reachable through reverse connections (conversation loops)
    const reverseReachableNodes = new Set(reachableNodes);
    let foundNewNodes = true;

    while (foundNewNodes) {
      foundNewNodes = false;
      flowData.connections.forEach(conn => {
        if (reverseReachableNodes.has(conn.targetNodeId) && !reverseReachableNodes.has(conn.sourceNodeId)) {
          reverseReachableNodes.add(conn.sourceNodeId);
          foundNewNodes = true;
        }
      });
    }

    flowData.nodes.forEach(node => {
      if (!reverseReachableNodes.has(node.id) && !node.isStartNode) {
        errors.push({
          type: 'warning',
          message: `Node "${node.name}" cannot be reached from the start node.`,
          nodeId: node.id
        });
      }
    });
  }
  
  return {
    isValid: errors.filter(e => e.type === 'error').length === 0,
    errors
  };
};

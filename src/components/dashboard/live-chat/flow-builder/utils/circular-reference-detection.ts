import { IEnhancedFlowNode, IFlowConnection, ValidationError } from '@/types/bot';

// Helper function to determine if a cycle represents a legitimate conversation loop
export const isValidConversationLoop = (
  cyclePath: string[],
  nodeMap: Map<string, IEnhancedFlowNode>,
  connections: IFlowConnection[]
): boolean => {
  const cycleNodes = cyclePath.map(id => nodeMap.get(id)).filter(Boolean) as IEnhancedFlowNode[];

  // Check for user interaction points in the cycle
  const hasUserInteraction = cycleNodes.some(node =>
    node.type === 'buttons' ||
    node.type === 'quick_reply' ||
    node.type === 'form' ||
    node.type === 'conditional'
  );

  // Check for multiple exit paths from nodes in the cycle
  const hasMultipleExitPaths = cycleNodes.some(node => {
    const outgoingConnections = connections.filter(conn => conn.sourceNodeId === node.id);
    return outgoingConnections.length > 1;
  });

  // Check if cycle includes the start node (common pattern for main menu loops)
  const includesStartNode = cycleNodes.some(node => node.isStartNode);

  // Allow loops that have user interaction AND (multiple exits OR include start node)
  return hasUserInteraction && (hasMultipleExitPaths || includesStartNode);
};

// Detect circular references in flow connections
export const detectCircularReferences = (nodes: IEnhancedFlowNode[], connections: IFlowConnection[]): ValidationError[] => {
  const errors: ValidationError[] = [];
  const nodeMap = new Map(nodes.map(node => [node.id, node]));
  
  // Build adjacency list
  const adjacencyList = new Map<string, string[]>();
  nodes.forEach(node => adjacencyList.set(node.id, []));
  
  connections.forEach(conn => {
    const sourceList = adjacencyList.get(conn.sourceNodeId) || [];
    sourceList.push(conn.targetNodeId);
    adjacencyList.set(conn.sourceNodeId, sourceList);
  });
  
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  function hasCycle(nodeId: string, path: string[]): boolean {
    if (recursionStack.has(nodeId)) {
      // Found a cycle - check if it's a legitimate conversation loop
      const cycleStart = path.indexOf(nodeId);
      const cyclePath = path.slice(cycleStart).concat(nodeId);
      const cycleNodeNames = cyclePath.map(id => nodeMap.get(id)?.name || id);

      // Check if this is a legitimate conversation loop
      const isLegitimateLoop = isValidConversationLoop(cyclePath, nodeMap, connections);

      if (!isLegitimateLoop) {
        errors.push({
          type: 'error',
          message: `Circular reference detected in flow: ${cycleNodeNames.join(' → ')}. This will cause infinite loops.`,
          nodeId: nodeId
        });
      } else {
        // Add as warning for awareness, but don't block saving
        errors.push({
          type: 'warning',
          message: `Conversation loop detected: ${cycleNodeNames.join(' → ')}. Ensure this provides value to users and has exit conditions.`,
          nodeId: nodeId
        });
      }
      return true;
    }
    
    if (visited.has(nodeId)) {
      return false;
    }
    
    visited.add(nodeId);
    recursionStack.add(nodeId);
    
    const neighbors = adjacencyList.get(nodeId) || [];
    for (const neighbor of neighbors) {
      if (hasCycle(neighbor, [...path, nodeId])) {
        return true;
      }
    }
    
    recursionStack.delete(nodeId);
    return false;
  }
  
  // Check for cycles starting from each unvisited node
  nodes.forEach(node => {
    if (!visited.has(node.id)) {
      hasCycle(node.id, []);
    }
  });
  
  return errors;
};

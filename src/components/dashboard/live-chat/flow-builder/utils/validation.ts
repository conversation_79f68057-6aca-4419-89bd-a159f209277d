import { IFlowVariable, IEnhancedFlowNode, BotResponseType, ValidationError } from '@/types/bot';

// Constants for validation (matching backend)
export const RESERVED_VARIABLE_NAMES = ['customer_name', 'customer_email'];
export const VARIABLE_NAME_REGEX = /^[a-z][a-z0-9_]*$/;
export const MAX_TEXT_LENGTH = 2000;
export const MAX_NODE_NAME_LENGTH = 100;

// Validate variable names according to naming conventions
export const validateVariableNames = (variables: IFlowVariable[]): ValidationError[] => {
  const errors: ValidationError[] = [];
  const seenNames = new Set<string>();
  
  variables.forEach((variable, index) => {
    const name = variable.name?.trim();
    
    // Check if name is empty
    if (!name) {
      errors.push({
        type: 'error',
        message: `Variable at position ${index + 1} has an empty name. Variable names are required.`,
        field: `variables[${index}].name`
      });
      return;
    }
    
    // Check for reserved names
    if (RESERVED_VARIABLE_NAMES.includes(name.toLowerCase())) {
      errors.push({
        type: 'error',
        message: `Variable name '${name}' is reserved for system use. Please choose a different name.`,
        field: `variables[${index}].name`
      });
      return;
    }
    
    // Check naming convention
    if (!VARIABLE_NAME_REGEX.test(name)) {
      if (!/^[a-z]/.test(name)) {
        errors.push({
          type: 'error',
          message: `Variable name '${name}' must start with a lowercase letter (a-z).`,
          field: `variables[${index}].name`
        });
      } else {
        errors.push({
          type: 'error',
          message: `Variable name '${name}' can only contain lowercase letters, numbers, and underscores.`,
          field: `variables[${index}].name`
        });
      }
      return;
    }
    
    // Check for duplicates
    if (seenNames.has(name)) {
      errors.push({
        type: 'error',
        message: `Duplicate variable name '${name}' found. Variable names must be unique.`,
        field: `variables[${index}].name`
      });
    } else {
      seenNames.add(name);
    }
  });
  
  return errors;
};

// Parse Handlebars template and extract variable references with context awareness
export const parseHandlebarsVariables = (text: string): {
  variables: string[];
  arrayContextVariables: { arrayName: string; variables: string[] }[];
  conditionalVariables: string[];
  helpers: string[];
} => {
  const variables: string[] = [];
  const arrayContextVariables: { arrayName: string; variables: string[] }[] = [];
  const conditionalVariables: string[] = [];
  const helpers: string[] = [];

  // Track current context
  let currentArrayContext: string | null = null;

  // Find all handlebars expressions
  const handlebarsMatches = text.match(/\{\{([^}]+)\}\}/g) || [];

  handlebarsMatches.forEach(match => {
    const content = match.slice(2, -2).trim();

    // Handle block helpers
    if (content.startsWith('#each ')) {
      const arrayName = content.substring(6).trim();
      currentArrayContext = arrayName;
      helpers.push(content);
      return;
    }

    if (content.startsWith('#if ')) {
      const conditionVar = content.substring(4).trim();

      // If we're inside an array context, treat the conditional variable as an array field
      if (currentArrayContext) {
        let arrayContext = arrayContextVariables.find(ctx => ctx.arrayName === currentArrayContext);
        if (!arrayContext) {
          arrayContext = { arrayName: currentArrayContext, variables: [] };
          arrayContextVariables.push(arrayContext);
        }
        if (!arrayContext.variables.includes(conditionVar)) {
          arrayContext.variables.push(conditionVar);
        }
      } else {
        // Only add to conditional variables if not in array context
        conditionalVariables.push(conditionVar);
      }

      helpers.push(content);
      return;
    }

    // Handle closing helpers
    if (content === '/each') {
      currentArrayContext = null;
      helpers.push(content);
      return;
    }

    if (content === '/if') {
      helpers.push(content);
      return;
    }

    // Handle regular variables
    if (!content.startsWith('#') && !content.startsWith('/')) {
      if (currentArrayContext) {
        // Variable inside array context
        let arrayContext = arrayContextVariables.find(ctx => ctx.arrayName === currentArrayContext);
        if (!arrayContext) {
          arrayContext = { arrayName: currentArrayContext, variables: [] };
          arrayContextVariables.push(arrayContext);
        }
        if (!arrayContext.variables.includes(content)) {
          arrayContext.variables.push(content);
        }
      } else {
        // Regular variable
        if (!variables.includes(content)) {
          variables.push(content);
        }
      }
    }
  });

  return { variables, arrayContextVariables, conditionalVariables, helpers };
};

// Get available variables including API response arrays and their fields
export const getAvailableVariablesWithContext = (variables: IFlowVariable[], nodes: IEnhancedFlowNode[]): {
  globalVariables: Set<string>;
  arrayVariables: Map<string, string[]>; // arrayName -> available fields
} => {
  const globalVariables = new Set([
    ...RESERVED_VARIABLE_NAMES,
    ...variables.map(v => v.name).filter(Boolean)
  ]);

  const arrayVariables = new Map<string, string[]>();

  // Extract array field information from API call nodes
  nodes.forEach(node => {
    if (node.type === 'api_call' && node.content) {
      const arrayFieldName = node.content.arrayFieldName;
      const displayFields = node.content.displayFields;

      if (arrayFieldName && displayFields && Array.isArray(displayFields)) {
        arrayVariables.set(arrayFieldName, displayFields);
        // Also add the array name as a global variable
        globalVariables.add(arrayFieldName);
      }
    }
  });

  // Add common array fields for known array types
  const commonArrayFields = {
    products: ['name', 'price', 'stock', 'rating', 'description'],
    categories: ['name', 'product_count', 'description', 'featured'],
    pricing_tiers: ['tier', 'price', 'discount', 'availability'],
    locations: ['store_name', 'stock_qty', 'distance', 'availability'],
    price_breakdown: ['item_name', 'base_price', 'discount_amt', 'final_price']
  };

  Object.entries(commonArrayFields).forEach(([arrayName, fields]) => {
    if (!arrayVariables.has(arrayName)) {
      arrayVariables.set(arrayName, fields);
    }
    globalVariables.add(arrayName);
  });

  return { globalVariables, arrayVariables };
};

// Validate variable references in node content with Handlebars awareness
export const validateVariableReferences = (nodes: IEnhancedFlowNode[], variables: IFlowVariable[]): ValidationError[] => {
  const errors: ValidationError[] = [];
  const { globalVariables, arrayVariables } = getAvailableVariablesWithContext(variables, nodes);

  nodes.forEach(node => {
    const textContent = node.content?.text || '';

    if (textContent) {
      const parsed = parseHandlebarsVariables(textContent);

      // Validate global variables
      parsed.variables.forEach(variableName => {
        if (!globalVariables.has(variableName)) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' references undefined variable '${variableName}'. This will display as {{${variableName}}} to customers.`,
            nodeId: node.id,
            field: 'content.text'
          });
        }
      });

      // Validate conditional variables
      parsed.conditionalVariables.forEach(variableName => {
        if (!globalVariables.has(variableName)) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' references undefined variable '${variableName}' in conditional statement. This will display as {{#if ${variableName}}} to customers.`,
            nodeId: node.id,
            field: 'content.text'
          });
        }
      });

      // Validate array context variables
      parsed.arrayContextVariables.forEach(({ arrayName, variables: arrayVars }) => {
        // Check if array exists
        if (!globalVariables.has(arrayName)) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' references undefined array '${arrayName}' in {{#each}} loop. This will display as {{#each ${arrayName}}} to customers.`,
            nodeId: node.id,
            field: 'content.text'
          });
          return;
        }

        // Check if array fields are valid
        const availableFields = arrayVariables.get(arrayName);
        if (availableFields) {
          arrayVars.forEach(fieldName => {
            if (!availableFields.includes(fieldName)) {
              errors.push({
                type: 'warning',
                message: `Node '${node.name}' references field '${fieldName}' in array '${arrayName}' that may not be available. Available fields: ${availableFields.join(', ')}.`,
                nodeId: node.id,
                field: 'content.text'
              });
            }
          });
        }
      });
    }

    // Check button text for variable references
    if (node.content?.options) {
      node.content.options.forEach((option, optionIndex) => {
        const buttonText = option.text || '';
        if (buttonText) {
          const parsed = parseHandlebarsVariables(buttonText);

          parsed.variables.forEach(variableName => {
            if (!globalVariables.has(variableName)) {
              errors.push({
                type: 'error',
                message: `Node '${node.name}' button ${optionIndex + 1} references undefined variable '${variableName}'.`,
                nodeId: node.id,
                field: `content.options[${optionIndex}].text`
              });
            }
          });
        }
      });
    }
  });

  return errors;
};

// Validate node content requirements
export const validateNodeContent = (nodes: IEnhancedFlowNode[]): ValidationError[] => {
  const errors: ValidationError[] = [];

  nodes.forEach(node => {
    // Validate node name
    if (!node.name?.trim()) {
      errors.push({
        type: 'error',
        message: `Node at position (${node.position?.x || 0}, ${node.position?.y || 0}) is missing a name.`,
        nodeId: node.id,
        field: 'name'
      });
    } else if (node.name.length > MAX_NODE_NAME_LENGTH) {
      errors.push({
        type: 'error',
        message: `Node '${node.name}' name exceeds maximum length of ${MAX_NODE_NAME_LENGTH} characters.`,
        nodeId: node.id,
        field: 'name'
      });
    }

    // Validate text content length
    if (node.content?.text && node.content.text.length > MAX_TEXT_LENGTH) {
      errors.push({
        type: 'error',
        message: `Node '${node.name}' text content exceeds maximum length of ${MAX_TEXT_LENGTH} characters.`,
        nodeId: node.id,
        field: 'content.text'
      });
    }

    // Validate conditional node conditions
    if (node.type === BotResponseType.CONDITIONAL && node.content?.conditions) {
      const validConditionTypes = ['equals', 'contains', 'starts_with', 'ends_with', 'regex', 'intent_match'];
      const validOperators = ['and', 'or'];

      node.content.conditions.forEach((condition: any, index: number) => {
        // Check required fields
        if (!condition.field) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' condition ${index + 1} is missing required field 'field'.`,
            nodeId: node.id,
            field: `content.conditions[${index}].field`
          });
        }

        if (!condition.type) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' condition ${index + 1} is missing required field 'type'.`,
            nodeId: node.id,
            field: `content.conditions[${index}].type`
          });
        } else if (!validConditionTypes.includes(condition.type)) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' condition ${index + 1} has invalid type '${condition.type}'. Valid types: ${validConditionTypes.join(', ')}.`,
            nodeId: node.id,
            field: `content.conditions[${index}].type`
          });
        }

        if (!condition.value) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' condition ${index + 1} is missing required field 'value'.`,
            nodeId: node.id,
            field: `content.conditions[${index}].value`
          });
        }

        // Check operator if provided
        if (condition.operator && !validOperators.includes(condition.operator)) {
          errors.push({
            type: 'error',
            message: `Node '${node.name}' condition ${index + 1} has invalid operator '${condition.operator}'. Valid operators: ${validOperators.join(', ')}.`,
            nodeId: node.id,
            field: `content.conditions[${index}].operator`
          });
        }
      });
    }
  });

  return errors;
};

import { IFlowNode, IEnhancedFlowNode, IFlowVariable, IFlowConnection, IFlowIntegration, BotResponseType, IApiConfig, ValidationError, ClientFlowData } from '@/types/bot';

// Improve node positioning with better spacing
export const improveNodePositioning = (nodes: IFlowNode[]): IFlowNode[] => {
  return nodes.map((node, index) => {
    // If node already has good positioning (within our spacing guidelines), keep it
    if (node.position && node.position.x >= 50 && node.position.y >= 50) {
      return node;
    }
    
    // Otherwise, apply improved positioning with 200px spacing
    const row = Math.floor(index / 3); // 3 nodes per row
    const col = index % 3;
    
    return {
      ...node,
      position: {
        x: 100 + (col * 250), // 250px horizontal spacing
        y: 100 + (row * 200)  // 200px vertical spacing
      }
    };
  });
};

// Common save validation and preparation
export const prepareSave = (flowData: ClientFlowData, validateFlow: (data: ClientFlowData) => { isValid: boolean; errors: ValidationError[] }):
  { isValid: boolean; payload?: ClientFlowData } => {
  if (!flowData.name.trim()) {
    return { isValid: false };
  }

  if (flowData.nodes.length === 0) {
    return { isValid: false };
  }

  // Validate flow structure
  const validationResult = validateFlow(flowData);

  if (!validationResult.isValid) {
    return { isValid: false };
  }

  const payload: ClientFlowData = {
    name: flowData.name,
    description: flowData.description,
    nodes: flowData.nodes,
    connections: flowData.connections,
    variables: flowData.variables,
    isActive: flowData.isActive,
    integrations: flowData.integrations,
  };

  return { isValid: true, payload };
};

// Process imported flow data to ensure proper structure
export const processImportedFlowData = (importedFlowData: {
  name: string;
  description: string;
  nodes: IEnhancedFlowNode[];
  connections: IFlowConnection[];
  variables: IFlowVariable[];
  integrations: IFlowIntegration[]
}): {
  processedNodes: IEnhancedFlowNode[];
  processedConnections: IFlowConnection[];
  extractedApiConfigs: IApiConfig[];
} => {
  // Process imported nodes to ensure they have proper structure and IDs
  const processedNodes = importedFlowData.nodes.map((node: any) => ({
    ...node,
    id: node.id || crypto.randomUUID(),
    validationErrors: [],
    // Ensure proper position
    position: node.position || {x: 200, y: 200},
    // Ensure proper content structure
    content: {
      ...node.content,
      type: node.type
    }
  }));
  
  // Process imported connections to ensure they have proper IDs
  const processedConnections = importedFlowData.connections.map((connection: any) => ({
    ...connection,
    id: connection.id || crypto.randomUUID()
  }));
  
  // Extract API configurations from imported API_CALL nodes
  const extractedApiConfigs: IApiConfig[] = [];
  processedNodes.forEach((node: any) => {
    if (node.type === BotResponseType.API_CALL && node.content) {
      // Extract API configuration from node content
      const apiConfig: IApiConfig & { isFromTemplate?: boolean; templateNodeId?: string } = {
        id: crypto.randomUUID(),
        name: node.name || `Imported API ${extractedApiConfigs.length + 1}`,
        url: node.content.url || '',
        method: node.content.method || 'GET',
        headers: node.content.headers || {},
        timeout: node.content.timeout || 30000,
        retries: node.content.retries || 3,
        isActive: true,
        isFromTemplate: true, // Mark as template API
        templateNodeId: node.id // Reference to original node
      };
      
      // Add request body if it exists
      if (node.content.body) {
        apiConfig.body = node.content.body;
      }
      
      // Store additional node-specific data in requestSchema for later use
      const nodeSpecificData: any = {};
      
      if (node.content.queryParams) {
        nodeSpecificData.queryParams = node.content.queryParams;
      }
      
      if (node.content.successConditions) {
        nodeSpecificData.successConditions = node.content.successConditions;
      }
      
      if (node.content.errorNextNodeId) {
        nodeSpecificData.errorNextNodeId = node.content.errorNextNodeId;
      }
      
      if (node.content.text) {
        nodeSpecificData.loadingText = node.content.text;
      }
      
      if (node.content.delay) {
        nodeSpecificData.delay = node.content.delay;
      }
      
      // Store node-specific data in requestSchema for preservation
      if (Object.keys(nodeSpecificData).length > 0) {
        apiConfig.requestSchema = nodeSpecificData;
      }
      
      extractedApiConfigs.push(apiConfig);
    }
  });
  
  return {
    processedNodes,
    processedConnections,
    extractedApiConfigs
  };
};

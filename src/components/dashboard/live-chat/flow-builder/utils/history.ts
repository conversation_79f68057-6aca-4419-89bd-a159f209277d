import { IEnhancedFlowNode, IFlowConnection, IFlowVariable, IFlowIntegration, ClientFlowData } from '@/types/bot';

// Add flow data to history for undo/redo functionality
export const addToHistory = (
  data: ClientFlowData,
  flowHistory: ClientFlowData[],
  historyIndex: number,
  setFlowHistory: (history: ClientFlowData[]) => void,
  setHistoryIndex: (index: number) => void
): void => {
  const newHistory = flowHistory.slice(0, historyIndex + 1);
  newHistory.push(JSON.parse(JSON.stringify(data)));
  setFlowHistory(newHistory);
  setHistoryIndex(newHistory.length - 1);
};

// Undo the last action
export const performUndo = (
  flowHistory: ClientFlowData[],
  historyIndex: number,
  setHistoryIndex: (index: number) => void,
  setFlowData: (data: ClientFlowData | ((prev: ClientFlowData) => ClientFlowData)) => void
): void => {
  if (historyIndex > 0) {
    setHistoryIndex(historyIndex - 1);
    setFlowData(JSON.parse(JSON.stringify(flowHistory[historyIndex - 1])));
  }
};

// Redo the last undone action
export const performRedo = (
  flowHistory: ClientFlowData[],
  historyIndex: number,
  setHistoryIndex: (index: number) => void,
  setFlowData: (data: ClientFlowData | ((prev: ClientFlowData) => ClientFlowData)) => void
): void => {
  if (historyIndex < flowHistory.length - 1) {
    setHistoryIndex(historyIndex + 1);
    setFlowData(JSON.parse(JSON.stringify(flowHistory[historyIndex + 1])));
  }
};

// Check if undo is available
export const canUndo = (historyIndex: number): boolean => {
  return historyIndex > 0;
};

// Check if redo is available
export const canRedo = (historyIndex: number, flowHistory: ClientFlowData[]): boolean => {
  return historyIndex < flowHistory.length - 1;
};

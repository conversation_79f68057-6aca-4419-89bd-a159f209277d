import { IApiConfig, BotResponseType, IEnhancedFlowNode } from '@/types/bot';
import { v4 } from 'uuid';

const MAX_API_NAME_LENGTH = 200;

// Validate API form data
export const validateApiFormData = (apiFormData: Partial<IApiConfig>, configuredApis: IApiConfig[], editingApi: IApiConfig | null): { isValid: boolean; error?: string } => {
  if (!apiFormData.name?.trim()) {
    return { isValid: false, error: 'API name is required' };
  }
  
  if (apiFormData.name.length > MAX_API_NAME_LENGTH) {
    return { isValid: false, error: 'API name must be 20 characters or less' };
  }
  
  // Validate special characters
  const nameRegex = /^[a-zA-Z0-9\s\-_]*$/;
  if (!nameRegex.test(apiFormData.name)) {
    return { isValid: false, error: 'API name can only contain letters, numbers, spaces, hyphens (-), and underscores (_)' };
  }
  
  if (!apiFormData.url?.trim()) {
    return { isValid: false, error: 'API URL is required' };
  }
  
  // Check for duplicate names
  const existingApi = configuredApis.find(api => api.name === apiFormData.name);
  if (existingApi && (!editingApi || editingApi.id !== existingApi.id)) {
    return { isValid: false, error: 'API name must be unique' };
  }
  
  return { isValid: true };
};

// Create API configuration from form data
export const createApiConfigFromFormData = (apiFormData: Partial<IApiConfig>, editingApi: IApiConfig | null): IApiConfig => {
  return {
    id: editingApi?.id || `api_${v4()}`,
    name: apiFormData.name!,
    url: apiFormData.url!,
    method: apiFormData.method || 'GET',
    headers: apiFormData.headers || {},
    timeout: apiFormData.timeout || 30000,
    retries: apiFormData.retries || 3,
    isActive: apiFormData.isActive || false
  };
};

// Extract API configurations from template/loaded API_CALL nodes
export const extractApiConfigsFromNodes = (nodes: IEnhancedFlowNode[], isFromTemplate: boolean = false): IApiConfig[] => {
  const extractedApiConfigs: IApiConfig[] = [];
  
  nodes.forEach((node: any) => {
    if (node.type === BotResponseType.API_CALL && node.content) {
      // Check if API configuration exists in node.content
      if (node.content.url || node.content.method) {
        // Extract API configuration from node content
        const apiConfig: IApiConfig & { isFromTemplate?: boolean; templateNodeId?: string } = {
          id: crypto.randomUUID(),
          name: node.name || `${isFromTemplate ? 'Template' : 'Loaded'} API ${extractedApiConfigs.length + 1}`,
          url: node.content.url || '',
          method: node.content.method || 'GET',
          headers: node.content.headers || {},
          timeout: node.content.timeout || 30000,
          retries: node.content.retries || 3,
          isActive: true,
          isFromTemplate: isFromTemplate,
          templateNodeId: node.id // Reference to original node
        };
        
        // Add request body if it exists
        if (node.content.body) {
          apiConfig.body = node.content.body;
        }
        
        // Store additional node-specific data in requestSchema for later use
        const nodeSpecificData: any = {};
        
        // Preserve legacy template fields for backward compatibility
        if (node.content.queryParams) {
          nodeSpecificData.queryParams = node.content.queryParams;
        }
        
        if (node.content.successConditions) {
          nodeSpecificData.successConditions = node.content.successConditions;
        }
        
        if (node.content.errorNextNodeId) {
          nodeSpecificData.errorNextNodeId = node.content.errorNextNodeId;
        }
        
        if (node.content.text) {
          nodeSpecificData.loadingText = node.content.text;
        }
        
        if (node.content.delay) {
          nodeSpecificData.delay = node.content.delay;
        }
        
        // Store the new API call fields in requestSchema for API management
        nodeSpecificData.responseVariable = node.content.responseVariable;
        nodeSpecificData.arrayFieldName = node.content.arrayFieldName;
        nodeSpecificData.maxDisplayItems = node.content.maxDisplayItems;
        nodeSpecificData.displayFields = node.content.displayFields;
        
        // Store node-specific data in requestSchema for preservation
        if (Object.keys(nodeSpecificData).length > 0) {
          apiConfig.requestSchema = nodeSpecificData;
        }
        
        extractedApiConfigs.push(apiConfig);
      }
    }
  });
  
  return extractedApiConfigs;
};

// Process node content for API call nodes to preserve all fields
export const processApiCallNodeContent = (node: any): any => {
  if (node.type !== BotResponseType.API_CALL || !node.content) {
    return node.content;
  }
  
  // Ensure all new API call node fields are preserved in the node content
  // These fields should remain in the node content for the ApiCallNodeEditor to display
  return {
    ...node.content,
    // Ensure all required API call fields are present with defaults
    type: BotResponseType.API_CALL,
    url: node.content.url || '',
    method: node.content.method || 'GET',
    timeout: node.content.timeout || 30000,
    retries: node.content.retries || 3,
    headers: node.content.headers || {},
    // Ensure body is always a string, not an object
    body: typeof node.content.body === 'string'
      ? node.content.body
      : (node.content.body ? JSON.stringify(node.content.body) : ''),
    responseVariable: node.content.responseVariable || '',
    // Preserve List Data Display Configuration fields from template
    arrayFieldName: node.content.arrayFieldName !== undefined ? node.content.arrayFieldName : '',
    displayFields: node.content.displayFields !== undefined ? node.content.displayFields : []
  };
};

// Synchronize API configuration with node content
export const syncApiConfigWithNodeContent = (
  apiContent: any,
  configuredApis: IApiConfig[],
  setConfiguredApis: (updater: (prev: IApiConfig[]) => IApiConfig[]) => void
): void => {
  // If the node has API configuration in content, sync it with configuredApis
  if (apiContent.url || apiContent.method) {
    // Find existing API configuration by matching URL and method
    const existingApiIndex = configuredApis.findIndex(api =>
      api.url === apiContent.url && api.method === apiContent.method
    );

    if (existingApiIndex !== -1) {
      // Update the existing API configuration in configuredApis
      setConfiguredApis(prev => {
        const updatedApis = [...prev];
        const existingApi = updatedApis[existingApiIndex];

        // Merge the updates while preserving important properties
        const updatedApi = {
          ...existingApi,
          url: apiContent.url || existingApi.url,
          method: apiContent.method || existingApi.method,
          headers: apiContent.headers || existingApi.headers,
          body: apiContent.body || existingApi.body,
          timeout: apiContent.timeout || existingApi.timeout,
          retries: apiContent.retries || existingApi.retries,
          // Preserve these properties from the original API
          id: existingApi.id,
          name: existingApi.name,
          isActive: existingApi.isActive
        };

        // Update requestSchema with new API call fields
        const updatedRequestSchema = {
          ...(existingApi.requestSchema || {}),
          responseVariable: apiContent.responseVariable,
          arrayFieldName: apiContent.arrayFieldName,
          displayFields: apiContent.displayFields
        };

        updatedApi.requestSchema = updatedRequestSchema;

        // Preserve template-specific properties if they exist
        if ((existingApi as any).isFromTemplate !== undefined) {
          (updatedApi as any).isFromTemplate = (existingApi as any).isFromTemplate;
        }
        if ((existingApi as any).templateNodeId !== undefined) {
          (updatedApi as any).templateNodeId = (existingApi as any).templateNodeId;
        }

        updatedApis[existingApiIndex] = updatedApi;
        
        return updatedApis;
      });
    } else if (apiContent.url && apiContent.url.trim()) {
      // Create a new API configuration if none exists and URL is provided
      const newApiConfig: IApiConfig = {
        id: crypto.randomUUID(),
        name: `API from Node`,
        url: apiContent.url,
        method: apiContent.method || 'GET',
        headers: apiContent.headers || {},
        timeout: apiContent.timeout || 30000,
        retries: apiContent.retries || 3,
        isActive: true,
        body: apiContent.body,
        // Include new API call fields in requestSchema
        requestSchema: {
          responseVariable: apiContent.responseVariable,
          arrayFieldName: apiContent.arrayFieldName,
          displayFields: apiContent.displayFields
        }
      };

      // Add the new API configuration
      setConfiguredApis(prev => {
        // Check for duplicates by URL and method
        const existingApiKeys = new Set(prev.map(api => `${api.method}:${api.url}`));
        if (!existingApiKeys.has(`${newApiConfig.method}:${newApiConfig.url}`)) {
          return [...prev, newApiConfig];
        }
        return prev;
      });
    }
  }
};

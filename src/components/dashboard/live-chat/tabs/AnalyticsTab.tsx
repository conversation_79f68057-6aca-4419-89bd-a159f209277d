'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import {
  getAnalyticsOverview,
  getAgentPerformanceMetrics,
  getConversationVolumeStats,
  getResponseTimeAnalytics,
  getCustomerSatisfactionTrends,
  getPeakHoursData,
  type AnalyticsOverview,
  type AgentPerformanceMetrics,
  type ConversationVolumeStats,
  type ResponseTimeAnalytics,
  type CustomerSatisfactionTrend,
  type PeakHoursData
} from '@/server/actions/chat-analytics-actions';
import { logger } from '@/utils/logger';

interface AnalyticsTabProps {
  organizationId: string;
}

type TimePeriod = 'today' | 'week' | 'month' | 'quarter';
type ViewMode = 'overview' | 'agents' | 'conversations' | 'satisfaction';

export function AnalyticsTab({ organizationId }: AnalyticsTabProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('week');
  const [selectedView, setSelectedView] = useState<ViewMode>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [overview, setOverview] = useState<AnalyticsOverview | null>(null);
  const [agentMetrics, setAgentMetrics] = useState<AgentPerformanceMetrics[]>([]);
  const [volumeStats, setVolumeStats] = useState<ConversationVolumeStats[]>([]);
  const [responseTimeAnalytics, setResponseTimeAnalytics] = useState<ResponseTimeAnalytics | null>(null);
  const [satisfactionTrends, setSatisfactionTrends] = useState<CustomerSatisfactionTrend[]>([]);
  const [peakHours, setPeakHours] = useState<PeakHoursData[]>([]);
  const { error: showError } = useToast();

  // Load analytics data
  const loadAnalyticsData = async () => {
    setIsLoading(true);
    try {
      const [
        overviewResponse,
        agentResponse,
        volumeResponse,
        responseTimeResponse,
        satisfactionResponse,
        peakHoursResponse
      ] = await Promise.all([
        getAnalyticsOverview(organizationId, selectedPeriod),
        getAgentPerformanceMetrics(organizationId, selectedPeriod),
        getConversationVolumeStats(organizationId, selectedPeriod),
        getResponseTimeAnalytics(organizationId, selectedPeriod),
        getCustomerSatisfactionTrends(organizationId, selectedPeriod),
        getPeakHoursData(organizationId, selectedPeriod)
      ]);

      if (overviewResponse.success && overviewResponse.data) {
        setOverview(overviewResponse.data);
      }

      if (agentResponse.success && agentResponse.data) {
        setAgentMetrics(agentResponse.data);
      }

      if (volumeResponse.success && volumeResponse.data) {
        setVolumeStats(volumeResponse.data);
      }

      if (responseTimeResponse.success && responseTimeResponse.data) {
        setResponseTimeAnalytics(responseTimeResponse.data);
      }

      if (satisfactionResponse.success && satisfactionResponse.data) {
        setSatisfactionTrends(satisfactionResponse.data);
      }

      if (peakHoursResponse.success && peakHoursResponse.data) {
        setPeakHours(peakHoursResponse.data);
      }

    } catch (error) {
      logger.error('Error loading analytics data:', error);
      showError('Error', 'Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  // Load data when period changes
  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod, organizationId]);

  // Format numbers for display
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Format time duration
  const formatDuration = (minutes: number): string => {
    if (minutes < 1) {
      return `${Math.round(minutes * 60)}s`;
    }
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    }
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h ${mins}m`;
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="animate-pulse">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
      <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header with Period and View Filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Live Chat Analytics
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Performance insights and metrics for your live chat system
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          {/* Time Period Filter */}
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {(['today', 'week', 'month', 'quarter'] as TimePeriod[]).map((period) => (
              <Button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                size="sm"
                variant={selectedPeriod === period ? 'default' : 'ghost'}
                className={`capitalize ${
                  selectedPeriod === period
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {period}
              </Button>
            ))}
          </div>

          {/* View Mode Filter */}
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {([
              { key: 'overview', label: 'Overview' },
              { key: 'agents', label: 'Agents' },
              { key: 'conversations', label: 'Volume' },
              { key: 'satisfaction', label: 'Satisfaction' }
            ] as { key: ViewMode; label: string }[]).map(({ key, label }) => (
              <Button
                key={key}
                onClick={() => setSelectedView(key)}
                size="sm"
                variant={selectedView === key ? 'default' : 'ghost'}
                className={`${
                  selectedView === key
                    ? 'bg-white dark:bg-gray-700 shadow-sm'
                    : 'hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* KPI Cards */}
      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Total Conversations
                </p>
                {isLoading ? (
                  <LoadingSkeleton />
                ) : (
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatNumber(overview?.totalConversations || 0)}
                  </p>
                )}
              </div>
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
            </div>
          </Card>

          <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Active Agents
                </p>
                {isLoading ? (
                  <LoadingSkeleton />
                ) : (
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {overview?.activeAgents || 0}
                    </p>
                    <Badge variant="success" className="text-xs">
                      Online
                    </Badge>
                  </div>
                )}
              </div>
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
            </div>
          </Card>

          <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Avg Response Time
                </p>
                {isLoading ? (
                  <LoadingSkeleton />
                ) : (
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatDuration(overview?.averageResponseTime || 0)}
                  </p>
                )}
              </div>
              <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </Card>

          <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:shadow-lg transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Satisfaction Score
                </p>
                {isLoading ? (
                  <LoadingSkeleton />
                ) : (
                  <div className="flex items-center gap-2">
                    <p className="text-2xl font-bold text-gray-900 dark:text-white">
                      {(overview?.customerSatisfactionScore || 0).toFixed(1)}
                    </p>
                    <span className="text-sm text-gray-500">/5.0</span>
                  </div>
                )}
              </div>
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* Agent Performance View */}
      {selectedView === 'agents' && (
        <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d]">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Agent Performance
          </h4>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {agentMetrics.map((agent) => (
                <div
                  key={agent.agentId}
                  className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${
                      agent.isCurrentlyOnline ? 'bg-green-400' : 'bg-gray-400'
                    }`} />
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {agent.agentName}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {agent.conversationsHandled} conversations
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-6 text-sm">
                    <div className="text-center">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {formatDuration(agent.averageResponseTime)}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">Avg Response</p>
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {agent.customerSatisfactionRating.toFixed(1)}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">Rating</p>
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-gray-900 dark:text-white">
                        {formatDuration(agent.totalOnlineTime)}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">Online Time</p>
                    </div>
                  </div>
                </div>
              ))}
              {agentMetrics.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No agent data available for the selected period
                </div>
              )}
            </div>
          )}
        </Card>
      )}

      {/* Conversation Volume View */}
      {selectedView === 'conversations' && (
        <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d]">
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Conversation Volume
          </h4>
          {isLoading ? (
            <div className="animate-pulse">
              <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {volumeStats.slice(0, 7).map((stat, index) => (
                <div
                  key={`volume-${stat.date}-${index}`}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
                >
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {new Date(stat.date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-6 text-sm">
                    <div className="text-center">
                      <p className="font-medium text-blue-600 dark:text-blue-400">
                        {stat.totalConversations}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">Total</p>
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-green-600 dark:text-green-400">
                        {stat.resolvedConversations}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">Resolved</p>
                    </div>
                    <div className="text-center">
                      <p className="font-medium text-yellow-600 dark:text-yellow-400">
                        {formatDuration(stat.averageResolutionTime)}
                      </p>
                      <p className="text-gray-600 dark:text-gray-400">Avg Time</p>
                    </div>
                  </div>
                </div>
              ))}
              {volumeStats.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No conversation data available for the selected period
                </div>
              )}
            </div>
          )}
        </Card>
      )}

      {/* Customer Satisfaction View */}
      {selectedView === 'satisfaction' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d]">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Satisfaction Trends
            </h4>
            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ) : (
              <div className="space-y-3">
                {satisfactionTrends.slice(0, 7).map((trend, index) => (
                  <div
                    key={`satisfaction-${trend.date}-${index}`}
                    className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
                  >
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(trend.date).toLocaleDateString()}
                    </p>
                    <div className="flex items-center gap-2">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-4 h-4 ${
                              i < Math.round(trend.averageRating)
                                ? 'text-yellow-400'
                                : 'text-gray-300 dark:text-gray-600'
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {trend.averageRating.toFixed(1)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>

          <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d]">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Response Time Distribution
            </h4>
            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ) : responseTimeAnalytics ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {formatDuration(responseTimeAnalytics.average)}
                    </p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">Average</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <p className="text-lg font-bold text-green-600 dark:text-green-400">
                      {formatDuration(responseTimeAnalytics.median)}
                    </p>
                    <p className="text-sm text-green-600 dark:text-green-400">Median</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Under 1 min</span>
                    <span className="text-sm font-medium">{responseTimeAnalytics.distribution.under1min}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">1-5 min</span>
                    <span className="text-sm font-medium">{responseTimeAnalytics.distribution.under5min}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">5-15 min</span>
                    <span className="text-sm font-medium">{responseTimeAnalytics.distribution.under15min}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Over 15 min</span>
                    <span className="text-sm font-medium">{responseTimeAnalytics.distribution.over15min}</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No response time data available
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  );
}

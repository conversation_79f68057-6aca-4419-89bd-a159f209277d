'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { useNotificationSound, useConversationData } from '@/hooks';
import { useSSE, parseSSEEventData } from '@/hooks/useSSE';
import { useAgentNotificationsSSE } from '@/hooks/useAgentNotificationsSSE';
import {  useTypingListener } from '@/hooks/useTypingIndicator';
import { AgentStatusToggle, ChatWindow } from '../components';
import {
  useConversationStore,
  type ConversationListItem,
  type LiveChatMessage,
  type ConversationError,
  type PendingChatAssignment
} from '@/stores/conversationStore';
import {
  sendAgentMessage,
  closeLiveChatConversation
} from '@/server/actions/live-chat-actions';
import {
  getPendingChatAssignments,
  acceptChatAssignment
} from '@/server/actions/chat-assignment-actions';
import { logger } from '@/utils/logger';
import type { ChatAssignmentData } from '@/server/actions/chat-assignment-actions';

// Live chat message response from server
interface LiveChatMessageResponse {
  id: string;
  content: string;
  type: 'customer' | 'agent' | 'system';
  senderName: string;
  senderEmail?: string;
  sentAt: Date;
  status?: string;
  metadata?: Record<string, unknown>;
}

// Agent message send response
interface AgentMessageSendResponse {
  message: {
    id: string;
    content: string;
    type: string;
    senderName: string;
    sentAt: Date;
    status: string;
  };
  conversationStatus: string;
}

// Legacy type for backward compatibility with ChatWindow component
export interface Conversation {
  id: string;
  customerName: string;
  customerEmail?: string;
  subject?: string;
  status: 'active' | 'waiting' | 'resolved' | 'closed';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  assignedAgent?: string;
  messageCount: number;
  lastMessageAt: Date | null;
  startedAt: Date;
  messages: LiveChatMessage[];
}

interface ConversationsTabProps {
  organizationId: string;
  conversations: Conversation[] | null;
  onConversationsUpdate: (conversations: Conversation[]) => void;
}

export function ConversationsTab({
  organizationId,
  conversations: _initialConversations,
  onConversationsUpdate
}: ConversationsTabProps) {
  // Zustand store state
  const {
    conversations: storeConversations,
    selectedConversationId,
    selectedConversationMessages,
    isLoadingConversations,
    isLoadingMessages,
    conversationsError,
    messagesError,
    searchTerm,
    statusFilter,
    pendingChats: storePendingChats, // Get pending chats from store
    setSearchTerm,
    setStatusFilter,
    clearError,
    setPendingChats // Add setPendingChats action
  } = useConversationStore();

  // Local state for UI only
  const [acceptingChatId, setAcceptingChatId] = useState<string | null>(null);
  const { success: showSuccess, error: showError } = useToast();

  // Handle ticket creation from live chat
  const handleTicketCreated = useCallback((ticketData: any) => {
    showSuccess(
      'Support Ticket Created',
      `Ticket #${ticketData.id} has been created successfully. The conversation has been escalated to the support team.`
    );
  }, [showSuccess]);

  // Initialize notification sound for pending chats and customer messages
  const { playNotification } = useNotificationSound({
    volume: 0.7,
    enabled: true,
    debounceMs: 3000 // Conservative debounce to prevent spam notifications
  });

  // Track previous state for detecting new pending chats and messages
  const previousPendingChatsCountRef = useRef<number>(0);
  const notifiedChatIdsRef = useRef<Set<string>>(new Set());
  const notifiedMessageIdsRef = useRef<Set<string>>(new Set());

  // Typing indicator state
  const { addTypingUser, removeTypingUser, getTypingUsers, clearAllTypingUsers } = useTypingListener();

  const {
    loadConversations,
    selectConversation,
    refreshConversations
  } = useConversationData({
    organizationId,
    autoRefreshInterval: 5000, // Enable auto-refresh every 5 seconds for conversation list
    onError: (error: ConversationError) => {
      showError(error.message);
      logger.error('Conversation data error:', error);
    }
  });

  // Agent notifications SSE handler for cross-conversation notifications
  const handleNotificationMessage = useCallback((eventData: any) => {
    switch (eventData.type) {
      case 'new-message':
        if (eventData.message && eventData.conversationId) {
          const message: LiveChatMessage = {
            id: eventData.message.id,
            content: eventData.message.content,
            type: eventData.message.type,
            senderName: eventData.message.senderName,
            senderEmail: eventData.message.senderEmail,
            sentAt: new Date(eventData.message.sentAt),
            status: eventData.message.status,
            metadata: eventData.message.metadata || {}
          };

          const isSelectedConversation = selectedConversationId === eventData.conversationId;

          // Only handle notifications for unselected conversations
          // (selected conversation is handled by the main SSE connection)
          if (!isSelectedConversation && message.type === 'customer' && !notifiedMessageIdsRef.current.has(message.id)) {
            const { incrementUnreadCount } = useConversationStore.getState();

            incrementUnreadCount(eventData.conversationId);
            playNotification();
            notifiedMessageIdsRef.current.add(message.id);

            // Update conversation list metadata
            const { updateConversationInList } = useConversationStore.getState();
            const currentConversations = useConversationStore.getState().conversations;
            const conversation = currentConversations.find(c => c.id === eventData.conversationId);

            if (conversation) {
              updateConversationInList(eventData.conversationId, {
                messageCount: conversation.messageCount + 1,
                lastMessageAt: new Date()
              });
            }
          }
        }
        break;

      case 'chat-accepted':
        if (eventData.assignmentId) {
          // Debug logging
          logger.log(`🔔 ConversationsTab received chat-accepted notification:`, {
            assignmentId: eventData.assignmentId,
            conversationId: eventData.conversationId,
            agentInfo: eventData.agentInfo
          });

          // Remove the accepted chat from pending list for all agents
          const { removePendingChatByAssignmentId } = useConversationStore.getState();
          removePendingChatByAssignmentId(eventData.assignmentId);
        }
        break;

      default:
        // Unhandled notification event type
    }
  }, [selectedConversationId, playNotification]);

  // Main SSE event handler for selected conversation updates
  const handleSSEMessage = useCallback((event: MessageEvent) => {
    const eventData = parseSSEEventData(event);
    if (!eventData) return;

    switch (eventData.type) {
      case 'new-message':
        if (eventData.message && eventData.conversationId) {
          const message: LiveChatMessage = {
            id: eventData.message.id,
            content: eventData.message.content,
            type: eventData.message.type,
            senderName: eventData.message.senderName,
            senderEmail: eventData.message.senderEmail,
            sentAt: new Date(eventData.message.sentAt),
            status: eventData.message.status,
            metadata: eventData.message.metadata || {}
          };

          const isSelectedConversation = selectedConversationId === eventData.conversationId;

          // Update conversation messages if this is the selected conversation
          if (isSelectedConversation) {
            // Check if message already exists to prevent duplicates
            const currentState = useConversationStore.getState();
            const existingMessage = currentState.selectedConversationMessages.find(m => m.id === message.id);

            if (!existingMessage) {
              const { addMessageToSelected } = useConversationStore.getState();
              addMessageToSelected(message);

              // Play notification for customer messages in selected conversation
              if (message.type === 'customer' && !notifiedMessageIdsRef.current.has(message.id)) {
                notifiedMessageIdsRef.current.add(message.id);
                playNotification();
              }
            }
          }
          // Note: Cross-conversation notifications are now handled by the dedicated notifications SSE

          // Update conversation list metadata for selected conversation only
          if (isSelectedConversation) {
            const { updateConversationInList } = useConversationStore.getState();
            const currentConversations = useConversationStore.getState().conversations;
            const conversation = currentConversations.find(c => c.id === eventData.conversationId);

            if (conversation) {
              updateConversationInList(eventData.conversationId, {
                messageCount: conversation.messageCount + 1,
                lastMessageAt: new Date()
              });
            }
          }


        }
        break;

      case 'pending-chat':
        if (eventData.assignment) {
          const newChat: PendingChatAssignment = {
            id: eventData.assignment.id,
            customerName: eventData.assignment.customerName || 'Anonymous Customer',
            customerEmail: eventData.assignment.customerEmail,
            priority: eventData.assignment.priority,
            source: eventData.assignment.source,
            createdAt: new Date(eventData.assignment.createdAt),
            metadata: eventData.assignment.metadata
          };

          // Add to pending chats if not already exists
          if (!notifiedChatIdsRef.current.has(newChat.id)) {
            const { setPendingChats } = useConversationStore.getState();
            const currentChats = useConversationStore.getState().pendingChats;

            // Check if chat already exists
            const existingChat = currentChats.find(chat => chat.id === newChat.id);
            if (!existingChat) {
              setPendingChats([...currentChats, newChat]);
              playNotification();
            }

            notifiedChatIdsRef.current.add(newChat.id);
          }
        }
        break;

      case 'chat-accepted':
        if (eventData.assignmentId) {
          // Immediately remove the accepted chat from pending list
          const { removePendingChatByAssignmentId } = useConversationStore.getState();
          removePendingChatByAssignmentId(eventData.assignmentId);

          // Refresh conversations to show the new active conversation
          setTimeout(() => {
            refreshConversations();
          }, 0);
        }
        break;

      case 'typing-start':
        if (eventData.conversationId && eventData.userType && eventData.userName) {
          addTypingUser(
            `${eventData.userType}-${eventData.conversationId}`,
            eventData.userType,
            eventData.userName
          );
        }
        break;

      case 'typing-stop':
        if (eventData.conversationId && eventData.userType) {
          removeTypingUser(`${eventData.userType}-${eventData.conversationId}`);
        }
        break;

      case 'conversation-closed':
        if (eventData.conversationId) {
          const { updateConversationInList, setSelectedConversationId } = useConversationStore.getState();
          updateConversationInList(eventData.conversationId, { status: 'closed' });

          // Clear selection if this was the selected conversation
          if (selectedConversationId === eventData.conversationId) {
            setSelectedConversationId(null);
          }
        }
        break;

      case 'heartbeat':
        // Heartbeat received - connection is healthy
        break;

      default:
        // Unknown SSE event type
    }
  }, [selectedConversationId, playNotification]);

  // Get filtered conversations from store
  const filteredConversations = storeConversations.filter(conversation => {
    const matchesSearch = searchTerm === '' ||
      conversation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      conversation.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || conversation.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  // Get selected conversation from store
  const getSelectedConversation = () => {
    return selectedConversationId
      ? storeConversations.find(conv => conv.id === selectedConversationId)
      : null;
  };

  // Create selected conversation object for ChatWindow compatibility
  const selectedConversation: Conversation | null = selectedConversationId ? {
    ...getSelectedConversation()!,
    messages: selectedConversationMessages
  } : null;

  // Load pending chat assignments and map to store format
  const loadPendingChats = useCallback(async () => {
    try {
      const response = await getPendingChatAssignments(organizationId);

      if (response.success && 'data' in response && response.data) {
        // Map ChatAssignmentData to PendingChatAssignment format
        const mappedChats: PendingChatAssignment[] = (response.data as ChatAssignmentData[]).map((chat) => ({
          id: chat.id,
          customerName: chat.customerName || 'Anonymous Customer',
          customerEmail: chat.customerEmail,
          priority: chat.priority,
          source: chat.source,
          createdAt: new Date(chat.createdAt),
          metadata: chat.metadata
        }));

        // Update store with pending chats
        setPendingChats(mappedChats);
      } else {
        logger.warn('Failed to load pending chats:', response);
      }
    } catch (error: unknown) {
      logger.error('❌ Error loading pending chats:', error);
    }
  }, [organizationId, setPendingChats]);

  // Memoize SSE params to prevent unnecessary reconnections
  const sseParams = useMemo(() => ({
    organizationId,
    ...(selectedConversationId && { conversationId: selectedConversationId })
  }), [organizationId, selectedConversationId]);

  // SSE connection for real-time updates
  
  const { connectionState } = useSSE({
    endpoint: '/api/v1/live-chat/sse/agent',
    params: sseParams,
    onMessage: handleSSEMessage,
    enabled: true,
    reconnectDelay: 1000,
    maxReconnectAttempts: 10
  });
   console.log({inHere:"law"});
   
  useAgentNotificationsSSE({
    organizationId,
    onMessage: handleNotificationMessage,
    enabled: true,
    reconnectDelay: 1000,
    maxReconnectAttempts: 10
  });

  // Handle chat acceptance
  const handleAcceptChat = async (assignmentId: string) => {
    if (acceptingChatId) return; // Prevent multiple simultaneous accepts

    setAcceptingChatId(assignmentId);
    try {
      const response = await acceptChatAssignment(assignmentId, organizationId);

      if (response.success) {
        showSuccess('Chat Accepted', 'You have successfully accepted the chat request');

        // Immediately remove from local pending list
        const { removePendingChatByAssignmentId } = useConversationStore.getState();
        removePendingChatByAssignmentId(assignmentId);

        // Refresh conversations to show the new active conversation
        await refreshConversations();
      } else {
        const errorMessage = ('error' in response ? response.error : 'Failed to accept chat assignment') || 'Failed to accept chat assignment';

        // Check if the error indicates the chat was already accepted
        if (errorMessage.includes('already accepted') || errorMessage.includes('not available')) {
          // Remove from local pending list since it's no longer available
          const { removePendingChatByAssignmentId } = useConversationStore.getState();
          removePendingChatByAssignmentId(assignmentId);

          showError('Chat Unavailable', 'This chat has already been accepted by another agent');
        } else {
          showError('Error', errorMessage || 'Failed to accept chat assignment');
        }
      }
    } catch (error: unknown) {
      logger.error('Error accepting chat:', error);
      showError('Error', 'Failed to accept chat assignment');
    } finally {
      setAcceptingChatId(null);
    }
  };

  // Initialize conversations and load initial data on mount and when organization changes
  useEffect(() => {
    // Load initial conversations and pending chats
    loadConversations();
    loadPendingChats();

    // Cleanup function when organization changes
    return () => {
      // Clear selected conversation to prevent state leakage
      const { setSelectedConversationId } = useConversationStore.getState();
      setSelectedConversationId(null);

      // Clear notification tracking when organization changes
      notifiedChatIdsRef.current.clear();
      notifiedMessageIdsRef.current.clear();
      clearAllTypingUsers();
    };
  }, [organizationId]);

  // Monitor polling state changes (debug logging removed for production)

  // Listen for customer message events to trigger immediate refresh
  useEffect(() => {
    const handleCustomerMessage = () => {
      refreshConversations();
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('customerMessageSent', handleCustomerMessage as EventListener);

      return () => {
        window.removeEventListener('customerMessageSent', handleCustomerMessage as EventListener);
      };
    }
  }, [refreshConversations]);

  useEffect(() => {
    previousPendingChatsCountRef.current = storePendingChats.length;
  }, [storePendingChats]);

  // Monitor conversation selection changes for SSE connection updates
  useEffect(() => {
    // Main SSE connection will automatically update with new conversationId parameter
    // Notifications SSE remains organization-wide for cross-conversation notifications
    // Load messages for newly selected conversation
    if (selectedConversationId) {
      selectConversation(selectedConversationId);
    }
  }, [selectedConversationId]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clear selected conversation to prevent state leakage when navigating away
      const { setSelectedConversationId } = useConversationStore.getState();
      setSelectedConversationId(null);

      // Clear notification tracking to prevent memory leaks
      notifiedChatIdsRef.current.clear();
      notifiedMessageIdsRef.current.clear();
      clearAllTypingUsers();
    };
  }, [clearAllTypingUsers]);

  const handleConversationSelect = async (conversation: ConversationListItem) => {
    // Use the store's selectConversation method which loads messages
    await selectConversation(conversation.id);
  };

  const handleSendMessage = async (content: string) => {
    if (!selectedConversationId) return;

    try {
      const response = await sendAgentMessage({
        conversationId: selectedConversationId,
        organizationId,
        content
      });

      if (response.success && 'data' in response && response.data) {
        const responseData = response.data as AgentMessageSendResponse;

        // Add the new message to the store
        const newMessage: LiveChatMessage = {
          id: responseData.message.id,
          content: responseData.message.content,
          type: 'agent',
          senderName: responseData.message.senderName,
          sentAt: new Date(responseData.message.sentAt),
          status: responseData.message.status,
          metadata: {}
        };

        // Update the store with the new message
        const { addMessageToSelected, updateConversationInList } = useConversationStore.getState();
        addMessageToSelected(newMessage);

        // Update conversation metadata in the list
        updateConversationInList(selectedConversationId, {
          messageCount: (selectedConversationMessages.length + 1),
          lastMessageAt: new Date()
        });

        // Update parent component
        onConversationsUpdate(storeConversations.map((conv): Conversation =>
          conv.id === selectedConversationId
            ? { ...conv, messageCount: conv.messageCount + 1, lastMessageAt: new Date(), messages: [] }
            : { ...conv, messages: [] }
        ));
      } else {
        const errorMessage = 'error' in response ? response.error : 'Failed to send message';
        logger.error('❌ Failed to send agent message:', errorMessage);
        showError('Error', errorMessage || 'Failed to send message');
      }
    } catch (error: unknown) {
      logger.error('❌ Error sending agent message:', error);
      showError('Error', 'Failed to send message');
    }
  };

  const handleCloseChat = async (conversationId: string, reason?: string) => {
    try {
      const result = await closeLiveChatConversation(conversationId, organizationId, reason);

      if (result.success) {
        showSuccess('Chat closed successfully', 'The conversation has been closed and the customer has been notified.');

        // Update the conversation status in the store
        const { updateConversationInList, setSelectedConversationId } = useConversationStore.getState();
        updateConversationInList(conversationId, { status: 'closed' });

        // If this was the selected conversation, clear selection
        if (selectedConversationId === conversationId) {
          setSelectedConversationId(null);
        }

        // Update parent component with closed status
        onConversationsUpdate(storeConversations.map((conv): Conversation =>
          conv.id === conversationId
            ? { ...conv, status: 'closed', messages: [] }
            : { ...conv, messages: [] }
        ));
      } else {
        const errorMessage = 'error' in result ? result.error : 'An unexpected error occurred';
        showError('Failed to close chat', errorMessage || 'An unexpected error occurred');
      }
    } catch (err: unknown) {
      logger.error('Error closing chat:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      showError('Failed to close chat', errorMessage);
    }
  };

  return (
    <div className="space-y-6">
      {/* Agent Status Header */}
      <div className="flex items-center justify-between p-4 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Live Chat Dashboard
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Manage customer conversations and agent availability
          </p>
          {/* SSE Connection Status */}
          <div className="flex items-center gap-2 mt-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionState.isConnected
                ? 'bg-green-500'
                : connectionState.isConnecting
                  ? 'bg-yellow-500 animate-pulse'
                  : 'bg-red-500'
            }`} />
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {connectionState.isConnected
                ? 'Real-time connected'
                : connectionState.isConnecting
                  ? 'Connecting...'
                  : connectionState.lastError
                    ? `Connection error: ${connectionState.lastError}`
                    : 'Disconnected'
              }
              {connectionState.reconnectAttempts > 0 && (
                <span className="ml-1">
                  (Attempt {connectionState.reconnectAttempts})
                </span>
              )}
            </span>
          </div>
        </div>
        <AgentStatusToggle organizationId={organizationId} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Conversation List */}
        <div className="lg:col-span-1">
          <Card className="h-[600px] flex flex-col">
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="space-y-4">
                <Input
                  placeholder="Search conversations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full"
                />

                <select
                  value={statusFilter}
                  onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
                    setStatusFilter(e.target.value as 'all' | 'active' | 'waiting' | 'resolved' | 'closed')
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="all">All Status</option>
                  <option value="waiting">Waiting</option>
                  <option value="active">Active</option>
                  <option value="resolved">Resolved</option>
                  <option value="closed">Closed</option>
                </select>
              </div>
            </div>

            <div className="flex-1 overflow-y-auto">
              {isLoadingConversations ? (
                <div className="flex items-center justify-center h-32">
                  <div className="flex items-center gap-2 text-gray-500 dark:text-gray-400">
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                    <span>Loading conversations...</span>
                  </div>
                </div>
              ) : conversationsError ? (
                <div className="p-4 text-center">
                  <div className="text-red-600 dark:text-red-400 mb-2">
                    Failed to load conversations
                  </div>
                  <Button
                    onClick={() => {
                      clearError('conversations');
                      loadConversations();
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Retry
                  </Button>
                </div>
              ) : (
                <ConversationList
                  conversations={filteredConversations.map((conv): Conversation => ({ ...conv, messages: [] }))}
                  pendingChats={storePendingChats}
                  selectedConversation={selectedConversation}
                  onConversationSelect={(conv) => handleConversationSelect(conv)}
                  onAcceptChat={handleAcceptChat}
                  acceptingChatId={acceptingChatId}
                />
              )}
            </div>
          </Card>
        </div>

        {/* Chat Window */}
        <div className="lg:col-span-2">
          <Card className="h-[600px]">
            {selectedConversationId && isLoadingMessages ? (
              <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                <div className="text-center">
                  <div className="w-8 h-8 mx-auto mb-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
                  <p className="text-lg font-medium">Loading messages...</p>
                  <p className="text-sm">Please wait while we fetch the conversation</p>
                </div>
              </div>
            ) : selectedConversationId && messagesError ? (
              <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium text-red-600 dark:text-red-400">Failed to load messages</p>
                  <p className="text-sm mb-4">{messagesError.message}</p>
                  <Button
                    onClick={() => {
                      clearError('messages');
                      if (selectedConversationId) {
                        selectConversation(selectedConversationId);
                      }
                    }}
                    size="sm"
                    variant="outline"
                  >
                    Retry
                  </Button>
                </div>
              </div>
            ) : selectedConversation ? (
              <ChatWindow
                conversation={selectedConversation}
                onSendMessage={handleSendMessage}
                onCloseChat={handleCloseChat}
                organizationId={organizationId}
                onTicketCreated={handleTicketCreated}
                typingUsers={(() => {
                  const allTypingUsers = getTypingUsers();
                  const filteredUsers = allTypingUsers
                    .filter(user => user.userId.includes(selectedConversation.id))
                    .map(user => ({
                      userId: user.userId,
                      userType: user.userType as 'agent' | 'customer',
                      userName: user.userName,
                      timestamp: user.timestamp
                    }));



                  return filteredUsers;
                })()}
              />
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                  </div>
                  <p className="text-lg font-medium">Select a conversation</p>
                  <p className="text-sm">Choose a conversation from the list to start chatting</p>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
}

// ConversationList component props interface
interface ConversationListProps {
  conversations: Conversation[];
  pendingChats: PendingChatAssignment[];
  selectedConversation: Conversation | null;
  onConversationSelect: (conversation: Conversation) => void;
  onAcceptChat: (assignmentId: string) => void;
  acceptingChatId: string | null;
}

// ConversationList component
function ConversationList({
  conversations,
  pendingChats,
  selectedConversation,
  onConversationSelect,
  onAcceptChat,
  acceptingChatId
}: ConversationListProps) {
  // Get unread counts from conversation store
  const { getUnreadCount } = useConversationStore();
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'waiting': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'resolved': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'normal': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatTime = (date: Date | null | undefined): string => {
    if (!date) return 'No messages';

    const now = new Date();
    const messageDate = new Date(date);

    // Handle invalid dates
    if (isNaN(messageDate.getTime())) return 'Invalid date';

    const diff = now.getTime() - messageDate.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
    return `${Math.floor(minutes / 1440)}d ago`;
  };

  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - new Date(date).getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
    return `${Math.floor(minutes / 1440)}d ago`;
  };

  return (
    <div className="p-4">
      {/* Pending Chat Requests Section */}
      {pendingChats.length > 0 && (
        <>
          <div className="mb-4">
            <div className="flex items-center gap-2 mb-3">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                Pending Chat Requests
              </h4>
              <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 text-xs">
                {pendingChats.length}
              </Badge>
            </div>
            <div className="space-y-2">
              {pendingChats.map((chat) => (
                <div
                  key={chat.id}
                  className="p-3 rounded-lg border border-yellow-200 dark:border-yellow-800/30 bg-yellow-50 dark:bg-yellow-900/10"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <div className="w-8 h-8 bg-gradient-to-r from-[#8178E8] to-[#6964D3] rounded-full flex items-center justify-center text-white text-xs font-medium">
                        {chat.customerName ? chat.customerName.charAt(0).toUpperCase() : 'C'}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {chat.customerName || 'Anonymous Customer'}
                          </p>
                          <Badge className={`text-xs ${getPriorityColor(chat.priority)}`}>
                            {chat.priority}
                          </Badge>
                          {chat.source === 'bot_handoff' && (
                            <Badge className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 text-xs">
                              Bot Handoff
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {chat.customerEmail && `${chat.customerEmail} • `}
                          Waiting {formatTimeAgo(chat.createdAt)}
                        </p>
                        {chat.metadata?.initialMessage && (
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 italic truncate">
                            &#34;{chat.metadata.initialMessage.substring(0, 60)}...&#34;
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-end">
                    <Button
                      onClick={() => onAcceptChat(chat.id)}
                      disabled={acceptingChatId === chat.id}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white text-xs px-3 py-1"
                    >
                      {acceptingChatId === chat.id ? (
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Accepting...
                        </div>
                      ) : (
                        'Accept Chat'
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-200 dark:border-gray-700 my-4"></div>
        </>
      )}

      {/* Active Conversations Section */}
      <div className="mb-3">
        <div className="flex items-center gap-2 mb-3">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            Active Conversations
          </h4>
          <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 text-xs">
            {conversations.length}
          </Badge>
        </div>
      </div>

      <div className="space-y-2">
        {conversations.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg className="w-12 h-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <p>No active conversations</p>
            <p className="text-sm">Accept pending requests to start conversations</p>
          </div>
        ) : (
          conversations.map((conversation) => (
            <div
              key={conversation.id}
              onClick={() => onConversationSelect(conversation)}
              className={`p-3 rounded-lg cursor-pointer transition-colors ${
                selectedConversation?.id === conversation.id
                  ? 'bg-[#8178E8]/10 border border-[#8178E8]/20'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-800'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2 flex-1 min-w-0">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {conversation.customerName}
                    </p>
                    {conversation.customerEmail && (
                      <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {conversation.customerEmail}
                      </p>
                    )}
                  </div>
                  {(() => {
                    const unreadCount = getUnreadCount(conversation.id);
                    return unreadCount > 0 && (
                      <div className="flex-shrink-0">
                        <Badge className="bg-red-500 text-white text-xs px-2 py-1 rounded-full min-w-[20px] h-5 flex items-center justify-center">
                          {unreadCount > 99 ? '99+' : unreadCount}
                        </Badge>
                      </div>
                    );
                  })()}
                </div>
                <Badge className={`text-xs ${getStatusColor(conversation.status)}`}>
                  {conversation.status}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {conversation.messageCount} messages
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {formatTime(conversation.lastMessageAt)}
                </p>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}



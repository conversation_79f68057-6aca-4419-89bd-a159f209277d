'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export interface Organization {
  id: string;
  name: string;
}

export interface LiveChatConfig {
  id: string;
  status: string;
  auth: {
    appId: string;
    apiKey: string;
    isActive: boolean;
  };
  branding: {
    companyName: string;
    logoUrl?: string;
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    backgroundColor: string;
    textColor: string;
    fontFamily: string;
  };
  settings: {
    welcomeMessage: string;
    offlineMessage: string;
    placeholderText: string;
    enableFileUpload: boolean;
    enableEmojis: boolean;
    enableTypingIndicator: boolean;
  };
}

interface SettingsTabProps {
  organization: Organization;
  liveChatConfig: LiveChatConfig;
}

export function SettingsTab({ organization, liveChatConfig }: SettingsTabProps) {
  const [copied, setCopied] = useState(false);
  const [showApiKey, setShowApiKey] = useState(false);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getWidgetCode = () => `<!-- NewInstance Live Chat Widget -->
<script src="https://live-chat.cinstance.com/dist/newinstance-widget.umd.js"></script>
<script>
  const widget = new NewInstanceWidget.ChatWidgetVanilla({
    appId: "${liveChatConfig.auth.appId}",
    apiKey: "${liveChatConfig.auth.apiKey}",
    theme: {
      primaryColor: "${liveChatConfig.branding.primaryColor}",
      secondaryColor: "${liveChatConfig.branding.secondaryColor}",
      accentColor: "${liveChatConfig.branding.accentColor}",
      backgroundColor: "${liveChatConfig.branding.backgroundColor}",
      textColor: "${liveChatConfig.branding.textColor}",
      fontFamily: "${liveChatConfig.branding.fontFamily}"
    }
  });
  widget.mount('newinstance-chat-widget');
</script>
<div id="newinstance-chat-widget"></div>`;

  const widgetCode = getWidgetCode();

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Widget Integration
        </h3>

        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Authentication Credentials</h4>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  App ID
                </label>
                <div className="flex items-center space-x-2">
                  <code className="bg-gray-100 dark:bg-gray-800 px-3 py-2 rounded text-sm font-mono flex-1">
                    {liveChatConfig.auth.appId}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(liveChatConfig.auth.appId)}
                  >
                    {copied ? 'Copied!' : 'Copy'}
                  </Button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  API Key
                </label>
                <div className="flex items-center space-x-2">
                  <code className="bg-gray-100 dark:bg-gray-800 px-3 py-2 rounded text-sm font-mono flex-1">
                    {showApiKey ? liveChatConfig.auth.apiKey : '••••••••••••••••••••••••••••••••'}
                  </code>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? 'Hide' : 'Show'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(liveChatConfig.auth.apiKey)}
                  >
                    Copy
                  </Button>
                </div>
              </div>

              {liveChatConfig.auth.isActive && (
                <div className="flex items-center space-x-2 text-sm">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                    Active
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    Live chat is enabled and accepting connections
                  </span>
                </div>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Widget Code
            </label>
            <div className="relative">
              <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg text-sm overflow-x-auto">
                <code>{widgetCode}</code>
              </pre>
              <Button
                onClick={() => copyToClipboard(widgetCode)}
                className="absolute top-2 right-2"
                size="sm"
                variant="outline"
              >
                {copied ? 'Copied!' : 'Copy Code'}
              </Button>
            </div>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Setup Instructions
        </h3>
        <div className="space-y-4 text-sm text-gray-600 dark:text-gray-400">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Integration Steps</h4>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Copy the widget code above</li>
              <li>Paste it into your website's HTML before the closing &lt;/body&gt; tag</li>
              <li>The widget will automatically connect using your App ID and API Key</li>
              <li>Customize the appearance through your live chat settings</li>
            </ol>
          </div>
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Security Notes</h4>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Your API Key is safe to use in client-side code for widget authentication</li>
              <li>The widget communicates directly with Cloud Instance servers</li>
              <li>All conversations are encrypted and securely stored</li>
            </ul>
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Additional Information
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Widget appearance and behavior are automatically configured based on your live chat settings.
          Use the setup wizard or configuration options to customize branding, colors, and chat behavior.
        </p>
      </Card>
    </div>
  );
}

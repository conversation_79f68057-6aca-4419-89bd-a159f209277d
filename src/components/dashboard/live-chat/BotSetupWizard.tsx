'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { Select } from '@/components/ui/Select';
import { Switch } from '@/components/ui/Switch';
import { Breadcrumb } from '@/components/ui/Breadcrumb';
import { HelpButton } from '@/components/ui/HelpButton';
import { createBot, getOrganizationBusinessHours } from '@/server/actions/bot-actions';
import { IBotPersonality, IBotSchedule, IBotSettings } from '@/types/bot';
import { updateBotConfig } from '@/server/actions/bot-actions';
import { logger } from '@/utils/logger';

interface BotSetupWizardProps {
  organizationId: string;
  onComplete: () => void;
  onCancel: () => void;
  editMode?: boolean;
  existingBotData?: {
    personality: IBotPersonality;
    schedule: IBotSchedule;
    settings: IBotSettings;
  };
}

interface WizardStep {
  id: string;
  title: string;
  description: string;
}

const wizardSteps: WizardStep[] = [
  {
    id: 'personality',
    title: 'Bot Personality',
    description: 'Configure your bot\'s name, tone, and basic responses'
  },
  {
    id: 'schedule',
    title: 'Availability Schedule',
    description: 'Set when your bot should be active'
  },
  {
    id: 'settings',
    title: 'Advanced Settings',
    description: 'Configure handoff conditions and response behavior'
  },
  {
    id: 'review',
    title: 'Review & Save',
    description: 'Review your configuration and save the bot'
  }
];

export const BotSetupWizard: React.FC<BotSetupWizardProps> = ({
  organizationId,
  onComplete,
  onCancel,
  editMode = false,
  existingBotData
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [organizationBusinessHours, setOrganizationBusinessHours] = useState<any>(null);
  const [loadingBusinessHours, setLoadingBusinessHours] = useState(false);

  // Bot configuration state with pre-population for edit mode
  const [personality, setPersonality] = useState<IBotPersonality>(
    existingBotData?.personality || {
      name: '',
      description: '',
      avatar: '',
      tone: 'friendly',
      language: 'en',
      welcomeMessage: 'Hello! I\'m here to help you. How can I assist you today?',
      fallbackMessage: 'I\'m sorry, I didn\'t understand that. Could you please rephrase your question?',
      handoffMessage: 'Let me connect you with one of our human agents who can better assist you.'
    }
  );

  const [schedule, setSchedule] = useState<IBotSchedule>(
    existingBotData?.schedule || {
      enabled: false,
      useOrganizationHours: true,
      timezone: 'UTC',
      schedule: {
        monday: { enabled: true, start: '09:00', end: '17:00' },
        tuesday: { enabled: true, start: '09:00', end: '17:00' },
        wednesday: { enabled: true, start: '09:00', end: '17:00' },
        thursday: { enabled: true, start: '09:00', end: '17:00' },
        friday: { enabled: true, start: '09:00', end: '17:00' },
        saturday: { enabled: false, start: '09:00', end: '17:00' },
        sunday: { enabled: false, start: '09:00', end: '17:00' }
      }
    }
  );

  const [settings, setSettings] = useState<IBotSettings>(
    existingBotData?.settings || {
      autoActivate: false,
      handoffConditions: {
        keywordTriggers: ['human', 'agent', 'representative', 'help'],
        sentimentThreshold: -0.5,
        maxFailedAttempts: 3,
        escalationKeywords: ['urgent', 'emergency', 'complaint', 'refund']
      },
      responseDelay: {
        enabled: true,
        minDelay: 1000,
        maxDelay: 3000
      },
      typingIndicator: true,
      collectUserInfo: true,
      requireHumanConfirmation: false,
      dataCollection: {
        timing: 'start',
        requiredFields: ['name', 'email'],
        message: 'To better assist you, could you please share some quick details?'
      }
    }
  );

  // Load organization business hours when component mounts
  useEffect(() => {
    const loadOrganizationBusinessHours = async () => {
      setLoadingBusinessHours(true);
      try {
        const result = await getOrganizationBusinessHours(organizationId);
        if (result.success) {
          setOrganizationBusinessHours(result.data);
        }
      } catch (error) {
        logger.error('Failed to load organization business hours:', error);
      } finally {
        setLoadingBusinessHours(false);
      }
    };

    loadOrganizationBusinessHours();
  }, [organizationId]);

  const handleNext = () => {
    if (currentStep < wizardSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsLoading(true);
    setError(null);

    try {
      let result;

      if (editMode) {
        // Import updateBotConfig for edit mode

        result = await updateBotConfig(organizationId, {
          personality,
          schedule,
          settings
        });
      } else {
        result = await createBot(organizationId, {
          personality,
          schedule,
          settings
        });
      }

      if (result.success) {
        onComplete();
      } else {
        setError((result as any).error || `Failed to ${editMode ? 'update' : 'create'} bot`);
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const renderPersonalityStep = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="botName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Bot Name *
          </Label>
          <Input
            id="botName"
            value={personality.name}
            onChange={(e) => setPersonality(prev => ({ ...prev, name: e.target.value }))}
            placeholder="e.g., Alex, Support Bot, Assistant"
            className="mt-1"
            required
          />
        </div>

        <div>
          <Label htmlFor="botTone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Tone
          </Label>
          <Select
            id="botTone"
            value={personality.tone}
            onChange={(value) => setPersonality(prev => ({ ...prev, tone: value as any }))}
            options={[
              { label: 'Professional', value: 'professional' },
              { label: 'Friendly', value: 'friendly' },
              { label: 'Casual', value: 'casual' },
              { label: 'Formal', value: 'formal' }
            ]}
            placeholder="Select bot tone"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="botDescription" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Description
        </Label>
        <Textarea
          id="botDescription"
          value={personality.description}
          onChange={(e) => setPersonality(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Brief description of your bot's purpose and capabilities"
          className="mt-1"
          rows={3}
        />
      </div>

      <div>
        <Label htmlFor="welcomeMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Welcome Message *
        </Label>
        <Textarea
          id="welcomeMessage"
          value={personality.welcomeMessage}
          onChange={(e) => setPersonality(prev => ({ ...prev, welcomeMessage: e.target.value }))}
          placeholder="The first message users will see"
          className="mt-1"
          rows={2}
          required
        />
      </div>

      <div>
        <Label htmlFor="fallbackMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Fallback Message *
        </Label>
        <Textarea
          id="fallbackMessage"
          value={personality.fallbackMessage}
          onChange={(e) => setPersonality(prev => ({ ...prev, fallbackMessage: e.target.value }))}
          placeholder="Message when bot doesn't understand user input"
          className="mt-1"
          rows={2}
          required
        />
      </div>

      <div>
        <Label htmlFor="handoffMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Handoff Message *
        </Label>
        <Textarea
          id="handoffMessage"
          value={personality.handoffMessage}
          onChange={(e) => setPersonality(prev => ({ ...prev, handoffMessage: e.target.value }))}
          placeholder="Message when transferring to human agent"
          className="mt-1"
          rows={2}
          required
        />
      </div>
    </div>
  );

  const renderScheduleStep = () => {
    const handleUseOrganizationHours = (useOrgHours: boolean) => {
      if (useOrgHours && organizationBusinessHours) {
        setSchedule(prev => ({
          ...prev,
          useOrganizationHours: true,
          timezone: organizationBusinessHours.timezone || 'UTC',
          schedule: organizationBusinessHours.schedule || prev.schedule
        }));
      } else {
        setSchedule(prev => ({
          ...prev,
          useOrganizationHours: false
        }));
      }
    };

    const validateTimeRange = (day: string, start: string, end: string) => {
      if (start && end && start >= end) {
        return 'End time must be after start time';
      }
      return null;
    };

    const dayNames = {
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    };

    return (
      <div className="space-y-6">
        {/* Schedule-based Activation Toggle */}
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Schedule-based Activation
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Automatically activate/deactivate bot based on business hours
            </p>
          </div>
          <Switch
            checked={schedule.enabled}
            onCheckedChange={(checked) => setSchedule(prev => ({ ...prev, enabled: checked }))}
          />
        </div>

        {schedule.enabled && (
          <div className="space-y-6">
            {/* Business Hours Mode Selection */}
            <div className="space-y-4">
              <h4 className="text-md font-medium text-gray-900 dark:text-white">
                Business Hours Configuration
              </h4>

              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="useOrgHours"
                    name="scheduleMode"
                    checked={schedule.useOrganizationHours}
                    onChange={() => handleUseOrganizationHours(true)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label htmlFor="useOrgHours" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Use Organization Hours
                    </Label>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Inherit business hours from your organization's support configuration
                    </p>
                    {loadingBusinessHours && (
                      <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                        Loading organization hours...
                      </p>
                    )}
                    {organizationBusinessHours && schedule.useOrganizationHours && (
                      <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <p className="text-xs text-blue-700 dark:text-blue-300">
                          <strong>Organization Hours:</strong> {organizationBusinessHours.timezone}
                        </p>
                        <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
                          {Object.entries(organizationBusinessHours.schedule || {}).map(([day, daySchedule]: [string, any]) => (
                            daySchedule.enabled && (
                              <span key={day} className="mr-3">
                                {day.charAt(0).toUpperCase() + day.slice(1)}: {daySchedule.start}-{daySchedule.end}
                              </span>
                            )
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <input
                    type="radio"
                    id="customSchedule"
                    name="scheduleMode"
                    checked={!schedule.useOrganizationHours}
                    onChange={() => handleUseOrganizationHours(false)}
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <Label htmlFor="customSchedule" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      Custom Schedule
                    </Label>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Set independent operating hours specific to this bot
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Timezone Selection */}
            <div>
              <Label htmlFor="timezone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Timezone
              </Label>
              <Select
                id="timezone"
                value={schedule.timezone}
                onChange={(value) => setSchedule(prev => ({ ...prev, timezone: value as string }))}
                options={[
                  { label: 'UTC', value: 'UTC' },
                  { label: 'Eastern Time (EST/EDT)', value: 'America/New_York' },
                  { label: 'Central Time (CST/CDT)', value: 'America/Chicago' },
                  { label: 'Mountain Time (MST/MDT)', value: 'America/Denver' },
                  { label: 'Pacific Time (PST/PDT)', value: 'America/Los_Angeles' },
                  { label: 'London (GMT/BST)', value: 'Europe/London' },
                  { label: 'Paris (CET/CEST)', value: 'Europe/Paris' },
                  { label: 'Tokyo (JST)', value: 'Asia/Tokyo' },
                  { label: 'Sydney (AEST/AEDT)', value: 'Australia/Sydney' },
                  { label: 'Mumbai (IST)', value: 'Asia/Kolkata' }
                ]}
                placeholder="Select timezone"
                filter
                disabled={schedule.useOrganizationHours}
              />
              {schedule.useOrganizationHours && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Timezone is inherited from organization settings
                </p>
              )}
            </div>

            {/* Weekly Schedule Configuration */}
            {!schedule.useOrganizationHours && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-md font-medium text-gray-900 dark:text-white">
                    Weekly Schedule
                  </h4>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const allEnabled = Object.values(schedule.schedule).every(day => day.enabled);
                        setSchedule(prev => ({
                          ...prev,
                          schedule: {
                            monday: { ...prev.schedule.monday, enabled: !allEnabled },
                            tuesday: { ...prev.schedule.tuesday, enabled: !allEnabled },
                            wednesday: { ...prev.schedule.wednesday, enabled: !allEnabled },
                            thursday: { ...prev.schedule.thursday, enabled: !allEnabled },
                            friday: { ...prev.schedule.friday, enabled: !allEnabled },
                            saturday: { ...prev.schedule.saturday, enabled: !allEnabled },
                            sunday: { ...prev.schedule.sunday, enabled: !allEnabled }
                          }
                        }));
                      }}
                    >
                      {Object.values(schedule.schedule).every(day => day.enabled) ? 'Disable All' : 'Enable All'}
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4">
                  {Object.entries(schedule.schedule).map(([day, daySchedule]) => {
                    const timeError = validateTimeRange(day, daySchedule.start, daySchedule.end);

                    return (
                      <div key={day} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Switch
                              checked={daySchedule.enabled}
                              onCheckedChange={(checked) =>
                                setSchedule(prev => ({
                                  ...prev,
                                  schedule: {
                                    ...prev.schedule,
                                    [day]: { ...daySchedule, enabled: checked }
                                  }
                                }))
                              }
                            />
                            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {dayNames[day as keyof typeof dayNames]}
                            </Label>
                          </div>
                          {!daySchedule.enabled && (
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              Bot will be inactive
                            </span>
                          )}
                        </div>

                        {daySchedule.enabled && (
                          <div className="flex items-center space-x-3">
                            <div className="flex-1">
                              <Label className="text-xs text-gray-600 dark:text-gray-400">Start Time</Label>
                              <Input
                                type="time"
                                value={daySchedule.start}
                                onChange={(e) =>
                                  setSchedule(prev => ({
                                    ...prev,
                                    schedule: {
                                      ...prev.schedule,
                                      [day]: { ...daySchedule, start: e.target.value }
                                    }
                                  }))
                                }
                                className={`mt-1 ${timeError ? 'border-red-300 dark:border-red-600' : ''}`}
                              />
                            </div>
                            <div className="flex items-center pt-6">
                              <span className="text-gray-500 dark:text-gray-400">to</span>
                            </div>
                            <div className="flex-1">
                              <Label className="text-xs text-gray-600 dark:text-gray-400">End Time</Label>
                              <Input
                                type="time"
                                value={daySchedule.end}
                                onChange={(e) =>
                                  setSchedule(prev => ({
                                    ...prev,
                                    schedule: {
                                      ...prev.schedule,
                                      [day]: { ...daySchedule, end: e.target.value }
                                    }
                                  }))
                                }
                                className={`mt-1 ${timeError ? 'border-red-300 dark:border-red-600' : ''}`}
                              />
                            </div>
                          </div>
                        )}

                        {timeError && (
                          <p className="text-xs text-red-600 dark:text-red-400 mt-2">
                            {timeError}
                          </p>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Schedule Summary */}
            <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <h5 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
                Schedule Summary
              </h5>
              <div className="text-sm text-green-700 dark:text-green-300">
                {schedule.useOrganizationHours ? (
                  <p>Bot will follow organization business hours ({schedule.timezone})</p>
                ) : (
                  <>
                    <p className="mb-1">Bot will be active during these hours ({schedule.timezone}):</p>
                    <div className="space-y-1">
                      {Object.entries(schedule.schedule).map(([day, daySchedule]) => (
                        <div key={day} className="flex justify-between">
                          <span className="capitalize">{day}:</span>
                          <span>
                            {daySchedule.enabled
                              ? `${daySchedule.start} - ${daySchedule.end}`
                              : 'Inactive'
                            }
                          </span>
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 24/7 Operation Notice */}
        {!schedule.enabled && (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  24/7 Operation
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  Your bot will operate continuously, providing support at all times regardless of business hours.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderSettingsStep = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Response Behavior
        </h3>

        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Typing Indicator
            </Label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Show typing indicator before bot responses
            </p>
          </div>
          <Switch
            checked={settings.typingIndicator}
            onCheckedChange={(checked) => setSettings(prev => ({ ...prev, typingIndicator: checked }))}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Response Delay
            </Label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Add natural delays to bot responses
            </p>
          </div>
          <Switch
            checked={settings.responseDelay.enabled}
            onCheckedChange={(checked) =>
              setSettings(prev => ({
                ...prev,
                responseDelay: { ...prev.responseDelay, enabled: checked }
              }))
            }
          />
        </div>

        {settings.responseDelay.enabled && (
          <div className="grid grid-cols-2 gap-4 ml-4">
            <div>
              <Label htmlFor="minDelay" className="text-xs text-gray-600 dark:text-gray-400">
                Min Delay (ms)
              </Label>
              <Input
                id="minDelay"
                type="number"
                value={settings.responseDelay.minDelay}
                onChange={(e) =>
                  setSettings(prev => ({
                    ...prev,
                    responseDelay: {
                      ...prev.responseDelay,
                      minDelay: parseInt(e.target.value) || 0
                    }
                  }))
                }
                min="0"
                max="5000"
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="maxDelay" className="text-xs text-gray-600 dark:text-gray-400">
                Max Delay (ms)
              </Label>
              <Input
                id="maxDelay"
                type="number"
                value={settings.responseDelay.maxDelay}
                onChange={(e) =>
                  setSettings(prev => ({
                    ...prev,
                    responseDelay: {
                      ...prev.responseDelay,
                      maxDelay: parseInt(e.target.value) || 0
                    }
                  }))
                }
                min="0"
                max="10000"
                className="mt-1"
              />
            </div>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Handoff Conditions
        </h3>

        <div>
          <Label htmlFor="maxFailedAttempts" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Max Failed Attempts
          </Label>
          <Input
            id="maxFailedAttempts"
            type="number"
            value={settings.handoffConditions.maxFailedAttempts}
            onChange={(e) =>
              setSettings(prev => ({
                ...prev,
                handoffConditions: {
                  ...prev.handoffConditions,
                  maxFailedAttempts: parseInt(e.target.value) || 1
                }
              }))
            }
            min="1"
            max="10"
            className="mt-1"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Number of failed attempts before offering human handoff
          </p>
        </div>

        <div>
          <Label htmlFor="handoffKeywords" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Handoff Keywords
          </Label>
          <Input
            id="handoffKeywords"
            value={settings.handoffConditions.keywordTriggers.join(', ')}
            onChange={(e) =>
              setSettings(prev => ({
                ...prev,
                handoffConditions: {
                  ...prev.handoffConditions,
                  keywordTriggers: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                }
              }))
            }
            placeholder="human, agent, representative, help"
            className="mt-1"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Comma-separated keywords that trigger human handoff
          </p>
        </div>

        <div>
          <Label htmlFor="escalationKeywords" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Escalation Keywords
          </Label>
          <Input
            id="escalationKeywords"
            value={settings.handoffConditions.escalationKeywords.join(', ')}
            onChange={(e) =>
              setSettings(prev => ({
                ...prev,
                handoffConditions: {
                  ...prev.handoffConditions,
                  escalationKeywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                }
              }))
            }
            placeholder="urgent, emergency, complaint, refund"
            className="mt-1"
          />
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            Keywords that immediately escalate to human agents
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Data Collection
        </h3>

        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Collect User Information
            </Label>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              Ask for user&#39;s name and email during conversations
            </p>
          </div>
          <Switch
            checked={settings.collectUserInfo}
            onCheckedChange={(checked) => setSettings(prev => ({ ...prev, collectUserInfo: checked }))}
          />
        </div>

        {settings.collectUserInfo && (
          <div className="ml-4 space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
              Collection Settings
            </h4>

            <div>
              <Label className="text-xs text-gray-600 dark:text-gray-400">
                When to Collect
              </Label>
              <Select
                value={settings.dataCollection?.timing || 'start'}
                onChange={(value) => {
                  setSettings((prev) => ({
                    ...prev,
                    dataCollection: {
                      timing: value as 'start' | 'handoff' | 'help' | 'after_value',
                      requiredFields: prev.dataCollection?.requiredFields || ['name', 'email'],
                      message: prev.dataCollection?.message || 'To better assist you, could you please share some quick details?'
                    }
                  }))
                }}
                options={[
                  { label: 'At conversation start', value: 'start' },
                  { label: 'Before handoff to human', value: 'handoff' },
                  { label: 'When user asks for help', value: 'help' },
                  { label: 'After bot provides value', value: 'after_value' }
                ]}
                placeholder="Select timing"
              />
            </div>

            <div>
              <Label className="text-xs text-gray-600 dark:text-gray-400">
                Required Fields
              </Label>
              <div className="mt-2 space-y-2">
                {['name', 'email'].map((field) => (
                  <div key={field} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`field_${field}`}
                      checked={settings.dataCollection?.requiredFields?.includes(field) || false}
                      onChange={(e) => {
                        const currentFields = settings.dataCollection?.requiredFields || [];
                        const newFields = e.target.checked
                          ? [...currentFields, field]
                          : currentFields.filter(f => f !== field);

                        setSettings(prev => ({
                          ...prev,
                          dataCollection: {
                            timing: prev.dataCollection?.timing || 'start',
                            message: prev.dataCollection?.message || 'To better assist you, could you please share some quick details?',
                            requiredFields: newFields
                          }
                        }));
                      }}
                      className="rounded border-gray-300 dark:border-gray-600"
                    />
                    <Label htmlFor={`field_${field}`} className="text-xs text-gray-700 dark:text-gray-300 capitalize">
                      {field}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="collectionMessage" className="text-xs text-gray-600 dark:text-gray-400">
                Collection Message
              </Label>
              <Textarea
                id="collectionMessage"
                value={settings.dataCollection?.message || 'To better assist you, could you please share some quick details?'}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  dataCollection: {
                    timing: prev.dataCollection?.timing || 'start',
                    requiredFields: prev.dataCollection?.requiredFields || ['name', 'email'],
                    message: e.target.value
                  }
                }))}
                placeholder="Message to show when collecting user information"
                className="mt-1"
                rows={2}
              />
            </div>
          </div>
        )}

        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                Bot Activation
              </h4>
              <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                Your bot will be created in inactive mode. You can activate it after creating conversation flows and testing the configuration.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderReviewStep = () => (
    <div className="space-y-6">
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Bot Configuration Summary
        </h3>

        <div className="space-y-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Personality</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Name: {personality.name || 'Not set'} | Tone: {personality.tone}
            </p>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Schedule</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {schedule.enabled ? `Enabled (${schedule.timezone})` : 'Always available'}
            </p>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Settings</h4>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Max failed attempts: {settings.handoffConditions.maxFailedAttempts} |
              Auto-activate: {settings.autoActivate ? 'Yes' : 'No'}
            </p>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
          Next Steps
        </h4>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• Your bot will be created with the configuration above</li>
          <li>• You'll need to create conversation flows to define bot responses</li>
          <li>• Test your bot before activating it for customers</li>
          <li>• Monitor bot performance and adjust settings as needed</li>
        </ul>
      </div>
    </div>
  );

  const currentStepData = wizardSteps[currentStep];
  const isLastStep = currentStep === wizardSteps.length - 1;
  const canProceed = currentStep === 0 ?
    personality.name && personality.welcomeMessage && personality.fallbackMessage && personality.handoffMessage :
    true;

  // Help documentation items
  const helpItems = [
    {
      title: 'Bot Personality Setup',
      content: 'Configure your bot\'s name, tone, and default messages to match your brand voice.',
      link: '/docs/live-chat-bots/personality'
    },
    {
      title: 'Schedule Configuration',
      content: 'Set up business hours to automatically activate/deactivate your bot.',
      link: '/docs/live-chat-bots/scheduling'
    },
    {
      title: 'Advanced Settings',
      content: 'Configure handoff conditions, response delays, and data collection preferences.',
      link: '/docs/live-chat-bots/settings'
    }
  ];

  // Breadcrumb navigation
  const breadcrumbItems = [
    { label: 'Dashboard', href: `/dashboard/organization/${organizationId}` },
    { label: 'Live Chat', href: `/dashboard/organization/${organizationId}/live-chat` },
    { label: 'Chatbots', href: `/dashboard/organization/${organizationId}/live-chat?tab=bots` },
    { label: 'Setup Bot', current: true }
  ];

  return (
    <div className="space-y-6">
      {/* Breadcrumb Navigation */}
      <Breadcrumb items={breadcrumbItems} />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {editMode ? 'Edit Bot Configuration' : 'Bot Setup Wizard'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {editMode
              ? 'Update your bot configuration and settings'
              : 'Configure your automated chatbot in a few simple steps'
            }
          </p>
        </div>
        <HelpButton items={helpItems} variant="text" />
      </div>

      <Card className="max-w-4xl mx-auto">
        <div className="p-6">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center">
            {wizardSteps.map((step, index) => (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                    index < currentStep
                      ? 'bg-indigo-600 border-indigo-600 text-white'
                      : index === currentStep
                      ? 'bg-indigo-600 border-indigo-600 text-white ring-4 ring-indigo-100 dark:ring-indigo-900/20'
                      : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400'
                  }`}>
                    {index < currentStep ? (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <span className="text-sm font-semibold">{index + 1}</span>
                    )}
                  </div>
                  <div className="mt-3 text-center max-w-[120px]">
                    <p className={`text-sm font-medium transition-colors duration-200 ${
                      index <= currentStep
                        ? 'text-indigo-600 dark:text-indigo-400'
                        : 'text-gray-500 dark:text-gray-400'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 leading-tight">
                      {step.description}
                    </p>
                  </div>
                </div>
                {index < wizardSteps.length - 1 && (
                  <div className="flex items-center mx-4 mb-8">
                    <div className={`h-0.5 w-16 transition-colors duration-200 ${
                      index < currentStep ? 'bg-indigo-600' : 'bg-gray-300 dark:bg-gray-600'
                    }`} />
                  </div>
                )}
              </React.Fragment>
            ))}
          </div>
          <div className="mt-6 text-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {currentStepData.title}
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {currentStepData.description}
            </p>
          </div>
        </div>

        {/* Step Content */}
        <div className="mb-8">
          {currentStep === 0 && renderPersonalityStep()}
          {currentStep === 1 && renderScheduleStep()}
          {currentStep === 2 && renderSettingsStep()}
          {currentStep === 3 && renderReviewStep()}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between">
          <div>
            {currentStep > 0 && (
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={isLoading}
              >
                Previous
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>

            {isLastStep ? (
              <Button
                onClick={handleSubmit}
                disabled={isLoading || !canProceed}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                {isLoading
                  ? `${editMode ? 'Updating' : 'Creating'} Bot...`
                  : `${editMode ? 'Update' : 'Create'} Bot`
                }
              </Button>
            ) : (
              <Button
                onClick={handleNext}
                disabled={!canProceed}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                Next
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
    </div>
  );
};

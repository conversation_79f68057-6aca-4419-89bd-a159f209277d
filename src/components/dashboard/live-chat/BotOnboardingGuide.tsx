'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface BotOnboardingGuideProps {
  onStartSetup: () => void;
  onDismiss?: () => void;
  className?: string;
}

export const BotOnboardingGuide: React.FC<BotOnboardingGuideProps> = ({
  onStartSetup,
  onDismiss,
  className = ''
}) => {
  const features = [
    {
      icon: '🤖',
      title: 'Automated Responses',
      description: 'Handle common customer questions automatically with intelligent responses'
    },
    {
      icon: '⚡',
      title: 'Instant Support',
      description: 'Provide 24/7 customer support even when your team is offline'
    },
    {
      icon: '🎯',
      title: 'Smart Handoffs',
      description: 'Seamlessly transfer complex queries to human agents when needed'
    },
    {
      icon: '📊',
      title: 'Performance Analytics',
      description: 'Track bot performance and optimize customer interactions'
    }
  ];

  const steps = [
    {
      number: 1,
      title: 'Configure Bot Personality',
      description: 'Set up your bot\'s name, tone, and default messages'
    },
    {
      number: 2,
      title: 'Create Conversation Flows',
      description: 'Design how your bot responds to different customer inquiries'
    },
    {
      number: 3,
      title: 'Test & Activate',
      description: 'Test your bot and activate it for live customer interactions'
    }
  ];

  return (
    <Card className={`p-8 bg-gradient-to-br from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-indigo-200 dark:border-indigo-800 ${className}`}>
      <div className="text-center mb-8">
        <div className="w-16 h-16 mx-auto mb-4 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8 text-indigo-600 dark:text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          Automate Your Customer Support
        </h2>
        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Set up an intelligent chatbot to handle customer inquiries automatically, 
          provide instant responses, and seamlessly hand off complex issues to your team.
        </p>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start space-x-3">
            <div className="text-2xl">{feature.icon}</div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-1">
                {feature.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {feature.description}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Setup Steps */}
      <div className="mb-8">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
          Get Started in 3 Simple Steps
        </h3>
        <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center text-center max-w-xs">
              <div className="w-8 h-8 bg-indigo-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                {step.number}
              </div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                {step.title}
              </h4>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {step.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-4">
        <Button
          onClick={onStartSetup}
          className="bg-indigo-600 hover:bg-indigo-700 text-white px-8 py-3 text-lg font-medium"
          size="lg"
        >
          Create Your First Bot
        </Button>
        <Button
          variant="outline"
          onClick={() => window.open('/docs/live-chat-bots', '_blank')}
          className="border-indigo-300 text-indigo-600 hover:bg-indigo-50 dark:border-indigo-700 dark:text-indigo-400 dark:hover:bg-indigo-900/20"
        >
          View Documentation
        </Button>
      </div>

      {/* Dismiss Option */}
      {onDismiss && (
        <div className="text-center mt-6">
          <button
            onClick={onDismiss}
            className="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 underline"
          >
            Maybe later
          </button>
        </div>
      )}

      {/* Benefits Banner */}
      <div className="mt-8 p-4 bg-white/50 dark:bg-gray-800/50 rounded-lg border border-indigo-200 dark:border-indigo-800">
        <div className="flex items-center justify-center space-x-6 text-sm">
          <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
            <span>Reduce response time</span>
          </div>
          <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span>24/7 availability</span>
          </div>
          <div className="flex items-center space-x-2 text-purple-600 dark:text-purple-400">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <span>Improve efficiency</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

BotOnboardingGuide.displayName = 'BotOnboardingGuide';

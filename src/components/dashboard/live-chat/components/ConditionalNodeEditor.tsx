'use client';

import React, { useState } from 'react';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { BotResponseType, IFlowVariable } from '@/types/bot';

// Component-specific constants
const CONDITION_OPERATORS = [
  { label: 'Equals', value: 'equals' },
  { label: 'Not Equals', value: 'not_equals' },
  { label: 'Contains', value: 'contains' },
  { label: 'Does Not Contain', value: 'not_contains' },
  { label: 'Greater Than', value: 'greater_than' },
  { label: 'Less Than', value: 'less_than' },
  { label: 'Greater Than or Equal', value: 'greater_than_equal' },
  { label: 'Less Than or Equal', value: 'less_than_equal' },
  { label: 'Is Empty', value: 'is_empty' },
  { label: 'Is Not Empty', value: 'is_not_empty' }
];

const MAX_CONDITION_VALUE_LENGTH = 200;
const HELP_TEXT = "Conditional nodes evaluate variables and route the conversation based on the result. Configure the condition below.";

// Component-specific interfaces
interface ConditionalValidation {
  isValid: boolean;
  hasVariableError: boolean;
  hasOperatorError: boolean;
  hasValueError: boolean;
  message: string;
}

interface ConditionalNodeContent {
  type: BotResponseType.CONDITIONAL;
  conditionVariable?: string;
  conditionOperator?: string;
  conditionValue?: string;
}

/**
 * ConditionalNodeEditor Component
 * 
 * Self-contained component that handles conditional node editing including:
 * - Variable selection for condition evaluation
 * - Operator selection with appropriate options
 * - Condition value input with validation
 * - Real-time condition validation and feedback
 * - Conditional logic guidance and examples
 * 
 * Encapsulates its own validation logic, condition management, and state.
 */
export const ConditionalNodeEditor: React.FC = () => {
  const { currentNode, updateNode, variables } = useNodeEditorStore();

  // Internal state for UI concerns
  const [showExamples, setShowExamples] = useState(false);

  if (!currentNode || currentNode.type !== BotResponseType.CONDITIONAL) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateConditional = (): ConditionalValidation => {
    const content = currentNode.content as ConditionalNodeContent;
    const variable = content.conditionVariable;
    const operator = content.conditionOperator;
    const value = content.conditionValue;

    const hasVariableError = !variable;
    const hasOperatorError = !operator;
    const hasValueError = needsValue(operator) && (!value || value.trim() === '');

    let message = '';
    if (hasVariableError) {
      message = 'Please select a variable to evaluate';
    } else if (hasOperatorError) {
      message = 'Please select a condition operator';
    } else if (hasValueError) {
      message = 'Please enter a value to compare against';
    }

    return {
      isValid: !hasVariableError && !hasOperatorError && !hasValueError,
      hasVariableError,
      hasOperatorError,
      hasValueError,
      message
    };
  };

  // Internal helper to check if operator needs a value
  const needsValue = (operator?: string): boolean => {
    return operator !== 'is_empty' && operator !== 'is_not_empty';
  };

  // Internal helper to get current conditional content
  const getCurrentConditionalContent = (): ConditionalNodeContent => {
    return currentNode.content as ConditionalNodeContent;
  };

  // Internal helper to get operator description
  const getOperatorDescription = (operator: string): string => {
    const descriptions: Record<string, string> = {
      'equals': 'Variable value exactly matches the specified value',
      'not_equals': 'Variable value does not match the specified value',
      'contains': 'Variable value contains the specified text (case-insensitive)',
      'not_contains': 'Variable value does not contain the specified text',
      'greater_than': 'Variable value is numerically greater than the specified number',
      'less_than': 'Variable value is numerically less than the specified number',
      'greater_than_equal': 'Variable value is greater than or equal to the specified number',
      'less_than_equal': 'Variable value is less than or equal to the specified number',
      'is_empty': 'Variable has no value or is empty',
      'is_not_empty': 'Variable has a value and is not empty'
    };
    return descriptions[operator] || '';
  };

  // Internal helper to get example values for operator
  const getExampleValues = (operator: string): string[] => {
    const examples: Record<string, string[]> = {
      'equals': ['"yes"', '"premium_user"', '"completed"'],
      'not_equals': ['"no"', '"basic_user"', '"pending"'],
      'contains': ['"@gmail.com"', '"urgent"', '"VIP"'],
      'not_contains': ['"spam"', '"test"', '"demo"'],
      'greater_than': ['18', '100', '1000'],
      'less_than': ['65', '50', '10'],
      'greater_than_equal': ['21', '0', '100'],
      'less_than_equal': ['120', '999', '5'],
      'is_empty': [],
      'is_not_empty': []
    };
    return examples[operator] || [];
  };

  // Internal event handlers
  const handleVariableChange = (value: string | number | null) => {
    updateNode({
      content: {
        ...currentNode.content,
        conditionVariable: String(value) || undefined
      } as ConditionalNodeContent
    });
  };

  const handleOperatorChange = (value: string | number | null) => {
    const content = { ...currentNode.content } as ConditionalNodeContent;
    content.conditionOperator = String(value) || undefined;

    // Clear value if operator doesn't need one
    if (!needsValue(String(value))) {
      content.conditionValue = undefined;
    }

    updateNode({ content });
  };

  const handleValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateNode({
      content: {
        ...currentNode.content,
        conditionValue: e.target.value || undefined
      } as ConditionalNodeContent
    });
  };

  // Internal helper to render condition examples
  const renderConditionExamples = () => {
    if (!showExamples) return null;

    return (
      <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
          📚 Condition Examples
        </h5>
        <div className="space-y-3 text-xs text-blue-800 dark:text-blue-200">
          <div>
            <p className="font-medium mb-1">Text Conditions:</p>
            <ul className="space-y-1 ml-4">
              <li>• <code>customer_type equals "premium"</code> - Check if user is premium</li>
              <li>• <code>email contains "@company.com"</code> - Check if corporate email</li>
              <li>• <code>status not_equals "banned"</code> - Ensure user is not banned</li>
            </ul>
          </div>
          <div>
            <p className="font-medium mb-1">Numeric Conditions:</p>
            <ul className="space-y-1 ml-4">
              <li>• <code>age greater_than 18</code> - Check if user is adult</li>
              <li>• <code>score less_than_equal 100</code> - Check if score is valid</li>
              <li>• <code>balance greater_than_equal 0</code> - Check if positive balance</li>
            </ul>
          </div>
          <div>
            <p className="font-medium mb-1">Existence Conditions:</p>
            <ul className="space-y-1 ml-4">
              <li>• <code>phone_number is_not_empty</code> - Check if phone provided</li>
              <li>• <code>optional_field is_empty</code> - Check if field not filled</li>
            </ul>
          </div>
        </div>
      </div>
    );
  };

  const content = getCurrentConditionalContent();
  const validation = validateConditional();
  const selectedOperator = content.conditionOperator;
  const operatorNeedsValue = needsValue(selectedOperator);

  return (
    <div className="space-y-4">
      {/* Conditional Node Information */}
      <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
        <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-3 flex items-center">
          🔀 Conditional Logic Configuration
        </h4>
        <p className="text-xs text-purple-700 dark:text-purple-300 mb-3">
          This node evaluates a condition and routes the conversation based on the result. Configure the condition below.
        </p>
        <div className="flex items-center justify-between">
          <span className="text-xs text-purple-600 dark:text-purple-400">
            True/False paths will be available after configuration
          </span>
          <button
            onClick={() => setShowExamples(!showExamples)}
            className="text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 underline"
          >
            {showExamples ? 'Hide' : 'Show'} Examples
          </button>
        </div>
      </div>

      {/* Condition Configuration */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Condition Configuration
        </h4>

        {/* Variable Selection */}
        <div>
          <Label htmlFor="conditionVariable" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Variable to Evaluate
          </Label>
          <Select
            id="conditionVariable"
            value={content.conditionVariable || ''}
            onChange={handleVariableChange}
            options={[
              { label: 'Select a variable...', value: '' },
              { label: 'customer_name (system)', value: 'customer_name' },
              { label: 'customer_email (system)', value: 'customer_email' },
              ...variables.map((variable: IFlowVariable) => ({
                label: `${variable.name} (${variable.type})`,
                value: variable.name
              }))
            ]}
            placeholder="Choose variable to evaluate"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Select the variable whose value will be evaluated in the condition.
          </p>
        </div>

        {/* Operator Selection */}
        <div>
          <Label htmlFor="conditionOperator" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Condition Operator
          </Label>
          <Select
            id="conditionOperator"
            value={content.conditionOperator || ''}
            onChange={handleOperatorChange}
            options={[
              { label: 'Select an operator...', value: '' },
              ...CONDITION_OPERATORS
            ]}
            placeholder="Choose comparison operator"
          />
          {selectedOperator && (
            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
              {getOperatorDescription(selectedOperator)}
            </p>
          )}
        </div>

        {/* Condition Value */}
        {operatorNeedsValue && (
          <div>
            <Label htmlFor="conditionValue" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Comparison Value
            </Label>
            <Input
              id="conditionValue"
              value={content.conditionValue || ''}
              onChange={handleValueChange}
              placeholder="Enter value to compare against..."
              className={`mt-1 ${validation.hasValueError ? 'border-red-500' : ''}`}
              maxLength={MAX_CONDITION_VALUE_LENGTH}
            />
            {selectedOperator && getExampleValues(selectedOperator).length > 0 && (
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Examples: {getExampleValues(selectedOperator).join(', ')}
              </p>
            )}
          </div>
        )}

        {/* Validation Error */}
        {!validation.isValid && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
            <div className="flex items-start gap-2">
              <span className="text-red-600 dark:text-red-400 text-sm">⚠️</span>
              <div>
                <p className="text-xs font-medium text-red-800 dark:text-red-200">
                  Condition Configuration Incomplete
                </p>
                <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                  {validation.message}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Condition Preview */}
        {validation.isValid && (
          <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
            <h5 className="text-xs font-medium text-green-900 dark:text-green-100 mb-2">
              ✅ Condition Preview
            </h5>
            <div className="text-xs text-green-800 dark:text-green-200">
              <code className="bg-green-100 dark:bg-green-800 px-2 py-1 rounded">
                {content.conditionVariable} {content.conditionOperator} {operatorNeedsValue ? content.conditionValue : ''}
              </code>
            </div>
            <p className="text-xs text-green-700 dark:text-green-300 mt-2">
              This condition will evaluate to <strong>true</strong> or <strong>false</strong> and route the conversation accordingly.
            </p>
          </div>
        )}
      </div>

      {/* Condition Examples */}
      {renderConditionExamples()}

      {/* How Conditionals Work */}
      <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
          🔄 How Conditional Nodes Work
        </h5>
        <div className="space-y-2 text-xs text-gray-700 dark:text-gray-300">
          <div className="flex items-start gap-2">
            <span className="text-green-600 dark:text-green-400">✓</span>
            <p><strong>True Path:</strong> When condition evaluates to true, conversation follows the "true" connection</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-red-600 dark:text-red-400">✗</span>
            <p><strong>False Path:</strong> When condition evaluates to false, conversation follows the "false" connection</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-600 dark:text-blue-400">ℹ</span>
            <p><strong>Variables:</strong> Conditions evaluate the current value of the selected variable</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-purple-600 dark:text-purple-400">⚡</span>
            <p><strong>Real-time:</strong> Evaluation happens when the conversation reaches this node</p>
          </div>
        </div>
      </div>

      {/* Development Analysis */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 Conditional Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Variable:</strong> {content.conditionVariable || 'Not selected'}</p>
            <p><strong>Operator:</strong> {content.conditionOperator || 'Not selected'}</p>
            <p><strong>Value:</strong> {operatorNeedsValue ? (content.conditionValue || 'Not set') : 'Not required'}</p>
            <p><strong>Is Valid:</strong> {validation.isValid ? 'Yes' : 'No'}</p>
            <p><strong>Examples Shown:</strong> {showExamples ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

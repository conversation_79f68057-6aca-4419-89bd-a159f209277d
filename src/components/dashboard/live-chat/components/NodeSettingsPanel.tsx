'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Switch } from '@/components/ui/Switch';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { BotResponseType } from '@/types/bot';

// Component-specific constants
const MIN_DELAY = 0;
const MAX_DELAY = 10000;
const DEFAULT_DELAY = 1000;

// Component-specific interfaces
interface DelayValidation {
  isValid: boolean;
  value: number;
  message: string;
}

/**
 * NodeSettingsPanel Component
 * 
 * Self-contained component that handles node settings including:
 * - Response delay input with validation (0-10000ms)
 * - Start node toggle
 * - End node toggle
 * - Node documentation guide
 * 
 * Encapsulates its own validation logic and state management.
 */
export const NodeSettingsPanel: React.FC = () => {
  const { currentNode, updateNode } = useNodeEditorStore();

  // Internal state for UI concerns
  const [delayInputValue, setDelayInputValue] = useState<string>('');
  const [showDocumentation, setShowDocumentation] = useState(false);

  if (!currentNode) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateDelay = (value: string): DelayValidation => {
    const numValue = parseInt(value, 10);
    
    if (isNaN(numValue)) {
      return {
        isValid: false,
        value: DEFAULT_DELAY,
        message: 'Delay must be a valid number'
      };
    }

    if (numValue < MIN_DELAY) {
      return {
        isValid: false,
        value: MIN_DELAY,
        message: `Delay cannot be less than ${MIN_DELAY}ms`
      };
    }

    if (numValue > MAX_DELAY) {
      return {
        isValid: false,
        value: MAX_DELAY,
        message: `Delay cannot exceed ${MAX_DELAY}ms (10 seconds)`
      };
    }

    return {
      isValid: true,
      value: numValue,
      message: ''
    };
  };

  // Internal helper to get current delay value
  const getCurrentDelay = (): number => {
    return currentNode.content?.delay || DEFAULT_DELAY;
  };

  // Internal helper to format delay for display
  const formatDelayForDisplay = (delay: number): string => {
    if (delay >= 1000) {
      return `${delay / 1000}s`;
    }
    return `${delay}ms`;
  };

  // Internal event handlers
  const handleDelayChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDelayInputValue(value);

    const validation = validateDelay(value);
    if (validation.isValid) {
      updateNode({
        content: {
          ...currentNode.content,
          delay: validation.value
        }
      });
    }
  };

  const handleDelayBlur = () => {
    const validation = validateDelay(delayInputValue);
    if (!validation.isValid) {
      // Reset to valid value on blur if invalid
      setDelayInputValue(validation.value.toString());
      updateNode({
        content: {
          ...currentNode.content,
          delay: validation.value
        }
      });
    }
  };

  const handleStartNodeToggle = (checked: boolean) => {
    updateNode({ isStartNode: checked });
  };

  const handleEndNodeToggle = (checked: boolean) => {
    updateNode({ isEndNode: checked });
  };

  // Internal helper to determine if node supports delay
  const supportsDelay = (): boolean => {
    const nodeType = currentNode.type;
    return nodeType !== BotResponseType.CONDITIONAL; // Most nodes support delay except conditional
  };

  // Internal helper to get node type display name
  const getNodeTypeDisplayName = (): string => {
    switch (currentNode.type) {
      case BotResponseType.TEXT:
        return 'Text Message';
      case BotResponseType.BUTTONS:
        return 'Button Options';
      case BotResponseType.QUICK_REPLY:
        return 'Quick Replies';
      case BotResponseType.API_CALL:
        return 'API Call';
      case BotResponseType.FORM:
        return 'Form Input';
      case BotResponseType.CONDITIONAL:
        return 'Conditional Logic';
      case BotResponseType.HANDOFF:
        return 'Agent Handoff';
      default:
        return 'Unknown';
    }
  };

  const currentDelay = getCurrentDelay();
  const delayValidation = validateDelay(delayInputValue || currentDelay.toString());

  return (
    <div className="space-y-4">
      {/* Response Delay Section */}
      {supportsDelay() && (
        <div>
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="responseDelay" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Response Delay
            </Label>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Current: {formatDelayForDisplay(currentDelay)}
            </span>
          </div>
          
          <Input
            id="responseDelay"
            type="number"
            value={delayInputValue || currentDelay.toString()}
            onChange={handleDelayChange}
            onBlur={handleDelayBlur}
            onFocus={() => setDelayInputValue(currentDelay.toString())}
            placeholder="1000"
            min={MIN_DELAY}
            max={MAX_DELAY}
            className={`mt-1 ${
              !delayValidation.isValid
                ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
                : ''
            }`}
          />
          
          {!delayValidation.isValid && delayInputValue && (
            <p className="text-xs text-red-500 dark:text-red-400 mt-1">
              {delayValidation.message}
            </p>
          )}
          
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Delay in milliseconds (0-10000ms). Controls how long to wait before showing this response.
          </p>
        </div>
      )}

      {/* Node Flow Settings */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Flow Settings
        </h4>
        
        {/* Start Node Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm text-gray-700 dark:text-gray-300">
              Start Node
            </Label>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              First node in the conversation flow
            </p>
          </div>
          <Switch
            checked={currentNode.isStartNode || false}
            onCheckedChange={handleStartNodeToggle}
          />
        </div>

        {/* End Node Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <Label className="text-sm text-gray-700 dark:text-gray-300">
              End Node
            </Label>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Final node that ends the conversation
            </p>
          </div>
          <Switch
            checked={currentNode.isEndNode || false}
            onCheckedChange={handleEndNodeToggle}
          />
        </div>
      </div>

      {/* Node Information */}
      <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="flex items-center justify-between mb-2">
          <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100">
            Node Information
          </h5>
          <button
            onClick={() => setShowDocumentation(!showDocumentation)}
            className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
          >
            {showDocumentation ? 'Hide' : 'Show'} Guide
          </button>
        </div>
        
        <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
          <p><strong>Type:</strong> {getNodeTypeDisplayName()}</p>
          <p><strong>ID:</strong> {currentNode.id}</p>
          {supportsDelay() && (
            <p><strong>Delay:</strong> {formatDelayForDisplay(currentDelay)}</p>
          )}
        </div>

        {showDocumentation && (
          <div className="mt-3 pt-3 border-t border-blue-200 dark:border-blue-700">
            <div className="text-xs text-blue-700 dark:text-blue-300 space-y-2">
              <p><strong>Start Node:</strong> The first node users encounter. Only one start node per flow.</p>
              <p><strong>End Node:</strong> Concludes the conversation. Can have multiple end nodes.</p>
              {supportsDelay() && (
                <p><strong>Response Delay:</strong> Simulates typing time. Longer delays for complex responses feel more natural.</p>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

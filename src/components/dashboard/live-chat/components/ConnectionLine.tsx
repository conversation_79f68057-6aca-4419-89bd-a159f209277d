'use client';

import React, { useState } from 'react';
import { IFlowConnection } from '@/types/bot';

interface ConnectionLineProps {
  connection: IFlowConnection;
  source: { x: number; y: number };
  target: { x: number; y: number };
  onDelete: () => void;
}

export const ConnectionLine: React.FC<ConnectionLineProps> = ({ 
  connection, 
  source, 
  target, 
  onDelete 
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Calculate connection points (right side of source, left side of target)
  const sourceX = source.x + 192; // Node width
  const sourceY = source.y + 40;  // Half node height
  const targetX = target.x;
  const targetY = target.y + 40;

  // Calculate control points for smooth curve
  const controlOffset = Math.abs(targetX - sourceX) * 0.5;
  const controlX1 = sourceX + controlOffset;
  const controlX2 = targetX - controlOffset;

  // Create curved path
  const pathData = `M ${sourceX} ${sourceY} C ${controlX1} ${sourceY} ${controlX2} ${targetY} ${targetX} ${targetY}`;

  // Calculate midpoint for label
  const midX = (sourceX + targetX) / 2;
  const midY = (sourceY + targetY) / 2;

  return (
    <g>
      {/* Invisible wider path for easier hover detection */}
      <path
        d={pathData}
        stroke="transparent"
        strokeWidth="12"
        fill="none"
        className="cursor-pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={(e) => {
          e.stopPropagation();
          if (isHovered) {
            onDelete();
          }
        }}
        style={{ pointerEvents: 'stroke' }}
      />

      {/* Visible connection line */}
      <path
        d={pathData}
        stroke={isHovered ? "#ef4444" : "#6366f1"}
        strokeWidth={isHovered ? "3" : "2"}
        fill="none"
        markerEnd="url(#arrowhead)"
        className="transition-all duration-200"
        style={{ pointerEvents: 'none' }}
      />

      {/* Connection label */}
      {connection.label && (
        <g>
          <rect
            x={midX - 30}
            y={midY - 10}
            width="60"
            height="20"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            rx="4"
            className="drop-shadow-sm"
          />
          <text
            x={midX}
            y={midY + 4}
            textAnchor="middle"
            className="text-xs fill-gray-700 font-medium"
            style={{ pointerEvents: 'none' }}
          >
            {connection.label}
          </text>
        </g>
      )}

      {/* Delete button when hovered */}
      {isHovered && (
        <g>
          <circle
            cx={midX}
            cy={midY - 15}
            r="8"
            fill="#ef4444"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          />
          <text
            x={midX}
            y={midY - 11}
            textAnchor="middle"
            className="text-xs fill-white font-bold cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          >
            ×
          </text>
        </g>
      )}
    </g>
  );
};

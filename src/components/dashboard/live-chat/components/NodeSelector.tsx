import React, { useState } from 'react';
import { Eye, Download, Upload } from 'lucide-react';
import {Button} from "@components/ui/Button";
import {onAddNode} from "@components/dashboard/live-chat/components/VisualDesigner";
import { useToast } from '@/components/ui/Toast';
import { ClientFlowData } from '@/types/bot';

// Define the BotResponseType enum
enum BotResponseType {
  TEXT = 'text',
  BUTTONS = 'buttons',
  QUICK_REPLY = 'quick_reply',
  FORM = 'form',
  CONDITIONAL = 'conditional',
  HANDOFF = 'handoff',
  API_CALL = 'api_call'
}

// Import the existing API configuration interface
interface IApiConfig {
  id?: string;
  name: string;
  url: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers: Record<string, string>;
  credentialId?: string;
  requestSchema?: Record<string, any>;
  responseSchema?: Record<string, any>;
  timeout: number;
  retries: number;
  transformRequest?: string; // JavaScript code for request transformation
  transformResponse?: string; // JavaScript code for response transformation
  isActive?: boolean;
}

// Define node configuration interface
interface NodeConfig {
  type: BotResponseType;
  icon: string;
  title: string;
  description: string;
  category: 'response' | 'logic' | 'action';
}

// Define component props
interface NodeSelectorProps {
  onAddNode: onAddNode;
  configuredApis: IApiConfig[];
  setShowJsonPreview: (show: boolean) => void;
  handleExportFlow: () => void;
  onFlowImport?: (flowData: ClientFlowData) => void;
}

const NodeSelector: React.FC<NodeSelectorProps> = ({
                                                     onAddNode,
                                                     configuredApis,
                                                     setShowJsonPreview,
                                                     handleExportFlow,
                                                     onFlowImport
                                                   }) => {
  const { error } = useToast();
  const [isImporting, setIsImporting] = useState(false);

  // Flow import handler
  const handleFlowImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      setIsImporting(true);
      try {
        const text = await file.text();
        const importedData = JSON.parse(text);

        // Validate the imported flow structure
        const validationResult = validateFlowData(importedData);
        if (!validationResult.isValid) {
          error('Import Error', validationResult.error || 'Invalid flow file format');
          return;
        }

        // Extract flow data from the imported structure
        const flowData = extractFlowData(importedData);

        // Call the parent component's import handler
        if (onFlowImport) {
          onFlowImport(flowData);
        } else {
          error('Import Error', 'Import functionality not available');
        }
      } catch (err) {
        error('Import Error', 'Failed to parse flow file. Please ensure it\'s a valid JSON file.');
      } finally {
        setIsImporting(false);
      }
    };
    input.click();
  };

  // Validate imported flow data structure
  const validateFlowData = (data: any): { isValid: boolean; error?: string } => {
    if (!data || typeof data !== 'object') {
      return { isValid: false, error: 'Invalid file format' };
    }

    // Check if it's a direct flow object or wrapped in metadata
    const flowData = data.flow || data;

    // Check if it's an exported flow with metadata wrapper
    if (data.metadata && data.flow) {
      // This is a properly exported flow with metadata
      if (!data.flow.name || typeof data.flow.name !== 'string') {
        return { isValid: false, error: 'Flow name is required in exported flow' };
      }
    } else {
      // This might be a direct flow object or legacy format
      // Be more flexible with name requirement
      if (flowData.name && typeof flowData.name !== 'string') {
        return { isValid: false, error: 'Flow name must be a string if provided' };
      }
    }

    // Nodes array is required
    if (!Array.isArray(flowData.nodes)) {
      return { isValid: false, error: 'Flow must contain nodes array' };
    }

    // Connections array is required (can be empty)
    if (!Array.isArray(flowData.connections)) {
      return { isValid: false, error: 'Flow must contain connections array' };
    }

    // Validate node structure - be more flexible
    for (const node of flowData.nodes) {
      if (!node.id) {
        return { isValid: false, error: 'Invalid node structure: missing node id' };
      }
      if (!node.type) {
        return { isValid: false, error: 'Invalid node structure: missing node type' };
      }
      // Name is not strictly required - we can generate it
    }

    // Validate connection structure - be more flexible
    for (const connection of flowData.connections) {
      if (!connection.sourceNodeId || !connection.targetNodeId) {
        return { isValid: false, error: 'Invalid connection structure: missing sourceNodeId or targetNodeId' };
      }
      // ID can be generated if missing
    }

    return { isValid: true };
  };

  // Extract flow data from imported structure
  const extractFlowData = (importedData: any): ClientFlowData => {
    // Handle both direct flow objects and wrapped metadata format
    const flowData = importedData.flow || importedData;

    // Generate a timestamp-based name if none provided
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[T:]/g, '-');
    const defaultName = `Imported Flow ${timestamp}`;

    // Process nodes to ensure they have proper structure
    const processedNodes = (flowData.nodes || []).map((node: any) => ({
      ...node,
      // Generate name if missing
      name: node.name || `${node.type || 'Node'} ${node.id?.slice(-4) || Math.random().toString(36).slice(-4)}`,
      // Ensure proper content structure
      content: {
        type: node.type,
        ...node.content
      }
    }));

    // Process connections to ensure they have IDs
    const processedConnections = (flowData.connections || []).map((connection: any) => ({
      ...connection,
      id: connection.id || crypto.randomUUID()
    }));

    return {
      name: flowData.name || defaultName,
      description: flowData.description || `Imported from JSON file on ${new Date().toLocaleDateString()}`,
      nodes: processedNodes,
      connections: processedConnections,
      variables: flowData.variables || [],
      integrations: flowData.integrations || [],
      isActive: false
    };
  };

  // Define node configurations
  const nodeConfigs: NodeConfig[] = [
    {
      type: BotResponseType.TEXT,
      icon: '💬',
      title: 'Text Response',
      description: 'Send a text message',
      category: 'response'
    },
    {
      type: BotResponseType.BUTTONS,
      icon: '🔘',
      title: 'Button Options',
      description: 'Configurable multiple choice buttons',
      category: 'response'
    },
    {
      type: BotResponseType.QUICK_REPLY,
      icon: '⚡',
      title: 'Quick Replies',
      description: 'Configurable fast response options',
      category: 'response'
    },
    {
      type: BotResponseType.FORM,
      icon: '📝',
      title: 'Form Input',
      description: 'Collect user information',
      category: 'response'
    },
    {
      type: BotResponseType.CONDITIONAL,
      icon: '🔀',
      title: 'Conditional Logic',
      description: 'Decision branches',
      category: 'logic'
    },
    {
      type: BotResponseType.HANDOFF,
      icon: '👤',
      title: 'Human Handoff',
      description: 'Transfer to agent',
      category: 'action'
    }
  ];

  // Group nodes by category
  const responseNodes = nodeConfigs.filter(node => node.category === 'response');
  const logicNodes = nodeConfigs.filter(node => node.category === 'logic');
  const actionNodes = nodeConfigs.filter(node => node.category === 'action');

  // Get active API configurations
  const activeApis = configuredApis.filter(api => api.isActive === true);

  // Render node button
  const renderNodeButton = (nodeConfig: NodeConfig) => (
    <Button
      key={nodeConfig.type}
      variant="outline"
      onClick={() => onAddNode(nodeConfig.type)}
      className="w-full justify-start text-left py-6 min-h-[4rem]"
    >
      <span className="mr-3 flex-shrink-0">{nodeConfig.icon}</span>
      <div className="flex-1 min-w-0">
        <div className="font-medium truncate">{nodeConfig.title}</div>
        <div className="text-xs text-gray-500 truncate">{nodeConfig.description}</div>
      </div>
    </Button>
  );

  // Render category section
  const renderCategorySection = (title: string, nodes: NodeConfig[]) => (
    <>
      <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2 mt-4">
        {title}
      </div>
      {nodes.map(renderNodeButton)}
    </>
  );

  return (
    <div className="h-full flex flex-col w-full">
      <div className="flex flex-col max-h-[90vh] overflow-y-auto gap-2">
        {/* Response Nodes */}
        {renderCategorySection('Response Nodes', responseNodes)}

        {/* Logic Nodes */}
        {renderCategorySection('Logic Nodes', logicNodes)}

        {/* Action Nodes */}
        {renderCategorySection('Action Nodes', actionNodes)}

        {/* Configured API Integrations */}
        {activeApis.length > 0 && (
          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2 mt-4">
            API Integrations
          </div>
        )}
        {activeApis.map((api) => (
          <Button
            key={api.id}
            variant="outline"
            onClick={() => onAddNode(BotResponseType.API_CALL, undefined, api)}
            className="w-full justify-start text-left py-6 min-h-[4rem]"
          >
            <span className="mr-3 flex-shrink-0">🔗</span>
            <div className="flex-1 min-w-0">
              <div className="font-medium truncate" title={api.name}>
                {api.name}
              </div>
              <div className="text-xs text-gray-500 truncate" title={`${api.method} ${api.url}`}>
                API: {api.method} {api.url}
              </div>
            </div>
          </Button>
        ))}

        {/* Show message if no APIs configured */}
        {activeApis.length === 0 && (
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="text-xs text-yellow-700 dark:text-yellow-300 break-words">
              <strong>No API integrations available</strong><br/>
              Configure APIs in the API Integration tab first, then activate them to use as nodes.
            </div>
          </div>
        )}

        {/* Export Controls */}
        <div className="mt-6 space-y-2">
          <div className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-2">
            Flow Export
          </div>

          {/* Export Actions Group */}
          <div className="flex flex-col space-y-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setShowJsonPreview(true)}
              className="w-full py-6 justify-start text-left bg-white/70 dark:bg-[#1e1e28]/70 border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:bg-[#8178E8]/10 dark:hover:bg-[#6964D3]/10 min-h-[4rem]"
              title="Preview Flow JSON Structure"
            >
              <Eye size={16} className="mr-2 flex-shrink-0"/>
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">Preview JSON</div>
                <div className="text-xs text-gray-500 truncate">View flow structure</div>
              </div>
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={handleExportFlow}
              className="w-full py-6 justify-start text-left bg-white/70 dark:bg-[#1e1e28]/70 border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:bg-[#8178E8]/10 dark:hover:bg-[#6964D3]/10 min-h-[4rem]"
              title="Export Flow as JSON File"
            >
              <Download size={16} className="mr-2 flex-shrink-0"/>
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">Export Flow</div>
                <div className="text-xs text-gray-500 truncate">Download as JSON</div>
              </div>
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={handleFlowImport}
              disabled={isImporting || !onFlowImport}
              className="w-full py-6 justify-start text-left bg-white/70 dark:bg-[#1e1e28]/70 border-[#E0D7FF]/50 dark:border-[#2c2d3d] hover:bg-[#8178E8]/10 dark:hover:bg-[#6964D3]/10 min-h-[4rem]"
              title={onFlowImport ? "Import Flow from JSON File" : "Import functionality not available"}
            >
              <Upload size={16} className="mr-2 flex-shrink-0"/>
              <div className="flex-1 min-w-0">
                <div className="font-medium truncate">{isImporting ? 'Importing...' : 'Import Flow'}</div>
                <div className="text-xs text-gray-500 truncate">Upload JSON file</div>
              </div>
            </Button>
          </div>
        </div>

        {/* Node Documentation */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            💡 Quick Guide
          </h4>
          <div className="text-xs text-blue-700 dark:text-blue-300 space-y-2">
            <div className="break-words">
              <strong>Start Node:</strong> Entry point where conversations begin. Mark your first node as a start node.
            </div>
            <div className="break-words">
              <strong>End Node:</strong> Terminal points where conversations conclude or hand off to humans.
            </div>
            <div className="break-words">
              <strong>Connections:</strong> Click and drag between nodes to create conversation paths.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NodeSelector;

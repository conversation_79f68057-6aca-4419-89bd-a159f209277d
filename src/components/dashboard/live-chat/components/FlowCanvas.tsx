'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

import { IEnhancedFlowNode, IFlowConnection, BotResponseType } from '@/types/bot';
import { FlowNodeComponent } from './FlowNodeComponent';
import { ConnectionLine } from './ConnectionLine';
import { FlowCanvasDesignSelector } from './FlowCanvasDesignSelector';


interface FlowCanvasProps {
  nodes: IEnhancedFlowNode[];
  connections: IFlowConnection[];
  selectedNode: IEnhancedFlowNode | null;
  selectedNodes: string[]; // Array of selected node IDs
  canvasScale: number;
  canvasOffset: { x: number; y: number };
  isConnecting: boolean;
  connectionSource: string | null;
  onNodeSelect: (node: IEnhancedFlowNode) => void;
  onMultiNodeSelect: (nodeIds: string[]) => void; // New handler for multi-selection
  onNodeUpdate: (nodeId: string, updates: Partial<IEnhancedFlowNode>) => void;
  onMultiNodeUpdate: (updates: { nodeId: string; position: { x: number; y: number } }[]) => void; // New handler for multi-node updates
  onNodeDelete: (nodeId: string) => void;
  onMultiNodeDelete: (nodeIds: string[]) => void; // New handler for multi-node deletion
  onConnectionCreate: (sourceNodeId: string, targetNodeId: string) => void;
  onConnectionDelete: (connectionId: string) => void;
  onStartConnection: (nodeId: string) => void;
  onEndConnection: (targetNodeId: string) => void;
  onCancelConnection: () => void;
  onScaleChange: (scale: number) => void;
  onOffsetChange: (offset: { x: number; y: number }) => void;
  onAddNode: (type: BotResponseType, position?: { x: number; y: number }) => void;
  onUndo?: () => void; // New handler for undo
  onRedo?: () => void; // New handler for redo
  canUndo?: boolean; // New prop for undo state
  canRedo?: boolean; // New prop for redo state
}

export const FlowCanvas: React.FC<FlowCanvasProps> = ({
                                                        nodes,
                                                        connections,
                                                        selectedNode,
                                                        selectedNodes,
                                                        canvasScale,
                                                        canvasOffset,
                                                        isConnecting,
                                                        onNodeSelect,
                                                        onMultiNodeSelect,
                                                        onNodeUpdate,
                                                        onMultiNodeUpdate,
                                                        onNodeDelete,
                                                        onMultiNodeDelete,
                                                        onConnectionDelete,
                                                        onStartConnection,
                                                        onEndConnection,
                                                        onCancelConnection,
                                                        onScaleChange,
                                                        onOffsetChange,
                                                        onAddNode,
                                                        onUndo,
                                                        onRedo,
                                                        canUndo = false,
                                                        canRedo = false
                                                      }) => {
  // Pan/zoom state
  const [isPanning, setIsPanning] = useState(false);
  const [isShiftPressed, setIsShiftPressed] = useState(false);
  const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 });
  const canvasRef = useRef<HTMLDivElement>(null);

  // Multi-selection state
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionStart, setSelectionStart] = useState({ x: 0, y: 0 });
  const [selectionEnd, setSelectionEnd] = useState({ x: 0, y: 0 });
  const [isCtrlPressed, setIsCtrlPressed] = useState(false);

  // Multi-node dragging state
  const [isDraggingMultiple, setIsDraggingMultiple] = useState(false);
  const [initialNodePositions, setInitialNodePositions] = useState<{ [nodeId: string]: { x: number; y: number } }>({});



  // Keyboard event handlers for Shift, Ctrl/Cmd, Escape, Delete, and Undo/Redo
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Check if any input field has focus
      const activeElement = document.activeElement;
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT' ||
        (activeElement as HTMLElement).contentEditable === 'true'
      );

      if (e.shiftKey && !e.repeat) {
        setIsShiftPressed(true);
      }
      if ((e.ctrlKey || e.metaKey) && !e.repeat) {
        setIsCtrlPressed(true);
      }

      // Only handle keyboard shortcuts if no input field is focused
      if (!isInputFocused) {
        if (e.key === 'Escape') {
          // Clear selection on Escape
          onMultiNodeSelect([]);
          setIsSelecting(false);
        }

        // Multi-delete functionality
        if ((e.key === 'Delete' || e.key === 'Backspace') && selectedNodes.length > 0) {
          e.preventDefault();
          if (onMultiNodeDelete) {
            onMultiNodeDelete(selectedNodes);
          }
        }

        // Undo/Redo functionality
        if ((e.ctrlKey || e.metaKey) && !e.shiftKey && e.key === 'z') {
          e.preventDefault();
          if (onUndo && canUndo) {
            onUndo();
          }
        }

        if (((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'z') ||
            (e.ctrlKey && e.key === 'y')) {
          e.preventDefault();
          if (onRedo && canRedo) {
            onRedo();
          }
        }
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (!e.shiftKey) {
        setIsShiftPressed(false);
        setIsPanning(false);
      }
      if (!e.ctrlKey && !e.metaKey) {
        setIsCtrlPressed(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [onMultiNodeSelect, selectedNodes, onMultiNodeDelete, onUndo, onRedo, canUndo, canRedo]);

  // Helper function to convert screen coordinates to canvas coordinates
  const screenToCanvas = useCallback((screenX: number, screenY: number) => {
    if (!canvasRef.current) return { x: 0, y: 0 };

    const rect = canvasRef.current.getBoundingClientRect();
    const canvasX = (screenX - rect.left) / canvasScale - canvasOffset.x;
    const canvasY = (screenY - rect.top) / canvasScale - canvasOffset.y;

    return { x: canvasX, y: canvasY };
  }, [canvasScale, canvasOffset]);

  // Helper function to check if a node is within selection rectangle
  const isNodeInSelection = useCallback((node: IEnhancedFlowNode, start: { x: number; y: number }, end: { x: number; y: number }) => {
    const nodeWidth = 208; // FlowNodeComponent width (w-52 = 208px)
    const nodeHeight = 120; // Approximate node height

    const minX = Math.min(start.x, end.x);
    const maxX = Math.max(start.x, end.x);
    const minY = Math.min(start.y, end.y);
    const maxY = Math.max(start.y, end.y);

    // Check if node overlaps with selection rectangle
    return !(node.position.x + nodeWidth < minX ||
      node.position.x > maxX ||
      node.position.y + nodeHeight < minY ||
      node.position.y > maxY);
  }, []);

  // Pan and selection handlers
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if ((isShiftPressed && e.button === 0) || e.button === 2) {
      // Shift + left click or right click for panning
      e.preventDefault();
      setIsPanning(true);
      setLastPanPoint({ x: e.clientX, y: e.clientY });
    } else if (e.button === 0 && !isConnecting) {
      // Left click for selection (only if not connecting)
      const canvasPos = screenToCanvas(e.clientX, e.clientY);

      // Clear selection if not holding Ctrl/Cmd and clicking on empty space
      if (!isCtrlPressed) {
        onMultiNodeSelect([]);
      }

      // Start selection rectangle
      setIsSelecting(true);
      setSelectionStart(canvasPos);
      setSelectionEnd(canvasPos);
    }
  }, [isShiftPressed, isConnecting, isCtrlPressed, screenToCanvas, onMultiNodeSelect]);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isPanning) {
      e.preventDefault();
      const deltaX = e.clientX - lastPanPoint.x;
      const deltaY = e.clientY - lastPanPoint.y;

      onOffsetChange({
        x: canvasOffset.x + deltaX / canvasScale,
        y: canvasOffset.y + deltaY / canvasScale
      });

      setLastPanPoint({ x: e.clientX, y: e.clientY });
    } else if (isSelecting) {
      // Update selection rectangle
      const canvasPos = screenToCanvas(e.clientX, e.clientY);
      setSelectionEnd(canvasPos);

      // Update selected nodes based on current selection rectangle
      const nodesInSelection = nodes.filter(node =>
        isNodeInSelection(node, selectionStart, canvasPos)
      );

      if (isCtrlPressed) {
        // Add to existing selection
        const newSelection = [...new Set([...selectedNodes, ...nodesInSelection.map(n => n.id)])];
        onMultiNodeSelect(newSelection);
      } else {
        // Replace selection
        onMultiNodeSelect(nodesInSelection.map(n => n.id));
      }
    }
  }, [isPanning, lastPanPoint, canvasOffset, canvasScale, onOffsetChange, isSelecting, screenToCanvas, nodes, isNodeInSelection, selectionStart, isCtrlPressed, selectedNodes, onMultiNodeSelect]);

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
    setIsSelecting(false);
  }, []);

  // Zoom handlers
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      const rect = canvasRef.current?.getBoundingClientRect();
      if (!rect) return;

      const mouseX = e.clientX - rect.left;
      const mouseY = e.clientY - rect.top;

      const delta = e.deltaY > 0 ? -0.1 : 0.1;
      const newScale = Math.min(Math.max(canvasScale + delta, 0.25), 3);

      // Zoom towards mouse position
      const offsetX = (mouseX / canvasScale - mouseX / newScale);
      const offsetY = (mouseY / canvasScale - mouseY / newScale);

      onScaleChange(newScale);
      onOffsetChange({
        x: canvasOffset.x + offsetX,
        y: canvasOffset.y + offsetY
      });
    }
  }, [canvasScale, canvasOffset, onScaleChange, onOffsetChange]);

  // Zoom controls
  const zoomIn = useCallback(() => {
    const newScale = Math.min(canvasScale + 0.25, 3);
    onScaleChange(newScale);
  }, [canvasScale, onScaleChange]);

  const zoomOut = useCallback(() => {
    const newScale = Math.max(canvasScale - 0.25, 0.25);
    onScaleChange(newScale);
  }, [canvasScale, onScaleChange]);

  return (
    <div className="max-h-[90vh] flex flex-col">
      <Card className="flex-1 transition-all duration-300 ease-in-out bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl overflow-hidden">
        <div className="p-4 border-b border-[#E0D7FF]/50 dark:border-[#2c2d3d]">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {/*<h3 className="text-lg font-medium text-gray-900 dark:text-white">*/}
              {/*  Flow Canvas*/}
              {/*</h3>*/}
              <div className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                {isShiftPressed ? '🖐️ Pan Mode' : '💡 Hold Shift to Pan'}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Design Selector */}
              <FlowCanvasDesignSelector />

              {/* Zoom Controls */}
              <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={zoomOut}
                  disabled={canvasScale <= 0.25}
                  className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
                  title="Zoom Out (Ctrl + Scroll)"
                >
                  ➖
                </Button>
                <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[50px] text-center">
                {Math.round(canvasScale * 100)}%
              </span>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={zoomIn}
                  disabled={canvasScale >= 3}
                  className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
                  title="Zoom In (Ctrl + Scroll)"
                >
                  ➕
                </Button>
              </div>

              {/* View Controls */}
              <div className="flex items-center space-x-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    if (nodes.length > 0) {
                      // Calculate bounds of all nodes
                      const minX = Math.min(...nodes.map(n => n.position.x));
                      const maxX = Math.max(...nodes.map(n => n.position.x + 192));
                      const minY = Math.min(...nodes.map(n => n.position.y));
                      const maxY = Math.max(...nodes.map(n => n.position.y + 80));

                      // Calculate scale to fit all nodes with padding
                      const padding = 50;
                      const canvasWidth = canvasRef.current?.clientWidth || 800;
                      const canvasHeight = canvasRef.current?.clientHeight || 400;
                      const contentWidth = maxX - minX + padding * 2;
                      const contentHeight = maxY - minY + padding * 2;

                      const scaleX = canvasWidth / contentWidth;
                      const scaleY = canvasHeight / contentHeight;
                      const newScale = Math.min(scaleX, scaleY, 1);

                      onScaleChange(newScale);
                      onOffsetChange({
                        x: (padding - minX),
                        y: (padding - minY)
                      });
                    }
                  }}
                  className="text-xs"
                  title="Fit all nodes in view"
                >
                  📐 Fit to View
                </Button>

                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    if (nodes.length > 0) {
                      // Auto Layout: Arrange nodes in a hierarchical layout
                      const startNodes = nodes.filter(n => n.isStartNode);
                      const endNodes = nodes.filter(n => n.isEndNode);
                      const middleNodes = nodes.filter(n => !n.isStartNode && !n.isEndNode);

                      const nodeSpacing = 250;
                      const levelSpacing = 200;
                      let currentY = 100;

                      // Position start nodes at the top
                      startNodes.forEach((node, index) => {
                        onNodeUpdate(node.id, {
                          position: { x: 100 + (index * nodeSpacing), y: currentY }
                        });
                      });

                      if (startNodes.length > 0) currentY += levelSpacing;

                      // Position middle nodes in the center
                      const nodesPerRow = Math.ceil(Math.sqrt(middleNodes.length));
                      middleNodes.forEach((node, index) => {
                        const row = Math.floor(index / nodesPerRow);
                        const col = index % nodesPerRow;
                        onNodeUpdate(node.id, {
                          position: {
                            x: 100 + (col * nodeSpacing),
                            y: currentY + (row * levelSpacing)
                          }
                        });
                      });

                      if (middleNodes.length > 0) {
                        const rows = Math.ceil(middleNodes.length / nodesPerRow);
                        currentY += rows * levelSpacing;
                      }

                      // Position end nodes at the bottom
                      endNodes.forEach((node, index) => {
                        onNodeUpdate(node.id, {
                          position: { x: 100 + (index * nodeSpacing), y: currentY }
                        });
                      });

                      // Auto-fit after layout
                      setTimeout(() => {
                        const minX = Math.min(...nodes.map(n => n.position.x));
                        const maxX = Math.max(...nodes.map(n => n.position.x + 192));
                        const minY = Math.min(...nodes.map(n => n.position.y));
                        const maxY = Math.max(...nodes.map(n => n.position.y + 80));

                        const padding = 50;
                        const canvasWidth = canvasRef.current?.clientWidth || 800;
                        const canvasHeight = canvasRef.current?.clientHeight || 400;
                        const contentWidth = maxX - minX + padding * 2;
                        const contentHeight = maxY - minY + padding * 2;

                        const scaleX = canvasWidth / contentWidth;
                        const scaleY = canvasHeight / contentHeight;
                        const newScale = Math.min(scaleX, scaleY, 1);

                        onScaleChange(newScale);
                        onOffsetChange({
                          x: (padding - minX),
                          y: (padding - minY)
                        });
                      }, 100);
                    }
                  }}
                  className="text-xs"
                  title="Automatically arrange nodes in an organized layout"
                >
                  🔄 Auto Layout
                </Button>
              </div>

              {/* History Controls */}
              {(onUndo || onRedo) && (
                <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onUndo}
                    disabled={!canUndo}
                    className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
                    title="Undo (Ctrl/Cmd + Z)"
                  >
                    ↶
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={onRedo}
                    disabled={!canRedo}
                    className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-700"
                    title="Redo (Ctrl/Cmd + Shift + Z)"
                  >
                    ↷
                  </Button>
                </div>
              )}

              {/* Selection Info */}
              {selectedNodes.length > 1 && (
                <div className="flex items-center space-x-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg px-3 py-1">
                  <span className="text-xs font-medium text-blue-900 dark:text-blue-100">
                    {selectedNodes.length} nodes selected
                  </span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onMultiNodeSelect([])}
                    className="h-6 w-6 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-100 dark:hover:bg-blue-800"
                    title="Clear selection (Escape)"
                  >
                    ✕
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
            <span className="mr-4">🖱️ Ctrl/Cmd + Scroll to zoom</span>
            <span className="mr-4">🖐️ Shift + Drag to pan</span>
            <span className="mr-4">🖱️ Right-click + Drag to pan</span>
            <span className="mr-4">🔘 Ctrl/Cmd + Click for multi-select</span>
          {/*<span className="mr-4">⌨️ Delete to remove selected</span>*/}
          <span className="mr-4">⌨️ Ctrl/Cmd + Z to undo</span>
          <span className="mr-4">⌨️ Ctrl/Cmd + Shift + Z to redo</span>
            <span>⌨️ Escape to clear selection</span>
          </div>
        </div>

        <div
          ref={canvasRef}
          className={`relative bg-gray-50 dark:bg-gray-800 overflow-auto transition-all duration-300 ease-in-out ${
            isPanning || isShiftPressed ? 'cursor-grab' : 'cursor-default'
          } ${isPanning ? 'cursor-grabbing' : ''}`}
          style={{
            flex: 1,
            minHeight: '400px'
          }}
          onWheel={handleWheel}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onContextMenu={(e) => {
            // Prevent context menu when right-clicking for panning
            if (isPanning) {
              e.preventDefault();
            }
          }}
          onClick={(e) => {
            if (isConnecting && e.target === e.currentTarget) {
              onCancelConnection();
            }
          }}
        >
          {/* Infinite Canvas Content */}
          <div
            className="relative transition-transform duration-200 ease-out"
            style={{
              transform: `scale(${canvasScale}) translate(${canvasOffset.x}px, ${canvasOffset.y}px)`,
              transformOrigin: 'top left',
              width: '20000px', // Large canvas for infinite scrolling
              height: '20000px',
              minWidth: '20000px',
              minHeight: '20000px'
            }}
          >
            {/* Infinite Dot Grid Background - Inside Canvas */}
            <div
              className="absolute inset-0 pointer-events-none"
              style={{
                left: '-10000px',
                top: '-10000px',
                width: '40000px',
                height: '40000px',
                backgroundImage: `
                  radial-gradient(circle, rgba(107, 114, 128, 0.3) 1px, transparent 1px),
                  radial-gradient(circle, rgba(75, 85, 99, 0.2) 1.5px, transparent 1.5px)
                `,
                backgroundSize: '20px 20px, 100px 100px'
              }}
            />

            {/* Dark mode dot grid overlay - Inside Canvas */}
            <div
              className="absolute inset-0 pointer-events-none dark:block hidden"
              style={{
                left: '-10000px',
                top: '-10000px',
                width: '40000px',
                height: '40000px',
                backgroundImage: `
                  radial-gradient(circle, rgba(156, 163, 175, 0.3) 1px, transparent 1px),
                  radial-gradient(circle, rgba(209, 213, 219, 0.2) 1.5px, transparent 1.5px)
                `,
                backgroundSize: '20px 20px, 100px 100px'
              }}
            />

            {/* Selection Rectangle */}
            {isSelecting && (
              <div
                className="absolute border-2 border-blue-500 bg-blue-500/10 pointer-events-none z-50"
                style={{
                  left: Math.min(selectionStart.x, selectionEnd.x),
                  top: Math.min(selectionStart.y, selectionEnd.y),
                  width: Math.abs(selectionEnd.x - selectionStart.x),
                  height: Math.abs(selectionEnd.y - selectionStart.y),
                }}
              />
            )}

            {/* Flow Nodes - Will be imported from existing components */}
            {nodes.map((node) => (
              <FlowNodeComponent
                key={node.id}
                node={node}
                isSelected={selectedNode?.id === node.id}
                isMultiSelected={selectedNodes.includes(node.id)}
                selectedNodes={selectedNodes}
                onSelect={() => onNodeSelect(node)}
                onMultiSelect={(nodeId, isCtrlPressed) => {
                  if (isCtrlPressed) {
                    // Toggle selection
                    if (selectedNodes.includes(nodeId)) {
                      onMultiNodeSelect(selectedNodes.filter(id => id !== nodeId));
                    } else {
                      onMultiNodeSelect([...selectedNodes, nodeId]);
                    }
                  } else {
                    // Single selection
                    onMultiNodeSelect([nodeId]);
                    onNodeSelect(node);
                  }
                }}
                onUpdate={(updates) => onNodeUpdate(node.id, updates)}
                onMultiNodeDragStart={() => {
                  setIsDraggingMultiple(true);
                  // Store initial positions of all selected nodes
                  const positions: { [nodeId: string]: { x: number; y: number } } = {};
                  selectedNodes.forEach(id => {
                    const selectedNode = nodes.find(n => n.id === id);
                    if (selectedNode) {
                      positions[id] = { ...selectedNode.position };
                    }
                  });
                  setInitialNodePositions(positions);
                }}
                onMultiNodeDrag={(deltaX, deltaY) => {
                  if (isDraggingMultiple) {
                    // Update positions of all selected nodes
                    const updates = selectedNodes.map(nodeId => {
                      const initialPos = initialNodePositions[nodeId];
                      if (initialPos) {
                        return {
                          nodeId,
                          position: {
                            x: initialPos.x + deltaX,
                            y: initialPos.y + deltaY
                          }
                        };
                      }
                      return null;
                    }).filter(Boolean) as { nodeId: string; position: { x: number; y: number } }[];

                    onMultiNodeUpdate(updates);
                  }
                }}
                onMultiNodeDragEnd={() => {
                  setIsDraggingMultiple(false);
                  setInitialNodePositions({});
                }}
                onDelete={() => onNodeDelete(node.id)}
                onConnect={() => {}} // Will be implemented
                isConnecting={isConnecting}
                onStartConnection={() => onStartConnection(node.id)}
                onEndConnection={onEndConnection}
              />
            ))}

            {/* Flow Connections - Will be imported from existing components */}
            <svg className="absolute inset-0 pointer-events-none w-full h-full">
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                  markerUnits="strokeWidth"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#6366f1"
                  />
                </marker>
              </defs>
              {connections.map((connection) => {
                const sourceNode = nodes.find(n => n.id === connection.sourceNodeId);
                const targetNode = nodes.find(n => n.id === connection.targetNodeId);

                if (!sourceNode || !targetNode) return null;

                return (
                  <ConnectionLine
                    key={connection.id}
                    connection={connection}
                    source={sourceNode.position}
                    target={targetNode.position}
                    onDelete={() => onConnectionDelete(connection.id)}
                  />
                );
              })}
            </svg>

            {/* Empty State */}
            {nodes.length === 0 && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🎨</div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    Start Building Your Flow
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    Drag nodes from the palette to create your conversation flow
                  </p>
                  <Button
                    onClick={() => onAddNode(BotResponseType.TEXT, { x: 200, y: 200 })}
                  >
                    Add First Node
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Minimap */}
          {nodes.length > 0 && (
            <div className="absolute bottom-4 right-4 w-48 h-32 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-lg shadow-lg overflow-hidden">
              <div className="p-2 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="text-xs font-medium text-gray-700 dark:text-gray-300">Minimap</div>
                  {selectedNodes.length > 1 && (
                    <div className="text-xs bg-blue-500 text-white px-2 py-1 rounded">
                      {selectedNodes.length} selected
                    </div>
                  )}
                </div>
              </div>
              <div className="relative w-full h-full bg-gray-50 dark:bg-gray-800">
                {/* Minimap nodes */}
                {nodes.map((node) => {
                  const minimapScale = 0.02; // Scale down for minimap
                  const minimapX = node.position.x * minimapScale;
                  const minimapY = node.position.y * minimapScale;

                  return (
                    <div
                      key={`minimap-${node.id}`}
                      className={`absolute w-2 h-2 rounded-sm ${
                        selectedNode?.id === node.id
                          ? 'bg-gradient-to-r from-[#8178E8] to-[#6964D3]'
                          : selectedNodes.includes(node.id)
                            ? 'bg-blue-500'
                            : 'bg-gray-400 dark:bg-gray-600'
                      }`}
                      style={{
                        left: `${minimapX}px`,
                        top: `${minimapY}px`,
                        transform: 'translate(-50%, -50%)'
                      }}
                    />
                  );
                })}

                {/* Viewport indicator */}
                <div
                  className="absolute border-2 border-blue-500 bg-blue-500/20 pointer-events-none"
                  style={{
                    left: `${-canvasOffset.x * 0.02}px`,
                    top: `${-canvasOffset.y * 0.02}px`,
                    width: `${(canvasRef.current?.clientWidth || 800) * 0.02 / canvasScale}px`,
                    height: `${(canvasRef.current?.clientHeight || 400) * 0.02 / canvasScale}px`,
                    transform: 'translate(0, 0)'
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </Card>


    </div>
  );
};



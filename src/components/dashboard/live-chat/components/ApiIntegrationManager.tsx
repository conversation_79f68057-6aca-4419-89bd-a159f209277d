'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Select } from '@/components/ui/Select';
import { Badge } from '@/components/ui/Badge';
import { IApiConfig, IFlowVariable, IEnhancedFlowNode } from '@/types/bot';
import { logger } from '@/utils/logger';
import {v4} from "uuid";

// Common HTTP headers for suggestions
const COMMON_HEADERS = [
  { label: 'Authorization', value: 'Authorization', description: 'Bearer tokens, API keys' },
  { label: 'X-API-Key', value: 'X-API-Key', description: 'API key header' },
  { label: 'Content-Type', value: 'Content-Type', description: 'Request content type' },
  { label: 'Accept', value: 'Accept', description: 'Response content type' },
  { label: 'User-Agent', value: 'User-Agent', description: 'Client identification' },
  { label: 'X-Requested-With', value: 'X-Requested-With', description: 'AJAX requests' },
  { label: 'Cache-Control', value: 'Cache-Control', description: 'Caching directives' },
  { label: 'X-RapidAPI-Key', value: 'X-RapidAPI-Key', description: 'RapidAPI key' },
  { label: 'X-RapidAPI-Host', value: 'X-RapidAPI-Host', description: 'RapidAPI host' }
];

// Maximum length for API node names
const MAX_NODE_NAME_LENGTH = 200;

// Valid characters for node names: alphanumeric, spaces, hyphens, underscores
const NODE_NAME_REGEX = /^[a-zA-Z0-9\s\-_]*$/;

// Extended API Config interface to track template origin
interface IExtendedApiConfig extends IApiConfig {
  isFromTemplate?: boolean;
  templateNodeId?: string;
  originalTemplateName?: string;
  hasEncryptedHeaders?: boolean;
  headerCount?: number;
}

// Client-side function to check if headers are encrypted (without importing server utilities)
const isHeaderEncrypted = (headerValue: any): boolean => {
  return (
    typeof headerValue === 'object' &&
    headerValue !== null &&
    typeof headerValue.value === 'string' &&
    typeof headerValue.iv === 'string' &&
    typeof headerValue.tag === 'string'
  );
};

// Check if a node has encrypted headers
const nodeHasEncryptedHeaders = (node: IEnhancedFlowNode): boolean => {
  if (node.type !== 'api_call' || !node.content) {
    return false;
  }

  const content = node.content as any;
  if (!content.headers) {
    return false;
  }

  const headers = content.headers as Record<string, any>;
  return Object.values(headers).some(value => isHeaderEncrypted(value));
};

// Utility function to extract API configurations from template nodes
const extractTemplateApis = async (nodes: IEnhancedFlowNode[]): Promise<IExtendedApiConfig[]> => {
  const templateApis: IExtendedApiConfig[] = [];

  for (const node of nodes) {
    if (node.type === 'api_call' && node.content) {
      const content = node.content as any;

      // Extract API configuration from node content
      if (content.url && content.method) {
        // For template APIs, we show that headers are encrypted but don't decrypt them
        let headers = content.headers || {};
        let headerCount = 0;

        if (nodeHasEncryptedHeaders(node)) {
          // Count encrypted headers but don't decrypt them
          headerCount = Object.keys(headers).length;
          headers = {}; // Don't show encrypted header values
        } else {
          // Show plain text headers for unencrypted template APIs
          headerCount = Object.keys(headers).length;
        }

        const templateApi: IExtendedApiConfig = {
          id: `template_${node.id}`,
          name: node.name || `Template_API_${node.id.slice(0, 8)}`,
          url: content.url,
          method: content.method,
          headers,
          timeout: content.timeout || 30000,
          retries: content.retries || 3,
          isActive: true, // Template APIs are active by default
          isFromTemplate: true,
          templateNodeId: node.id,
          originalTemplateName: node.name,
          hasEncryptedHeaders: nodeHasEncryptedHeaders(node),
          headerCount
        };

        // Add request body if present
        if (content.body) {
          templateApi.requestSchema = content.body;
        }

        // Add query parameters as part of URL or headers
        if (content.queryParams) {
          templateApi.responseSchema = { queryParams: content.queryParams };
        }

        templateApis.push(templateApi);
      }
    }
  }

  return templateApis;
};

// Validation function for node names
const validateNodeName = (name: string) => {
  const length = name.length;
  const isValidLength = length <= MAX_NODE_NAME_LENGTH;
  const isValidCharacters = NODE_NAME_REGEX.test(name);
  const isValid = isValidLength && isValidCharacters;

  let message = '';
  if (!isValidLength) {
    message = `Name exceeds maximum length of ${MAX_NODE_NAME_LENGTH} characters`;
  } else if (!isValidCharacters) {
    message = 'Name can only contain letters, numbers, spaces, hyphens (-), and underscores (_)';
  }

  return {
    isValid,
    isValidLength,
    isValidCharacters,
    remaining: MAX_NODE_NAME_LENGTH - length,
    length,
    message
  };
};

interface ApiIntegrationManagerProps {
  configuredApis: IApiConfig[];
  editingApi: IApiConfig | null;
  apiFormData: Partial<IApiConfig>;
  apiTestResult: { success: boolean; message: string } | null;
  flowVariables?: IFlowVariable[]; // Add flow variables for substitution
  flowNodes?: IEnhancedFlowNode[]; // Add flow nodes to extract template APIs
  onConfiguredApisChange: (apis: IApiConfig[]) => void;
  onEditingApiChange: (api: IApiConfig | null) => void;
  onApiFormDataChange: (data: Partial<IApiConfig>) => void;
  onApiTestResultChange: (result: { success: boolean; message: string } | null) => void;
  onAddApiIntegration: () => void;
  onEditApiIntegration: (api: IApiConfig) => void;
  onDeleteApiIntegration: (apiId: string) => void;
  onToggleApiActive: (apiId: string) => void;
  onTestApiConnection: () => Promise<void>;
}

export const ApiIntegrationManager: React.FC<ApiIntegrationManagerProps> = ({
  configuredApis,
  editingApi,
  apiFormData,
  apiTestResult,
  flowVariables = [],
  flowNodes = [],
  onEditingApiChange,
  onApiFormDataChange,
  onApiTestResultChange,
  onAddApiIntegration,
  onEditApiIntegration,
  onDeleteApiIntegration,
  onToggleApiActive,
  onTestApiConnection
}) => {
  const [newHeaderKey, setNewHeaderKey] = useState('');
  const [newHeaderValue, setNewHeaderValue] = useState('');
  const [showHeaderSuggestions, setShowHeaderSuggestions] = useState(false);
  const [showVariableHelper, setShowVariableHelper] = useState(false);
  const [templateApis, setTemplateApis] = useState<IExtendedApiConfig[]>([]);

  // Use the centralized validation function
  const getNameValidation = validateNodeName;

  // Extract template APIs when flow nodes change
  useEffect(() => {
    const loadTemplateApis = async () => {
      if (flowNodes && flowNodes.length > 0) {
        try {
          const extractedTemplateApis = await extractTemplateApis(flowNodes);
          setTemplateApis(extractedTemplateApis);
        } catch (error) {
          logger.error('Failed to extract template APIs:', error);
          setTemplateApis([]);
        }
      } else {
        setTemplateApis([]);
      }
    };

    loadTemplateApis();
  }, [flowNodes]);

  // Merge configured APIs with template APIs
  const allApis: IExtendedApiConfig[] = [
    ...configuredApis.map(api => ({ ...api, isFromTemplate: false })),
    ...templateApis
  ];

  // Remove duplicates (prefer manually configured over template)
  const uniqueApis = allApis.reduce((acc, api) => {
    const existingIndex = acc.findIndex(existing =>
      existing.name === api.name ||
      (api.isFromTemplate && existing.templateNodeId === api.templateNodeId)
    );

    if (existingIndex >= 0) {
      // If we have a manually configured API with the same name, prefer it
      if (!api.isFromTemplate) {
        acc[existingIndex] = api;
      }
    } else {
      acc.push(api);
    }

    return acc;
  }, [] as IExtendedApiConfig[]);
 
  // Helper function to get available variables for substitution
  const getAvailableVariables = () => {
    const systemVariables = [
      { name: 'user_id', description: 'Current user ID' },
      { name: 'conversation_id', description: 'Current conversation ID' },
      { name: 'user_message', description: 'User\'s current message' },
      { name: 'timestamp', description: 'Current timestamp' },
      { name: 'flow_id', description: 'Current flow ID' },
      { name: 'node_id', description: 'Current node ID' }
    ];

    const customVariables = flowVariables.map(variable => ({
      name: variable.name,
      description: variable.description || `Custom ${variable.type} variable`
    }));

    return [...systemVariables, ...customVariables];
  };

  // Helper function to insert variable placeholder
  const insertVariablePlaceholder = (variableName: string, currentValue: string) => {
    return currentValue + `{{${variableName}}}`;
  };

  // Helper function to validate variable syntax
  const validateVariableSyntax = (text: string) => {
    const variablePattern = /\{\{([^}]+)\}\}/g;
    const matches = text.match(variablePattern);
    if (!matches) return { isValid: true, errors: [] };

    const errors: string[] = [];
    const availableVars = getAvailableVariables().map(v => v.name);

    matches.forEach(match => {
      const varName = match.slice(2, -2).trim();
      if (!availableVars.includes(varName)) {
        errors.push(`Unknown variable: ${varName}`);
      }
    });

    return { isValid: errors.length === 0, errors };
  };



  // Add a new header
  const addHeader = (key?: string, value?: string) => {
    const headerKey = key || newHeaderKey.trim();
    const headerValue = value || newHeaderValue.trim();

    if (!headerKey || !headerValue) return;

    const updatedHeaders = {
      ...apiFormData.headers,
      [headerKey]: headerValue
    };

    onApiFormDataChange({ ...apiFormData, headers: updatedHeaders });
    setNewHeaderKey('');
    setNewHeaderValue('');
    setShowHeaderSuggestions(false);
  };

  // Remove a header
  const removeHeader = (key: string) => {
    const updatedHeaders = { ...apiFormData.headers };
    delete updatedHeaders[key];
    onApiFormDataChange({ ...apiFormData, headers: updatedHeaders });
  };

  // Update header value
  const updateHeader = (key: string, value: string) => {
    const updatedHeaders = {
      ...apiFormData.headers,
      [key]: value
    };
    onApiFormDataChange({ ...apiFormData, headers: updatedHeaders });
  };
  const handleAddNewApi = () => {
    onEditingApiChange(null);
    onApiFormDataChange({
      url: '',
      method: 'GET',
      headers: {},
      timeout: 30000,
      retries: 3
    });
    onApiTestResultChange(null);
  };

  const handleCancelEdit = () => {
    onEditingApiChange(null);
    onApiFormDataChange({
      url: '',
      method: 'GET',
      headers: {},
      timeout: 30000,
      retries: 3
    });
    onApiTestResultChange(null);
  };

  // Handle editing template APIs - convert them to manually configured APIs
  const handleEditTemplateApi = (templateApi: IExtendedApiConfig) => {
    if (templateApi.isFromTemplate) {
      // Create a new manually configured API based on the template
      const newManualApi: IApiConfig = {
        id: `manual_${v4()}`, // New ID for manual API
        name: `${templateApi.name}_Custom`,
        url: templateApi.url,
        method: templateApi.method,
        headers: templateApi.headers,
        timeout: templateApi.timeout,
        retries: templateApi.retries,
        isActive: false, // Start as inactive for manual APIs
        requestSchema: templateApi.requestSchema,
        responseSchema: templateApi.responseSchema
      };

      onEditApiIntegration(newManualApi);
    } else {
      onEditApiIntegration(templateApi);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            API Integration Management
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Configure external APIs before using them as nodes in your conversation flows
          </p>
        </div>
        <Button
          onClick={handleAddNewApi}
          className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
        >
          ➕ Add New API
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* API Configuration Form */}
        <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            {editingApi ? '✏️ Edit API Integration' : '🔗 New API Integration'}
          </h3>

          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="apiName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  API Name *
                </Label>
                <span className={`text-xs ${
                  getNameValidation(apiFormData.name || '').isValid
                    ? 'text-gray-500 dark:text-gray-400'
                    : 'text-red-500 dark:text-red-400'
                }`}>
                  {getNameValidation(apiFormData.name || '').length}/{MAX_NODE_NAME_LENGTH}
                </span>
              </div>
              <Input
                id="apiName"
                value={apiFormData.name || ''}
                onChange={(e) => onApiFormDataChange({ ...apiFormData, name: e.target.value })}
                placeholder="e.g., Customer_API or Payment-Service"
                className={`mt-1 ${
                  !getNameValidation(apiFormData.name || '').isValid
                    ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
                    : ''
                }`}
                maxLength={MAX_NODE_NAME_LENGTH}
              />
              {!getNameValidation(apiFormData.name || '').isValid && (
                <p className="text-xs text-red-500 dark:text-red-400 mt-1">
                  {getNameValidation(apiFormData.name || '').message}
                </p>
              )}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                This name will appear as the node name in your flow. Use letters, numbers, spaces, hyphens (-), and underscores (_) only.
              </p>
            </div>

            <div>
              <div className="flex items-center justify-between mb-2">
                <Label htmlFor="apiUrl" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  API Endpoint URL *
                </Label>
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={() => setShowVariableHelper(!showVariableHelper)}
                  className="text-xs"
                >
                  🔧 Variables
                </Button>
              </div>
              <Input
                id="apiUrl"
                value={apiFormData.url || ''}
                onChange={(e) => onApiFormDataChange({ ...apiFormData, url: e.target.value })}
                placeholder="https://api.example.com/users/{{user_id}}/profile"
                className="mt-1"
              />
              {apiFormData.url && !validateVariableSyntax(apiFormData.url).isValid && (
                <div className="mt-1 text-xs text-red-500 dark:text-red-400">
                  {validateVariableSyntax(apiFormData.url).errors.join(', ')}
                </div>
              )}

              {/* Variable Helper */}
              {showVariableHelper && (
                <div className="mt-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                  <h5 className="text-xs font-medium text-purple-900 dark:text-purple-100 mb-2">Available Variables:</h5>
                  <div className="grid grid-cols-1 gap-1 max-h-32 overflow-y-auto">
                    {getAvailableVariables().map((variable) => (
                      <button
                        key={variable.name}
                        type="button"
                        onClick={() => {
                          const newUrl = insertVariablePlaceholder(variable.name, apiFormData.url || '');
                          onApiFormDataChange({ ...apiFormData, url: newUrl });
                        }}
                        className="text-left p-2 text-xs bg-white dark:bg-gray-800 border border-purple-200 dark:border-purple-700 rounded hover:bg-purple-50 dark:hover:bg-purple-900/30 transition-colors"
                      >
                        <div className="font-medium text-purple-900 dark:text-purple-100">{`{{${variable.name}}}`}</div>
                        <div className="text-purple-700 dark:text-purple-300">{variable.description}</div>
                      </button>
                    ))}
                  </div>
                  <div className="mt-2 text-xs text-purple-700 dark:text-purple-300">
                    💡 Variables are replaced with actual values during API calls
                  </div>
                </div>
              )}

              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Use {`{{variable_name}}`} syntax to insert dynamic values from the conversation flow
              </p>
            </div>

            <div>
              <Label htmlFor="apiMethod" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                HTTP Method
              </Label>
              <Select
                id="apiMethod"
                value={apiFormData.method || 'GET'}
                onChange={(value) => onApiFormDataChange({ ...apiFormData, method: value as any })}
                options={[
                  { label: 'GET', value: 'GET' },
                  { label: 'POST', value: 'POST' },
                  { label: 'PUT', value: 'PUT' },
                  { label: 'DELETE', value: 'DELETE' },
                  { label: 'PATCH', value: 'PATCH' }
                ]}
                placeholder="Select HTTP method"
              />
            </div>

            {/* Request Body for POST/PUT/PATCH methods */}
            {apiFormData.method && ['POST', 'PUT', 'PATCH'].includes(apiFormData.method) && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label htmlFor="apiBody" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Request Body (JSON)
                  </Label>
                  <Button
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => setShowVariableHelper(!showVariableHelper)}
                    className="text-xs"
                  >
                    🔧 Variables
                  </Button>
                </div>
                <textarea
                  id="apiBody"
                  value={typeof apiFormData.body === 'string' ? apiFormData.body : JSON.stringify(apiFormData.body || {}, null, 2)}
                  onChange={(e) => {
                    try {
                      // Try to parse as JSON for validation
                      const parsed = JSON.parse(e.target.value);
                      onApiFormDataChange({ ...apiFormData, body: parsed });
                    } catch {
                      // Keep as string if not valid JSON (might contain variables)
                      onApiFormDataChange({ ...apiFormData, body: e.target.value });
                    }
                  }}
                  placeholder={
                    editingApi && (editingApi as any).isFromTemplate && (editingApi as any).requestSchema
                      ? JSON.stringify((editingApi as any).requestSchema, null, 2)
                      : `{
  "user_id": "{{user_id}}",
  "message": "{{user_message}}",
  "timestamp": "{{timestamp}}"
}`
                  }
                  className="mt-1 w-full h-32 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white resize-vertical"
                />
                {apiFormData.body && typeof apiFormData.body === 'string' && !validateVariableSyntax(apiFormData.body).isValid && (
                  <div className="mt-1 text-xs text-red-500 dark:text-red-400">
                    {validateVariableSyntax(apiFormData.body).errors.join(', ')}
                  </div>
                )}
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  JSON format with variable substitution support. Use {`{{variable_name}}`} for dynamic values.
                </p>
              </div>
            )}

            {/* Custom Headers Section */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Custom Headers
                </Label>
                <Button
                  type="button"
                  size="sm"
                  variant="outline"
                  onClick={() => setShowHeaderSuggestions(!showHeaderSuggestions)}
                  className="text-xs"
                >
                  💡 Suggestions
                </Button>
              </div>

              {/* Header Suggestions */}
              {showHeaderSuggestions && (
                <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <h5 className="text-xs font-medium text-blue-900 dark:text-blue-100 mb-2">Common Headers:</h5>
                  <div className="grid grid-cols-1 gap-1">
                    {COMMON_HEADERS.map((header) => (
                      <button
                        key={header.value}
                        type="button"
                        onClick={() => {
                          setNewHeaderKey(header.value);
                          setShowHeaderSuggestions(false);
                        }}
                        className="text-left p-2 text-xs bg-white dark:bg-gray-800 border border-blue-200 dark:border-blue-700 rounded hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                      >
                        <div className="font-medium text-blue-900 dark:text-blue-100">{header.label}</div>
                        <div className="text-blue-700 dark:text-blue-300">{header.description}</div>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Existing Headers */}
              {apiFormData.headers && Object.keys(apiFormData.headers).length > 0 && (
                <div className="space-y-2 mb-3">
                  {Object.entries(apiFormData.headers).map(([key, value]) => (
                    <div key={key} className="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                      <div className="flex-1 grid grid-cols-2 gap-2">
                        <Input
                          value={key}
                          onChange={(e) => {
                            const newKey = e.target.value;
                            const updatedHeaders = { ...apiFormData.headers };
                            delete updatedHeaders[key];
                            updatedHeaders[newKey] = value;
                            onApiFormDataChange({ ...apiFormData, headers: updatedHeaders });
                          }}
                          placeholder="Header name"
                          className="text-xs"
                        />
                        <Input
                          value={value}
                          onChange={(e) => updateHeader(key, e.target.value)}
                          placeholder="Bearer {{api_token}} or {{user_id}}"
                          className="text-xs"
                        />
                        {value && !validateVariableSyntax(value).isValid && (
                          <div className="col-span-2 text-xs text-red-500 dark:text-red-400">
                            {validateVariableSyntax(value).errors.join(', ')}
                          </div>
                        )}
                      </div>
                      <Button
                        type="button"
                        size="sm"
                        variant="outline"
                        onClick={() => removeHeader(key)}
                        className="text-red-600 hover:text-red-700 px-2"
                      >
                        🗑️
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {/* Add New Header */}
              <div className="flex items-center space-x-2">
                <Input
                  value={newHeaderKey}
                  onChange={(e) => setNewHeaderKey(e.target.value)}
                  placeholder="Header name (e.g., Authorization)"
                  className="flex-1 text-xs"
                />
                <Input
                  value={newHeaderValue}
                  onChange={(e) => setNewHeaderValue(e.target.value)}
                  placeholder="Header value (e.g., Bearer token)"
                  className="flex-1 text-xs"
                />
                <Button
                  type="button"
                  size="sm"
                  onClick={() => addHeader()}
                  disabled={!newHeaderKey.trim() || !newHeaderValue.trim()}
                  className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0 px-3"
                >
                  ➕
                </Button>
              </div>
              <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded text-xs">
                <div className="flex items-center space-x-1 text-green-700 dark:text-green-300">
                  <span>🔒</span>
                  <span className="font-medium">Security:</span>
                  <span>All header values are encrypted before storage</span>
                </div>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Add custom headers for authentication, content-type, or other API requirements
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="apiTimeout" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Timeout (ms)
                </Label>
                <Input
                  id="apiTimeout"
                  type="number"
                  value={apiFormData.timeout || 30000}
                  onChange={(e) => onApiFormDataChange({ ...apiFormData, timeout: parseInt(e.target.value) })}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="apiRetries" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Retries
                </Label>
                <Input
                  id="apiRetries"
                  type="number"
                  value={apiFormData.retries || 3}
                  onChange={(e) => onApiFormDataChange({ ...apiFormData, retries: parseInt(e.target.value) })}
                  className="mt-1"
                />
              </div>
            </div>

            {/* API Test Result */}
            {apiTestResult && (
              <div className={`p-3 rounded-lg ${
                apiTestResult.success
                  ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                  : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
              }`}>
                <p className={`text-sm ${
                  apiTestResult.success
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}>
                  {apiTestResult.message}
                </p>
              </div>
            )}

            <div className="flex space-x-3">
              <Button
                onClick={onTestApiConnection}
                variant="outline"
                className="flex-1"
                disabled={!apiFormData.url}
              >
                🧪 Test Connection
              </Button>
              <Button
                onClick={onAddApiIntegration}
                className="flex-1 bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
                disabled={
                  !apiFormData.name ||
                  !apiFormData.url ||
                  !getNameValidation(apiFormData.name || '').isValid
                }
              >
                {editingApi ? 'Update API' : 'Save API'}
              </Button>
            </div>

            {editingApi && (
              <Button
                onClick={handleCancelEdit}
                variant="outline"
                className="w-full"
              >
                Cancel Edit
              </Button>
            )}
          </div>
        </Card>

        {/* Configured APIs List */}
        <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              📋 API Integrations ({uniqueApis.length})
            </h3>
            {templateApis.length > 0 && (
              <div className="flex items-center space-x-2 text-xs text-blue-600 dark:text-blue-400">
                <span>🎯</span>
                <span>{templateApis.length} from template</span>
              </div>
            )}
          </div>

          {/* Template APIs Info Banner */}
          {templateApis.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-start space-x-2">
                <span className="text-blue-600 dark:text-blue-400 mt-0.5">💡</span>
                <div className="text-xs text-blue-700 dark:text-blue-300">
                  <div className="font-medium mb-1">Template APIs Detected</div>
                  <div>
                    Your selected bot template includes {templateApis.length} pre-configured API integration{templateApis.length > 1 ? 's' : ''}.
                    You can edit these APIs to customize endpoints, headers, authentication, and other settings.
                    Editing a template API will create a custom copy that you can modify without affecting the original template.
                  </div>
                </div>
              </div>
            </div>
          )}

          {uniqueApis.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">🔗</div>
              <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No APIs Configured
              </h4>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Add your first API integration to start using external services in your flows
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {uniqueApis.map((api) => (
                <div key={api.id} className="border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl p-4 bg-white/50 dark:bg-[#1e1e28]/50 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-[#1e1e28]/70 transition-all duration-200">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {api.name}
                      </h4>
                      <Badge variant={api.isActive ? 'success' : 'secondary'}>
                        {api.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      {api.isFromTemplate && (
                        <Badge variant="info" className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                          🎯 Template
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEditTemplateApi(api)}
                        title={api.isFromTemplate ? 'Edit Template API (creates custom copy)' : 'Edit API'}
                      >
                        ✏️
                      </Button>
                      {!api.isFromTemplate && (
                        <>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onToggleApiActive(api.id!)}
                            className={api.isActive ? 'text-orange-600 hover:text-orange-700' : 'text-green-600 hover:text-green-700'}
                            title={api.isActive ? 'Deactivate API' : 'Activate API'}
                          >
                            {api.isActive ? '⏸️' : '▶️'}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => onDeleteApiIntegration(api.id!)}
                            className="text-red-600 hover:text-red-700"
                            title="Delete API"
                          >
                            🗑️
                          </Button>
                        </>
                      )}
                      {api.isFromTemplate && (
                        <div className="text-xs text-blue-600 dark:text-blue-400 px-2">
                          Template API
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-gray-600 dark:text-gray-400">
                    <div>
                      <div><strong>Method:</strong> <span className="font-mono bg-gray-100 dark:bg-gray-800 px-1 rounded">{api.method}</span></div>
                      <div><strong>Timeout:</strong> {api.timeout}ms</div>
                    </div>
                    <div>
                      <div><strong>URL:</strong> <span className="font-mono text-xs break-all">{api.url}</span></div>
                      {((api.headers && Object.keys(api.headers).length > 0) || (api.headerCount && api.headerCount > 0)) && (
                        <div className="flex items-center space-x-1">
                          <span><strong>Headers:</strong> {api.headerCount || Object.keys(api.headers || {}).length} configured</span>
                          {(api.hasEncryptedHeaders || (api.isFromTemplate && api.headerCount && api.headerCount > 0)) && (
                            <span className="text-green-600 dark:text-green-400" title="Headers are encrypted at rest">🔒</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Headers Preview */}
                  {((api.headers && Object.keys(api.headers).length > 0) || (api.hasEncryptedHeaders && api.headerCount && api.headerCount > 0)) && (
                    <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                      <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Headers:</div>
                      <div className="flex flex-wrap gap-1">
                        {api.hasEncryptedHeaders ? (
                          // Show encrypted header placeholder
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                            <span className="mr-1">🔒</span>
                            <span>{api.headerCount} encrypted header{api.headerCount !== 1 ? 's' : ''}</span>
                          </span>
                        ) : (
                          // Show plain text headers (header names only, not values)
                          Object.keys(api.headers || {}).map((headerKey) => (
                            <span
                              key={headerKey}
                              className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"
                            >
                              {headerKey}
                            </span>
                          ))
                        )}
                      </div>
                    </div>
                  )}

                  {/* Template API Request Body Preview */}
                  {api.isFromTemplate && api.requestSchema && (
                    <div className="mt-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                      <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">Request Body:</div>
                      <pre className="text-xs text-blue-600 dark:text-blue-400 overflow-x-auto">
                        {JSON.stringify(api.requestSchema, null, 2)}
                      </pre>
                    </div>
                  )}

                  {api.isActive && (
                    <div className="mt-3 space-y-1">
                      <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                        <span className="mr-1">✅</span>
                        Available as API_CALL node in Visual Designer
                      </div>
                      <div className="flex items-center text-xs text-blue-600 dark:text-blue-400">
                        <span className="mr-1">🔗</span>
                        Supports variable substitution and data flow
                      </div>
                      <div className="flex items-center text-xs text-purple-600 dark:text-purple-400">
                        <span className="mr-1">📊</span>
                        Response data available to subsequent nodes
                      </div>
                      {api.isFromTemplate && (
                        <div className="flex items-center text-xs text-blue-600 dark:text-blue-400">
                          <span className="mr-1">🎯</span>
                          Pre-configured from bot template
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};

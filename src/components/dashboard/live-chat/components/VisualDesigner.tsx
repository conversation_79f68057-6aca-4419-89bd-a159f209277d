'use client';

import React, {useState, useCallback} from 'react';
import {Card} from '@/components/ui/Card';
import {Button} from '@/components/ui/Button';
import {Dialog} from '@/components/ui/Dialog';
import {FlowCanvasContainer} from './FlowCanvasContainer';
import {NodeEditor} from './NodeEditor';
import {Download, Maximize2} from 'lucide-react';

import {IEnhancedFlowNode, IFlowConnection, IApiConfig, BotResponseType, ClientFlowData} from '@/types/bot';
import NodeSelector from "@components/dashboard/live-chat/components/NodeSelector";
import { logger } from '@/utils/logger';

export type onAddNode = (type: BotResponseType, position?: { x: number; y: number }, apiConfig?: IApiConfig) => void

interface VisualDesignerProps {
  flowData: ClientFlowData;
  selectedNode: IEnhancedFlowNode | null;
  canvasScale: number;
  canvasOffset: { x: number; y: number };
  isConnecting: boolean;
  connectionSource: string | null;
  configuredApis: IApiConfig[];

  onNodeSelect: (node: IEnhancedFlowNode) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<IEnhancedFlowNode>) => void;
  onNodeDelete: (nodeId: string) => void;
  onMultiNodeDelete?: (nodeIds: string[]) => void;
  onAddNode: onAddNode;
  onConnectionCreate: (sourceNodeId: string, targetNodeId: string) => void;
  onConnectionDelete: (connectionId: string) => void;
  onStartConnection: (nodeId: string) => void;
  onEndConnection: (targetNodeId: string) => void;
  onCancelConnection: () => void;
  onScaleChange: (scale: number) => void;
  onOffsetChange: (offset: { x: number; y: number }) => void;
  onFlowImport?: (flowData: { name: string; description: string; nodes: any[]; connections: any[]; variables: any[]; integrations: any[] }) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;

}

export const BaseVisualDesigner: React.FC<VisualDesignerProps & { handleSetIsFloatMode: (state:boolean) => void }> =
  ({
     flowData,
     selectedNode,
     canvasScale,
     canvasOffset,
     handleSetIsFloatMode,
     isConnecting,
     connectionSource,
     configuredApis,
     onNodeSelect,
     onNodeUpdate,
     onNodeDelete,
     onMultiNodeDelete,
     onAddNode,
     onConnectionCreate,
     onConnectionDelete,
     onStartConnection,
     onEndConnection,
     onCancelConnection,
     onScaleChange,
     onOffsetChange,
     onFlowImport,
     onUndo,
     onRedo,
     canUndo,
     canRedo
   }) => {
    const [isPaletteCollapsed, setIsPaletteCollapsed] = useState(false);
    const [isEditorCollapsed, setIsEditorCollapsed] = useState(false);
    const [showJsonPreview, setShowJsonPreview] = useState(false);

    // Multi-selection state
    const [selectedNodes, setSelectedNodes] = useState<string[]>([]);

    // Multi-node update handler
    const handleMultiNodeUpdate = useCallback((updates: { nodeId: string; position: { x: number; y: number } }[]) => {
      updates.forEach(update => {
        onNodeUpdate(update.nodeId, { position: update.position });
      });
    }, [onNodeUpdate]);

    // Multi-node delete handler
    const handleMultiNodeDelete = useCallback((nodeIds: string[]) => {
      if (onMultiNodeDelete) {
        onMultiNodeDelete(nodeIds);
      } else {
        // Fallback to individual deletes if multi-delete not provided
        nodeIds.forEach(nodeId => onNodeDelete(nodeId));
      }
      setSelectedNodes([]); // Clear selection after deletion
    }, [onMultiNodeDelete, onNodeDelete]);

    // Generate complete flow JSON
    const generateFlowJson = useCallback(() => {
      return {
        metadata: {
          name: flowData.name,
          description: flowData.description || '',
          version: '1.0',
          createdAt: new Date().toISOString(),
          totalNodes: flowData.nodes.length,
          totalConnections: flowData.connections.length
        },
        nodes: flowData.nodes.map(node => ({
          id: node.id,
          name: node.name,
          type: node.type,
          content: node.content,
          position: node.position,
          isStartNode: node.isStartNode,
          isEndNode: node.isEndNode,
          triggers: node.triggers
        })),
        connections: flowData.connections.map(connection => ({
          id: connection.id,
          sourceNodeId: connection.sourceNodeId,
          targetNodeId: connection.targetNodeId,
          sourceHandle: connection.sourceHandle,
          targetHandle: connection.targetHandle,
          condition: connection.condition,
          label: connection.label
        })),
        variables: flowData.variables.map(variable => ({
          name: variable.name,
          type: variable.type,
          defaultValue: variable.defaultValue,
          description: variable.description,
          required: variable.required
        }))
      };
    }, [flowData]);

    // Export flow as JSON file
    const handleExportFlow = useCallback(() => {
      const flowDataJson = generateFlowJson();
      const jsonString = JSON.stringify(flowDataJson, null, 2);
      const blob = new Blob([jsonString], {type: 'application/json'});
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = `${flowData.name.replace(/[^a-z0-9]/gi, '_').toLowerCase?.()}_flow.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }, [generateFlowJson, flowData.name]);

    // Calculate grid columns based on panel states
    const getGridCols = () => {
      if (isPaletteCollapsed && isEditorCollapsed) return 'grid-cols-1';
      if (isPaletteCollapsed || isEditorCollapsed) return 'lg:grid-cols-3';
      return 'lg:grid-cols-4';
    };

    const getCanvasColSpan = () => {
      if (isPaletteCollapsed && isEditorCollapsed) return '';
      if (isPaletteCollapsed || isEditorCollapsed) return 'lg:col-span-2';
      return 'lg:col-span-2';
    };

    const Footer = () => {
      return (
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              const jsonString = JSON.stringify(generateFlowJson(), null, 2);
              navigator.clipboard.writeText(jsonString);
            }}
            className="text-sm"
          >
            Copy to Clipboard
          </Button>
          <Button
            onClick={handleExportFlow}
            className="text-sm bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169D9] hover:to-[#5854C4] text-white"
          >
            <Download size={16} className="mr-2"/>
            Download JSON
          </Button>
        </div>
      )
    }

    return (
      <div
        className={`grid grid-cols-1 ${getGridCols()} gap-6 h-full relative transition-all duration-300 ease-in-out`}>
        {/* Node Palette */}
        <div className={`space-y-4 transition-all duration-300 ease-in-out transform ${
          isPaletteCollapsed
            ? 'opacity-0 -translate-x-full pointer-events-none absolute w-0 overflow-hidden'
            : 'opacity-100 translate-x-0 pointer-events-auto relative'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => onScaleChange(Math.min(canvasScale + 0.1, 2))}
                title="Zoom In"
              >
                🔍+
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onScaleChange(Math.max(canvasScale - 0.1, 0.5))}
                title="Zoom Out"
              >
                🔍-
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleSetIsFloatMode(true)}
                title="Open in Float Mode"
                className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169D9] hover:to-[#5854C4] text-white border-0"
              >
                <Maximize2 size={14} className="mr-1"/>
                Float
              </Button>
            </div>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsPaletteCollapsed(true)}
              title="Hide Panel"
            >
              ◀
            </Button>
          </div>
          <NodeSelector
            onAddNode={onAddNode}
            configuredApis={configuredApis}
            setShowJsonPreview={setShowJsonPreview}
            handleExportFlow={handleExportFlow}
            onFlowImport={onFlowImport || ((flowData) => logger.warn('onFlowImport not provided:', flowData))}
          />
        </div>

        {/* Collapsed Palette Toggle */}
        {isPaletteCollapsed && (
          <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-50">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsPaletteCollapsed(false)}
              className="rounded-r-lg rounded-l-none bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-lg border-l-0"
              title="Show Node Palette"
            >
              ▶
            </Button>
          </div>
        )}

        {/* Flow Canvas */}
        <div className={`${getCanvasColSpan()} relative transition-all duration-300 ease-in-out`}>
          <FlowCanvasContainer
            nodes={flowData.nodes}
            connections={flowData.connections}
            selectedNode={selectedNode}
            selectedNodes={selectedNodes}
            canvasScale={canvasScale}
            canvasOffset={canvasOffset}
            isConnecting={isConnecting}
            connectionSource={connectionSource}
            onNodeSelect={onNodeSelect}
            onMultiNodeSelect={setSelectedNodes}
            onNodeUpdate={onNodeUpdate}
            onMultiNodeUpdate={handleMultiNodeUpdate}
            onNodeDelete={onNodeDelete}
            onMultiNodeDelete={handleMultiNodeDelete}
            onConnectionCreate={onConnectionCreate}
            onConnectionDelete={onConnectionDelete}
            onStartConnection={onStartConnection}
            onEndConnection={onEndConnection}
            onCancelConnection={onCancelConnection}
            onScaleChange={onScaleChange}
            onOffsetChange={onOffsetChange}
            onAddNode={onAddNode}
            onUndo={onUndo}
            onRedo={onRedo}
            canUndo={canUndo}
            canRedo={canRedo}
          />
        </div>

        {/* Node Editor */}
        <div className={`space-y-4 transition-all duration-300 ease-in-out transform ${
          isEditorCollapsed
            ? 'opacity-0 translate-x-full pointer-events-none absolute w-0 overflow-hidden'
            : 'opacity-100 translate-x-0 pointer-events-auto relative'
        }`}>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Node Properties
            </h3>
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsEditorCollapsed(true)}
              title="Hide Properties Panel"
            >
              ▶
            </Button>
          </div>
          {selectedNode ? (
            <NodeEditor
              node={selectedNode}
              onUpdate={(updates) => onNodeUpdate(selectedNode.id, updates)}
              variables={flowData.variables || []}
            />
          ) : (
            <Card className="p-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                Select a node to edit its properties
              </p>
            </Card>
          )}
        </div>

        {/* Collapsed Editor Toggle */}
        {isEditorCollapsed && (
          <div className="absolute right-0 top-1/2 transform -translate-y-1/2 z-50">
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsEditorCollapsed(false)}
              className="rounded-l-lg rounded-r-none bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-lg border-r-0"
              title="Show Properties Panel"
            >
              ◀
            </Button>
          </div>
        )}

        {/* JSON Preview Dialog */}
        <Dialog
          title="Flow JSON Preview"
          open={showJsonPreview}
          onOpenChange={setShowJsonPreview}
          size="large"
          footer={<Footer/>}
        >
          <div className="space-y-4">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Complete flow structure including nodes, connections, and variables.
            </div>

            <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 max-h-[100%] overflow-auto">
            <pre className="text-xs text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono ">
              {JSON.stringify(generateFlowJson(), null, 2)}
            </pre>
            </div>
          </div>
        </Dialog>
      </div>
    );
  };


export const VisualDesigner: React.FC<VisualDesignerProps> = ({
                                                                ...visualDesignerProps
                                                              }) => {
  const [isFloatMode, setIsFloatMode] = useState<boolean>(false);

  const handleSetIsFloatMode = () => {
    setIsFloatMode(!isFloatMode);
  }

  return (
    <>
      <Dialog
        title=""
        open={isFloatMode}
        onOpenChange={setIsFloatMode}
        size="full"
        className="p-0"
      >
        <BaseVisualDesigner {...visualDesignerProps} handleSetIsFloatMode={handleSetIsFloatMode}/>
      </Dialog>
      <BaseVisualDesigner {...visualDesignerProps} handleSetIsFloatMode={handleSetIsFloatMode}/>
    </>
  )

}

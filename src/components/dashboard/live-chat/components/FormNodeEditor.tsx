'use client';

import React, { useRef, useState } from 'react';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Button } from '@/components/ui/Button';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { VariableReferenceSection } from './VariableReferenceSection';
import { BotResponseType, IFormField } from '@/types/bot';

// Component-specific constants
const MIN_FORM_MESSAGE_LENGTH = 1;
const MAX_FORM_MESSAGE_LENGTH = 1000;
const MAX_FORM_FIELDS = 10;
const FIELD_TYPES = [
  { label: 'Text Input', value: 'text' },
  { label: 'Email Address', value: 'email' },
  { label: 'Phone Number', value: 'phone' },
  { label: 'Number', value: 'number' },
  { label: 'Dropdown Select', value: 'select' }
];
const DEFAULT_PLACEHOLDER = "Enter the message that will appear above the form. Use {{variable_name}} to personalize...";
const HELP_TEXT = "This message appears before the form. Use variables to personalize the form introduction.";

// Component-specific interfaces
interface FormValidation {
  isValid: boolean;
  length: number;
  remaining: number;
  message: string;
}

interface FormFieldValidation {
  isValid: boolean;
  hasNameError: boolean;
  hasTypeError: boolean;
  hasOptionsError: boolean;
  message: string;
}

/**
 * FormNodeEditor Component
 * 
 * Self-contained component that handles form node editing including:
 * - Form message input with character count and validation
 * - Form field management (add, edit, remove, reorder)
 * - Field type configuration with validation
 * - Variable insertion with cursor positioning
 * - Form-specific guidance and validation
 * 
 * Encapsulates its own validation logic, field management, and state.
 */
export const FormNodeEditor: React.FC = () => {
  const { currentNode, updateNode } = useNodeEditorStore();

  // Internal state for UI concerns
  const [isFocused, setIsFocused] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  const [newFieldName, setNewFieldName] = useState('');
  const [newFieldType, setNewFieldType] = useState('text');
  const [newFieldRequired, setNewFieldRequired] = useState(true);
  const [newFieldOptions, setNewFieldOptions] = useState('');

  // Internal ref for textarea management
  const formMessageRef = useRef<HTMLTextAreaElement>(null);

  if (!currentNode || currentNode.type !== BotResponseType.FORM) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateFormMessage = (message: string): FormValidation => {
    const length = message.length;
    const isValidLength = length >= MIN_FORM_MESSAGE_LENGTH && length <= MAX_FORM_MESSAGE_LENGTH;
    const remaining = MAX_FORM_MESSAGE_LENGTH - length;

    let validationMessage = '';
    if (length === 0) {
      validationMessage = 'Form message is required';
    } else if (length < MIN_FORM_MESSAGE_LENGTH) {
      validationMessage = `Message must be at least ${MIN_FORM_MESSAGE_LENGTH} character`;
    } else if (length > MAX_FORM_MESSAGE_LENGTH) {
      validationMessage = `Message exceeds maximum length by ${Math.abs(remaining)} characters`;
    }

    return {
      isValid: isValidLength,
      length,
      remaining,
      message: validationMessage
    };
  };

  // Internal helper to validate form field
  const validateFormField = (name: string, type: string, options: string): FormFieldValidation => {
    const hasNameError = !name.trim();
    const hasTypeError = !type;
    const hasOptionsError = type === 'select' && !options.trim();

    let message = '';
    if (hasNameError) {
      message = 'Field name is required';
    } else if (hasTypeError) {
      message = 'Field type is required';
    } else if (hasOptionsError) {
      message = 'Select fields require options (comma-separated)';
    }

    return {
      isValid: !hasNameError && !hasTypeError && !hasOptionsError,
      hasNameError,
      hasTypeError,
      hasOptionsError,
      message
    };
  };

  // Internal helper to get current form message
  const getCurrentFormMessage = (): string => {
    return currentNode.content?.text || '';
  };

  // Internal helper to get current form fields
  const getCurrentFormFields = (): IFormField[] => {
    return currentNode.content?.formFields || [];
  };

  // Internal helper to format character count display
  const formatCharacterCount = (length: number, remaining: number): string => {
    if (remaining < 0) {
      return `${length}/${MAX_FORM_MESSAGE_LENGTH} (${Math.abs(remaining)} over)`;
    }
    return `${length}/${MAX_FORM_MESSAGE_LENGTH}`;
  };

  // Internal helper to get character count color
  const getCharacterCountColor = (remaining: number): string => {
    if (remaining < 0) {
      return 'text-red-500 dark:text-red-400';
    } else if (remaining < 50) {
      return 'text-orange-500 dark:text-orange-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  // Internal event handlers
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    updateNode({
      content: {
        ...currentNode.content,
        text: newMessage
      }
    });
  };

  const handleTextareaFocus = () => {
    setIsFocused(true);
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
  };

  const handleCursorPositionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.currentTarget.selectionStart || 0);
  };

  // Internal variable insertion with scroll preservation
  const handleVariableClick = (variableName: string) => {
    const textarea = formMessageRef.current;
    const currentMessage = getCurrentFormMessage();

    if (textarea) {
      // Store current scroll position and cursor position
      const scrollTop = textarea.scrollTop;
      const selectionStart = textarea.selectionStart || 0;
      const selectionEnd = textarea.selectionEnd || 0;

      // Insert variable at cursor position
      const variableText = `{{${variableName}}}`;
      const newValue = currentMessage.substring(0, selectionStart) + variableText + currentMessage.substring(selectionEnd);

      // Update the node content
      updateNode({
        content: {
          ...currentNode.content,
          text: newValue
        }
      });

      // Restore focus and cursor position after React re-render
      setTimeout(() => {
        if (textarea) {
          textarea.focus();
          // Set cursor position after the inserted variable
          const newCursorPosition = selectionStart + variableText.length;
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          // Restore scroll position to prevent jumping to bottom
          textarea.scrollTop = scrollTop;
          setCursorPosition(newCursorPosition);
        }
      }, 0);
    } else {
      // Fallback: append to end if no textarea ref
      const variableText = `{{${variableName}}}`;
      updateNode({
        content: {
          ...currentNode.content,
          text: currentMessage + variableText
        }
      });
    }
  };

  // Internal form field management
  const handleAddField = () => {
    const validation = validateFormField(newFieldName, newFieldType, newFieldOptions);
    if (!validation.isValid) return;

    const currentFields = getCurrentFormFields();
    if (currentFields.length >= MAX_FORM_FIELDS) return;

    const newField: IFormField = {
      id: crypto.randomUUID(),
      label: newFieldName.trim(),
      type: newFieldType as IFormField['type'],
      required: newFieldRequired,
      ...(newFieldType === 'select' && {
        options: newFieldOptions.split(',').map(opt => opt.trim()).filter(opt => opt)
      })
    };

    updateNode({
      content: {
        ...currentNode.content,
        formFields: [...currentFields, newField]
      }
    });

    // Reset form
    setNewFieldName('');
    setNewFieldType('text');
    setNewFieldRequired(true);
    setNewFieldOptions('');
  };

  const handleRemoveField = (index: number) => {
    const currentFields = getCurrentFormFields();
    const updatedFields = currentFields.filter((_, i) => i !== index);
    updateNode({
      content: {
        ...currentNode.content,
        formFields: updatedFields
      }
    });
  };

  const currentMessage = getCurrentFormMessage();
  const messageValidation = validateFormMessage(currentMessage);
  const currentFields = getCurrentFormFields();
  const fieldValidation = validateFormField(newFieldName, newFieldType, newFieldOptions);

  return (
    <div className="space-y-4">
      {/* Form Node Information */}
      <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
        <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-3 flex items-center">
          📝 Form Input Configuration
        </h4>
        <p className="text-xs text-green-700 dark:text-green-300 mb-3">
          This node collects structured information from users through a form interface. Configure the form message and fields below.
        </p>
        <div className="text-xs text-green-600 dark:text-green-400">
          Form responses are stored in the configured variable for use in subsequent nodes.
        </div>
      </div>

      {/* Form Message Input */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label htmlFor="formMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Form Message
          </Label>
          <span className={`text-xs ${getCharacterCountColor(messageValidation.remaining)}`}>
            {formatCharacterCount(messageValidation.length, messageValidation.remaining)}
          </span>
        </div>

        <Textarea
          id="formMessage"
          ref={formMessageRef}
          value={currentMessage}
          onChange={handleMessageChange}
          onFocus={handleTextareaFocus}
          onBlur={handleTextareaBlur}
          onSelect={handleCursorPositionChange}
          onKeyUp={handleKeyUp}
          placeholder={DEFAULT_PLACEHOLDER}
          rows={3}
          className={`mt-1 ${!messageValidation.isValid
              ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
              : isFocused
                ? 'border-green-300 dark:border-green-600'
                : ''
            }`}
          maxLength={MAX_FORM_MESSAGE_LENGTH}
        />

        {!messageValidation.isValid && (
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">
            {messageValidation.message}
          </p>
        )}

        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {HELP_TEXT}
        </p>
      </div>

      {/* Available Variables Reference */}
      <VariableReferenceSection
        onVariableClick={handleVariableClick}
        title="Available Variables for Form Message:"
      />

      {/* Form Fields Configuration */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Form Fields ({currentFields.length}/{MAX_FORM_FIELDS})
          </h4>
        </div>

        {/* Add New Field */}
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Add New Field</h5>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="fieldName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Field Name
              </Label>
              <Input
                id="fieldName"
                value={newFieldName}
                onChange={(e) => setNewFieldName(e.target.value)}
                placeholder="e.g., full_name, email_address"
                className={`mt-1 ${fieldValidation.hasNameError ? 'border-red-500' : ''}`}
              />
            </div>

            <div>
              <Label htmlFor="fieldType" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Field Type
              </Label>
              <Select
                value={newFieldType}
                onChange={(value) => setNewFieldType(value as string)}
                options={FIELD_TYPES}
                placeholder="Select field type"
              />
            </div>

            {newFieldType === 'select' && (
              <div className="md:col-span-2">
                <Label htmlFor="fieldOptions" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Options (comma-separated)
                </Label>
                <Input
                  id="fieldOptions"
                  value={newFieldOptions}
                  onChange={(e) => setNewFieldOptions(e.target.value)}
                  placeholder="Option 1, Option 2, Option 3"
                  className={`mt-1 ${fieldValidation.hasOptionsError ? 'border-red-500' : ''}`}
                />
              </div>
            )}
          </div>

          <div className="flex items-center justify-between mt-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={newFieldRequired}
                onChange={(e) => setNewFieldRequired(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">Required field</span>
            </label>

            <Button
              onClick={handleAddField}
              disabled={!fieldValidation.isValid || currentFields.length >= MAX_FORM_FIELDS}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              Add Field
            </Button>
          </div>

          {!fieldValidation.isValid && (
            <p className="text-xs text-red-500 dark:text-red-400 mt-2">
              {fieldValidation.message}
            </p>
          )}
        </div>

        {/* Current Fields List */}
        {currentFields.length > 0 && (
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-900 dark:text-white">Current Fields</h5>
            {currentFields.map((field, index) => (
              <div
                key={field.id}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900 dark:text-white">{field.label}</span>
                    <span className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-600 text-gray-600 dark:text-gray-300 rounded">
                      {field.type}
                    </span>
                    {field.required && (
                      <span className="text-xs px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded">
                        Required
                      </span>
                    )}
                  </div>
                  {field.options && (
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Options: {field.options.join(', ')}
                    </p>
                  )}
                </div>
                <Button
                  onClick={() => handleRemoveField(index)}
                  variant="outline"
                  size="sm"
                  className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}

        {currentFields.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No form fields configured yet.</p>
            <p className="text-xs mt-1">Add fields above to collect user information.</p>
          </div>
        )}
      </div>
      {/* Development Analysis */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 Form Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Message Length:</strong> {messageValidation.length} characters</p>
            <p><strong>Remaining:</strong> {messageValidation.remaining} characters</p>
            <p><strong>Form Fields:</strong> {currentFields.length}/{MAX_FORM_FIELDS}</p>
            <p><strong>Required Fields:</strong> {currentFields.filter(f => f.required).length}</p>
            <p><strong>Cursor Position:</strong> {cursorPosition}</p>
            <p><strong>Is Focused:</strong> {isFocused ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

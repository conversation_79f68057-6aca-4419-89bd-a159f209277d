'use client';

import React, {useCallback, useState} from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Select } from '@/components/ui/Select';
import { Checkbox } from '@/components/ui/Checkbox';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { generateAITemplate, AITemplateRequest } from '@/server/actions/ai-template-generator';
import { IFlowTemplate } from '@/types/bot';
import { Sparkles, Loader2, Wand2, Brain, Zap } from 'lucide-react';
import { logger } from '@/utils/logger';

interface AITemplateGeneratorProps {
  onTemplateGenerated: (template: IFlowTemplate) => void;
}

export const AITemplateGenerator: React.FC<AITemplateGeneratorProps> = ({
  onTemplateGenerated
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedTemplate, setGeneratedTemplate] = useState<IFlowTemplate | null>(null);
  const [showAIGenerator, setShowAIGenerator] = useState(false);

  const handleOpenChange = useCallback((open: boolean) => {
    setShowAIGenerator(open);
    if (!open) {
      // Reset state when closing
      setGeneratedTemplate(null);
      setIsGenerating(false);
    }
  }, []);
  
  const [formData, setFormData] = useState<AITemplateRequest>({
    prompt: '',
    category: 'general',
    difficulty: 'beginner',
    includeApiIntegration: false,
    includeFormCollection: false,
    includeConditionalLogic: false,
    businessType: '',
    targetAudience: ''
  });

  const { success, error } = useToast();

  const categoryOptions = [
    { value: 'general', label: 'General Purpose' },
    { value: 'customer_support', label: 'Customer Support' },
    { value: 'lead_qualification', label: 'Lead Qualification' },
    { value: 'ecommerce', label: 'E-commerce' },
    { value: 'appointment_booking', label: 'Appointment Booking' }
  ];

  const difficultyOptions = [
    { value: 'beginner', label: 'Beginner (3-5 nodes)' },
    { value: 'intermediate', label: 'Intermediate (6-12 nodes)' },
    { value: 'advanced', label: 'Advanced (13+ nodes)' }
  ];

  const handleInputChange = <K extends keyof AITemplateRequest>(field: K, value: AITemplateRequest[K]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerate = async () => {
    if (!formData.prompt.trim()) {
      error('Error', 'Please provide a description of the bot you want to create');
      return;
    }

    setIsGenerating(true);
    try {
      const result = await generateAITemplate(formData);
      
      if (result.success && result.data) {
        setGeneratedTemplate(result.data);
        success('Success', result.message);
      } else {
        error('Error', result.error || 'Failed to generate template');
      }
    } catch (err) {
      logger.error('Error generating AI template:', err);
      error('Error', 'An unexpected error occurred while generating the template');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleUseTemplate = () => {
    if (generatedTemplate) {
      onTemplateGenerated(generatedTemplate);
      handleOpenChange(false);
      setFormData({
        prompt: '',
        category: 'general',
        difficulty: 'beginner',
        includeApiIntegration: false,
        includeFormCollection: false,
        includeConditionalLogic: false,
        businessType: '',
        targetAudience: ''
      });
    }
  };

  const handleClose = () => {
    handleOpenChange(false);
  };

  const renderGenerationForm = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full">
            <Sparkles className="w-8 h-8 text-white" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          AI Template Generator
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Describe your bot requirements and let AI create a complete conversation flow template
        </p>
      </div>

      {/* Main Prompt */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Bot Description *
        </label>
        <Textarea
          value={formData.prompt}
          onChange={(e) => handleInputChange('prompt', e.target.value)}
          placeholder="Describe what you want your bot to do. For example: 'Create a customer support bot that helps users with product returns, tracks order status, and collects feedback'"
          rows={4}
          className="w-full"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Be specific about the {"bot's"} purpose, target audience, and key features
        </p>
      </div>

      {/* Configuration Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Category
          </label>
          <Select
            value={formData.category || 'general'}
            onChange={(value: string | number | null) => handleInputChange('category', value as string)}
            options={categoryOptions}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Complexity Level
          </label>
          <Select
            value={formData.difficulty || 'beginner'}
            onChange={(value: string | number | null) => handleInputChange('difficulty', value as 'beginner' | 'intermediate' | 'advanced')}
            options={difficultyOptions}
          />
        </div>
      </div>

      {/* Optional Context */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Business Type (Optional)
          </label>
          <Input
            value={formData.businessType}
            onChange={(e) => handleInputChange('businessType', e.target.value)}
            placeholder="e.g., SaaS, E-commerce, Healthcare"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Target Audience (Optional)
          </label>
          <Input
            value={formData.targetAudience}
            onChange={(e) => handleInputChange('targetAudience', e.target.value)}
            placeholder="e.g., Existing customers, New leads"
          />
        </div>
      </div>

      {/* Feature Toggles */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Include Advanced Features
        </label>
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <Checkbox
              checked={formData.includeFormCollection || false}
              onChange={(checked: boolean) => handleInputChange('includeFormCollection', checked)}
            />
            <div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Form Collection
              </span>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Add forms to collect user information
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Checkbox
              checked={formData.includeApiIntegration || false}
              onChange={(checked: boolean) => handleInputChange('includeApiIntegration', checked)}
            />
            <div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                API Integration
              </span>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Include external API calls for data retrieval
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Checkbox
              checked={formData.includeConditionalLogic || false}
              onChange={(checked: boolean) => handleInputChange('includeConditionalLogic', checked)}
            />
            <div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Conditional Logic
              </span>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Add branching logic based on user responses
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Generate Button */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          variant="outline"
          onClick={handleClose}
          disabled={isGenerating}
        >
          Cancel
        </Button>
        <Button
          onClick={handleGenerate}
          disabled={isGenerating || !formData.prompt.trim()}
          className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
        >
          {isGenerating ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            <>
              <Wand2 className="w-4 h-4 mr-2" />
              Generate Template
            </>
          )}
        </Button>
      </div>
    </div>
  );

  const renderTemplatePreview = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-3 bg-gradient-to-r from-green-500 to-blue-500 rounded-full">
            <Brain className="w-8 h-8 text-white" />
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Generated Template Preview
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Review your AI-generated template before applying it to your flow
        </p>
      </div>

      {generatedTemplate && (
        <Card className="p-6">
          {/* Template Info */}
          <div className="mb-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {generatedTemplate.name}
                </h4>
                <p className="text-gray-600 dark:text-gray-400 mb-3">
                  {generatedTemplate.description}
                </p>
              </div>
              <Badge
                variant={
                  generatedTemplate.difficulty === 'beginner' ? 'success' :
                  generatedTemplate.difficulty === 'intermediate' ? 'warning' : 'danger'
                }
              >
                {generatedTemplate.difficulty}
              </Badge>
            </div>

            {/* Template Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {generatedTemplate.nodes.length}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Nodes</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {generatedTemplate.connections.length}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Connections</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {generatedTemplate.variables.length}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Variables</div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                  {generatedTemplate.estimatedSetupTime}m
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">Setup Time</div>
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-4">
              {generatedTemplate.tags.map((tag, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Node Types Summary */}
          <div className="mb-6">
            <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Node Types Included:
            </h5>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {Array.from(new Set(generatedTemplate.nodes.map(node => node.type))).map((nodeType) => {
                const count = generatedTemplate.nodes.filter(node => node.type === nodeType).length;
                const typeLabels = {
                  'text': 'Text Messages',
                  'buttons': 'Button Menus',
                  'quick_reply': 'Quick Replies',
                  'form': 'Data Forms',
                  'handoff': 'Agent Handoff',
                  'conditional': 'Conditional Logic',
                  'api_call': 'API Calls'
                };

                return (
                  <div key={nodeType} className="flex items-center space-x-2 text-sm">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-gray-600 dark:text-gray-400">
                      {typeLabels[nodeType as keyof typeof typeLabels] || nodeType} ({count})
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Variables Preview */}
          {generatedTemplate.variables.length > 0 && (
            <div className="mb-6">
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Variables:
              </h5>
              <div className="space-y-2">
                {generatedTemplate.variables.map((variable, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                    <div>
                      <span className="font-mono text-sm text-blue-600 dark:text-blue-400">
                        {variable.name}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                        ({variable.type})
                      </span>
                    </div>
                    {variable.description && (
                      <span className="text-xs text-gray-600 dark:text-gray-400">
                        {variable.description}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          variant="outline"
          onClick={() => setGeneratedTemplate(null)}
        >
          Generate New
        </Button>
        <Button
          onClick={handleUseTemplate}
          className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
        >
          <Zap className="w-4 h-4 mr-2" />
          Use This Template
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <Button
        onClick={() => setShowAIGenerator(true)}
        className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200"
      >
        <Sparkles className="w-5 h-5 mr-2" />
        Generate with AI
      </Button>
      <Dialog
        title="AI Template Generator"
        open={showAIGenerator}
        onOpenChange={handleOpenChange}
        size="large"
        // className="max-w-4xl"
      >
        {generatedTemplate ? renderTemplatePreview() : renderGenerationForm()}
      </Dialog>
    </>
  );
};

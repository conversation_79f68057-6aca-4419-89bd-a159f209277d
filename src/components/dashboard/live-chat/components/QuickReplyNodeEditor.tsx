'use client';

import React, { useRef, useState } from 'react';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Button } from '@/components/ui/Button';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { VariableReferenceSection } from './VariableReferenceSection';
import { BotResponseType, IFlowVariable, IResponseOption } from '@/types/bot';

// Component-specific constants
const MIN_QUICK_REPLY_MESSAGE_LENGTH = 1;
const MAX_QUICK_REPLY_MESSAGE_LENGTH = 1000;
const MAX_QUICK_REPLY_OPTIONS = 10;
const MAX_QUICK_REPLY_TEXT_LENGTH = 30;
const DEFAULT_PLACEHOLDER = "Enter the message that will appear with the quick reply options. Use {{variable_name}} to personalize...";
const HELP_TEXT = "This message appears with the quick reply options. Use variables to personalize the quick reply prompt.";

// Component-specific interfaces
interface QuickReplyMessageValidation {
  isValid: boolean;
  length: number;
  remaining: number;
  message: string;
}

/**
 * QuickReplyNodeEditor Component
 * 
 * Self-contained component that handles quick reply node editing including:
 * - Quick reply message input with character count and validation
 * - Quick reply option management (add, edit, remove, reorder)
 * - Quick reply text and value configuration with validation
 * - Variable insertion with cursor positioning
 * - Variable storage configuration
 * - Quick reply-specific guidance and validation
 * 
 * Encapsulates its own validation logic, quick reply management, and state.
 */
export const QuickReplyNodeEditor: React.FC = () => {
  const { currentNode, updateNode, variables } = useNodeEditorStore();

  // Internal state for UI concerns
  const [isFocused, setIsFocused] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  const [newQuickReplyText, setNewQuickReplyText] = useState('');
  const [newQuickReplyValue, setNewQuickReplyValue] = useState('');

  // Internal ref for textarea management
  const quickReplyMessageRef = useRef<HTMLTextAreaElement>(null);

  if (!currentNode || currentNode.type !== BotResponseType.QUICK_REPLY) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateQuickReplyMessage = (message: string): QuickReplyMessageValidation => {
    const length = message.length;
    const isValidLength = length >= MIN_QUICK_REPLY_MESSAGE_LENGTH && length <= MAX_QUICK_REPLY_MESSAGE_LENGTH;
    const remaining = MAX_QUICK_REPLY_MESSAGE_LENGTH - length;

    let validationMessage = '';
    if (length === 0) {
      validationMessage = 'Quick reply message is required';
    } else if (length < MIN_QUICK_REPLY_MESSAGE_LENGTH) {
      validationMessage = `Message must be at least ${MIN_QUICK_REPLY_MESSAGE_LENGTH} character`;
    } else if (length > MAX_QUICK_REPLY_MESSAGE_LENGTH) {
      validationMessage = `Message exceeds maximum length by ${Math.abs(remaining)} characters`;
    }

    return {
      isValid: isValidLength,
      length,
      remaining,
      message: validationMessage
    };
  };

  // Internal helper to validate quick reply option
  const validateQuickReplyOption = (text: string, value: string): { isValid: boolean; message: string } => {
    if (!text.trim()) {
      return { isValid: false, message: 'Quick reply text is required' };
    }
    if (text.length > MAX_QUICK_REPLY_TEXT_LENGTH) {
      return { isValid: false, message: `Quick reply text exceeds ${MAX_QUICK_REPLY_TEXT_LENGTH} characters` };
    }
    if (!value.trim()) {
      return { isValid: false, message: 'Quick reply value is required' };
    }
    return { isValid: true, message: '' };
  };

  // Internal helper to get current quick reply message
  const getCurrentQuickReplyMessage = (): string => {
    return currentNode.content?.text || '';
  };

  // Internal helper to get current quick reply options
  const getCurrentQuickReplyOptions = (): IResponseOption[] => {
    return currentNode.content?.options || [];
  };

  // Internal helper to format character count display
  const formatCharacterCount = (length: number, remaining: number): string => {
    if (remaining < 0) {
      return `${length}/${MAX_QUICK_REPLY_MESSAGE_LENGTH} (${Math.abs(remaining)} over)`;
    }
    return `${length}/${MAX_QUICK_REPLY_MESSAGE_LENGTH}`;
  };

  // Internal helper to get character count color
  const getCharacterCountColor = (remaining: number): string => {
    if (remaining < 0) {
      return 'text-red-500 dark:text-red-400';
    } else if (remaining < 50) {
      return 'text-orange-500 dark:text-orange-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  // Internal event handlers
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    updateNode({
      content: {
        ...currentNode.content,
        text: newMessage
      }
    });
  };

  const handleTextareaFocus = () => {
    setIsFocused(true);
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
  };

  const handleCursorPositionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.currentTarget.selectionStart || 0);
  };

  // Internal variable insertion with scroll preservation
  const handleVariableClick = (variableName: string) => {
    const textarea = quickReplyMessageRef.current;
    const currentMessage = getCurrentQuickReplyMessage();
    
    if (textarea) {
      // Store current scroll position and cursor position
      const scrollTop = textarea.scrollTop;
      const selectionStart = textarea.selectionStart || 0;
      const selectionEnd = textarea.selectionEnd || 0;
      
      // Insert variable at cursor position
      const variableText = `{{${variableName}}}`;
      const newValue = currentMessage.substring(0, selectionStart) + variableText + currentMessage.substring(selectionEnd);
      
      // Update the node content
      updateNode({
        content: {
          ...currentNode.content,
          text: newValue
        }
      });
      
      // Restore focus and cursor position after React re-render
      setTimeout(() => {
        if (textarea) {
          textarea.focus();
          // Set cursor position after the inserted variable
          const newCursorPosition = selectionStart + variableText.length;
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          // Restore scroll position to prevent jumping to bottom
          textarea.scrollTop = scrollTop;
          setCursorPosition(newCursorPosition);
        }
      }, 0);
    } else {
      // Fallback: append to end if no textarea ref
      const variableText = `{{${variableName}}}`;
      updateNode({
        content: {
          ...currentNode.content,
          text: currentMessage + variableText
        }
      });
    }
  };

  // Internal quick reply option management
  const handleAddQuickReply = () => {
    const validation = validateQuickReplyOption(newQuickReplyText, newQuickReplyValue);
    if (!validation.isValid) return;

    const currentOptions = getCurrentQuickReplyOptions();
    if (currentOptions.length >= MAX_QUICK_REPLY_OPTIONS) return;

    const newOption: IResponseOption = {
      id: crypto.randomUUID(),
      text: newQuickReplyText.trim(),
      value: newQuickReplyValue.trim()
    };

    updateNode({
      content: {
        ...currentNode.content,
        options: [...currentOptions, newOption]
      }
    });

    // Reset form
    setNewQuickReplyText('');
    setNewQuickReplyValue('');
  };

  const handleRemoveQuickReply = (index: number) => {
    const currentOptions = getCurrentQuickReplyOptions();
    const updatedOptions = currentOptions.filter((_, i) => i !== index);
    updateNode({
      content: {
        ...currentNode.content,
        options: updatedOptions
      }
    });
  };

  const handleUpdateQuickReply = (index: number, text: string, value: string) => {
    const currentOptions = getCurrentQuickReplyOptions();
    const updatedOptions = currentOptions.map((option, i) =>
      i === index ? { ...option, text, value } : option
    );
    updateNode({
      content: {
        ...currentNode.content,
        options: updatedOptions
      }
    });
  };

  const currentMessage = getCurrentQuickReplyMessage();
  const messageValidation = validateQuickReplyMessage(currentMessage);
  const currentOptions = getCurrentQuickReplyOptions();
  const quickReplyValidation = validateQuickReplyOption(newQuickReplyText, newQuickReplyValue);

  return (
    <div className="space-y-4">
      {/* Quick Reply Node Information */}
      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
        <h4 className="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-3 flex items-center">
          ⚡ Quick Reply Options Configuration
        </h4>
        <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-3">
          This node presents users with quick reply options that appear as convenient buttons. Perfect for common responses and quick actions.
        </p>
        <div className="text-xs text-yellow-600 dark:text-yellow-400">
          Quick replies disappear after selection to keep the chat interface clean.
        </div>
      </div>

      {/* Quick Reply Message Input */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label htmlFor="quickReplyMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Quick Reply Message
          </Label>
          <span className={`text-xs ${getCharacterCountColor(messageValidation.remaining)}`}>
            {formatCharacterCount(messageValidation.length, messageValidation.remaining)}
          </span>
        </div>
        
        <Textarea
          id="quickReplyMessage"
          ref={quickReplyMessageRef}
          value={currentMessage}
          onChange={handleMessageChange}
          onFocus={handleTextareaFocus}
          onBlur={handleTextareaBlur}
          onSelect={handleCursorPositionChange}
          onKeyUp={handleKeyUp}
          placeholder={DEFAULT_PLACEHOLDER}
          rows={3}
          className={`mt-1 ${
            !messageValidation.isValid
              ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
              : isFocused
                ? 'border-yellow-300 dark:border-yellow-600'
                : ''
          }`}
          maxLength={MAX_QUICK_REPLY_MESSAGE_LENGTH}
        />
        
        {!messageValidation.isValid && (
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">
            {messageValidation.message}
          </p>
        )}
        
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {HELP_TEXT}
        </p>
      </div>

      {/* Available Variables Reference */}
      <VariableReferenceSection
        onVariableClick={handleVariableClick}
        title="Available Variables for Quick Reply Message:"
      />

      {/* Quick Reply Options Configuration */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Quick Reply Options ({currentOptions.length}/{MAX_QUICK_REPLY_OPTIONS})
          </h4>
        </div>

        {/* Add New Quick Reply */}
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Add New Quick Reply</h5>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="quickReplyText" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Quick Reply Text
              </Label>
              <Input
                id="quickReplyText"
                value={newQuickReplyText}
                onChange={(e) => setNewQuickReplyText(e.target.value)}
                placeholder="e.g., Yes, No, More info"
                className="mt-1"
                maxLength={MAX_QUICK_REPLY_TEXT_LENGTH}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Text displayed on the quick reply ({newQuickReplyText.length}/{MAX_QUICK_REPLY_TEXT_LENGTH})
              </p>
            </div>

            <div>
              <Label htmlFor="quickReplyValue" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Quick Reply Value
              </Label>
              <Input
                id="quickReplyValue"
                value={newQuickReplyValue}
                onChange={(e) => setNewQuickReplyValue(e.target.value)}
                placeholder="e.g., yes, no, more_info"
                className="mt-1"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Value stored when quick reply is selected
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {!quickReplyValidation.isValid && quickReplyValidation.message}
            </div>

            <Button
              onClick={handleAddQuickReply}
              disabled={!quickReplyValidation.isValid || currentOptions.length >= MAX_QUICK_REPLY_OPTIONS}
              className="bg-yellow-600 hover:bg-yellow-700 text-white"
            >
              Add Quick Reply
            </Button>
          </div>
        </div>

        {/* Current Quick Replies List */}
        {currentOptions.length > 0 && (
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-900 dark:text-white">Current Quick Replies</h5>
            {currentOptions.map((option, index) => (
              <div
                key={option.id}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex-1 grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Text</Label>
                    <Input
                      value={option.text}
                      onChange={(e) => handleUpdateQuickReply(index, e.target.value, option.value)}
                      className="mt-1 h-8 text-sm"
                      maxLength={MAX_QUICK_REPLY_TEXT_LENGTH}
                    />
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Value</Label>
                    <Input
                      value={option.value}
                      onChange={(e) => handleUpdateQuickReply(index, option.text, e.target.value)}
                      className="mt-1 h-8 text-sm"
                    />
                  </div>
                </div>
                <Button
                  onClick={() => handleRemoveQuickReply(index)}
                  variant="outline"
                  size="sm"
                  className="ml-4 text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}

        {currentOptions.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No quick reply options configured yet.</p>
            <p className="text-xs mt-1">Add quick replies above to give users convenient response options.</p>
          </div>
        )}
      </div>

      {/* Variable Storage Configuration */}
      <div>
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
          Store Response In Variable
        </Label>
        <Select
          value={(currentNode.content?.storeInVariable as string) || ''}
          onChange={(value) => {
            const updatedContent = { ...currentNode.content };
            if (value === '' || value === null) {
              delete updatedContent.storeInVariable;
            } else {
              updatedContent.storeInVariable = value as string;
            }
            updateNode({ content: updatedContent });
          }}
          options={[
            { label: "Don't store in variable", value: '' },
            { label: 'customer_name (system)', value: 'customer_name' },
            { label: 'customer_email (system)', value: 'customer_email' },
            ...variables.map((variable: IFlowVariable) => ({
              label: `${variable.name} (${variable.type})`,
              value: variable.name
            }))
          ]}
          placeholder="Select variable to store quick reply selection"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Choose a variable to store the user's quick reply selection for later use in the conversation.
        </p>
      </div>

      {/* Development Analysis */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 Quick Replies Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Message Length:</strong> {messageValidation.length} characters</p>
            <p><strong>Remaining:</strong> {messageValidation.remaining} characters</p>
            <p><strong>Quick Reply Options:</strong> {currentOptions.length}/{MAX_QUICK_REPLY_OPTIONS}</p>
            <p><strong>Cursor Position:</strong> {cursorPosition}</p>
            <p><strong>Is Focused:</strong> {isFocused ? 'Yes' : 'No'}</p>
            <p><strong>Storage Variable:</strong> {currentNode.content?.storeInVariable || 'None'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

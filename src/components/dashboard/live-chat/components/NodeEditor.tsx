'use client';

import React, {useEffect} from 'react';
import { Card } from '@/components/ui/Card';
import {
  IEnhancedFlowNode,
  IFlowVariable,
  BotResponseType,
} from '@/types/bot';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { NodeNameEditor } from './NodeNameEditor';
import { NodeSettingsPanel } from './NodeSettingsPanel';
import { TextNodeEditor } from './TextNodeEditor';
import { HandoffNodeEditor } from './HandoffNodeEditor';
import { FormNodeEditor } from './FormNodeEditor';
import { ButtonsNodeEditor } from './ButtonsNodeEditor';
import { QuickReplyNodeEditor } from './QuickReplyNodeEditor';
import { ConditionalNodeEditor } from './ConditionalNodeEditor';
import { ApiCallNodeEditor } from './ApiCallNodeEditor';

interface NodeEditorProps {
  node: IEnhancedFlowNode;
  onUpdate: (updates: Partial<IEnhancedFlowNode>) => void;
  variables: IFlowVariable[];
}

// Note: VariableReferenceSection and insertVariableAtCursor moved to separate component for better encapsulation

export const NodeEditor: React.FC<NodeEditorProps> = ({ node, onUpdate, variables }) => {
  // Initialize the Zustand store with current props
  const { setCurrentNode, setVariables, setOnUpdate } = useNodeEditorStore();

  // Initialize store when component mounts or props change
  useEffect(() => {
    setCurrentNode(node);
    setVariables(variables);
    setOnUpdate(onUpdate);
  }, [node, variables, onUpdate, setCurrentNode, setVariables, setOnUpdate]);

  return (
    <Card className="p-4 space-y-4 max-h-[90vh] overflow-y-auto">
      {/* Node Name Editor Component */}
      <NodeNameEditor />

      {/* Text Node Editor Component */}
      {node.type === BotResponseType.TEXT && <TextNodeEditor />}

      {/* Conditional Node Editor Component */}
      {node.type === BotResponseType.CONDITIONAL && <ConditionalNodeEditor />}
      
      {/* Buttons Node Editor Component */}
      {node.type === BotResponseType.BUTTONS && <ButtonsNodeEditor />}

      {/* Quick Reply Node Editor Component */}
      {node.type === BotResponseType.QUICK_REPLY && <QuickReplyNodeEditor />}

      {/* Form Node Editor Component */}
      {node.type === BotResponseType.FORM && <FormNodeEditor />}

      {/* Handoff Node Editor Component */}
      {node.type === BotResponseType.HANDOFF && <HandoffNodeEditor />}

      {/* API Call Node Editor Component */}
      {node.type === BotResponseType.API_CALL && <ApiCallNodeEditor />}

      <NodeSettingsPanel />
    </Card>
  );
};

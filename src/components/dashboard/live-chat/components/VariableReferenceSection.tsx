'use client';

import React from 'react';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { IFlowVariable } from '@/types/bot';
import { logger } from '@/utils/logger';

// Component-specific interfaces
interface VariableReferenceSectionProps {
  onVariableClick: (variableName: string) => void;
  title?: string;
}

// Component-specific constants
const SYSTEM_VARIABLES = [
  { name: 'customer_name', description: 'Customer\'s name' },
  { name: 'customer_email', description: 'Customer\'s email' }
];

const DEFAULT_TITLE = "Available Variables:";
const NO_VARIABLES_MESSAGE = "System variables (customer_name, customer_email) are always available. Create flow variables to add more personalization options.";

/**
 * VariableReferenceSection Component
 * 
 * Self-contained component that handles variable display and insertion including:
 * - System variables display (customer_name, customer_email)
 * - Flow variables display with type information
 * - Click-to-insert functionality
 * - Visual differentiation between system and flow variables
 * - Helpful messaging when no flow variables exist
 * 
 * Encapsulates its own display logic and variable categorization.
 */
export const VariableReferenceSection: React.FC<VariableReferenceSectionProps> = ({ 
  onVariableClick, 
  title = DEFAULT_TITLE 
}) => {
  const { variables } = useNodeEditorStore();

  // Internal helper to format variable display name
  const formatVariableDisplayName = (variable: IFlowVariable): string => {
    if (variable.type) {
      return `${variable.name} (${variable.type})`;
    }
    return variable.name;
  };

  // Internal helper to get variable tooltip
  const getVariableTooltip = (variable: IFlowVariable, isSystem: boolean = false): string => {
    if (isSystem) {
      const systemVar = SYSTEM_VARIABLES.find(sv => sv.name === variable.name);
      return systemVar 
        ? `System variable: ${systemVar.description}. Click to add to text`
        : 'System variable. Click to add to text';
    }
    
    return `Flow variable${variable.type ? ` (${variable.type})` : ''}. Click to add to text`;
  };

  // Internal helper to handle variable click with validation
  const handleVariableClick = (variableName: string) => {
    if (!variableName || typeof variableName !== 'string') {
      logger.warn('Invalid variable name provided to VariableReferenceSection');
      return;
    }
    
    onVariableClick(variableName);
  };

  // Internal helper to check if variables exist
  const hasFlowVariables = (): boolean => {
    return variables && variables.length > 0;
  };

  // Internal helper to render system variable
  const renderSystemVariable = (sysVar: typeof SYSTEM_VARIABLES[0]) => (
    <span
      key={sysVar.name}
      className="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 cursor-pointer hover:bg-green-200 dark:hover:bg-green-800 transition-colors"
      onClick={() => handleVariableClick(sysVar.name)}
      title={`System variable: ${sysVar.description}. Click to add to text`}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleVariableClick(sysVar.name);
        }
      }}
    >
      {sysVar.name} (system)
    </span>
  );

  // Internal helper to render flow variable
  const renderFlowVariable = (variable: IFlowVariable, index: number) => (
    <span
      key={variable.name || `variable-${index}`}
      className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
      onClick={() => handleVariableClick(variable.name)}
      title={getVariableTooltip(variable)}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleVariableClick(variable.name);
        }
      }}
    >
      {formatVariableDisplayName(variable)}
    </span>
  );

  // Internal helper to render no variables message
  const renderNoVariablesMessage = () => (
    <p className="text-xs text-gray-500 dark:text-gray-400">
      {NO_VARIABLES_MESSAGE}
    </p>
  );

  return (
    <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
        {title}
      </h5>
      
      <div className="flex flex-wrap gap-1">
        {/* System Variables */}
        {SYSTEM_VARIABLES.map(renderSystemVariable)}

        {/* Flow Variables */}
        {hasFlowVariables() && variables.map(renderFlowVariable)}

        {/* No Variables Message */}
        {!hasFlowVariables() && renderNoVariablesMessage()}
      </div>
    </div>
  );
};

// Export utility function for cursor-based variable insertion
export const insertVariableAtCursor = (
  textareaRef: React.RefObject<HTMLTextAreaElement | HTMLInputElement | null>,
  variableName: string,
  currentValue: string,
  onUpdate: (newValue: string) => void
) => {
  // Validate inputs
  if (!variableName || typeof variableName !== 'string') {
    logger.warn('Invalid variable name provided to insertVariableAtCursor');
    return;
  }

  if (typeof currentValue !== 'string') {
    logger.warn('Invalid current value provided to insertVariableAtCursor');
    return;
  }

  if (typeof onUpdate !== 'function') {
    logger.warn('Invalid onUpdate function provided to insertVariableAtCursor');
    return;
  }

  const textarea = textareaRef.current;
  if (textarea) {
    const start = textarea.selectionStart ?? 0;
    const end = textarea.selectionEnd ?? 0;
    const variableText = `{{${variableName}}}`;
    const newValue = currentValue.substring(0, start) + variableText + currentValue.substring(end);
    
    onUpdate(newValue);

    // Set cursor position after the inserted variable
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + variableText.length, start + variableText.length);
    }, 0);
  } else {
    // Fallback: append to end if no textarea ref
    const variableText = `{{${variableName}}}`;
    onUpdate(currentValue + variableText);
  }
};

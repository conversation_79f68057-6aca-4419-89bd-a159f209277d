'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { fetchBotTemplates } from '@server/actions/bot-template-actions';
import { useUrlState } from '@/hooks/useUrlStateManager';
import { logger } from '@/utils/logger';
import { AITemplateGenerator } from './AITemplateGenerator';
import { IFlowTemplate as AIFlowTemplate } from '@/types/bot';

type IFlowTemplate = Awaited<ReturnType<typeof fetchBotTemplates>>
type FlowData = NonNullable<IFlowTemplate['data']>[number];

// URL state interface for template selection
interface TemplateSelectorUrlState {
  selectedTemplate?: string;
  templateCategory?: string;
  templateView?: string;
  templateFilter?: string;
}

// Error boundary component for defensive programming
const TemplateSelectorErrorBoundary: React.FC<{
  children: React.ReactNode;
  onError: (error: string) => void
}> = ({ children, onError }) => {
  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      logger.error('TemplateSelector Error:', event.error);
      onError(`An unexpected error occurred: ${event.error?.message || 'Unknown error'}`);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      logger.error('TemplateSelector Unhandled Promise Rejection:', event.reason);
      onError(`An unexpected error occurred: ${event.reason?.message || 'Promise rejection'}`);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return <>{children}</>;
};

interface TemplateSelectorProps {
  onTemplateSelect: (template: FlowData) => void;
  onShowTemplateSelectorChange: (show: boolean) => void;
  onActiveTabChange: (tab: 'design' | 'templates' | 'api' | 'variables' | 'settings') => void;
  onFlowDataChange: (updates: Partial<FlowData>) => void;
}


export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onTemplateSelect,
  onShowTemplateSelectorChange,
  onActiveTabChange,
  onFlowDataChange
}) => {
  // State management
  const [templates, setTemplates] = useState<IFlowTemplate['data']>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // URL state management with validation
  const { urlState, setUrl } = useUrlState({
    selectedTemplate: '',
    templateCategory: 'all',
    templateView: 'grid',
    templateFilter: ''
  }, 'template-selector');

  // Validation functions
  const validateTemplateId = useCallback((templateId: string | undefined): boolean => {
    if (!templateId) return true; // Empty is valid (no selection)
    return templates?.some(template => template.id === templateId) || false;
  }, [templates]);

  const isValidUrlState = useCallback((state: TemplateSelectorUrlState): boolean => {
    // Validate template view
    if (state.templateView && !['grid', 'list'].includes(state.templateView)) {
      return false;
    }

    // Validate selected template if provided
    if (state.selectedTemplate && !validateTemplateId(state.selectedTemplate)) {
      return false;
    }

    return true;
  }, [validateTemplateId]);

  // Safe navigation functions with validation
  const selectTemplate = useCallback((templateId: string) => {
    // Validate template exists
    if (!validateTemplateId(templateId)) {
      logger.error(`Invalid template ID: ${templateId}`);
      setError(`Template not found: ${templateId}`);
      return;
    }

    setUrl({
      ...urlState,
      selectedTemplate: templateId
    });
  }, [urlState, setUrl, validateTemplateId, setError]);

  const setTemplateCategory = useCallback((category: string) => {
    setUrl({
      ...urlState,
      templateCategory: category,
      selectedTemplate: '' // Clear selection when changing category
    });
  }, [urlState, setUrl]);

  const setTemplateView = useCallback((view: 'grid' | 'list') => {
    setUrl({
      ...urlState,
      templateView: view
    });
  }, [urlState, setUrl]);

  const clearSelection = useCallback(() => {
    setUrl({
      ...urlState,
      selectedTemplate: ''
    });
  }, [urlState, setUrl]);

  // Derived state from URL with validation
  const safeUrlState = isValidUrlState(urlState) ? urlState : {
    selectedTemplate: '',
    templateCategory: 'all',
    templateView: 'grid',
    templateFilter: ''
  };

  const selectedTemplateId = safeUrlState.selectedTemplate;
  const currentCategory = safeUrlState.templateCategory || 'all';
  const currentView = safeUrlState.templateView || 'grid';
  const currentFilter = safeUrlState.templateFilter || '';

  // Get selected template object
  const selectedTemplate = selectedTemplateId ?
    templates?.find(template => template.id === selectedTemplateId) : null;

  // Load templates function
  const loadTemplates = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const result = await fetchBotTemplates();

      if (result.success && result.data) {
        setTemplates(result.data);
      } else {
        setError(result.error || result.message || 'Failed to load templates');
      }
    } catch (err) {
      setError('Failed to load templates');
      logger.error('Error loading templates:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // All useEffect hooks before any conditional returns
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Handle invalid template ID by clearing selection
  useEffect(() => {
    if (selectedTemplateId && !validateTemplateId(selectedTemplateId)) {
      logger.warn(`Invalid template ID in URL: ${selectedTemplateId}, clearing selection`);
      setError(`Template not found: ${selectedTemplateId}`);
      clearSelection();
    }
  }, [selectedTemplateId]);

  // Clear error when navigating away from error state
  useEffect(() => {
    if (error && !selectedTemplateId) {
      // Clear error after a delay to allow user to see it
      const timer = setTimeout(() => setError(null), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, selectedTemplateId]);

  // Template selection handler with URL state integration
  const handleTemplateSelect = useCallback((template: FlowData) => {
    // Update URL state
    selectTemplate(template.id);

    // Call parent handler
    onTemplateSelect(template);
    onShowTemplateSelectorChange(false);
    onActiveTabChange('design');
  }, [selectTemplate, onTemplateSelect, onShowTemplateSelectorChange, onActiveTabChange]);

  // Handle AI-generated template
  const handleAITemplateGenerated = useCallback((template: AIFlowTemplate) => {
    // Convert AI template to FlowData format
    const flowData: FlowData = {
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      nodes: template.nodes,
      connections: template.connections,
      variables: template.variables,
      integrations: template.integrations,
      tags: template.tags,
      difficulty: template.difficulty,
      estimatedSetupTime: template.estimatedSetupTime,
      isActive: true
    };

    handleTemplateSelect(flowData);
  }, [handleTemplateSelect]);

  const handleStartFromScratch = useCallback(() => {
    // Clear URL state
    clearSelection();

    onShowTemplateSelectorChange(false);
    onActiveTabChange('design');

    // Initialize with empty flow
    const emptyFlow: FlowData = {
      id: '',
      category: '',
      difficulty: "beginner",
      tags: [],
      estimatedSetupTime: new Date().getTime(),
      name: 'New Flow',
      description: '',
      nodes: [],
      connections: [],
      variables: [],
      isActive: false,
      integrations: []
    };
    onFlowDataChange(emptyFlow);
  }, [clearSelection, onShowTemplateSelectorChange, onActiveTabChange, onFlowDataChange]);

  // Filter templates based on URL state
  const filteredTemplates = useCallback(() => {
    if (!templates) return [];

    let filtered = [...templates];

    // Filter by category
    if (currentCategory && currentCategory !== 'all') {
      filtered = filtered.filter(template =>
        template.category?.toLowerCase() === currentCategory.toLowerCase()
      );
    }

    // Filter by search term
    if (currentFilter) {
      const searchTerm = currentFilter.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchTerm) ||
        template.description.toLowerCase().includes(searchTerm) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    return filtered;
  }, [templates, currentCategory, currentFilter]);

  // Get unique categories for filtering
  const availableCategories = useCallback(() => {
    if (!templates) return [];

    const categories = templates
      .map(template => template.category)
      .filter(Boolean)
      .filter((category, index, arr) => arr.indexOf(category) === index);

    return ['all', ...categories];
  }, [templates]);

  return (
    <TemplateSelectorErrorBoundary onError={setError}>
      <Card className="p-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Choose a Template
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Start with a pre-built template, generate one with AI, or create a custom flow from scratch
          </p>

          {/* AI Generator Button */}
          <div className="flex justify-center mb-6">
            <AITemplateGenerator
              onTemplateGenerated={handleAITemplateGenerated}
            />
          </div>
        </div>

        {/* Error State with Dismissible Message */}
        {error && (
          <div className="mb-6">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setError(null)}
                  className="text-red-600 border-red-300 hover:bg-red-50"
                >
                  Dismiss
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Filters and Search */}
        {!loading && templates && templates.length > 0 && (
          <div className="mb-6 space-y-4">
            {/* Search Input */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={currentFilter}
                  onChange={(e) => setUrl({ ...urlState, templateFilter: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-800 dark:text-white"
                />
              </div>

              {/* View Toggle */}
              <div className="flex border border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
                <button
                  onClick={() => setTemplateView('grid')}
                  className={`px-3 py-2 text-sm ${
                    currentView === 'grid'
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  Grid
                </button>
                <button
                  onClick={() => setTemplateView('list')}
                  className={`px-3 py-2 text-sm ${
                    currentView === 'list'
                      ? 'bg-indigo-600 text-white'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  List
                </button>
              </div>
            </div>

            {/* Category Filter */}
            <div className="flex flex-wrap gap-2">
              {availableCategories().map((category) => (
                <button
                  key={category}
                  onClick={() => setTemplateCategory(category)}
                  className={`px-3 py-1 text-sm rounded-full transition-colors capitalize ${
                    currentCategory === category
                      ? 'bg-indigo-600 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {category === 'all' ? 'All Categories' : category?.replaceAll("_", " ")}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center py-12">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#8178E8]"></div>
            <p className="mt-4 text-gray-600 dark:text-gray-400">Loading templates...</p>
          </div>
        )}

        {/* Templates Display */}
        {!loading && !error && (
          <>
            {filteredTemplates().length === 0 ? (
              <div className="text-center py-12">
                <div className="w-12 h-12 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
                  </svg>
                </div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  No Templates Found
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  {currentFilter || currentCategory !== 'all'
                    ? 'Try adjusting your filters or search terms'
                    : 'No templates are available at the moment'
                  }
                </p>
                {(currentFilter || currentCategory !== 'all') && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setTemplateCategory('all');
                      setUrl({ ...urlState, templateFilter: '' });
                    }}
                    size="sm"
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className={`mb-8 ${
                currentView === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              }`}>
                {filteredTemplates().map((template) => (
                  <div
                    key={template.id}
                    onClick={() => handleTemplateSelect(template)}
                    className={`border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:border-indigo-500 hover:shadow-lg transition-all duration-200 ${
                      selectedTemplateId === template.id
                        ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20'
                        : ''
                    } ${
                      currentView === 'grid' ? 'p-6' : 'p-4 flex items-center space-x-4'
                    }`}
                  >
                    {currentView === 'grid' ? (
                      <>
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {template.name}
                          </h3>
                          <Badge variant={template.difficulty === 'beginner' ? 'success' : template.difficulty === 'intermediate' ? 'warning' : 'danger'}>
                            <span className="capitalize">{template.difficulty}</span>
                          </Badge>
                        </div>

                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                          {template.description}
                        </p>

                        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                          <span>⏱️ {template.estimatedSetupTime} min setup</span>
                          <div className="flex space-x-1">
                            {template.tags.slice(0, 2).map((tag) => (
                              <span key={tag} className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {template.name}
                            </h3>
                            <Badge variant={template.difficulty === 'beginner' ? 'success' : template.difficulty === 'intermediate' ? 'warning' : 'danger'}>
                              <span className="capitalize">{template.difficulty}</span>
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            {template.description}
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                            <span>⏱️ {template.estimatedSetupTime} min setup</span>
                            <div className="flex space-x-1">
                              {template.tags.slice(0, 3).map((tag) => (
                                <span key={tag} className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                  {tag}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {/* Start from Scratch Option */}
        {!loading && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Or Start from Scratch
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Create a completely custom conversation flow
              </p>
              <Button
                onClick={handleStartFromScratch}
                variant="outline"
                className="border-[#8178E8] text-[#8178E8] hover:bg-[#8178E8] hover:text-white"
              >
                Start from Scratch
              </Button>
            </div>
          </div>
        )}

        {/* AI Template Generator */}
        
      </Card>
    </TemplateSelectorErrorBoundary>
  );
};

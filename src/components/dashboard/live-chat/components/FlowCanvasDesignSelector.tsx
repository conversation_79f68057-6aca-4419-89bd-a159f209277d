'use client';

import React from 'react';
// import { Button } from '@/components/ui/Button';
// import { useNodeEditorStore } from '@/stores/nodeEditorStore';

export const FlowCanvasDesignSelector: React.FC = () => {
  // const { flowCanvasDesign, setFlowCanvasDesign } = useNodeEditorStore();
   return null
  // return (
  //   <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
  //     <Button
  //       size="sm"
  //       variant={flowCanvasDesign === 'classic' ? 'default' : 'ghost'}
  //       onClick={() => setFlowCanvasDesign('classic')}
  //       className="h-8 px-3 text-xs font-medium transition-all duration-200"
  //       title="Classic design with glass morphism and curved connections"
  //     >
  //       🎨 Classic
  //     </Button>
  //     <Button
  //       size="sm"
  //       variant={flowCanvasDesign === 'modern' ? 'default' : 'ghost'}
  //       onClick={() => setFlowCanvasDesign('modern')}
  //       className="h-8 px-3 text-xs font-medium transition-all duration-200"
  //       title="Modern design with sharp edges and straight connections"
  //     >
  //       ⚡ Modern
  //     </Button>
  //   </div>
  // );
};

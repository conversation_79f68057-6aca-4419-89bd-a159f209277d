'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Badge } from '@/components/ui/Badge';
import { IEnhancedFlowNode, BotResponseType } from '@/types/bot';

interface FlowNodeComponentProps {
  node: IEnhancedFlowNode;
  isSelected: boolean;
  isMultiSelected: boolean; // New prop for multi-selection state
  selectedNodes: string[]; // Array of all selected node IDs
  onSelect: () => void;
  onMultiSelect: (nodeId: string, isCtrlPressed: boolean) => void; // New handler for multi-selection
  onUpdate: (updates: Partial<IEnhancedFlowNode>) => void;
  onMultiNodeDragStart: () => void; // New handler for multi-node drag
  onMultiNodeDrag: (deltaX: number, deltaY: number) => void; // New handler for multi-node drag
  onMultiNodeDragEnd: () => void; // New handler for multi-node drag end
  onDelete: () => void;
  onConnect: (targetId: string) => void;
  isConnecting?: boolean;
  onStartConnection?: () => void;
  onEndConnection?: (nodeId: string) => void;
}

export const FlowNodeComponent: React.FC<FlowNodeComponentProps> = ({
  node,
  isSelected,
  isMultiSelected,
  selectedNodes,
  onSelect,
  onMultiSelect,
  onUpdate,
  onMultiNodeDragStart,
  onMultiNodeDrag,
  onMultiNodeDragEnd,
  onDelete,
  isConnecting = false,
  onStartConnection,
  onEndConnection
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const nodeRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();

    const isCtrlPressed = e.ctrlKey || e.metaKey;

    // Always set up dragging first
    setIsDragging(true);
    setDragStart({
      x: e.clientX - node.position.x,
      y: e.clientY - node.position.y
    });

    // Handle selection logic
    if (isCtrlPressed) {
      // Ctrl+click for multi-selection
      onMultiSelect(node.id, true);
      if (selectedNodes.length > 0) {
        onMultiNodeDragStart();
      }
    } else if (isMultiSelected && selectedNodes.length > 1) {
      // If this node is part of a multi-selection and no Ctrl, start multi-node drag
      onMultiNodeDragStart();
    } else {
      // Single selection
      onSelect();
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      if (selectedNodes.length > 1 && selectedNodes.includes(node.id)) {
        // Multi-node drag - calculate delta from current position
        const deltaX = newX - node.position.x;
        const deltaY = newY - node.position.y;
        onMultiNodeDrag(deltaX, deltaY);
      } else {
        // Single node drag
        onUpdate({
          position: {
            x: newX,
            y: newY
          }
        });
      }
    }
  };

  const handleMouseUp = () => {
    if (isDragging && selectedNodes.length > 1 && selectedNodes.includes(node.id)) {
      onMultiNodeDragEnd();
    }
    setIsDragging(false);
  };

  // Keyboard event handling for delete functionality
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (isSelected) {
      // Check if any input field has focus
      const activeElement = document.activeElement;
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT' ||
        (activeElement as HTMLElement).contentEditable === 'true'
      );

      // Only delete node if no input field is focused
      if (!isInputFocused && (e.key === 'Delete' || e.key === 'Backspace')) {
        e.preventDefault();
        onDelete();
      }
    }
  }, [isSelected, onDelete]);

  // Focus management
  useEffect(() => {
    if (isSelected && nodeRef.current) {
      nodeRef.current.focus();
    }
  }, [isSelected]);

  // Keyboard event listener
  useEffect(() => {
    if (isSelected) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isSelected, handleKeyDown]);



  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart]);





  const getNodeIcon = (type: BotResponseType) => {
    switch (type) {
      case BotResponseType.TEXT: return '💬';
      case BotResponseType.BUTTONS: return '🔘';
      case BotResponseType.QUICK_REPLY: return '⚡';
      case BotResponseType.FORM: return '📝';
      case BotResponseType.HANDOFF: return '👤';
      case BotResponseType.CONDITIONAL: return '🔀';
      case BotResponseType.API_CALL: return '🔗';
      default: return '📄';
    }
  };

  const getNodeColor = (type: BotResponseType) => {
    switch (type) {
      case BotResponseType.TEXT:
        return 'from-blue-500/20 to-blue-600/20 border-blue-400/30 text-blue-700 dark:text-blue-300';
      case BotResponseType.BUTTONS:
        return 'from-green-500/20 to-green-600/20 border-green-400/30 text-green-700 dark:text-green-300';
      case BotResponseType.QUICK_REPLY:
        return 'from-yellow-500/20 to-yellow-600/20 border-yellow-400/30 text-yellow-700 dark:text-yellow-300';
      case BotResponseType.FORM:
        return 'from-purple-500/20 to-purple-600/20 border-purple-400/30 text-purple-700 dark:text-purple-300';
      case BotResponseType.HANDOFF:
        return 'from-red-500/20 to-red-600/20 border-red-400/30 text-red-700 dark:text-red-300';
      case BotResponseType.CONDITIONAL:
        return 'from-orange-500/20 to-orange-600/20 border-orange-400/30 text-orange-700 dark:text-orange-300';
      case BotResponseType.API_CALL:
        return 'from-cyan-500/20 to-cyan-600/20 border-cyan-400/30 text-cyan-700 dark:text-cyan-300';
      default:
        return 'from-gray-500/20 to-gray-600/20 border-gray-400/30 text-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div
      ref={nodeRef}
      className={`absolute cursor-move select-none transition-all duration-200 outline-none ${
        isSelected ? 'z-30' : 'z-10'
      }`}
      style={{
        left: node.position.x,
        top: node.position.y,
        transform: isDragging ? 'scale(1.05)' : 'scale(1)',
        transition: isDragging ? 'none' : 'transform 0.2s ease-in-out'
      }}
      onMouseDown={handleMouseDown}
      onClick={onSelect}
      tabIndex={0}
      role="button"
      aria-label={`Flow node: ${node.name}`}
      aria-selected={isSelected}
    >
      <div className={`
        relative w-52 bg-white/85 dark:bg-[#1e1e28]/85 backdrop-blur-md
        border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl
        shadow-lg hover:shadow-xl hover:shadow-[#8178E8]/15
        transition-all duration-300 hover:-translate-y-1
        ${isSelected ? 'ring-2 ring-[#8178E8] shadow-xl shadow-[#8178E8]/25 ring-offset-2' : ''}
        ${isMultiSelected && !isSelected ? 'ring-2 ring-blue-500 shadow-xl shadow-blue-500/25 ring-offset-2' : ''}
        ${isDragging ? 'shadow-2xl shadow-[#8178E8]/35' : ''}
        group overflow-visible
      `}>
        {/* Enhanced gradient overlay for node type */}
        <div className={`absolute inset-0 bg-gradient-to-br ${getNodeColor(node.type)} opacity-20 rounded-xl`} />

        {/* Glass morphism overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-white/5 dark:from-white/10 dark:to-white/2 rounded-xl" />

        {/* Node Header */}
        <div className="relative p-4 pb-3 z-10">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-white/90 dark:bg-[#1e1e28]/90 rounded-lg shadow-sm border border-white/50 dark:border-[#2c2d3d]/50">
                <span className="text-lg">{getNodeIcon(node.type)}</span>
              </div>
              <div className="flex items-center space-x-2">
                {node.isStartNode && (
                  <Badge variant="success" className="text-xs px-2 py-1 font-medium shadow-sm">
                    Start
                  </Badge>
                )}
                {node.isEndNode && (
                  <Badge variant="danger" className="text-xs px-2 py-1 font-medium shadow-sm">
                    End
                  </Badge>
                )}
                {/* Variable Usage Indicator */}
                {(((node.content as any).storeInVariable && (node.content as any).storeInVariable?.trim?.()) ||
                  ((node.content as any).responseVariable && (node.content as any).responseVariable?.trim?.()) ||
                  ((node.content as any).conditionVariable && (node.content as any).conditionVariable?.trim?.()) ||
                  ((node.content as any).requestBody && (node.content as any).requestBody.includes('{{'))
                ) && (
                  <Badge variant="info" className="text-xs px-2 py-1 font-medium shadow-sm">
                    📊
                  </Badge>
                )}
              </div>
            </div>

            {/* Enhanced Remove Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="
                flex items-center justify-center w-7 h-7 z-20
                bg-red-500/15 hover:bg-red-500/25
                border border-red-400/40 hover:border-red-500/60
                rounded-lg transition-all duration-200
                text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300
                hover:scale-110 active:scale-95
                opacity-80 group-hover:opacity-100
                focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-1
                shadow-sm hover:shadow-md
              "
              aria-label="Delete node"
              title="Delete node"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Node Content - Display Only */}
          <div className="space-y-2">
            {/* Node Name - Display Only */}
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white truncate drop-shadow-sm">
              {node.name}
            </h3>

            {/* Content Text - Display Only */}
            {node.type === BotResponseType.TEXT && (
              <p
                className="text-xs text-gray-700 dark:text-gray-300 leading-relaxed"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}
              >
                {node.content.text || 'No message content'}
              </p>
            )}

            {/* Non-text node type indicator */}
            {node.type !== BotResponseType.TEXT && (
              <p className="text-xs text-gray-600 dark:text-gray-400 italic">
                {node.type} node
              </p>
            )}
          </div>



          {/* Validation Errors */}
          {node.validationErrors && node.validationErrors.length > 0 && (
            <div className="mt-3 flex items-center space-x-2 p-2 bg-red-50/80 dark:bg-red-900/20 border border-red-200/50 dark:border-red-800/30 rounded-lg">
              <div className="flex items-center justify-center w-4 h-4 bg-red-100 dark:bg-red-900/40 rounded-full">
                <svg className="w-3 h-3 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-xs font-medium text-red-700 dark:text-red-300">
                {node.validationErrors.length} error{node.validationErrors.length > 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>

        {/* Connection Points */}
        {/* Input handle (left side) */}
        <div
          className="absolute -left-4 top-1/2 transform -translate-y-1/2 z-40 group/input"
          onClick={(e) => {
            e.stopPropagation();
            if (isConnecting && onEndConnection) {
              onEndConnection(node.id);
            }
          }}
        >
          <div className={`
            w-8 h-8 bg-white dark:bg-[#1e1e28] border-2 rounded-full cursor-pointer
            transition-all duration-200 shadow-lg hover:shadow-xl
            flex items-center justify-center hover:scale-110
            ${isConnecting
              ? 'border-green-500 bg-gradient-to-r from-green-500 to-green-600 shadow-green-500/40 animate-pulse scale-110'
              : 'border-[#E0D7FF] dark:border-[#2c2d3d] hover:border-[#8178E8] hover:bg-gradient-to-r hover:from-[#8178E8]/20 hover:to-[#6964D3]/20'
            }
          `}>
            <div className={`w-3 h-3 rounded-full transition-all duration-200 ${
              isConnecting ? 'bg-white' : 'bg-[#8178E8]'
            }`} />
          </div>

        </div>

        {/* Output handles - Enhanced for Conditional Nodes */}
        {node.type === BotResponseType.CONDITIONAL ? (
          <>
            {/* True branch (top-right) */}
            <div
              className="absolute -right-4 top-1/4 transform -translate-y-1/2 z-40 group/output-true"
              onClick={(e) => {
                e.stopPropagation();
                if (onStartConnection) {
                  onStartConnection();
                }
              }}
            >
              <div className={`
                w-8 h-8 bg-white dark:bg-[#1e1e28] border-2 rounded-full cursor-pointer
                transition-all duration-200 shadow-lg hover:shadow-xl
                flex items-center justify-center hover:scale-110
                ${isConnecting
                  ? 'border-green-500 bg-gradient-to-r from-green-500 to-green-600 shadow-green-500/40 scale-110'
                  : 'border-green-400 dark:border-green-500 hover:border-green-500 hover:bg-gradient-to-r hover:from-green-500/20 hover:to-green-600/20'
                }
              `}>
                <div className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  isConnecting ? 'bg-white' : 'bg-green-500'
                }`} />
              </div>
              {/* True branch label */}
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 rounded font-medium shadow-sm">
                TRUE
              </div>
            </div>

            {/* False branch (bottom-right) */}
            <div
              className="absolute -right-4 bottom-1/4 transform translate-y-1/2 z-40 group/output-false"
              onClick={(e) => {
                e.stopPropagation();
                if (onStartConnection) {
                  onStartConnection();
                }
              }}
            >
              <div className={`
                w-8 h-8 bg-white dark:bg-[#1e1e28] border-2 rounded-full cursor-pointer
                transition-all duration-200 shadow-lg hover:shadow-xl
                flex items-center justify-center hover:scale-110
                ${isConnecting
                  ? 'border-red-500 bg-gradient-to-r from-red-500 to-red-600 shadow-red-500/40 scale-110'
                  : 'border-red-400 dark:border-red-500 hover:border-red-500 hover:bg-gradient-to-r hover:from-red-500/20 hover:to-red-600/20'
                }
              `}>
                <div className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  isConnecting ? 'bg-white' : 'bg-red-500'
                }`} />
              </div>
              {/* False branch label */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium shadow-sm">
                FALSE
              </div>
            </div>
          </>
        ) : (
          /* Standard output handle for non-conditional nodes */
          <div
            className="absolute -right-4 top-1/2 transform -translate-y-1/2 z-40 group/output"
            onClick={(e) => {
              e.stopPropagation();
              if (onStartConnection) {
                onStartConnection();
              }
            }}
          >
            <div className={`
              w-8 h-8 bg-white dark:bg-[#1e1e28] border-2 rounded-full cursor-pointer
              transition-all duration-200 shadow-lg hover:shadow-xl
              flex items-center justify-center hover:scale-110
              ${isConnecting
                ? 'border-[#8178E8] bg-gradient-to-r from-[#8178E8] to-[#6964D3] shadow-[#8178E8]/40 scale-110'
                : 'border-[#E0D7FF] dark:border-[#2c2d3d] hover:border-[#8178E8] hover:bg-gradient-to-r hover:from-[#8178E8]/20 hover:to-[#6964D3]/20'
              }
            `}>
              <div className={`w-3 h-3 rounded-full transition-all duration-200 ${
                isConnecting ? 'bg-white' : 'bg-[#8178E8]'
              }`} />
            </div>

          </div>
        )}
      </div>
    </div>
  );
};

'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { Switch } from '@/components/ui/Switch';

import {IEnhancedFlowNode, IFlowIntegration, ClientFlowData, ValidationError} from "@/types/bot";
import { useToast } from '@/components/ui/Toast';
import { ValidationErrorModal } from './ValidationErrorModal';

interface FlowSettingsProps {
  flowData: ClientFlowData;
  flowId?: string | null;
  organizationId: string;
  onFlowDataChange: (updates: Partial<ClientFlowData>) => void;
  onValidateFlow: () => { isValid: boolean; errors: ValidationError[] };
  onToggleFlowStatus: (organizationId: string, flowId: string, isActive: boolean) => Promise<any>;
}

export const FlowSettings: React.FC<FlowSettingsProps> = ({
  flowData,
  flowId,
  organizationId,
  onFlowDataChange,
  onValidateFlow,
  onToggleFlowStatus
}) => {
  const { success, error, warn } = useToast();
  const [isValidating, setIsValidating] = useState(false);
  const [validationResult, setValidationResult] = useState<{ success: boolean; message: string } | null>(null);
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);

  const handleValidateFlow = () => {
    setIsValidating(true);
    setValidationResult(null);

    try {
      const validationResult = onValidateFlow();
      if (validationResult.isValid) {
        const warningCount = validationResult.errors.filter(e => e.type === 'warning').length;
        const message = warningCount > 0
          ? `Flow validation passed! ${warningCount} warning(s) found.`
          : 'Flow validation passed! No issues found.';
        setValidationResult({ success: true, message: `✅ ${message}` });
        success('Validation Success', message);

        // Show modal even for warnings if there are any
        if (warningCount > 0) {
          setValidationErrors(validationResult.errors);
          setShowValidationModal(true);
        }
      } else {
        const errorCount = validationResult.errors.filter(e => e.type === 'error').length;
        const warningCount = validationResult.errors.filter(e => e.type === 'warning').length;

        let message = `Validation failed with ${errorCount} error${errorCount !== 1 ? 's' : ''}`;
        if (warningCount > 0) {
          message += ` and ${warningCount} warning${warningCount !== 1 ? 's' : ''}`;
        }

        setValidationResult({ success: false, message: `❌ ${message}` });
        setValidationErrors(validationResult.errors);
        setShowValidationModal(true);
      }
    } catch (err) {
      const errorMessage = 'Validation failed due to an unexpected error';
      setValidationResult({ success: false, message: `❌ ${errorMessage}.` });
      error('Validation Error', errorMessage);
    } finally {
      setIsValidating(false);
    }
  };

  const handleToggleActiveStatus = async (isActive: boolean) => {
    if (!flowId) return;

    try {
      const result = await onToggleFlowStatus(organizationId, flowId, isActive);
      if (result.success) {
        onFlowDataChange({ isActive });
        success('Success', `Flow ${isActive ? 'activated' : 'deactivated'} successfully`);
      } else {
        error('Error', result.error || 'Failed to update flow status');
      }
    } catch (err) {
      error('Error', 'Failed to update flow status');
    }
  };

  const handleFixErrors = () => {
    setShowValidationModal(false);
    // You can add logic here to navigate to specific tabs or highlight problematic nodes
    // For now, we'll just close the modal and let the user manually fix the issues
  };



  return (
    <>
      <Card className="p-6">
        <div className="space-y-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Flow Settings
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Configure general settings for your conversation flow
            </p>
          </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="flowName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Flow Name *
            </Label>
            <Input
              id="flowName"
              value={flowData.name}
              onChange={(e) => onFlowDataChange({ name: e.target.value })}
              placeholder="Enter flow name"
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              This name will be displayed in the chatbot interface
            </p>
          </div>

          <div>
            <Label htmlFor="flowDescription" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Description
            </Label>
            <Textarea
              id="flowDescription"
              value={flowData.description}
              onChange={(e) => onFlowDataChange({ description: e.target.value })}
              placeholder="Describe what this flow does"
              rows={3}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 mt-1">
              Internal description for team reference
            </p>
          </div>

          {flowId && (
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Active Status
                </Label>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  Only one flow can be active at a time
                </p>
              </div>
              <Switch
                checked={flowData.isActive}
                onChange={(e) => handleToggleActiveStatus(e.value)}
              />
            </div>
          )}
        </div>

        {/* Flow Validation */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Flow Validation
          </h3>
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full"
              onClick={handleValidateFlow}
              disabled={isValidating}
            >
              {isValidating ? '🔄 Validating...' : '🔍 Validate Flow'}
            </Button>

            {validationResult && (
              <div className={`p-3 rounded-lg ${
                validationResult.success
                  ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                  : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
              }`}>
                <p className={`text-sm ${
                  validationResult.success
                    ? 'text-green-700 dark:text-green-300'
                    : 'text-red-700 dark:text-red-300'
                }`}>
                  {validationResult.message}
                </p>
              </div>
            )}
          </div>
        </div>



        {/* Flow Statistics */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Flow Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="text-sm font-medium text-blue-900 dark:text-blue-100">Status</div>
              <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                {flowId ? (flowData.isActive ? 'Active' : 'Inactive') : 'Draft'}
              </div>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <div className="text-sm font-medium text-green-900 dark:text-green-100">Type</div>
              <div className="text-lg font-bold text-green-700 dark:text-green-300">
                {flowId ? 'Saved Flow' : 'New Flow'}
              </div>
            </div>
            <div className="p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-sm font-medium text-purple-900 dark:text-purple-100">Version</div>
              <div className="text-lg font-bold text-purple-700 dark:text-purple-300">1.0</div>
            </div>
          </div>
        </div>
      </div>
    </Card>

    {/* Validation Error Modal */}
    <ValidationErrorModal
      open={showValidationModal}
      onClose={() => setShowValidationModal(false)}
      errors={validationErrors}
      onFixErrors={handleFixErrors}
    />
  </>
  );
};

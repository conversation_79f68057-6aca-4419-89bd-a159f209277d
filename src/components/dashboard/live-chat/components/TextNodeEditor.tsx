'use client';

import React, { useRef, useState } from 'react';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { VariableReferenceSection } from './VariableReferenceSection';
import { BotResponseType } from '@/types/bot';

// Component-specific constants
const MIN_TEXT_LENGTH = 1;
const MAX_TEXT_LENGTH = 2000;
const DEFAULT_PLACEHOLDER = "Enter your message text here. Use {{variable_name}} to insert dynamic content...";
const HELP_TEXT = "Use {{variable_name}} to insert dynamic content. Click variables below to add them at your cursor position.";

// Component-specific interfaces
interface TextValidation {
  isValid: boolean;
  length: number;
  remaining: number;
  message: string;
}

interface VariableUsage {
  variableName: string;
  isValid: boolean;
  positions: number[];
}

/**
 * TextNodeEditor Component
 * 
 * Self-contained component that handles text node editing including:
 * - Text input with character count and validation
 * - Variable insertion with cursor positioning
 * - Variable usage validation and highlighting
 * - Real-time text analysis and feedback
 * - Accessibility support for text editing
 * 
 * Encapsulates its own validation logic, text manipulation, and state management.
 */
export const TextNodeEditor: React.FC = () => {
  const { currentNode, updateNode, variables } = useNodeEditorStore();

  // Internal state for UI concerns
  const [isFocused, setIsFocused] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<number>(0);

  // Internal ref for textarea management
  const textMessageRef = useRef<HTMLTextAreaElement>(null);

  if (!currentNode || currentNode.type !== BotResponseType.TEXT) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateText = (text: string): TextValidation => {
    const length = text.length;
    const isValidLength = length >= MIN_TEXT_LENGTH && length <= MAX_TEXT_LENGTH;
    const remaining = MAX_TEXT_LENGTH - length;

    let message = '';
    if (length === 0) {
      message = 'Message text is required';
    } else if (length < MIN_TEXT_LENGTH) {
      message = `Message must be at least ${MIN_TEXT_LENGTH} character`;
    } else if (length > MAX_TEXT_LENGTH) {
      message = `Message exceeds maximum length by ${Math.abs(remaining)} characters`;
    }

    return {
      isValid: isValidLength,
      length,
      remaining,
      message
    };
  };

  // Internal helper to analyze variable usage
  const analyzeVariableUsage = (text: string): VariableUsage[] => {
    const variableMatches = text.match(/\{\{([^}]+)\}\}/g) || [];
    const usedVariables = variableMatches.map(match => match.slice(2, -2).trim());
    const availableVariableNames = ['customer_name', 'customer_email', ...variables.map(v => v.name)];

    const uniqueVariables = [...new Set(usedVariables)];
    
    return uniqueVariables.map(variableName => {
      const isValid = availableVariableNames.includes(variableName);
      const regex = new RegExp(`\\{\\{\\s*${variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*\\}\\}`, 'g');
      const positions: number[] = [];
      let match;
      
      while ((match = regex.exec(text)) !== null) {
        positions.push(match.index);
      }

      return {
        variableName,
        isValid,
        positions
      };
    });
  };

  // Internal helper to get current text value
  const getCurrentText = (): string => {
    return currentNode.content?.text || '';
  };

  // Internal helper to format character count display
  const formatCharacterCount = (length: number, remaining: number): string => {
    if (remaining < 0) {
      return `${length}/${MAX_TEXT_LENGTH} (${Math.abs(remaining)} over)`;
    }
    return `${length}/${MAX_TEXT_LENGTH}`;
  };

  // Internal helper to get character count color
  const getCharacterCountColor = (remaining: number): string => {
    if (remaining < 0) {
      return 'text-red-500 dark:text-red-400';
    } else if (remaining < 100) {
      return 'text-orange-500 dark:text-orange-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  // Internal event handlers
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    updateNode({
      content: {
        ...currentNode.content,
        text: newText
      }
    });
  };

  const handleTextareaFocus = () => {
    setIsFocused(true);
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
  };

  const handleCursorPositionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.currentTarget.selectionStart || 0);
  };

  const handleVariableClick = (variableName: string) => {
    const textarea = textMessageRef.current;
    const currentText = getCurrentText();

    if (textarea) {
      // Store current scroll position and cursor position
      const scrollTop = textarea.scrollTop;
      const selectionStart = textarea.selectionStart || 0;
      const selectionEnd = textarea.selectionEnd || 0;

      // Insert variable at cursor position
      const variableText = `{{${variableName}}}`;
      const newValue = currentText.substring(0, selectionStart) + variableText + currentText.substring(selectionEnd);

      // Update the node content
      updateNode({
        content: {
          ...currentNode.content,
          text: newValue
        }
      });

      // Restore focus and cursor position after React re-render
      setTimeout(() => {
        if (textarea) {
          textarea.focus();
          // Set cursor position after the inserted variable
          const newCursorPosition = selectionStart + variableText.length;
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          // Restore scroll position to prevent jumping to bottom
          textarea.scrollTop = scrollTop;
          setCursorPosition(newCursorPosition);
        }
      }, 0);
    } else {
      // Fallback: append to end if no textarea ref
      const variableText = `{{${variableName}}}`;
      updateNode({
        content: {
          ...currentNode.content,
          text: currentText + variableText
        }
      });
    }
  };

  // Internal helper to render variable validation warnings
  const renderVariableValidation = () => {
    const currentText = getCurrentText();
    const variableUsage = analyzeVariableUsage(currentText);
    const invalidVariables = variableUsage.filter(usage => !usage.isValid);

    if (invalidVariables.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg">
        <div className="flex items-start gap-2">
          <span className="text-orange-600 dark:text-orange-400 text-sm">⚠️</span>
          <div>
            <p className="text-xs font-medium text-orange-800 dark:text-orange-200">
              Undefined Variables Detected
            </p>
            <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
              The following variables are not defined: <strong>{invalidVariables.map(v => v.variableName).join(', ')}</strong>
            </p>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              Customers will see {"{{variable_name}}"} instead of actual values. Please define these variables or remove them from the message.
            </p>
          </div>
        </div>
      </div>
    );
  };

  const currentText = getCurrentText();
  const textValidation = validateText(currentText);

  return (
    <div className="space-y-4">
      {/* Text Input Section */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label htmlFor="nodeText" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Message Text
          </Label>
          <span className={`text-xs ${getCharacterCountColor(textValidation.remaining)}`}>
            {formatCharacterCount(textValidation.length, textValidation.remaining)}
          </span>
        </div>
        
        <Textarea
          id="nodeText"
          ref={textMessageRef}
          value={currentText}
          onChange={handleTextChange}
          onFocus={handleTextareaFocus}
          onBlur={handleTextareaBlur}
          onSelect={handleCursorPositionChange}
          onKeyUp={handleKeyUp}
          placeholder={DEFAULT_PLACEHOLDER}
          rows={3}
          className={`mt-1 ${
            !textValidation.isValid
              ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
              : isFocused
                ? 'border-blue-300 dark:border-blue-600'
                : ''
          }`}
          maxLength={MAX_TEXT_LENGTH}
        />
        
        {!textValidation.isValid && (
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">
            {textValidation.message}
          </p>
        )}
        
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {HELP_TEXT}
        </p>
      </div>

      {/* Variable Validation Warnings */}
      {renderVariableValidation()}

      {/* Available Variables Reference */}
      <VariableReferenceSection
        onVariableClick={handleVariableClick}
      />

      {/* Text Analysis Info (Development Helper) */}
      {process.env.NODE_ENV === 'development' && currentText && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 Text Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Length:</strong> {textValidation.length} characters</p>
            <p><strong>Remaining:</strong> {textValidation.remaining} characters</p>
            <p><strong>Variables Used:</strong> {analyzeVariableUsage(currentText).length}</p>
            <p><strong>Cursor Position:</strong> {cursorPosition}</p>
            <p><strong>Is Focused:</strong> {isFocused ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

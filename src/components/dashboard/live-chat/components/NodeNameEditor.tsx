'use client';

import React from 'react';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';

// Component-specific constants
const MAX_NODE_NAME_LENGTH = 100;
const NODE_NAME_REGEX = /^[a-zA-Z0-9_]*$/;

// Component-specific validation interface
interface NodeNameValidation {
  isValid: boolean;
  isValidLength: boolean;
  isValidCharacters: boolean;
  remaining: number;
  length: number;
  message: string;
}

/**
 * NodeNameEditor Component
 *
 * Self-contained component that handles node name input with validation including:
 * - Character count display (X/100)
 * - Real-time validation feedback
 * - Alphanumeric + underscore only validation
 * - Maximum length enforcement
 *
 * Encapsulates its own validation logic and helper functions.
 */
export const NodeNameEditor: React.FC = () => {
  const { currentNode, updateNode } = useNodeEditorStore();

  if (!currentNode) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateNodeName = (name: string): NodeNameValidation => {
    const length = name.length;
    const isValidLength = length <= MAX_NODE_NAME_LENGTH;
    const isValidCharacters = NODE_NAME_REGEX.test(name);
    const isValid = isValidLength && isValidCharacters;

    let message = '';
    if (!isValidLength) {
      message = `Name exceeds maximum length of ${MAX_NODE_NAME_LENGTH} characters`;
    } else if (!isValidCharacters) {
      message = 'Name can only contain letters, numbers, and underscores (_)';
    }

    return {
      isValid,
      isValidLength,
      isValidCharacters,
      remaining: MAX_NODE_NAME_LENGTH - length,
      length,
      message
    };
  };

  // Internal event handler
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateNode({ name: e.target.value });
  };

  const validation = validateNodeName(currentNode.name);

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <Label htmlFor="nodeName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Node Name
        </Label>
        <span className={`text-xs ${
          validation.isValid
            ? 'text-gray-500 dark:text-gray-400'
            : 'text-red-500 dark:text-red-400'
        }`}>
          {validation.length}/{MAX_NODE_NAME_LENGTH}
        </span>
      </div>

      <Input
        id="nodeName"
        value={currentNode.name}
        onChange={handleNameChange}
        placeholder="e.g., Welcome_Message or User_Input"
        className={`mt-1 ${
          !validation.isValid
            ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
            : ''
        }`}
        maxLength={MAX_NODE_NAME_LENGTH}
      />

      {!validation.isValid && (
        <p className="text-xs text-red-500 dark:text-red-400 mt-1">
          {validation.message}
        </p>
      )}

      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
        Use letters, numbers, and underscores (_) only.
      </p>
    </div>
  );
};

'use client';

import React, { useRef, useState } from 'react';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { VariableReferenceSection } from './VariableReferenceSection';
import { BotResponseType } from '@/types/bot';

// Component-specific constants
const MIN_HANDOFF_MESSAGE_LENGTH = 1;
const MAX_HANDOFF_MESSAGE_LENGTH = 1000;
const DEFAULT_HANDOFF_MESSAGE = "Thank you for contacting us. Let me connect you with one of our team members who can assist you further.";
const DEFAULT_PLACEHOLDER = "Enter the message customers will see when being transferred to a human agent. Use {{variable_name}} to personalize...";
const HELP_TEXT = "This message is shown to customers when they're being transferred to a human agent. Use variables to personalize the handoff experience.";

// Component-specific interfaces
interface HandoffValidation {
  isValid: boolean;
  length: number;
  remaining: number;
  message: string;
}

interface HandoffVariableUsage {
  variableName: string;
  isValid: boolean;
  positions: number[];
}

/**
 * HandoffNodeEditor Component
 * 
 * Self-contained component that handles handoff node editing including:
 * - Handoff message input with character count and validation
 * - Variable insertion with cursor positioning and scroll preservation
 * - Variable usage validation and warnings
 * - Real-time message analysis and feedback
 * - Handoff-specific guidance and best practices
 * 
 * Encapsulates its own validation logic, message formatting, and state management.
 */
export const HandoffNodeEditor: React.FC = () => {
  const { currentNode, updateNode, variables } = useNodeEditorStore();

  // Internal state for UI concerns
  const [isFocused, setIsFocused] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  const [showBestPractices, setShowBestPractices] = useState(false);

  // Internal ref for textarea management
  const handoffMessageRef = useRef<HTMLTextAreaElement>(null);

  if (!currentNode || currentNode.type !== BotResponseType.HANDOFF) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateHandoffMessage = (message: string): HandoffValidation => {
    const length = message.length;
    const isValidLength = length >= MIN_HANDOFF_MESSAGE_LENGTH && length <= MAX_HANDOFF_MESSAGE_LENGTH;
    const remaining = MAX_HANDOFF_MESSAGE_LENGTH - length;

    let validationMessage = '';
    if (length === 0) {
      validationMessage = 'Handoff message is required';
    } else if (length < MIN_HANDOFF_MESSAGE_LENGTH) {
      validationMessage = `Message must be at least ${MIN_HANDOFF_MESSAGE_LENGTH} character`;
    } else if (length > MAX_HANDOFF_MESSAGE_LENGTH) {
      validationMessage = `Message exceeds maximum length by ${Math.abs(remaining)} characters`;
    }

    return {
      isValid: isValidLength,
      length,
      remaining,
      message: validationMessage
    };
  };

  // Internal helper to analyze variable usage in handoff message
  const analyzeHandoffVariableUsage = (message: string): HandoffVariableUsage[] => {
    const variableMatches = message.match(/\{\{([^}]+)\}\}/g) || [];
    const usedVariables = variableMatches.map(match => match.slice(2, -2).trim());
    const availableVariableNames = ['customer_name', 'customer_email', ...variables.map(v => v.name)];

    const uniqueVariables = [...new Set(usedVariables)];
    
    return uniqueVariables.map(variableName => {
      const isValid = availableVariableNames.includes(variableName);
      const regex = new RegExp(`\\{\\{\\s*${variableName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*\\}\\}`, 'g');
      const positions: number[] = [];
      let match;
      
      while ((match = regex.exec(message)) !== null) {
        positions.push(match.index);
      }

      return {
        variableName,
        isValid,
        positions
      };
    });
  };

  // Internal helper to get current handoff message
  const getCurrentHandoffMessage = (): string => {
    return currentNode.content?.text || '';
  };

  // Internal helper to format character count display
  const formatCharacterCount = (length: number, remaining: number): string => {
    if (remaining < 0) {
      return `${length}/${MAX_HANDOFF_MESSAGE_LENGTH} (${Math.abs(remaining)} over)`;
    }
    return `${length}/${MAX_HANDOFF_MESSAGE_LENGTH}`;
  };

  // Internal helper to get character count color
  const getCharacterCountColor = (remaining: number): string => {
    if (remaining < 0) {
      return 'text-red-500 dark:text-red-400';
    } else if (remaining < 50) {
      return 'text-orange-500 dark:text-orange-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  // Internal event handlers
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    updateNode({
      content: {
        ...currentNode.content,
        text: newMessage
      }
    });
  };

  const handleTextareaFocus = () => {
    setIsFocused(true);
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
  };

  const handleCursorPositionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.currentTarget.selectionStart || 0);
  };

  // Internal variable insertion with scroll preservation
  const handleVariableClick = (variableName: string) => {
    const textarea = handoffMessageRef.current;
    const currentMessage = getCurrentHandoffMessage();
    
    if (textarea) {
      // Store current scroll position and cursor position
      const scrollTop = textarea.scrollTop;
      const selectionStart = textarea.selectionStart || 0;
      const selectionEnd = textarea.selectionEnd || 0;
      
      // Insert variable at cursor position
      const variableText = `{{${variableName}}}`;
      const newValue = currentMessage.substring(0, selectionStart) + variableText + currentMessage.substring(selectionEnd);
      
      // Update the node content
      updateNode({
        content: {
          ...currentNode.content,
          text: newValue
        }
      });
      
      // Restore focus and cursor position after React re-render
      setTimeout(() => {
        if (textarea) {
          textarea.focus();
          // Set cursor position after the inserted variable
          const newCursorPosition = selectionStart + variableText.length;
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          // Restore scroll position to prevent jumping to bottom
          textarea.scrollTop = scrollTop;
          setCursorPosition(newCursorPosition);
        }
      }, 0);
    } else {
      // Fallback: append to end if no textarea ref
      const variableText = `{{${variableName}}}`;
      updateNode({
        content: {
          ...currentNode.content,
          text: currentMessage + variableText
        }
      });
    }
  };

  // Internal helper to render variable validation warnings
  const renderVariableValidation = () => {
    const currentMessage = getCurrentHandoffMessage();
    const variableUsage = analyzeHandoffVariableUsage(currentMessage);
    const invalidVariables = variableUsage.filter(usage => !usage.isValid);

    if (invalidVariables.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded-lg">
        <div className="flex items-start gap-2">
          <span className="text-orange-600 dark:text-orange-400 text-sm">⚠️</span>
          <div>
            <p className="text-xs font-medium text-orange-800 dark:text-orange-200">
              Undefined Variables in Handoff Message
            </p>
            <p className="text-xs text-orange-700 dark:text-orange-300 mt-1">
              The following variables are not defined: <strong>{invalidVariables.map(v => v.variableName).join(', ')}</strong>
            </p>
            <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
              Customers will see {"{{variable_name}}"} instead of actual values during handoff.
            </p>
          </div>
        </div>
      </div>
    );
  };

  // Internal helper to render handoff best practices
  const renderBestPractices = () => {
    if (!showBestPractices) {
      return null;
    }

    return (
      <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
        <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
          📋 Handoff Message Best Practices
        </h5>
        <div className="space-y-2 text-xs text-blue-800 dark:text-blue-200">
          <div className="flex items-start gap-2">
            <span className="text-blue-600 dark:text-blue-400">•</span>
            <p><strong>Be reassuring:</strong> Let customers know they're being connected to help</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-600 dark:text-blue-400">•</span>
            <p><strong>Set expectations:</strong> Mention estimated wait times if known</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-600 dark:text-blue-400">•</span>
            <p><strong>Personalize:</strong> Use {`{{customer_name}}`} to make it feel personal</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-600 dark:text-blue-400">•</span>
            <p><strong>Keep it brief:</strong> Customers are waiting, so be concise but friendly</p>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-blue-600 dark:text-blue-400">•</span>
            <p><strong>Thank them:</strong> Acknowledge their patience and time</p>
          </div>
        </div>
      </div>
    );
  };

  const currentMessage = getCurrentHandoffMessage();
  const messageValidation = validateHandoffMessage(currentMessage);

  return (
    <div className="space-y-4">
      {/* Handoff Node Information */}
      <div className="p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
        <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-3 flex items-center">
          🤝 Agent Handoff Configuration
        </h4>
        <p className="text-xs text-purple-700 dark:text-purple-300 mb-3">
          This node transfers customers to a human agent. Configure the message customers see during the handoff process.
        </p>
        <div className="flex items-center justify-between">
          <span className="text-xs text-purple-600 dark:text-purple-400">
            Handoff will occur after this message is sent
          </span>
          <button
            onClick={() => setShowBestPractices(!showBestPractices)}
            className="text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-200 underline"
          >
            {showBestPractices ? 'Hide' : 'Show'} Best Practices
          </button>
        </div>
      </div>

      {/* Handoff Message Input */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label htmlFor="handoffMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Handoff Message
          </Label>
          <span className={`text-xs ${getCharacterCountColor(messageValidation.remaining)}`}>
            {formatCharacterCount(messageValidation.length, messageValidation.remaining)}
          </span>
        </div>
        
        <Textarea
          id="handoffMessage"
          ref={handoffMessageRef}
          value={currentMessage}
          onChange={handleMessageChange}
          onFocus={handleTextareaFocus}
          onBlur={handleTextareaBlur}
          onSelect={handleCursorPositionChange}
          onKeyUp={handleKeyUp}
          placeholder={DEFAULT_PLACEHOLDER}
          rows={3}
          className={`mt-1 ${
            !messageValidation.isValid
              ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
              : isFocused
                ? 'border-purple-300 dark:border-purple-600'
                : ''
          }`}
          maxLength={MAX_HANDOFF_MESSAGE_LENGTH}
        />
        
        {!messageValidation.isValid && (
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">
            {messageValidation.message}
          </p>
        )}
        
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {HELP_TEXT}
        </p>
      </div>

      {/* Variable Validation Warnings */}
      {renderVariableValidation()}

      {/* Available Variables Reference */}
      <VariableReferenceSection
        onVariableClick={handleVariableClick}
        title="Available Variables for Handoff:"
      />

      {/* Best Practices */}
      {renderBestPractices()}

      {/* Development Analysis */}
      {process.env.NODE_ENV === 'development' && currentMessage && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 Handoff Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Message Length:</strong> {messageValidation.length} characters</p>
            <p><strong>Remaining:</strong> {messageValidation.remaining} characters</p>
            <p><strong>Variables Used:</strong> {analyzeHandoffVariableUsage(currentMessage).length}</p>
            <p><strong>Cursor Position:</strong> {cursorPosition}</p>
            <p><strong>Is Focused:</strong> {isFocused ? 'Yes' : 'No'}</p>
            <p><strong>Best Practices Shown:</strong> {showBestPractices ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

'use client';

import React, { useState } from 'react';
import { IFlowConnection } from '@/types/bot';

interface ConnectionLineModernProps {
  connection: IFlowConnection;
  source: { x: number; y: number };
  target: { x: number; y: number };
  onDelete: () => void;
}

export const ConnectionLineModern: React.FC<ConnectionLineModernProps> = ({
  connection,
  source,
  target,
  onDelete
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Calculate connection points (right side of source, left side of target)
  const sourceX = source.x + 288; // Updated node width (w-72 = 288px)
  const sourceY = source.y + 60;  // Adjusted for larger node height
  const targetX = target.x;
  const targetY = target.y + 60;

  // Create professional orthogonal routing path with proper flowchart-style turns
  const createOrthogonalPath = () => {
    const minHorizontalDistance = 60; // Minimum horizontal distance before turning
    const horizontalGap = Math.abs(targetX - sourceX);

    // Calculate the horizontal offset for the first turn
    let horizontalOffset;
    if (horizontalGap < minHorizontalDistance * 2) {
      // For close nodes, use a fixed offset
      horizontalOffset = minHorizontalDistance;
    } else {
      // For distant nodes, use half the distance but ensure minimum
      horizontalOffset = Math.max(minHorizontalDistance, horizontalGap * 0.4);
    }

    // Calculate intermediate points for clean orthogonal routing
    const point1X = sourceX + horizontalOffset;  // First horizontal segment end
    const point1Y = sourceY;                     // Same Y as source
    const point2X = point1X;                     // Vertical segment (same X)
    const point2Y = targetY;                     // Vertical to target Y level
    const point3X = targetX;                     // Final horizontal to target
    const point3Y = targetY;                     // Same Y as target

    // Create path with sharp 90-degree turns
    return `M ${sourceX} ${sourceY} L ${point1X} ${point1Y} L ${point2X} ${point2Y} L ${point3X} ${point3Y}`;
  };

  const pathData = createOrthogonalPath();

  // Calculate proper midpoint for label positioning (center of the path)
  const horizontalOffset = Math.max(60, Math.abs(targetX - sourceX) * 0.4);
  const midX = sourceX + horizontalOffset / 2;
  const midY = (sourceY + targetY) / 2;

  return (
    <g>
      {/* Invisible wider path for easier hover detection */}
      <path
        d={pathData}
        stroke="transparent"
        strokeWidth="12"
        fill="none"
        className="cursor-pointer"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={(e) => {
          e.stopPropagation();
          if (isHovered) {
            onDelete();
          }
        }}
        style={{ pointerEvents: 'stroke' }}
      />

      {/* Visible connection line - thinner professional styling */}
      <path
        d={pathData}
        stroke={isHovered ? "#ef4444" : "#6366f1"}
        strokeWidth={isHovered ? "3" : "2"}
        fill="none"
        markerEnd="url(#arrowhead)"
        className="transition-all duration-200"
        style={{ pointerEvents: 'none' }}
      />

      {/* Connection label with clean professional styling */}
      {connection.label && (
        <g>
          <rect
            x={midX - 30}
            y={midY - 10}
            width="60"
            height="20"
            fill="white"
            stroke="#e5e7eb"
            strokeWidth="1"
            rx="4"
            className="drop-shadow-sm"
          />
          <text
            x={midX}
            y={midY + 4}
            textAnchor="middle"
            className="text-xs fill-gray-700 font-medium"
            style={{ pointerEvents: 'none' }}
          >
            {connection.label}
          </text>
        </g>
      )}

      {/* Delete button when hovered */}
      {isHovered && (
        <g>
          <circle
            cx={midX}
            cy={midY - 15}
            r="8"
            fill="#ef4444"
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          />
          <text
            x={midX}
            y={midY - 11}
            textAnchor="middle"
            className="text-xs fill-white font-bold cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
          >
            ×
          </text>
        </g>
      )}
    </g>
  );
};

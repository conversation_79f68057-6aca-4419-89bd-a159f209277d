'use client';

import React, {useCallback, useEffect, useRef, useMemo} from 'react';
import {logger} from '@/utils/logger';

/**
 * ChatSimulator Component - Refactored for Dual-Access Architecture
 *
 * This component has been refactored to follow the established dual-access pattern.
 * Bot-related functionality is now exposed through both:
 * 1. Internal Server Actions (with user authentication) - used by this component
 * 2. External API endpoints (with API key authentication) - for external integrations
 *
 * The component continues to use Server Actions internally while the same core
 * business logic is available via external APIs for widget integrations.
 *
 * Core functions are located in: src/server/core/live-chat/bot.ts
 * External APIs are located in: src/app/api/v1/live-chat/bot/
 *
 * No breaking changes - all existing functionality preserved.
 */

// Remove global window interface - using Zustand store instead
import {FlowState, FormState, PaginationState, useChatSimulatorStore} from '@/stores/chatSimulatorStore';
import {CustomerInfo} from '@/components/ui/CustomerInfoForm';
import {BotResponse, ConversationState} from '@/server/actions/bot-execution-actions';
import {useBotInteraction, useFormInitialization, useNotificationSound} from '@/hooks';
import {useSSE, parseSSEEventData} from '@/hooks/useSSE';
import {useTypingIndicator, useTypingListener} from '@/hooks/useTypingIndicator';
import {MessageList} from './v2/MessageList';
import {UserInput} from './v2/UserInput';
import {ErrorDisplay} from './v2/ErrorDisplay';
import {LoadingIndicator} from './v2/LoadingIndicator';
import {TypingIndicator} from '@/components/ui/TypingIndicator';

import {useConversationStore} from '@/stores/conversationStore';

import {v4} from "uuid";

interface ChatSimulatorV2Props {
  conversationState: ConversationState | null;
  botResponse: BotResponse | null;
  customerInfo: CustomerInfo;
  organizationId: string;
}

const ChatSimulatorV2Component: React.FC<ChatSimulatorV2Props> = ({
                                                                    conversationState,
                                                                    botResponse,
                                                                    customerInfo,
                                                                    organizationId,
                                                                  }) => {
  const {
    flowState,
    messages,
    inputValue,
    isLoading,
    isTyping,
    activeForm,
    paginationState,
    isLiveChatMode,
    liveChatConversationId,
    waitingForAgent,
    handoffTriggered,
    error,
    setConversationState,
    setBotResponse,
    setCustomerInfo,
    setFlowState,
    setInputValue,
    setIsLoading,
    setIsTyping,
    addMessage,
    addMessages,
    clearMessages,
    setError,
    clearError,
    addToFlowHistory,
    processPaginationCommand,
    setButtonClickHandler,
    setHandoffTriggered,
    setLiveChatConversationId,
    setWaitingForAgent,
    setLiveChatMode
  } = useChatSimulatorStore();
  
  // Use custom hooks for bot interaction and form handling
  const {
    handleBotMessage,
    handleLiveChatMessage,
    handleButtonClick
  } = useBotInteraction();

  // Initialize form handling
  useFormInitialization();

  // Initialize notification sound for incoming messages
  const { playNotification } = useNotificationSound({
    volume: 0.6,
    enabled: true,
    debounceMs: 1500 // Prevent rapid-fire notifications
  });

  // Conversation store for real-time updates
  const {
    conversations,
    selectedConversationMessages,
    selectedConversationId: storeSelectedConversationId,
    setSelectedConversationId
  } = useConversationStore();

  // Typing indicator hooks
  const {
    addTypingUser,
    removeTypingUser,
    getTypingUsers,
    clearAllTypingUsers
  } = useTypingListener();

  // Customer typing indicator for live chat
  const customerTyping = useTypingIndicator({
    conversationId: liveChatConversationId || '',
    organizationId,
    userType: 'customer',
    userName: customerInfo.name || 'Customer',
    enabled: isLiveChatMode && !!liveChatConversationId
  });

  // Handoff waiting state (no more polling)
  const isPollingActiveRef = useRef(false); // Renamed but kept for compatibility
  const handoffAssignmentIdRef = useRef<string | null>(null);
  const handoffTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const statusUpdateIntervalRef = useRef<NodeJS.Timeout | any>(null); // Can hold timeout IDs
  const handoffStartTimeRef = useRef<number | null>(null);
  const agentAcceptedCountRef = useRef<number>(0);

  // Track previous message count for detecting new messages
  const previousMessageCountRef = React.useRef(0);

  // Memoize SSE params to prevent unnecessary reconnections
  // Support both handoff waiting (using assignment ID) and live chat (using conversation ID)
  const sseParams = useMemo(() => {
    // During live chat mode, use the live chat conversation ID
    if (isLiveChatMode && liveChatConversationId) {
      logger.log('🔧 SSE params: Using live chat mode', {
        conversationId: liveChatConversationId,
        organizationId,
        mode: 'live_chat'
      });
      return {
        conversationId: liveChatConversationId,
        organizationId
      };
    }

    // During handoff waiting, use the assignment ID as conversation ID
    // The SSE endpoint will need to handle assignment-based connections
    if (waitingForAgent && handoffAssignmentIdRef.current) {
      logger.log('🔧 SSE params: Using handoff waiting mode', {
        conversationId: handoffAssignmentIdRef.current,
        organizationId,
        mode: 'handoff_waiting'
      });
      return {
        conversationId: handoffAssignmentIdRef.current,
        organizationId
      };
    }

    logger.log('🔧 SSE params: No connection needed', {
      isLiveChatMode,
      liveChatConversationId,
      waitingForAgent,
      handoffAssignmentId: handoffAssignmentIdRef.current
    });
    return undefined;
  }, [isLiveChatMode, liveChatConversationId, waitingForAgent, organizationId]);

  // SSE event handler for real-time updates
  const handleSSEMessage = useCallback((event: MessageEvent) => {
    const eventData = parseSSEEventData(event);
    if (!eventData) return;

    logger.log('📡 Customer SSE event received:', eventData);

    switch (eventData.type) {
      case 'new-message':
        if (eventData.message && eventData.conversationId === liveChatConversationId) {
          // Only process agent and system messages (customer messages are added locally)
          if (eventData.message.type === 'agent' || eventData.message.type === 'system') {
            const formattedMessage = {
              id: eventData.message.id,
              type: (eventData.message.type === 'agent' ? 'bot' : eventData.message.type) as 'system' | 'user' | 'bot',
              content: eventData.message.content,
              timestamp: new Date(eventData.message.sentAt),
              metadata: {
                type: 'live_chat',
                senderName: eventData.message.senderName,
                senderEmail: eventData.message.senderEmail,
                liveChatMessage: true,
                originalType: eventData.message.type
              }
            };

            // Check if message already exists to prevent duplicates
            const existingMessage = messages.find(msg => msg.id === formattedMessage.id);
            if (!existingMessage) {
              addMessage(formattedMessage);

              // Play notification for agent messages
              if (eventData.message.type === 'agent') {
                playNotification();
              }
            }
          }
        }
        break;

      case 'typing-start':
        if (eventData.conversationId === liveChatConversationId && eventData.userType === 'agent') {
          addTypingUser(
            `agent-${eventData.conversationId}`,
            'agent',
            eventData.userName || 'Agent'
          );
        }
        break;

      case 'typing-stop':
        if (eventData.conversationId === liveChatConversationId && eventData.userType === 'agent') {
          removeTypingUser(`agent-${eventData.conversationId}`);
        }
        break;

      case 'chat-accepted':
        // Handle chat-accepted events for both handoff and live chat scenarios
        if (waitingForAgent && eventData.assignmentId === handoffAssignmentIdRef.current) {
          // This is a handoff acceptance event
          logger.log('✅ Agent accepted handoff via SSE:', {
            assignmentId: eventData.assignmentId,
            agentId: eventData.agentInfo?.id,
            agentName: eventData.agentInfo?.name,
            conversationId: eventData.conversationId
          });

          // Stop handoff waiting and transition to live chat
          stopHandoffWaiting();

          // Show agent joined message
          const agentJoinedMessage = {
            id: `agent_joined_${v4()}`,
            type: 'system' as const,
            content: `👋 ${eventData.agentInfo?.name || 'An agent'} has joined the conversation. You can now chat directly with them.`,
            timestamp: new Date(),
            metadata: {
              type: 'agent_joined',
              agentId: eventData.agentInfo?.id,
              agentName: eventData.agentInfo?.name,
              acceptedAt: eventData.acceptedAt
            }
          };
          addMessage(agentJoinedMessage);

          // Transition to live chat mode
          setLiveChatMode(true);
          setLiveChatConversationId(eventData.conversationId);
          setWaitingForAgent(false);
          setFlowState('live_chat');

          // Set the selected conversation in the conversation store for real-time updates
          setSelectedConversationId(eventData.conversationId);

          // Play notification sound
          playNotification();

          // Load initial messages - SSE will handle real-time updates
          loadInitialLiveChatMessages(eventData.conversationId);

        } else if (eventData.conversationId === liveChatConversationId) {
          // This is a regular chat-accepted event during live chat
          const agentJoinedMessage = {
            id: `agent_joined_sse_${v4()}`,
            type: 'system' as const,
            content: `👋 ${eventData.agentInfo?.name || 'An agent'} has joined the conversation.`,
            timestamp: new Date(),
            metadata: {
              type: 'agent_joined',
              agentId: eventData.agentInfo?.id,
              agentName: eventData.agentInfo?.name
            }
          };
          addMessage(agentJoinedMessage);
          playNotification();
        }
        break;

      case 'conversation-closed':
        if (eventData.conversationId === liveChatConversationId) {
          setLiveChatMode(false);
          setFlowState('completed');

          const closedMessage = {
            id: `conversation_closed_sse_${v4()}`,
            type: 'system' as const,
            content: '💬 The conversation has been closed by the agent.',
            timestamp: new Date(),
            metadata: {
              type: 'conversation_closed',
              reason: eventData.reason
            }
          };
          addMessage(closedMessage);
        }
        break;

      case 'heartbeat':
        // Heartbeat received - connection is healthy
        logger.log('💓 SSE heartbeat received');
        break;

      default:
        logger.warn('Unknown SSE event type:', eventData.type);
    }
  }, [liveChatConversationId, messages, addMessage, playNotification, addTypingUser, removeTypingUser, setLiveChatMode, setFlowState]);

  // SSE connection for real-time updates (both handoff and live chat)
  const { connectionState } = useSSE({
    endpoint: '/api/v1/live-chat/sse/customer',
    params: sseParams,
    onMessage: handleSSEMessage,
    onError: (error) => {
      logger.error('❌ Customer SSE connection error:', error);
    },
    onOpen: () => {
      logger.log('✅ Customer SSE connection established');
    },
    // Enable SSE during handoff waiting OR live chat mode
    enabled: (waitingForAgent && !!handoffAssignmentIdRef.current) || (isLiveChatMode && !!liveChatConversationId),
    reconnectDelay: 1000,
    maxReconnectAttempts: 10
  });

  /**
   * Load initial messages when entering live chat mode
   * SSE will handle real-time updates after this
   */
  const loadInitialLiveChatMessages = useCallback(async (conversationId: string) => {
    try {
      // Use the customer-specific message retrieval function
      const { getCustomerLiveChatMessages } = await import('@/server/actions/live-chat-actions');
      const response = await getCustomerLiveChatMessages(conversationId, organizationId);

      if (response.success && 'data' in response && response.data) {
        // Map the response data to ensure correct types
        const newMessages = response.data.map((msg: any) => ({
          ...msg,
          type: msg.type as 'customer' | 'agent' | 'system'
        }));

        // Update the conversation store with initial messages
        const { setConversationMessages } = useConversationStore.getState();
        setConversationMessages(conversationId, newMessages);

        logger.log('📨 Loaded initial live chat messages:', newMessages.length);
      } else {
        logger.warn('Failed to load initial live chat messages:', response);
      }
    } catch (error) {
      logger.error('Error loading initial live chat messages:', error);
    }
  }, [organizationId]);

  /**
   * Start handoff waiting with real-time SSE updates
   * Uses SSE to detect agent acceptance instead of polling
   * Includes timeout handling and periodic status updates for user feedback
   */
  const startHandoffWaiting = useCallback(async (assignmentId: string) => {
    logger.log('🔄 Starting handoff waiting for assignment:', assignmentId);
    logger.log('🔍 Current state before handoff waiting:', {
      waitingForAgent,
      isLiveChatMode,
      liveChatConversationId,
      organizationId,
      handoffAssignmentIdRef: handoffAssignmentIdRef.current
    });

    // Stop any existing waiting
    stopHandoffWaiting();

    isPollingActiveRef.current = true;
    handoffAssignmentIdRef.current = assignmentId;
    handoffStartTimeRef.current = Date.now();
    agentAcceptedCountRef.current = 0;

    // Show initial handoff message
    const handoffMessage = {
      id: `handoff_initiated_${v4()}`,
      type: 'system' as const,
      content: '🔄 Connecting you to an agent... Please wait while we find an available agent.',
      timestamp: new Date(),
      metadata: {
        type: 'handoff_initiated',
        assignmentId
      }
    };
    addMessage(handoffMessage);

    // Set up periodic status updates using timeouts instead of intervals
    const scheduleStatusUpdate = (minutes: number, message: string) => {
      const timeoutId = setTimeout(() => {
        if (isPollingActiveRef.current && handoffAssignmentIdRef.current === assignmentId) {
          const statusMessage = {
            id: `handoff_status_${minutes}min_${v4()}`,
            type: 'system' as const,
            content: message,
            timestamp: new Date(),
            metadata: {
              type: 'handoff_status_update',
              elapsedMinutes: minutes
            }
          };
          addMessage(statusMessage);
        }
      }, minutes * 60 * 1000);

      return timeoutId;
    };

    // Schedule status updates at 1 and 3 minutes
    const statusTimeout1Min = scheduleStatusUpdate(1, '⏳ Still looking for an available agent... Thank you for your patience.');
    const statusTimeout3Min = scheduleStatusUpdate(3, '⏳ We\'re still working to connect you with an agent. Please continue to wait.');

    // Store timeout IDs for cleanup
    statusUpdateIntervalRef.current = {
      timeout1Min: statusTimeout1Min,
      timeout3Min: statusTimeout3Min
    } as any;

    // Set up timeout for handoff (5 minutes)
    handoffTimeoutRef.current = setTimeout(() => {
      if (isPollingActiveRef.current && handoffAssignmentIdRef.current === assignmentId) {
        logger.log('⏰ Handoff timeout reached');
        stopHandoffWaiting();

        const timeoutMessage = {
          id: `handoff_timeout_${v4()}`,
          type: 'system' as const,
          content: '⏰ No agents are currently available. Please try again later or contact support directly.',
          timestamp: new Date(),
          metadata: {
            type: 'handoff_timeout',
            assignmentId
          }
        };
        addMessage(timeoutMessage);

        setWaitingForAgent(false);
        setFlowState('completed');
      }
    }, 5 * 60 * 1000); // 5 minutes timeout

  }, [addMessage, setWaitingForAgent, setFlowState]);



  /**
   * Stop handoff waiting activities
   * Cleans up timeouts and resets handoff state (no more polling)
   */
  const stopHandoffWaiting = useCallback(() => {
    logger.log('🛑 Stopping handoff waiting');

    if (handoffTimeoutRef.current) {
      clearTimeout(handoffTimeoutRef.current);
      handoffTimeoutRef.current = null;
    }

    // Clear status update timeouts
    if (statusUpdateIntervalRef.current) {
      if (typeof statusUpdateIntervalRef.current === 'object') {
        const timeouts = statusUpdateIntervalRef.current as any;
        if (timeouts.timeout1Min) clearTimeout(timeouts.timeout1Min);
        if (timeouts.timeout3Min) clearTimeout(timeouts.timeout3Min);
      } else {
        clearInterval(statusUpdateIntervalRef.current);
      }
      statusUpdateIntervalRef.current = null;
    }

    isPollingActiveRef.current = false;
    handoffAssignmentIdRef.current = null;
    handoffStartTimeRef.current = null;
    agentAcceptedCountRef.current = 0;
  }, []);

  // Cleanup handoff waiting on unmount
  useEffect(() => {
    return () => {
      stopHandoffWaiting();
      clearAllTypingUsers();
    };
  }, [stopHandoffWaiting, clearAllTypingUsers]);

  // Monitor handoff state changes and start waiting when needed
  useEffect(() => {
    if (handoffTriggered && waitingForAgent && !isPollingActiveRef.current) {
      // Check if we have an assignment ID from the bot response or conversation state
      const assignmentId = botResponse?.handoffResult?.assignmentId || liveChatConversationId;

      if (assignmentId && !isLiveChatMode) {
        logger.log('🔄 Handoff detected, starting waiting for assignment:', assignmentId);
        startHandoffWaiting(assignmentId);
      }
    } else if (!handoffTriggered && isPollingActiveRef.current) {
      // Handoff was cancelled or completed, stop waiting
      stopHandoffWaiting();
    }
  }, [handoffTriggered, waitingForAgent, botResponse, liveChatConversationId, isLiveChatMode, startHandoffWaiting, stopHandoffWaiting]);

  // Handle component unmount or page refresh during handoff
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isPollingActiveRef.current) {
        logger.log('🔄 Page unloading during handoff, cleaning up waiting');
        stopHandoffWaiting();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      // Also cleanup on component unmount
      stopHandoffWaiting();
    };
  }, [stopHandoffWaiting]);

  // Monitor conversation store for real-time message updates
  useEffect(() => {
    if (!isLiveChatMode || !liveChatConversationId || storeSelectedConversationId !== liveChatConversationId) {
      return;
    }

    const currentMessageCount = selectedConversationMessages.length;

    if (currentMessageCount > previousMessageCountRef.current) {
      // Find new messages since last check
      const newMessages = selectedConversationMessages.slice(previousMessageCountRef.current);

      // Filter for agent/system messages only (customer messages are added locally)
      const agentSystemMessages = newMessages.filter(msg =>
        msg.type === 'agent' || msg.type === 'system'
      );

      if (agentSystemMessages.length > 0) {
        // Get existing message IDs to prevent duplicates
        const existingMessageIds = new Set(messages.map(msg => msg.id));

        const formattedMessages = agentSystemMessages
          .filter(msg => !existingMessageIds.has(msg.id)) // Prevent duplicates
          .map(msg => ({
            id: msg.id,
            type: (msg.type === 'agent' ? 'bot' : msg.type) as 'system' | 'user' | 'bot',
            content: msg.content,
            timestamp: msg.sentAt,
            metadata: {
              type: 'live_chat',
              senderName: msg.senderName,
              senderEmail: msg.senderEmail,
              liveChatMessage: true,
              originalType: msg.type
            }
          }));

        if (formattedMessages.length > 0) {
          addMessages(formattedMessages);

          // Play notification for agent messages
          const hasAgentMessages = formattedMessages.some(msg => msg.metadata?.originalType === 'agent');
          if (hasAgentMessages) {
            playNotification();
          }
        }
      }
    }

    previousMessageCountRef.current = currentMessageCount;
  }, [isLiveChatMode, liveChatConversationId, storeSelectedConversationId, selectedConversationMessages, messages, addMessages, playNotification]);

  // Monitor conversation status changes
  useEffect(() => {
    if (!isLiveChatMode || !liveChatConversationId) {
      return;
    }

    const conversation = conversations.find(conv => conv.id === liveChatConversationId);
    if (conversation && conversation.status === 'closed') {
      // Clear typing indicators when conversation is closed
      clearAllTypingUsers();

      setLiveChatMode(false);
      setFlowState('completed');

      const closedMessage = {
        id: `conversation_closed_${v4()}`,
        type: 'system' as const,
        content: '💬 The conversation has been closed by the agent.',
        timestamp: new Date(),
        metadata: {
          type: 'conversation_closed'
        }
      };
      addMessage(closedMessage);
    }
  }, [isLiveChatMode, liveChatConversationId, conversations, setLiveChatMode, setFlowState, addMessage, clearAllTypingUsers]);

  // Monitor live chat mode changes and load initial messages
  useEffect(() => {
    if (isLiveChatMode && liveChatConversationId) {
      // Load initial messages when entering live chat mode
      // SSE connection will handle real-time updates
      loadInitialLiveChatMessages(liveChatConversationId);
    } else if (!isLiveChatMode) {
      // Clear typing indicators when leaving live chat mode
      clearAllTypingUsers();
    }
  }, [isLiveChatMode, liveChatConversationId, loadInitialLiveChatMessages, clearAllTypingUsers]);

  // Initialize store with props and start conversation flow
  useEffect(() => {
    setConversationState(conversationState);
    setBotResponse(botResponse);
    setCustomerInfo(customerInfo);

    if (conversationState) {
      // Normal bot conversation flow
      setFlowState('bot_conversation');

      // Initialize messages from conversation state if available
      if (conversationState.messageHistory && conversationState.messageHistory.length > 0) {
        // Clear existing messages and add initial messages
        clearMessages();
        addMessages(conversationState.messageHistory);

        // Track flow history from initial messages
        conversationState.messageHistory.forEach(message => {
          if (message.nodeId) {
            addToFlowHistory(message.nodeId);
          }
        });
      } else {
        clearMessages();
      }
    } else if (botResponse) {
      // Handle case where no published bot flow exists but we have a bot response (handoff scenario)
      clearMessages();

      if (botResponse.messages && botResponse.messages.length > 0) {
        addMessages(botResponse.messages);
      }

      // Check if this is a handoff scenario
      if (botResponse.handoffTriggered) {
        setFlowState('handoff_pending');
        setHandoffTriggered(true);
        setWaitingForAgent(true);

        if (botResponse.handoffResult?.assignmentId) {
          // Start waiting for agent assignment via SSE
          startHandoffWaiting(botResponse.handoffResult.assignmentId);
        } else {
          logger.warn('Handoff triggered but no assignment ID provided');
          setWaitingForAgent(false);
          setFlowState('completed');
        }
      } else if (botResponse.offlineMessageShown) {
        setFlowState('completed');
      }
    } else {
      // No conversation state and no bot response - clear everything
      clearMessages();
      setFlowState('idle');
    }
  }, [conversationState, botResponse, customerInfo, setConversationState, setBotResponse, setCustomerInfo, setFlowState, clearMessages, addMessages, addToFlowHistory, setHandoffTriggered, setLiveChatConversationId, setWaitingForAgent]);
  
  // Handle flow completion and restart options
  const handleFlowCompletion = useCallback(() => {
    if (conversationState?.isComplete && !handoffTriggered) {
      setFlowState('completed');
      
      const completionMessage = {
        id: `completion_${v4()}`,
        type: 'system' as const,
        content: '✅ Conversation completed successfully! You can restart the flow or continue to live chat.',
        timestamp: new Date(),
        metadata: {
          type: 'flow_completion',
          canRestart: true
        }
      };
      
      addMessage(completionMessage);
    }
  }, [conversationState?.isComplete, handoffTriggered, setFlowState, addMessage]);
  
  useEffect(() => {
    handleFlowCompletion();
  }, [handleFlowCompletion]);
  
  
  // Error recovery handler
  const handleErrorRecovery = useCallback(() => {
    if (error?.recoverable) {
      clearError();
      setFlowState('bot_conversation');
      
      const recoveryMessage = {
        id: `recovery_${v4()}`,
        type: 'system' as const,
        content: '🔧 Error resolved. You can continue the conversation.',
        timestamp: new Date(),
        metadata: {
          type: 'error_recovery'
        }
      };
      
      addMessage(recoveryMessage);
    }
  }, [error, clearError, setFlowState, addMessage]);
  
  // Handle sending user messages with optional pagination data
  const handleSendMessage = useCallback(async (paginationData?: { targetPage?: number; command?: 'next' | 'previous' | 'exit' }) => {
    // Handle pagination data directly or use input value
    let userMessage: string;

    if (paginationData) {
      // Direct pagination command from button
      if (paginationData.targetPage !== undefined) {
        userMessage = paginationData.targetPage.toString();
      } else if (paginationData.command) {
        userMessage = paginationData.command === 'previous' ? 'prev' : paginationData.command;
      } else {
        return;
      }
    } else {
      // Regular text input
      if (!inputValue.trim() || isLoading) return;
      userMessage = inputValue.trim();
    }

    // Clear input only for text input (not for direct pagination commands)
    if (!paginationData) {
      setInputValue('');
    }
    setIsLoading(true);
    setIsTyping(true);

    try {
      let shouldAddUserMessage = true;
      let shouldContinueBotFlow = true;

      // Check if we're in pagination mode and handle pagination commands
      if (paginationState?.isActive) {
        const paginationResult = await processPaginationCommand(userMessage);

        if (paginationResult === 'exit') {
          shouldAddUserMessage = true; // Add user message for exit command
          shouldContinueBotFlow = true; // Continue to bot processing
        } else if (paginationResult === 'enhance') {
          shouldAddUserMessage = true; // Add user message for the pagination command
          shouldContinueBotFlow = true; // Continue to bot processing with enhanced state
        } else if (paginationResult === true) {
          setIsLoading(false);
          setIsTyping(false);
          return;
        } else {
          // paginationResult is false - treat as regular message
        }
        // If paginationResult is false, treat as regular message and continue to bot processing
      }

      // Add user message to chat
      if (shouldAddUserMessage) {
        const userChatMessage = {
          id: `user_${v4()}`,
          type: 'user' as const,
          content: userMessage,
          timestamp: new Date(),
          metadata: {
            userInput: true,
            paginationExit: paginationState?.isActive && userMessage.toLowerCase().trim() === 'exit'
          }
        };

        addMessage(userChatMessage);
      }

      // Handle different flow states
      if (shouldContinueBotFlow) {
        if (isLiveChatMode && liveChatConversationId) {
          // Stop typing indicator when sending message
          customerTyping.onMessageSent();
          await handleLiveChatMessage(userMessage, organizationId);
        } else {
          await handleBotMessage(userMessage);
        }
      }

    } catch (error: any) {
      logger.error('Error sending message:', error);
      setError({
        type: 'system',
        message: 'Failed to send message',
        details: error.message,
        recoverable: true,
        timestamp: new Date()
      });
    } finally {
      setIsLoading(false);
      setIsTyping(false);
    }
  }, [inputValue, isLoading, paginationState, setInputValue, setIsLoading, setIsTyping, addMessage, isLiveChatMode, liveChatConversationId, setError, processPaginationCommand, handleLiveChatMessage, handleBotMessage, customerTyping]);
 
  // Register button click handler with Zustand store for MessageRenderer
  useEffect(() => {
    setButtonClickHandler(handleButtonClick);

    return () => {
      setButtonClickHandler(null);
    };
  }, [handleButtonClick, setButtonClickHandler]);
  
  
  // Determine if input should be disabled
  const isInputDisabled = isLoading ||
    waitingForAgent ||
    flowState === 'completed' ||
    (flowState === 'error' && !error?.recoverable);

  // Show loading indicator for handoff status
  const showHandoffLoading = waitingForAgent && flowState === 'handoff_pending';
  
  // Get current typing users for display
  const currentTypingUsers = getTypingUsers();



  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900">
      {error && (
        <ErrorDisplay
          error={error}
          onRecovery={handleErrorRecovery}
          onClear={clearError}
        />
      )}

      {/* Connection Status for Live Chat */}
      {isLiveChatMode && (
        <div className="flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionState.isConnected
                ? 'bg-green-500'
                : connectionState.isConnecting
                  ? 'bg-yellow-500 animate-pulse'
                  : 'bg-red-500'
            }`} />
            <span className="text-xs text-gray-600 dark:text-gray-400">
              {connectionState.isConnected
                ? 'Connected to live chat'
                : connectionState.isConnecting
                  ? 'Connecting...'
                  : connectionState.lastError
                    ? `Connection error: ${connectionState.lastError}`
                    : 'Disconnected'
              }
              {connectionState.reconnectAttempts > 0 && (
                <span className="ml-1">
                  (Attempt {connectionState.reconnectAttempts})
                </span>
              )}
            </span>
          </div>
        </div>
      )}

      {/* Loading Indicator */}
      {(isLoading || showHandoffLoading) && (
        <LoadingIndicator
          isTyping={showHandoffLoading ? false : isTyping}
          customMessage={showHandoffLoading ? "Connecting to agent..." : undefined}
        />
      )}

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto max-h-[19em] min-h-[19em] relative">
        <MessageList
          messages={messages}
          isTyping={isTyping}
          customerInfo={customerInfo}
          organizationId={organizationId}
        />

        {/* Fixed Agent Typing Indicator - positioned absolutely at bottom of messages area */}
        {isLiveChatMode && currentTypingUsers.length > 0 && (
          <div
            className="fixed bottom-[7rem] left-0 right-[1em] px-4 py-2 pointer-events-none z-10 bg-gradient-to-t from-white/95 via-white/80 to-transparent dark:from-gray-900/95 dark:via-gray-900/80 dark:to-transparent backdrop-blur-sm"
          >
            <div className="pointer-events-auto">
              <TypingIndicator
                typingUsers={currentTypingUsers.map(user => ({
                  userId: user.userId,
                  userType: user.userType as 'agent' | 'customer',
                  userName: user.userName,
                  timestamp: user.timestamp
                }))}
                className="justify-start shadow-lg"
              />
            </div>
          </div>
        )}
      </div>

      {/* User Input */}
      <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700">
        <UserInput
          value={inputValue}
          onChange={(value) => {
            setInputValue(value);
            // Improved typing indicator for live chat with proper debouncing
            if (isLiveChatMode) {
              if (value.trim()) {
                customerTyping.handleTyping();
              } else {
                customerTyping.handleStopTyping();
              }
            }
          }}
          onSend={handleSendMessage}
          disabled={isInputDisabled}
          placeholder={getInputPlaceholder(paginationState, activeForm, waitingForAgent, isLiveChatMode, flowState)}
          flowState={flowState}
          activeForm={activeForm}
          paginationState={paginationState}
        />
      </div>
    </div>
  );
};

// Helper function for input placeholder
function getInputPlaceholder(
  paginationState: PaginationState | null,
  activeForm: FormState | null,
  waitingForAgent: boolean,
  isLiveChatMode: boolean,
  flowState: FlowState
): string {
  

  if (paginationState?.isActive) {
    // Enhanced pagination placeholder with comprehensive contextual information
    const currentPage = paginationState.currentPage || 1;
    const totalPages = paginationState.totalPages || 1;
    const hasNext = paginationState.hasNextPage;
    const hasPrevious = paginationState.hasPreviousPage;
    const resultsCount = paginationState.resultsCount || 0;
    const totalRecords = paginationState.totalRecords || 0;
    const query = paginationState.query || '';
    

    // Build smart contextual navigation commands
    const commands = [];

    // Navigation commands based on current position
    if (hasPrevious) {
      commands.push("'prev'");
    }

    if (hasNext) {
      commands.push("'next'");
    }

    // Smart page jump suggestions - show that users can just type numbers
    if (totalPages > 1) {
      if (currentPage === 1 && totalPages > 2) {
        // From first page, suggest jumping to middle or last
        const middlePage = Math.ceil(totalPages / 2);
        commands.push(`'${middlePage}'`); // Just the number
      } else if (currentPage === totalPages && totalPages > 2) {
        // From last page, suggest jumping to first
        commands.push("'1'"); // Just the number
      } else {
        // From middle pages, suggest a nearby page
        const suggestedPage = currentPage < totalPages ? currentPage + 1 : Math.max(1, currentPage - 1);
        commands.push(`'${suggestedPage}'`); // Just the number
      }
    }

    commands.push("'exit' to continue");

    // Enhanced placeholder with results context
    const resultInfo = totalRecords > resultsCount ?
      `${resultsCount} of ${totalRecords} results` :
      `${resultsCount} results`;

    const queryInfo = query ? ` for "${query}"` : '';

    return `📄 Page ${currentPage}/${totalPages} (${resultInfo}${queryInfo}) • ${commands.join(', ')}`;
  }
  if (activeForm) {
    const currentField = activeForm.formFields[activeForm.currentFieldIndex];
    return `${currentField?.label || 'Enter value'} (or type "exit" to cancel)`;
  }
  if (waitingForAgent) {
    if (flowState === 'handoff_pending') {
      return "🔄 Connecting to agent... Please wait";
    }
    return "Please wait for agent acceptance...";
  }
  if (isLiveChatMode) return "Type your message to live agent...";
  if (flowState === 'completed') return "Flow completed - restart to continue";
  if (flowState === 'error') return "Error occurred - check error message";
  return "Type your message...";
}

// Export the component without memo to ensure immediate re-renders for pagination
export const ChatSimulator = ChatSimulatorV2Component;

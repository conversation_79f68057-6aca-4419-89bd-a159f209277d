'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { Dialog } from '@/components/ui/Dialog';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { TicketPriority } from '@/types/ticket-types';
import { createTicketFromLiveChat } from '@/server/actions/support';
import type { LiveChatMessage } from '@/stores/conversationStore';

// Form validation schema
const createTicketSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  additionalContext: z.string().optional(),
  priority: z.nativeEnum(TicketPriority),
  category: z.string().optional(),
  tags: z.string().optional()
});

type CreateTicketFormData = z.infer<typeof createTicketSchema>;

interface CreateTicketModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversationId: string;
  organizationId: string;
  customerInfo: {
    name: string;
    email?: string;
  };
  chatHistory: LiveChatMessage[];
  onTicketCreated?: (ticketData: any) => void;
}

const priorityOptions = [
  { value: TicketPriority.LOW, label: 'Low', color: 'bg-gray-100 text-gray-800' },
  { value: TicketPriority.MEDIUM, label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  { value: TicketPriority.HIGH, label: 'High', color: 'bg-orange-100 text-orange-800' },
  { value: TicketPriority.URGENT, label: 'Urgent', color: 'bg-red-100 text-red-800' }
];

const categoryOptions = [
  { value: 'technical-support', label: 'Technical Support' },
  { value: 'billing', label: 'Billing' },
  { value: 'feature-request', label: 'Feature Request' },
  { value: 'bug-report', label: 'Bug Report' },
  { value: 'general-inquiry', label: 'General Inquiry' },
  { value: 'account-issue', label: 'Account Issue' }
];

// Convert to format expected by Select component
const prioritySelectOptions = priorityOptions.map(option => ({
  label: option.label,
  value: option.value
}));

const categorySelectOptions = categoryOptions.map(option => ({
  label: option.label,
  value: option.value
}));

export function CreateTicketModal({
  isOpen,
  onClose,
  conversationId,
  organizationId,
  customerInfo,
  chatHistory,
  onTicketCreated
}: CreateTicketModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { success:showSuccess, error:showError } = useToast();

  const {
    handleSubmit,
    watch,
    reset,
    control,
    formState: { errors }
  } = useForm<CreateTicketFormData>({
    resolver: zodResolver(createTicketSchema),
    defaultValues: {
      title: `Support request from ${customerInfo.name}`,
      priority: TicketPriority.MEDIUM,
      category: 'general-inquiry',
      additionalContext: '',
      tags: ''
    },
    mode: 'onChange' // Enable real-time validation
  });

  const selectedPriority = watch('priority');

  // Debug: Watch all form values
  const formValues = watch();
  console.log('🔍 Form values:', formValues);

  const handleClose = () => {
    reset({
      title: `Support request from ${customerInfo.name}`,
      priority: TicketPriority.MEDIUM,
      category: 'general-inquiry',
      additionalContext: '',
      tags: ''
    });
    onClose();
  };

  const onSubmit = async (data: CreateTicketFormData) => {
    console.log('🎯 Form submitted with data:', data);
    setIsSubmitting(true);
    try {
      // Parse tags from comma-separated string
      const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];

      const result = await createTicketFromLiveChat({
        conversationId,
        organizationId,
        title: data.title,
        additionalContext: data.additionalContext,
        priority: data.priority,
        category: data.category,
        tags,
        chatHistory: chatHistory.map(msg => ({
          id: msg.id,
          content: msg.content,
          type: msg.type,
          senderName: msg.senderName,
          senderEmail: msg.senderEmail,
          sentAt: msg.sentAt
        })),
        customerInfo
      });

      if (result.success) {
        showSuccess(
          'Ticket Created Successfully',
          `Support ticket #${result.data.id} has been created and linked to this conversation.`
        );
        onTicketCreated?.(result.data);
        handleClose();
      } else {
        const errorMessage = result.error || 'An unexpected error occurred';

        // Check if this is a duplicate ticket error for this conversation
        if (errorMessage.includes('already been created') && errorMessage.includes('conversation')) {
          showError('Conversation Already Has Ticket', errorMessage);
        } else {
          showError('Failed to Create Ticket', errorMessage);
        }
      }
    } catch (error) {
      console.error('Error creating ticket:', error);
      showError('Failed to Create Ticket', 'An unexpected error occurred while creating the ticket');
    } finally {
      setIsSubmitting(false);
    }
  };

  const messageCount = chatHistory.length;
  const customerMessageCount = chatHistory.filter(msg => msg.type === 'customer').length;
  const agentMessageCount = chatHistory.filter(msg => msg.type === 'agent').length;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Create Support Ticket
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Escalate this live chat conversation to a formal support ticket
                </p>
              </div>
              <Button
                onClick={handleClose}
                variant="outline"
                size="sm"
                className="text-gray-500 hover:text-gray-700"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>

            {/* Conversation Summary */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                Conversation Summary
              </h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Customer:</span>
                  <span className="ml-2 font-medium text-gray-900 dark:text-white">
                    {customerInfo.name}
                  </span>
                  {customerInfo.email && (
                    <div className="text-gray-600 dark:text-gray-400 text-xs">
                      {customerInfo.email}
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Messages:</span>
                  <span className="ml-2 font-medium text-gray-900 dark:text-white">
                    {messageCount} total
                  </span>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {customerMessageCount} customer, {agentMessageCount} agent
                  </div>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Ticket Title *
                </label>
                <Controller
                  name="title"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      placeholder="Brief description of the issue"
                      className={`w-full h-10 rounded-md border ${
                        errors.title ? 'border-red-500' : 'border-gray-300 dark:border-gray-700'
                      } bg-transparent px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500`}
                    />
                  )}
                />
                {errors.title && (
                  <p className="text-red-500 text-xs mt-1">{errors.title.message}</p>
                )}
              </div>

              {/* Priority and Category */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Priority
                  </label>
                  <Controller
                    name="priority"
                    control={control}
                    render={({ field }) => (
                      <Select
                        options={prioritySelectOptions}
                        value={field.value}
                        onChange={(value) => field.onChange(value)}
                        placeholder="Select priority"
                      />
                    )}
                  />
                  <div className="mt-1">
                    <Badge className={priorityOptions.find(p => p.value === selectedPriority)?.color}>
                      {priorityOptions.find(p => p.value === selectedPriority)?.label}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Category
                  </label>
                  <Controller
                    name="category"
                    control={control}
                    render={({ field }) => (
                      <Select
                        options={categorySelectOptions}
                        value={field.value as string}
                        onChange={(value) => field.onChange(value)}
                        placeholder="Select category"
                      />
                    )}
                  />
                </div>
              </div>

              {/* Additional Context */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Additional Context
                </label>
                <Controller
                  name="additionalContext"
                  control={control}
                  render={({ field }) => (
                    <textarea
                      {...field}
                      placeholder="Add any additional context, notes, or follow-up actions needed..."
                      rows={4}
                      className="w-full rounded-md border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500 resize-none"
                    />
                  )}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  The complete chat history will be automatically included in the ticket.
                </p>
              </div>

              {/* Tags */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tags
                </label>
                <Controller
                  name="tags"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      placeholder="Enter tags separated by commas (e.g., urgent, billing, api)"
                      className="w-full h-10 rounded-md border border-gray-300 dark:border-gray-700 bg-transparent px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500"
                    />
                  )}
                />
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Tags help categorize and filter tickets. Separate multiple tags with commas.
                </p>
              </div>

              {/* Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-[#8178E8] hover:bg-[#6964D3] text-white"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Creating Ticket...
                    </div>
                  ) : (
                    'Create Ticket'
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Dialog>
  );
}

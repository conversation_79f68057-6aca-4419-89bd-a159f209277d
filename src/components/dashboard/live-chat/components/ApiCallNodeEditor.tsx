'use client';

import React, { useRef, useState } from 'react';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { VariableReferenceSection, insertVariableAtCursor } from './VariableReferenceSection';
import { BotResponseType, IFlowVariable } from '@/types/bot';
import { ApiResponseDesigner } from './v2/ApiResponseDesigner';
import { DisplayField, TemplateConfig, TemplateStructure } from '@/stores/apiResponseDesignerStore';
import { logger } from '@/utils/logger';

// Component-specific constants
const HTTP_METHODS = [
  { label: 'GET', value: 'GET' },
  { label: 'POST', value: 'POST' },
  { label: 'PUT', value: 'PUT' },
  { label: 'DELETE', value: 'DELETE' },
  { label: 'PATCH', value: 'PATCH' }
];

const DEFAULT_TIMEOUT = 30000;
const MIN_TIMEOUT = 1000;
const MAX_TIMEOUT = 300000;
const DEFAULT_RETRIES = 3;
const MIN_RETRIES = 0;
const MAX_RETRIES = 10;
const MAX_DISPLAY_ITEMS = 5;

// Component-specific interfaces
interface ApiCallValidation {
  isValid: boolean;
  hasUrlError: boolean;
  hasMethodError: boolean;
  hasHeadersError: boolean;
  hasBodyError: boolean;
  message: string;
}

interface ApiCallNodeContent {
  type: BotResponseType.API_CALL;
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  timeout?: number;
  retries?: number;
  headers?: Record<string, string | number | boolean>;
  body?: string;
  responseVariable?: string;
  arrayFieldName?: string;
  displayFields?: string[];
  templateConfig?: {
    title?: string;
    description?: string;
    customTemplate?: string;
    showPagination?: boolean;
    displayFields?: Array<{
      id: string;
      name: string;
      label: string;
      type: string;
    }>;
    templateStructure?: {
      blocks: Array<Record<string, unknown>>;
      variables: string[];
      loopVariables: string[];
      conditionalVariables: string[];
    };
    validatedAt?: string;
    maxItems?: number;
    preserveFormatting?: boolean;
    generatedTemplate?: string;
    sampleData?: Record<string, unknown>; // Node-specific sample data
  };
}

/**
 * ApiCallNodeEditor Component
 *
 * Self-contained component that handles API call node editing including:
 * - API endpoint URL configuration with validation
 * - HTTP method selection and configuration
 * - Headers and request body management with JSON validation
 * - Timeout and retry configuration
 * - Response variable storage configuration
 * - Array data display configuration
 * - Variable insertion with cursor positioning
 *
 * Encapsulates its own validation logic, API configuration, and state.
 */
export const ApiCallNodeEditor: React.FC = () => {
  const { currentNode, updateNode, variables } = useNodeEditorStore();

  // Internal state for UI concerns
  const [newFieldName, setNewFieldName] = useState('');
  const [fieldInputError, setFieldInputError] = useState<string>('');

  // Internal ref for textarea management
  const apiRequestBodyRef = useRef<HTMLTextAreaElement>(null);

  if (!currentNode || currentNode.type !== BotResponseType.API_CALL) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateApiCall = (): ApiCallValidation => {
    const content = currentNode.content as ApiCallNodeContent;
    const url = content.url;
    const method = content.method;

    const hasUrlError = !url || !url.trim();
    const hasMethodError = !method;
    let hasHeadersError = false;
    let hasBodyError = false;

    let message = '';
    if (hasUrlError) {
      message = 'API endpoint URL is required';
    } else if (hasMethodError) {
      message = 'HTTP method is required';
    } else if (hasHeadersError) {
      message = 'Headers must be valid JSON';
    } else if (hasBodyError) {
      message = 'Request body must be valid JSON';
    }

    return {
      isValid: !hasUrlError && !hasMethodError && !hasHeadersError && !hasBodyError,
      hasUrlError,
      hasMethodError,
      hasHeadersError,
      hasBodyError,
      message
    };
  };

  // Internal helper to get current API call content
  const getCurrentApiCallContent = (): ApiCallNodeContent => {
    return currentNode.content as ApiCallNodeContent;
  };

  // Internal helper to get display fields
  const getDisplayFields = (): string[] => {
    const content = getCurrentApiCallContent();
    return content.displayFields || [];
  };

  // Internal helper to validate field name
  const validateFieldName = (fieldName: string): { isValid: boolean; message: string } => {
    if (!fieldName.trim()) {
      return { isValid: false, message: 'Field name is required' };
    }
    if (!/^[a-zA-Z0-9_]+$/.test(fieldName)) {
      return { isValid: false, message: 'Only letters, numbers, and underscores allowed' };
    }
    if (fieldName.length > 20) {
      return { isValid: false, message: 'Field name must be 20 characters or less' };
    }
    const currentFields = getDisplayFields();
    if (currentFields.includes(fieldName)) {
      return { isValid: false, message: 'Field name already exists' };
    }
    if (currentFields.length >= MAX_DISPLAY_ITEMS) {
      return { isValid: false, message: 'Maximum 5 fields allowed' };
    }
    return { isValid: true, message: '' };
  };

  // Internal event handlers
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateNode({
      content: {
        ...currentNode.content,
        url: e.target.value
      } as ApiCallNodeContent
    });
  };

  const handleMethodChange = (value: string | number | null) => {
    if (typeof value === 'string') {
      updateNode({
        content: {
          ...currentNode.content,
          method: value as 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
        }
      });
    }
  };

  const handleTimeoutChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || DEFAULT_TIMEOUT;
    updateNode({
      content: {
        ...currentNode.content,
        timeout: Math.min(Math.max(value, MIN_TIMEOUT), MAX_TIMEOUT)
      } as ApiCallNodeContent
    });
  };

  const handleRetriesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value) || DEFAULT_RETRIES;
    updateNode({
      content: {
        ...currentNode.content,
        retries: Math.min(Math.max(value, MIN_RETRIES), MAX_RETRIES)
      } as ApiCallNodeContent
    });
  };

  const handleHeadersChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value || '{}';
    try {
      const headers = JSON.parse(value);
      updateNode({
        content: {
          ...currentNode.content,
          headers
        }
      });
    } catch {
      // For invalid JSON, we'll store it as a temporary state but not update the node
      // The validation will catch this and show an error
      logger.warn('Invalid JSON in headers field:', value);
    }
  };

  const handleBodyChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newBodyValue = e.target.value;
    updateNode({
      content: {
        ...currentNode.content,
        body: newBodyValue // Always store as string
      } as ApiCallNodeContent
    });
  };

  const handleResponseVariableChange = (value: string | number | null) => {
    if (typeof value === 'string' || value === null) {
      const updatedContent = { ...currentNode.content };
      if (value === '' || value === null) {
        delete updatedContent.responseVariable;
      } else {
        updatedContent.responseVariable = value;
      }
      updateNode({ content: updatedContent });
    }
  };

  const handleArrayFieldNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    updateNode({
      content: {
        ...currentNode.content,
        arrayFieldName: e.target.value
      } as ApiCallNodeContent
    });
  };



  // Internal field management
  const handleFieldInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewFieldName(value);
    
    if (value.trim()) {
      const validation = validateFieldName(value.trim());
      setFieldInputError(validation.isValid ? '' : validation.message);
    } else {
      setFieldInputError('');
    }
  };

  const handleAddField = () => {
    const trimmedFieldName = newFieldName.trim();
    if (!trimmedFieldName) return;

    const validation = validateFieldName(trimmedFieldName);
    if (!validation.isValid) {
      setFieldInputError(validation.message);
      return;
    }

    const currentFields = getDisplayFields();
    const truncatedFieldName = trimmedFieldName.length > 20 ? trimmedFieldName.substring(0, 20) : trimmedFieldName;
    const updatedFields = [...currentFields, truncatedFieldName].slice(0, MAX_DISPLAY_ITEMS);
    
    updateNode({
      content: {
        ...currentNode.content,
        displayFields: updatedFields
      } as ApiCallNodeContent
    });

    setNewFieldName('');
    setFieldInputError('');
  };

  const handleRemoveField = (index: number) => {
    const currentFields = getDisplayFields();
    const updatedFields = currentFields.filter((_, i) => i !== index);
    updateNode({
      content: {
        ...currentNode.content,
        displayFields: updatedFields
      } as ApiCallNodeContent
    });
  };

  const handleQuickAddField = (fieldName: string) => {
    const currentFields = getDisplayFields();
    const isFieldSelected = currentFields.includes(fieldName);
    const isMaxFieldsReached = currentFields.length >= MAX_DISPLAY_ITEMS;

    if (!isFieldSelected && !isMaxFieldsReached) {
      const truncatedFieldName = fieldName.length > 20 ? fieldName.substring(0, 20) : fieldName;
      const updatedFields = [...currentFields, truncatedFieldName].slice(0, MAX_DISPLAY_ITEMS);
      updateNode({
        content: {
          ...currentNode.content,
          displayFields: updatedFields
        } as ApiCallNodeContent
      });
    }
  };

  // Handle template designer save
  const handleTemplateDesignerSave = (config: any) => {
    // Extract field names from the template configuration
    const templateFields = config.displayFields?.map((field: any) => field.name).filter(Boolean) || [];

    // Update the node with the new display fields and complete template configuration
    updateNode({
      content: {
        ...currentNode.content,
        displayFields: templateFields.slice(0, MAX_DISPLAY_ITEMS),
        // Store the complete template configuration for future use
        templateConfig: {
          title: config.title,
          description: config.description,
          customTemplate: config.customTemplate,
          generatedTemplate: config.generatedTemplate,
          showPagination: config.showPagination,
          displayFields: config.displayFields,
          templateStructure: config.templateStructure,
          maxItems: config.maxItems,
          preserveFormatting: config.preserveFormatting,
          validatedAt: config.validatedAt || new Date().toISOString()
        }
      } as ApiCallNodeContent & { templateConfig?: any }
    });
    
  };

  const content = getCurrentApiCallContent();
  const validation = validateApiCall();
  const currentFields = getDisplayFields();
  

  return (
    <div className="space-y-4">
      {/* API Call Node Information */}
      <div className="p-4 bg-cyan-50 dark:bg-cyan-900/20 rounded-lg border border-cyan-200 dark:border-cyan-800">
        <h4 className="text-sm font-medium text-cyan-900 dark:text-cyan-100 mb-3 flex items-center">
          🔗 API Integration Configuration
        </h4>
        <p className="text-xs text-cyan-700 dark:text-cyan-300 mb-3">
          This node calls an external API and processes the response. Configure the API endpoint and response handling below.
        </p>
        <div className="text-xs text-cyan-600 dark:text-cyan-400">
          API responses can be stored in variables and displayed to users as structured data.
        </div>
      </div>

      {/* API Endpoint Configuration */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          API Endpoint Configuration
        </h4>

        {/* URL Input */}
        <div>
          <Label htmlFor="apiUrl" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            API Endpoint URL
          </Label>
          <Input
            id="apiUrl"
            value={content.url || ''}
            onChange={handleUrlChange}
            placeholder="https://api.example.com/endpoint"
            className={`mt-1 ${validation.hasUrlError ? 'border-red-500' : ''}`}
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Enter the complete URL for the API endpoint you want to call.
          </p>
        </div>

        {/* HTTP Method */}
        <div>
          <Label htmlFor="apiMethod" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            HTTP Method
          </Label>
          <Select
            id="apiMethod"
            value={content.method || 'GET'}
            onChange={handleMethodChange}
            options={HTTP_METHODS}
            placeholder="Select HTTP method"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Choose the HTTP method for your API request.
          </p>
        </div>

        {/* Timeout and Retries */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="apiTimeout" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Timeout (ms)
            </Label>
            <Input
              id="apiTimeout"
              type="number"
              value={content.timeout || DEFAULT_TIMEOUT}
              onChange={handleTimeoutChange}
              min={MIN_TIMEOUT}
              max={MAX_TIMEOUT}
              placeholder={DEFAULT_TIMEOUT.toString()}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Request timeout in milliseconds ({MIN_TIMEOUT}-{MAX_TIMEOUT})
            </p>
          </div>
          <div>
            <Label htmlFor="apiRetries" className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Retries
            </Label>
            <Input
              id="apiRetries"
              type="number"
              value={content.retries || DEFAULT_RETRIES}
              onChange={handleRetriesChange}
              min={MIN_RETRIES}
              max={MAX_RETRIES}
              placeholder={DEFAULT_RETRIES.toString()}
              className="mt-1"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Number of retry attempts ({MIN_RETRIES}-{MAX_RETRIES})
            </p>
          </div>
        </div>
      </div>

      {/* Headers Configuration */}
      <div>
        <Label htmlFor="apiHeaders" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Headers (JSON)
        </Label>
        <Textarea
          id="apiHeaders"
          value={(() => {
            try {
              return JSON.stringify(content.headers || {}, null, 2);
            } catch {
              return '{}';
            }
          })()}
          onChange={handleHeadersChange}
          placeholder='{"Authorization": "Bearer {{api_key}}", "Content-Type": "application/json"}'
          rows={3}
          className={`mt-1 ${validation.hasHeadersError ? 'border-red-500' : ''}`}
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Add HTTP headers as JSON. Use {"{{variable_name}}"} for dynamic values.
        </p>
        {validation.hasHeadersError && (
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">
            Headers must be valid JSON format.
          </p>
        )}
      </div>

      {/* Request Body */}
      <div>
        <Label htmlFor="requestBody" className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Request Body (JSON)
        </Label>
        <Textarea
          id="requestBody"
          ref={apiRequestBodyRef}
          value={(() => {
            try {
              if (typeof content.body === 'string') {
                return content.body;
              } else if (content.body && typeof content.body === 'object') {
                return JSON.stringify(content.body, null, 2);
              } else {
                return '';
              }
            } catch {
              return '';
            }
          })()}
          onChange={handleBodyChange}
          placeholder='{"key": "{{variable_name}}", "data": "value"}'
          rows={3}
          className="mt-1"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Use {"{{variable_name}}"} to insert variable values into the request. Click variables below to add them at your cursor position.
        </p>
      </div>

      {/* Available Variables Reference */}
      <VariableReferenceSection
        onVariableClick={(variableName) =>
          insertVariableAtCursor(
            apiRequestBodyRef,
            variableName,
            content.body || '',
            (newValue) => updateNode({
              content: { ...currentNode.content, body: newValue } as ApiCallNodeContent
            })
          )
        }
        title="Available Variables for Request Body:"
      />

      {/* Response Configuration */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Response Configuration
        </h4>

        {/* Response Variable Storage */}
        <div>
          <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
            Store Response In Variable
          </Label>
          <Select
            value={(content.responseVariable as string) || ''}
            onChange={handleResponseVariableChange}
            options={[
              { label: "Don't store response", value: '' },
              { label: 'customer_name (system)', value: 'customer_name' },
              { label: 'customer_email (system)', value: 'customer_email' },
              ...variables.map((variable: IFlowVariable) => ({
                label: `${variable.name} (${variable.type})`,
                value: variable.name
              }))
            ]}
            placeholder="Select variable to store API response"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Choose a variable to store the API response for later use in the conversation.
          </p>
        </div>
      </div>

      {/* List Data Display Configuration - Separate Component */}
      <div className="space-y-4 p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <span className="text-lg">🎯</span>
            <h4 className="text-sm font-medium text-purple-900 dark:text-purple-100">
              API Response Configuration
            </h4>
          </div>
        </div>
        <p className="text-xs text-purple-700 dark:text-purple-300 mb-4">
          Configure how API responses are formatted and displayed in the chat interface.
        </p>

        {!content.templateConfig && (
          <div className="p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-amber-600 dark:text-amber-400">⚠️</span>
              <span className="text-sm font-medium text-amber-800 dark:text-amber-200">
                Template Configuration Required
              </span>
            </div>
            <p className="text-xs text-amber-700 dark:text-amber-300 mt-1">
              Use the designer below to configure response formatting.
            </p>
          </div>
        )}

        {/* Visual Template Designer Section */}
        <ApiResponseDesigner
          onSave={handleTemplateDesignerSave}
          initialConfig={content.templateConfig ? {
            title: content.templateConfig.title || "API Results",
            description: content.templateConfig.description || "Found {{total}} items",
            displayFields: (content.templateConfig.displayFields as DisplayField[]) || currentFields.map((field, index) => ({
              id: `field-${index}`,
              name: field,
              label: field.charAt(0).toUpperCase() + field.slice(1),
              type: 'text' as const
            })),
            templateStructure: (content.templateConfig.templateStructure as unknown as TemplateStructure) || { blocks: [], variables: [], loopVariables: [], conditionalVariables: [] },

            customTemplate: content.templateConfig.customTemplate,
            preserveFormatting: content.templateConfig.preserveFormatting ?? true,
            generatedTemplate: content.templateConfig.generatedTemplate,
            validatedAt: content.templateConfig.validatedAt
          } : {
            title: "API Results",
            description: "Found {{total}} items",
            displayFields: currentFields.map((field, index) => ({
              id: `field-${index}`,
              name: field,
              label: field.charAt(0).toUpperCase() + field.slice(1),
              type: 'text' as const
            })),
            templateStructure: { blocks: [], variables: [], loopVariables: [], conditionalVariables: [] },

            preserveFormatting: true
          }}
          apiNodeData={{
            displayFields: currentFields,
            responseStructure: (content as unknown as { responseStructure?: Record<string, unknown> }).responseStructure || {},
            url: content.url,
            method: content.method,
            headers: content.headers,
            body: content.body,
            // Include node-specific sample data from templateConfig
            sampleData: content.templateConfig?.sampleData || {}
          }}
        />
      </div>

      {/* Validation Error */}
      {!validation.isValid && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg">
          <div className="flex items-start gap-2">
            <span className="text-red-600 dark:text-red-400 text-sm">⚠️</span>
            <div>
              <p className="text-xs font-medium text-red-800 dark:text-red-200">
                API Configuration Incomplete
              </p>
              <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                {validation.message}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Development Analysis */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 API Call Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>URL:</strong> {content.url || 'Not set'}</p>
            <p><strong>Method:</strong> {content.method || 'Not set'}</p>
            <p><strong>Timeout:</strong> {content.timeout || DEFAULT_TIMEOUT}ms</p>
            <p><strong>Retries:</strong> {content.retries || DEFAULT_RETRIES}</p>
            <p><strong>Response Variable:</strong> {content.responseVariable || 'None'}</p>
            <p><strong>Display Fields:</strong> {currentFields.length}/{MAX_DISPLAY_ITEMS}</p>
            <p><strong>Is Valid:</strong> {validation.isValid ? 'Yes' : 'No'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Badge } from '@/components/ui/Badge';
import { IEnhancedFlowNode, BotResponseType } from '@/types/bot';

interface FlowNodeComponentModernProps {
  node: IEnhancedFlowNode;
  isSelected: boolean;
  isMultiSelected: boolean;
  selectedNodes: string[];
  onSelect: () => void;
  onMultiSelect: (nodeId: string, isCtrlPressed: boolean) => void;
  onUpdate: (updates: Partial<IEnhancedFlowNode>) => void;
  onMultiNodeDragStart: () => void;
  onMultiNodeDrag: (deltaX: number, deltaY: number) => void;
  onMultiNodeDragEnd: () => void;
  onDelete: () => void;
  onConnect: (targetId: string) => void;
  isConnecting?: boolean;
  onStartConnection?: (handle?: string) => void;
  onEndConnection?: (nodeId: string) => void;
}

export const FlowNodeComponentModern: React.FC<FlowNodeComponentModernProps> = ({
  node,
  isSelected,
  isMultiSelected,
  selectedNodes,
  onSelect,
  onMultiSelect,
  onUpdate,
  onMultiNodeDragStart,
  onMultiNodeDrag,
  onMultiNodeDragEnd,
  onDelete,
  isConnecting = false,
  onStartConnection,
  onEndConnection
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const nodeRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation();

    const isCtrlPressed = e.ctrlKey || e.metaKey;

    setIsDragging(true);
    setDragStart({
      x: e.clientX - node.position.x,
      y: e.clientY - node.position.y
    });

    if (isCtrlPressed) {
      onMultiSelect(node.id, true);
      if (selectedNodes.length > 0) {
        onMultiNodeDragStart();
      }
    } else if (isMultiSelected && selectedNodes.length > 1) {
      onMultiNodeDragStart();
    } else {
      onSelect();
    }
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.x;
      const newY = e.clientY - dragStart.y;

      if (selectedNodes.length > 1 && selectedNodes.includes(node.id)) {
        const deltaX = newX - node.position.x;
        const deltaY = newY - node.position.y;
        onMultiNodeDrag(deltaX, deltaY);
      } else {
        onUpdate({
          position: {
            x: newX,
            y: newY
          }
        });
      }
    }
  };

  const handleMouseUp = () => {
    if (isDragging && selectedNodes.length > 1 && selectedNodes.includes(node.id)) {
      onMultiNodeDragEnd();
    }
    setIsDragging(false);
  };

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (isSelected) {
      const activeElement = document.activeElement;
      const isInputFocused = activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.tagName === 'SELECT' ||
        (activeElement as HTMLElement).contentEditable === 'true'
      );

      if (!isInputFocused && (e.key === 'Delete' || e.key === 'Backspace')) {
        e.preventDefault();
        onDelete();
      }
    }
  }, [isSelected, onDelete]);

  useEffect(() => {
    if (isSelected && nodeRef.current) {
      nodeRef.current.focus();
    }
  }, [isSelected]);

  useEffect(() => {
    if (isSelected) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isSelected, handleKeyDown]);

  React.useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, dragStart]);

  const getNodeIcon = (type: BotResponseType) => {
    switch (type) {
      case BotResponseType.TEXT: return '💬';
      case BotResponseType.BUTTONS: return '🔘';
      case BotResponseType.QUICK_REPLY: return '⚡';
      case BotResponseType.FORM: return '📝';
      case BotResponseType.HANDOFF: return '👤';
      case BotResponseType.CONDITIONAL: return '🔀';
      case BotResponseType.API_CALL: return '🔗';
      default: return '📄';
    }
  };

  const getNodeColor = (type: BotResponseType) => {
    switch (type) {
      case BotResponseType.TEXT:
        return 'from-blue-500/20 to-blue-600/20 border-blue-400/30 text-blue-700 dark:text-blue-300';
      case BotResponseType.BUTTONS:
        return 'from-green-500/20 to-green-600/20 border-green-400/30 text-green-700 dark:text-green-300';
      case BotResponseType.QUICK_REPLY:
        return 'from-yellow-500/20 to-yellow-600/20 border-yellow-400/30 text-yellow-700 dark:text-yellow-300';
      case BotResponseType.FORM:
        return 'from-purple-500/20 to-purple-600/20 border-purple-400/30 text-purple-700 dark:text-purple-300';
      case BotResponseType.HANDOFF:
        return 'from-red-500/20 to-red-600/20 border-red-400/30 text-red-700 dark:text-red-300';
      case BotResponseType.CONDITIONAL:
        return 'from-orange-500/20 to-orange-600/20 border-orange-400/30 text-orange-700 dark:text-orange-300';
      case BotResponseType.API_CALL:
        return 'from-cyan-500/20 to-cyan-600/20 border-cyan-400/30 text-cyan-700 dark:text-cyan-300';
      default:
        return 'from-gray-500/20 to-gray-600/20 border-gray-400/30 text-gray-700 dark:text-gray-300';
    }
  };

  // Helper function to get meaningful content for display
  const getNodeDisplayContent = (node: IEnhancedFlowNode) => {
    const content = node.content as any;

    switch (node.type) {
      case BotResponseType.TEXT:
        return {
          primary: content.text || 'No message content',
          secondary: null,
          type: 'Message'
        };

      case BotResponseType.CONDITIONAL:
        let conditionText = 'No condition set';
        if (content.conditions && content.conditions.length > 0) {
          const condition = content.conditions[0];
          conditionText = `If ${condition.field} ${condition.type} "${condition.value}"`;
        } else if (content.conditionVariable && content.conditionValue) {
          conditionText = `If ${content.conditionVariable} ${content.conditionOperator || 'equals'} "${content.conditionValue}"`;
        } else if (content.condition) {
          conditionText = `If ${content.condition}`;
        }
        return {
          primary: conditionText,
          secondary: content.text || null,
          type: 'Conditions'
        };

      case BotResponseType.API_CALL:
        const method = content.method || 'GET';
        const url = content.url || 'No URL set';
        return {
          primary: `${method} ${url}`,
          secondary: content.responseVariable ? `→ ${content.responseVariable}` : null,
          type: 'API'
        };

      case BotResponseType.BUTTONS:
        const buttonCount = content.options?.length || 0;
        const buttonText = buttonCount > 0 ?
          content.options.slice(0, 2).map((opt: any) => opt.text).join(', ') +
          (buttonCount > 2 ? `... (+${buttonCount - 2} more)` : '') :
          'No buttons configured';
        return {
          primary: content.text || 'Choose an option:',
          secondary: buttonText,
          type: 'Buttons'
        };

      case BotResponseType.QUICK_REPLY:
        const replyCount = content.options?.length || 0;
        const replyText = replyCount > 0 ?
          content.options.slice(0, 2).map((opt: any) => opt.text).join(', ') +
          (replyCount > 2 ? `... (+${replyCount - 2} more)` : '') :
          'No quick replies configured';
        return {
          primary: content.text || 'Quick replies:',
          secondary: replyText,
          type: 'Quick Reply'
        };

      case BotResponseType.FORM:
        const fieldCount = content.formFields?.length || 0;
        const fieldText = fieldCount > 0 ?
          content.formFields.slice(0, 2).map((field: any) => field.label || field.name).join(', ') +
          (fieldCount > 2 ? `... (+${fieldCount - 2} more)` : '') :
          'No form fields configured';
        return {
          primary: content.text || 'Please fill out this form:',
          secondary: fieldText,
          type: 'Form'
        };

      case BotResponseType.HANDOFF:
        return {
          primary: content.text || 'Transferring to human agent...',
          secondary: content.department ? `Department: ${content.department}` : null,
          type: 'Handoff'
        };

      default:
        return {
          primary: content.text || `${node.type} node`,
          secondary: null,
          type: node.type
        };
    }
  };

  return (
    <div
      ref={nodeRef}
      className={`absolute cursor-move select-none transition-all duration-200 outline-none ${
        isSelected ? 'z-30' : 'z-10'
      }`}
      style={{
        left: node.position.x,
        top: node.position.y,
        transform: isDragging ? 'scale(1.05)' : 'scale(1)',
        transition: isDragging ? 'none' : 'transform 0.2s ease-in-out'
      }}
      onMouseDown={handleMouseDown}
      onClick={onSelect}
      tabIndex={0}
      role="button"
      aria-label={`Flow node: ${node.name}`}
      aria-selected={isSelected}
    >
      <div className={`
        relative w-72 bg-white/85 dark:bg-[#1e1e28]/85 backdrop-blur-md border border-[#E0D7FF]/50 dark:border-[#2c2d3d]
        shadow-lg hover:shadow-xl
        transition-all duration-300 hover:-translate-y-1 rounded-xl
        ${isSelected ? 'ring-4 ring-[#8178E8] shadow-[#8178E8]/50 border-[#8178E8]' : ''}
        ${isMultiSelected && !isSelected ? 'ring-4 ring-blue-500 shadow-blue-500/50 border-blue-500' : ''}
        ${isDragging ? 'shadow-2xl shadow-[#8178E8]/75' : ''}
        group overflow-visible
      `}>
        {/* Modern accent bar */}
        <div className={`absolute top-0 left-0 right-0 h-1 ${getNodeColor(node.type)}`} />

        {/* Node Header */}
        <div className="relative p-4 pb-3 z-10">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <div className={`flex items-center justify-center w-8 h-8 ${getNodeColor(node.type)} border-2 border-white/20`}>
                <span className="text-lg">{getNodeIcon(node.type)}</span>
              </div>
              <div className="flex items-center space-x-2">
                {node.isStartNode && (
                  <Badge variant="success" className="text-xs px-2 py-1 font-bold bg-green-600 text-white border-0">
                    START
                  </Badge>
                )}
                {node.isEndNode && (
                  <Badge variant="danger" className="text-xs px-2 py-1 font-bold bg-red-600 text-white border-0">
                    END
                  </Badge>
                )}
                {(((node.content as any).storeInVariable && (node.content as any).storeInVariable?.trim?.()) ||
                  ((node.content as any).responseVariable && (node.content as any).responseVariable?.trim?.()) ||
                  ((node.content as any).conditionVariable && (node.content as any).conditionVariable?.trim?.()) ||
                  ((node.content as any).requestBody && (node.content as any).requestBody.includes('{{'))
                ) && (
                  <Badge variant="info" className="text-xs px-2 py-1 font-bold bg-cyan-600 text-white border-0">
                    📊
                  </Badge>
                )}
              </div>
            </div>

            {/* Modern Remove Button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="
                flex items-center justify-center w-7 h-7 z-20
                bg-red-500 hover:bg-red-600
                border-2 border-white/20 hover:border-white/40
                transition-all duration-200
                text-white hover:text-white
                hover:scale-110 active:scale-95
                opacity-80 group-hover:opacity-100 rounded-lg
                focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-1
                shadow-lg hover:shadow-xl
              "
              aria-label="Delete node"
              title="Delete node"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Enhanced Node Content Display */}
          <div className="space-y-3">
            {/* Node Name and Type */}
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white truncate drop-shadow-sm">
                {node.name}
              </h3>
              <span className="text-xs text-gray-500 dark:text-gray-400 font-medium px-2 py-1 bg-gray-100/50 dark:bg-gray-800/50 rounded">
                {getNodeDisplayContent(node).type}
              </span>
            </div>

            {/* Primary Content */}
            <div className="bg-white/50 dark:bg-gray-800/30 rounded-lg p-3 border border-gray-200/30 dark:border-gray-700/30">
              <p
                className="text-sm text-gray-800 dark:text-gray-200 leading-relaxed font-medium"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 3,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}
              >
                {getNodeDisplayContent(node).primary}
              </p>

              {/* Secondary Content */}
              {getNodeDisplayContent(node).secondary && (
                <p
                  className="text-xs text-gray-600 dark:text-gray-400 mt-2 leading-relaxed"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 2,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden'
                  }}
                >
                  {getNodeDisplayContent(node).secondary}
                </p>
              )}
            </div>
          </div>

          {/* Validation Errors */}
          {node.validationErrors && node.validationErrors.length > 0 && (
            <div className="mt-3 flex items-center space-x-2 p-2 bg-red-900/50 border-2 border-red-600 rounded">
              <div className="flex items-center justify-center w-4 h-4 bg-red-600 rounded">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-xs font-bold text-red-300">
                {node.validationErrors.length} ERROR{node.validationErrors.length > 1 ? 'S' : ''}
              </span>
            </div>
          )}
        </div>

        {/* Connection Points - Modern square design */}
        {/* Input handle (left side) */}
        <div
          className="absolute -left-4 top-1/2 transform -translate-y-1/2 z-40 group/input"
          onClick={(e) => {
            e.stopPropagation();
            if (isConnecting && onEndConnection) {
              onEndConnection(node.id);
            }
          }}
        >
          <div className={`
            w-8 h-8 bg-white dark:bg-gray-800 border-2 cursor-pointer
            transition-all duration-200 shadow-lg hover:shadow-xl
            flex items-center justify-center hover:scale-110 rounded-lg
            ${isConnecting
              ? 'border-green-400 bg-green-500 shadow-green-400/60 animate-pulse scale-110'
              : 'border-[#6366f1] hover:border-[#5855eb] hover:bg-[#6366f1]/20'
            }
          `}>
            <div className={`w-3 h-3 transition-all duration-200 rounded-sm ${
              isConnecting ? 'bg-white' : 'bg-[#6366f1]'
            }`} />
          </div>
        </div>

        {/* Output handles */}
        {node.type === BotResponseType.CONDITIONAL ? (
          <>
            {/* True branch (top-right) */}
            <div
              className="absolute -right-4 top-1/4 transform -translate-y-1/2 z-40 group/output-true"
              onClick={(e) => {
                e.stopPropagation();
                if (onStartConnection) {
                  onStartConnection('true');
                }
              }}
            >
              <div className={`
                w-8 h-8 bg-white dark:bg-gray-800 border-2 cursor-pointer
                transition-all duration-200 shadow-lg hover:shadow-xl
                flex items-center justify-center hover:scale-110 rounded-lg
                ${isConnecting
                  ? 'border-green-400 bg-green-500 shadow-green-400/60 scale-110'
                  : 'border-green-400 hover:border-green-300 hover:bg-green-500/20'
                }
              `}>
                <div className={`w-3 h-3 transition-all duration-200 rounded-sm ${
                  isConnecting ? 'bg-white' : 'bg-green-400'
                }`} />
              </div>
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-green-600 text-white text-xs px-2 py-1 font-bold shadow-lg">
                TRUE
              </div>
            </div>

            {/* False branch (bottom-right) */}
            <div
              className="absolute -right-4 bottom-1/4 transform translate-y-1/2 z-40 group/output-false"
              onClick={(e) => {
                e.stopPropagation();
                if (onStartConnection) {
                  onStartConnection('false');
                }
              }}
            >
              <div className={`
                w-8 h-8 bg-white dark:bg-gray-800 border-2 cursor-pointer
                transition-all duration-200 shadow-lg hover:shadow-xl
                flex items-center justify-center hover:scale-110 rounded-lg
                ${isConnecting
                  ? 'border-red-400 bg-red-500 shadow-red-400/60 scale-110'
                  : 'border-red-400 hover:border-red-300 hover:bg-red-500/20'
                }
              `}>
                <div className={`w-3 h-3 transition-all duration-200 rounded-sm ${
                  isConnecting ? 'bg-white' : 'bg-red-400'
                }`} />
              </div>
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-red-600 text-white text-xs px-2 py-1 font-bold shadow-lg">
                FALSE
              </div>
            </div>
          </>
        ) : (
          <div
            className="absolute -right-4 top-1/2 transform -translate-y-1/2 z-40 group/output"
            onClick={(e) => {
              e.stopPropagation();
              if (onStartConnection) {
                onStartConnection();
              }
            }}
          >
            <div className={`
              w-8 h-8 bg-white dark:bg-gray-800 border-2 cursor-pointer
              transition-all duration-200 shadow-lg hover:shadow-xl
              flex items-center justify-center hover:scale-110 rounded-lg
              ${isConnecting
                ? 'border-[#6366f1] bg-[#6366f1] shadow-[#6366f1]/60 scale-110'
                : 'border-[#6366f1] hover:border-[#5855eb] hover:bg-[#6366f1]/20'
              }
            `}>
              <div className={`w-3 h-3 transition-all duration-200 rounded-sm ${
                isConnecting ? 'bg-white' : 'bg-[#6366f1]'
              }`} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

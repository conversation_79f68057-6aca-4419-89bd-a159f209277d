'use client';

import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';
import {
  toggleAgentStatus,
  getAgentStatus,
  updateAgentHeartbeat
} from '@/server/actions/agent-status-actions';
import { logger } from '@/utils/logger';

interface AgentStatusToggleProps {
  organizationId: string;
}

interface AgentStatus {
  agentId: string;
  organizationId: string;
  isOnline: boolean;
  lastSeen: Date;
  sessionId?: string;
}

export function AgentStatusToggle({ organizationId }: AgentStatusToggleProps) {
  const [status, setStatus] = useState<AgentStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const { success: showSuccess, error: showError } = useToast();

  // Load initial status
  const loadStatus = useCallback(async () => {
    try {
      const response = await getAgentStatus(organizationId);
      if (response.success && response.data) {
        setStatus(response.data);
      } else {
        logger.error('Failed to load agent status:', response.error);
      }
    } catch (error) {
      logger.error('Error loading agent status:', error);
    } finally {
      setIsInitializing(false);
    }
  }, [organizationId]);

  // Initialize status on mount
  useEffect(() => {
    loadStatus();
  }, [loadStatus]);

  // Heartbeat to keep agent online
  useEffect(() => {
    if (!status?.isOnline) return;

    const heartbeatInterval = setInterval(async () => {
      try {
        await updateAgentHeartbeat(organizationId);
      } catch (error) {
        logger.error('Heartbeat failed:', error);
      }
    }, 60000); // Send heartbeat every minute

    return () => clearInterval(heartbeatInterval);
  }, [status?.isOnline, organizationId]);

  // Handle status toggle
  const handleToggle = async () => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const response = await toggleAgentStatus(organizationId);

      if (response.success && response.data) {
        setStatus(response.data);
        showSuccess(
          'Status Updated',
          response.message || `You are now ${response.data.isOnline ? 'online' : 'offline'}`
        );
      } else {
        showError(
          'Error',
          response.error || 'Failed to update status'
        );
      }
    } catch (error) {
      logger.error('Error toggling status:', error);
      showError(
        'Error',
        'Failed to update status'
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Format last seen time
  const formatLastSeen = (lastSeen: Date) => {
    const now = new Date();
    const diff = now.getTime() - new Date(lastSeen).getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
    return `${Math.floor(minutes / 1440)}d ago`;
  };

  // Show loading state during initialization
  if (isInitializing) {
    return (
      <div className="flex items-center space-x-2">
        <div className="w-3 h-3 bg-gray-300 rounded-full animate-pulse"></div>
        <span className="text-sm text-gray-500 dark:text-gray-400">Loading...</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-3">
      {/* Status Indicator */}
      <div className="flex items-center space-x-2">
        <div className={`w-3 h-3 rounded-full ${
          isLoading
            ? 'bg-yellow-400 animate-pulse'
            : status?.isOnline
              ? 'bg-green-400'
              : 'bg-gray-400'
        }`} />
        <div className="flex flex-col">
          <span className={`text-sm font-medium ${
            status?.isOnline
              ? 'text-green-600 dark:text-green-400'
              : 'text-gray-600 dark:text-gray-400'
          }`}>
            {isLoading ? 'Updating...' : status?.isOnline ? 'Online' : 'Offline'}
          </span>
          {status && (
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Last seen {formatLastSeen(status.lastSeen)}
            </span>
          )}
        </div>
      </div>

      {/* Toggle Button */}
      <Button
        onClick={handleToggle}
        disabled={isLoading}
        size="sm"
        variant={status?.isOnline ? 'outline' : 'default'}
        className={`min-w-[80px] ${
          status?.isOnline
            ? 'border-red-300 text-red-600 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20'
            : 'bg-green-600 hover:bg-green-700 text-white'
        }`}
      >
        {isLoading ? (
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin" />
            <span>...</span>
          </div>
        ) : (
          status?.isOnline ? 'Go Offline' : 'Go Online'
        )}
      </Button>
    </div>
  );
}

'use client';

import React, { useMemo } from 'react';
import { AlertTriangle, CheckCircle, XCircle, Info } from 'lucide-react';
import { useApiResponseDesignerStore } from '@/stores/apiResponseDesignerStore';

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  info: string[];
}

export const TemplateValidation: React.FC = () => {
  const { config, generatedTemplate, errors } = useApiResponseDesignerStore();

  const validationResult = useMemo((): ValidationResult => {
    const result: ValidationResult = {
      isValid: true,
      errors: [...errors],
      warnings: [],
      info: []
    };

    const template = config.customTemplate || generatedTemplate;

    // Check for dangerous Handlebars features
    const dangerousPatterns = [
      { pattern: /\{\{\{.*\}\}\}/g, message: 'Raw blocks ({{{}}}) are not allowed for security reasons' },
      { pattern: /\{\{>\s*\w+/g, message: 'Partials ({{> partial}}) are not allowed' },
      { pattern: /\{\{\s*lookup\s+/g, message: 'Lookup helper is not allowed' },
      { pattern: /\{\{\s*with\s+/g, message: 'With helper can be dangerous and is not recommended' },
      { pattern: /\{\{\s*#unless\s+/g, message: 'Unless blocks are not recommended, use {{#if}} instead' },
    ];

    dangerousPatterns.forEach(({ pattern, message }) => {
      if (pattern.test(template)) {
        result.errors.push(message);
        result.isValid = false;
      }
    });

    // Check field name length
    const fieldMatches = template.match(/\{\{([^}#\/\s]+)\}\}/g);
    if (fieldMatches) {
      fieldMatches.forEach(match => {
        const fieldName = match.replace(/[{}]/g, '');
        if (fieldName.length > 20) {
          result.errors.push(`Field name "${fieldName}" exceeds 20 character limit`);
          result.isValid = false;
        }
      });
    }

    // Check field count using template structure
    const fieldBlocks = config.templateStructure.blocks.filter(block => block.type === 'field');
    const loopFieldBlocks = config.templateStructure.blocks
      .filter(block => block.type === 'loop')
      .flatMap(block => block.loopContent?.filter(subBlock => subBlock.type === 'field') || []);
    
    const totalFieldCount = fieldBlocks.length + loopFieldBlocks.length;
    if (totalFieldCount > 5) {
      result.errors.push('Maximum 5 display fields allowed');
      result.isValid = false;
    }

    // Validate field names in template structure
    [...fieldBlocks, ...loopFieldBlocks].forEach(fieldBlock => {
      if (fieldBlock.fieldName && fieldBlock.fieldName.length > 20) {
        result.errors.push(`Field name "${fieldBlock.fieldName}" exceeds 20 character limit`);
        result.isValid = false;
      }
      
      // Check for valid field name format
      if (fieldBlock.fieldName && !/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(fieldBlock.fieldName)) {
        result.warnings.push(`Field name "${fieldBlock.fieldName}" should only contain letters, numbers, and underscores`);
      }
    });

    // Check for empty template
    if (!template.trim()) {
      result.warnings.push('Template is empty');
    }

    // Check for unmatched Handlebars blocks
    const openBlocks = (template.match(/\{\{#\w+/g) || []).length;
    const closeBlocks = (template.match(/\{\{\/\w+/g) || []).length;
    if (openBlocks !== closeBlocks) {
      result.errors.push('Unmatched Handlebars blocks detected');
      result.isValid = false;
    }

    // Performance warnings
    if (totalFieldCount > 3) {
      result.warnings.push('Consider reducing the number of fields for better performance');
    }

    if (config.templateStructure.blocks.filter(b => b.type === 'loop').length > 2) {
      result.warnings.push('Multiple loops may impact performance with large datasets');
    }

    // Accessibility info
    if (fieldBlocks.some(block => !block.fieldIcon)) {
      result.info.push('Consider adding icons to fields for better visual accessibility');
    }

    // Template structure info
    result.info.push(`Template contains ${config.templateStructure.blocks.length} blocks`);
    result.info.push(`Using ${config.templateStructure.variables.length} variables`);

    return result;
  }, [config, generatedTemplate, errors]);

  if (validationResult.errors.length === 0 && validationResult.warnings.length === 0 && validationResult.info.length === 0) {
    return (
      <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
          <span className="text-sm font-medium text-green-900 dark:text-green-100">
            Template is valid and ready to use
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {/* Errors */}
      {validationResult.errors.length > 0 && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <XCircle className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-red-900 dark:text-red-100 mb-1">
                Template Errors ({validationResult.errors.length})
              </div>
              <div className="text-xs text-red-800 dark:text-red-200 space-y-1">
                {validationResult.errors.map((error, index) => (
                  <div key={index} className="flex items-start space-x-1">
                    <span className="text-red-600 dark:text-red-400">•</span>
                    <span>{error}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Warnings */}
      {validationResult.warnings.length > 0 && (
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-1">
                Template Warnings ({validationResult.warnings.length})
              </div>
              <div className="text-xs text-yellow-800 dark:text-yellow-200 space-y-1">
                {validationResult.warnings.map((warning, index) => (
                  <div key={index} className="flex items-start space-x-1">
                    <span className="text-yellow-600 dark:text-yellow-400">•</span>
                    <span>{warning}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Info */}
      {validationResult.info.length > 0 && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <Info className="w-4 h-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <div className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-1">
                Template Information
              </div>
              <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
                {validationResult.info.map((info, index) => (
                  <div key={index} className="flex items-start space-x-1">
                    <span className="text-blue-600 dark:text-blue-400">•</span>
                    <span>{info}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Validation Summary */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          Validation Summary
        </div>
        <div className="grid grid-cols-3 gap-4 text-xs">
          <div className="text-center">
            <div className={`text-lg font-semibold ${validationResult.errors.length > 0 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
              {validationResult.errors.length}
            </div>
            <div className="text-gray-600 dark:text-gray-400">Errors</div>
          </div>
          <div className="text-center">
            <div className={`text-lg font-semibold ${validationResult.warnings.length > 0 ? 'text-yellow-600 dark:text-yellow-400' : 'text-gray-600 dark:text-gray-400'}`}>
              {validationResult.warnings.length}
            </div>
            <div className="text-gray-600 dark:text-gray-400">Warnings</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
              {validationResult.info.length}
            </div>
            <div className="text-gray-600 dark:text-gray-400">Info</div>
          </div>
        </div>
      </div>
    </div>
  );
};

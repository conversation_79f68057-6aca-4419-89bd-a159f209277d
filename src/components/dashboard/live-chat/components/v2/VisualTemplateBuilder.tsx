'use client';

import React, { useCallback } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Code, GripVertical } from 'lucide-react';
import { Badge } from '@/components/ui/Badge';
import { useApiResponseDesignerStore } from '@/stores/apiResponseDesignerStore';
import { VisualBlock } from './VisualBlock';

export const VisualTemplateBuilder: React.FC = () => {
  const { 
    config, 
    activeId, 
    setActiveId, 
    setEditingBlock, 
    deleteBlock, 
    reorderBlocks 
  } = useApiResponseDesignerStore();

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Drag and drop handlers
  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id);
  }, [setActiveId]);

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = config.templateStructure.blocks.findIndex(block => block.id === active.id);
      const newIndex = config.templateStructure.blocks.findIndex(block => block.id === over?.id);
      
      if (oldIndex !== -1 && newIndex !== -1) {
        reorderBlocks(oldIndex, newIndex);
      }
    }

    setActiveId(null);
  }, [config.templateStructure.blocks, reorderBlocks, setActiveId]);

  const handleEditBlock = useCallback((block: any) => {
    setEditingBlock(block);
  }, [setEditingBlock]);

  const handleDeleteBlock = useCallback((id: string) => {
    deleteBlock(id);
  }, [deleteBlock]);

  // Calculate field count for display
  const fieldCount = config.templateStructure.blocks.filter(b => b.type === 'field').length + 
    config.templateStructure.blocks
      .filter(b => b.type === 'loop')
      .flatMap(b => b.loopContent?.filter(sub => sub.type === 'field') || []).length;

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium flex items-center">
            <Code className="w-5 h-5 mr-2" />
            Visual Template Structure
          </h3>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              Direct Handlebars Mapping
            </Badge>
            <Badge 
              variant={fieldCount >= 5 ? "danger" : "secondary"}
              className="text-xs"
            >
              {fieldCount}/5 Fields
            </Badge>
          </div>
        </div>

        {/* Template Structure Display */}
        <div className="min-h-[500px] p-4 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-800/50 overflow-y-auto">
          <SortableContext
            items={config.templateStructure.blocks.map(block => block.id)}
            strategy={verticalListSortingStrategy}
          >
            <div className="space-y-2">
              {config.templateStructure.blocks
                .sort((a, b) => a.order - b.order)
                .map((block) => (
                  <VisualBlock
                    key={block.id}
                    block={block}
                    onEdit={handleEditBlock}
                    onDelete={handleDeleteBlock}
                  />
                ))}
            </div>
          </SortableContext>

          {config.templateStructure.blocks.length === 0 && (
            <div className="flex flex-col items-center justify-center h-64 text-gray-500 dark:text-gray-400">
              <Code className="w-12 h-12 mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">Build Your Template Structure</p>
              <p className="text-sm text-center max-w-md">
                Add blocks from the palette to create a template that maps directly to Handlebars syntax.
                Each visual element corresponds exactly to the generated template.
              </p>
            </div>
          )}
        </div>

        {/* Template Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {config.templateStructure.blocks.length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Total Blocks</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {config.templateStructure.blocks.filter(b => b.type === 'loop').length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Loops</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {config.templateStructure.blocks.filter(b => b.type === 'conditional').length}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Conditionals</div>
          </div>
          <div className="text-center">
            <div className={`text-2xl font-bold ${fieldCount >= 5 ? 'text-red-600 dark:text-red-400' : 'text-orange-600 dark:text-orange-400'}`}>
              {fieldCount}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Fields</div>
          </div>
        </div>

        {/* Template Structure Info */}
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Template Structure Overview
          </h4>
          <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
            <div>• Each block represents a specific part of the Handlebars template</div>
            <div>• Drag blocks up or down to change their order in the template</div>
            <div>• Click the edit icon to modify block properties</div>
            <div>• Use the delete icon to remove unwanted blocks</div>
            <div>• The order shown here matches the exact template output</div>
          </div>
        </div>
      </div>

      {/* Drag Overlay */}
      <DragOverlay>
        {activeId ? (
          <div className="p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg opacity-90">
            <div className="flex items-center space-x-3">
              <GripVertical className="w-4 h-4 text-gray-400" />
              <span className="text-sm font-medium">Moving block...</span>
            </div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

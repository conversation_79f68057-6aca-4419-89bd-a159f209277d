'use client';

import React, { useState, useEffect } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { Select } from '@/components/ui/Select';
import { X, Save } from 'lucide-react';
import { useApiResponseDesignerStore, TemplateBlock } from '@/stores/apiResponseDesignerStore';

export const BlockEditModal: React.FC = () => {
  const { editingBlock, setEditingBlock, updateBlock } = useApiResponseDesignerStore();
  const [localBlock, setLocalBlock] = useState<TemplateBlock | null>(null);

  useEffect(() => {
    if (editingBlock) {
      setLocalBlock({ ...editingBlock });
    }
  }, [editingBlock]);

  const handleClose = () => {
    setEditingBlock(null);
    setLocalBlock(null);
  };

  const handleSave = () => {
    if (localBlock) {
      updateBlock(localBlock);
      handleClose();
    }
  };

  const updateLocalBlock = (updates: Partial<TemplateBlock>) => {
    if (localBlock) {
      setLocalBlock({ ...localBlock, ...updates });
    }
  };

  if (!editingBlock || !localBlock) {
    return null;
  }

  const fieldTypeOptions = [
    { label: 'Text', value: 'text' },
    { label: 'Currency', value: 'currency' },
    { label: 'Rating', value: 'rating' },
    { label: 'Number', value: 'number' },
    { label: 'Badge', value: 'badge' }
  ];

  const iconOptions = [
    { label: '📦 Package', value: '📦' },
    { label: '💰 Money', value: '💰' },
    { label: '⭐ Star', value: '⭐' },
    { label: '📊 Chart', value: '📊' },
    { label: '🏷️ Tag', value: '🏷️' },
    { label: '📝 Note', value: '📝' },
    { label: '🔗 Link', value: '🔗' },
    { label: '📄 Document', value: '📄' },
    { label: '🎯 Target', value: '🎯' },
    { label: '✅ Check', value: '✅' }
  ];

  return (
    <Dialog open={!!editingBlock} onOpenChange={handleClose}>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Edit {localBlock.type.charAt(0).toUpperCase() + localBlock.type.slice(1)} Block
            </h3>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="p-4 space-y-4">
            {/* Common fields */}
            <div>
              <Label htmlFor="block-id">Block ID</Label>
              <Input
                id="block-id"
                value={localBlock.id}
                disabled
                className="bg-gray-50 dark:bg-gray-700"
              />
            </div>

            <div>
              <Label htmlFor="block-order">Order</Label>
              <Input
                id="block-order"
                type="number"
                value={localBlock.order}
                onChange={(e) => updateLocalBlock({ order: parseInt(e.target.value) || 0 })}
              />
            </div>

            {/* Type-specific fields */}
            {localBlock.type === 'text' && (
              <>
                <div>
                  <Label htmlFor="text-content">Text Content</Label>
                  <Textarea
                    id="text-content"
                    value={localBlock.content}
                    onChange={(e) => updateLocalBlock({ content: e.target.value })}
                    placeholder="Enter text content..."
                    rows={3}
                  />
                </div>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={localBlock.isBold || false}
                      onChange={(e) => updateLocalBlock({ isBold: e.target.checked })}
                      className="rounded"
                    />
                    <span className="text-sm">Bold</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={localBlock.isItalic || false}
                      onChange={(e) => updateLocalBlock({ isItalic: e.target.checked })}
                      className="rounded"
                    />
                    <span className="text-sm">Italic</span>
                  </label>
                </div>
              </>
            )}

            {localBlock.type === 'variable' && (
              <div>
                <Label htmlFor="variable-name">Variable Name</Label>
                <Input
                  id="variable-name"
                  value={localBlock.content}
                  onChange={(e) => updateLocalBlock({ content: e.target.value })}
                  placeholder="e.g., total, query, category"
                />
              </div>
            )}

            {localBlock.type === 'conditional' && (
              <>
                <div>
                  <Label htmlFor="condition">Condition Variable</Label>
                  <Input
                    id="condition"
                    value={localBlock.condition || ''}
                    onChange={(e) => updateLocalBlock({ condition: e.target.value })}
                    placeholder="e.g., query, category"
                  />
                </div>
                <div>
                  <Label htmlFor="condition-content">Content to Show</Label>
                  <Textarea
                    id="condition-content"
                    value={localBlock.conditionContent || ''}
                    onChange={(e) => updateLocalBlock({ conditionContent: e.target.value })}
                    placeholder="Content to display when condition is true..."
                    rows={3}
                  />
                </div>
              </>
            )}

            {localBlock.type === 'loop' && (
              <div>
                <Label htmlFor="loop-variable">Loop Variable</Label>
                <Input
                  id="loop-variable"
                  value={localBlock.loopVariable || ''}
                  onChange={(e) => updateLocalBlock({ loopVariable: e.target.value })}
                  placeholder="e.g., products, items"
                />
              </div>
            )}

            {localBlock.type === 'field' && (
              <>
                <div>
                  <Label htmlFor="field-name">Field Name</Label>
                  <Input
                    id="field-name"
                    value={localBlock.fieldName || ''}
                    onChange={(e) => updateLocalBlock({ fieldName: e.target.value.slice(0, 20) })}
                    placeholder="e.g., name, price, rating"
                    maxLength={20}
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    {(localBlock.fieldName || '').length}/20 characters
                  </div>
                </div>
                <div>
                  <Label htmlFor="field-label">Field Label</Label>
                  <Input
                    id="field-label"
                    value={localBlock.fieldLabel || ''}
                    onChange={(e) => updateLocalBlock({ fieldLabel: e.target.value })}
                    placeholder="e.g., Product Name, Price, Rating"
                  />
                </div>
                <div>
                  <Label htmlFor="field-icon">Field Icon</Label>
                  <Select
                    options={iconOptions}
                    value={localBlock.fieldIcon || '📦'}
                    onChange={(value) => updateLocalBlock({ fieldIcon: value ? String(value) : undefined })}
                    placeholder="Select an icon"
                  />
                </div>
                <div>
                  <Label htmlFor="field-type">Field Type</Label>
                  <Select
                    options={fieldTypeOptions}
                    value={localBlock.fieldType || 'text'}
                    onChange={(value) => updateLocalBlock({ fieldType: value as any })}
                    placeholder="Select field type"
                  />
                </div>
              </>
            )}

            {localBlock.type === 'separator' && (
              <div>
                <Label htmlFor="separator-content">Separator Content</Label>
                <Input
                  id="separator-content"
                  value={localBlock.content}
                  onChange={(e) => updateLocalBlock({ content: e.target.value })}
                  placeholder="e.g., ---, ___"
                />
              </div>
            )}
          </div>

          <div className="flex items-center justify-end space-x-2 p-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button onClick={handleSave} className="flex items-center space-x-2">
              <Save className="w-4 h-4" />
              <span>Save Changes</span>
            </Button>
          </div>
        </div>
      </div>
    </Dialog>
  );
};

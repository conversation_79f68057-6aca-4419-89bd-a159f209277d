'use client';

import React, { useEffect, useCallback } from 'react';
import { Code, Copy, AlertTriangle, Database, BookOpen, HelpCircle, Shield, Eye } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useApiResponseDesignerStore } from '@/stores/apiResponseDesignerStore';
import { ColoredAccordionItem, Accordion } from '@/components/ui/Accordion';
import { autoInitializePreview } from '@/utils/sampleDataManager';

import dynamic from 'next/dynamic';
import { DynamicPreviewFormButton } from './DynamicPreviewFormModal';
import { logger } from '@/utils/logger';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

// Dynamically import MDEditor to avoid SSR issues in Next.js
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor'),
  { ssr: false }
);

interface TemplateCodeEditorProps {
  onApply?: (canClose: boolean) => void;
}

export const TemplateCodeEditor: React.FC<TemplateCodeEditorProps> = ({
  onApply
}) => {
  const {
    config,
    localTemplate,
    hasUnsavedTemplateChanges,
    setLocalTemplate,
    applyTemplateChanges,
    syncTemplateToPreview,
    updateSampleData,
    regeneratePreview,
    apiNodeData,
    sampleData,
    previewContent
  } = useApiResponseDesignerStore();

  // Auto-initialize preview with sample data when component mounts
  useEffect(() => {
    const initializePreview = async () => {

      try {
        await autoInitializePreview(
          localTemplate,
          apiNodeData,
          updateSampleData,
          syncTemplateToPreview,
          regeneratePreview
        );
      } catch (error) {
        logger.error('❌ TemplateCodeEditor: Auto-initialization failed:', error);
      }
    };

    // Add a small delay to avoid conflicts with other initialization
    const timeoutId = setTimeout(initializePreview, 200);

    return () => clearTimeout(timeoutId);
  }, [localTemplate, apiNodeData, updateSampleData, syncTemplateToPreview, regeneratePreview]);

  // Auto-apply sample data when it changes (e.g., when a new template is loaded)
  useEffect(() => {
    if (localTemplate) {
      syncTemplateToPreview(localTemplate);
    }
  }, [sampleData, apiNodeData?.sampleData, localTemplate, syncTemplateToPreview]);

  const handleTemplateChange = (value: string) => {
    // Use Zustand action to update local template and detect changes
    setLocalTemplate(value);
  };

  const handleApplyChanges = useCallback(() => {
    if (onApply) {
      // Use the parent's handler which will apply changes and save to node
      onApply(false); // Don't close modal, just apply changes
    } else {
      // Fallback: directly apply changes if no parent handler provided
      try {
        applyTemplateChanges();
      } catch (error) {
        logger.error('Failed to apply template changes:', error);
      }
    }
  }, [onApply, applyTemplateChanges]);


  const handleCopyTemplate = async () => {
    try {
      await navigator.clipboard.writeText(localTemplate);
      // Could add a toast notification here
    } catch (err) {
      logger.error('Failed to copy template:', err);
    }
  };

  const isUsingCustomTemplate = !!config.customTemplate;
  const templateLines = localTemplate.split('\n').length;
  const templateChars = localTemplate.length;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium flex items-center">
          <Code className="w-5 h-5 mr-2" />
          Handlebars Template Code
        </h3>
        <div className="flex items-center space-x-2">
          {isUsingCustomTemplate && (
            <Badge variant="secondary" className="text-xs">
              Custom Template
            </Badge>
          )}
          {hasUnsavedTemplateChanges && (
            <Badge variant="danger" className="text-xs">
              Unsaved Changes
            </Badge>
          )}
        </div>
      </div>

      <p className="text-sm text-gray-600 dark:text-gray-400">
        Edit the Handlebars template directly with enhanced syntax highlighting and dedicated live preview.
        The right panel shows processed Handlebars content with variable substitution using our custom HandlebarsPreview component.
      </p>

      {/* Unsaved Changes Indicator */}
      {hasUnsavedTemplateChanges && (
        <div className="flex items-center space-x-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
          <AlertTriangle className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
          <span className="text-sm text-yellow-800 dark:text-yellow-200">
            You have unsaved changes to the template. Use the {'"Apply Changes"'} button above to save them.
          </span>
        </div>
      )}

      {/* Template Statistics */}
      <div className="grid grid-cols-4 gap-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900 dark:text-white">
            {templateLines}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Lines</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900 dark:text-white">
            {templateChars}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Characters</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-gray-900 dark:text-white">
            {(localTemplate.match(/\{\{[^}]+\}\}/g) || []).length}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Variables</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
            🔄
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Live Preview</div>
        </div>
      </div>

      {/* Template Editor */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Template Content
          </label>
          <div className="flex items-center space-x-2">
            <DynamicPreviewFormButton />
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyTemplate}
              className="text-xs"
            >
              <Copy className="w-3 h-3 mr-1" />
              Copy
            </Button>
            {hasUnsavedTemplateChanges && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleApplyChanges}
                className="text-xs bg-yellow-600 hover:bg-yellow-700 text-white border-yellow-600 transition-colors"
              >
                <AlertTriangle className="w-3 h-3 mr-1" />
                {onApply ? 'Apply & Save' : 'Apply Changes'}
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* Editor Panel */}
          <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <MDEditor
              value={localTemplate}
              onChange={(value) => handleTemplateChange(value || '')}
              preview="edit"
              extraCommands={[]}
              hideToolbar={false}
              visibleDragbar={false}
              textareaProps={{
                placeholder: 'Enter your Handlebars template here...',
                style: {
                  fontSize: '14px',
                  fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                  minHeight: '400px',
                  lineHeight: '1.5',
                  resize: 'none'
                },
                spellCheck: false,
                autoComplete: 'off',
                autoCorrect: 'off',
                autoCapitalize: 'off'
              }}
              height={400}
            />
          </div>

          {/* Custom Preview Panel */}
          <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
            <div className="bg-gray-50 dark:bg-gray-800 px-3 py-2 border-b border-gray-300 dark:border-gray-600">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Live Preview
                </span>
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  Handlebars Processing
                </span>
              </div>
            </div>
            <div className="h-[400px] overflow-y-auto p-4 bg-white dark:bg-gray-900">
              {previewContent ? (
                <MarkdownRenderer>
                  {previewContent}
                </MarkdownRenderer>
              ) : (
                <div className="text-gray-500 italic">
                  No preview available. Add template content to see the preview.
                  <div className="text-xs mt-2">
                    Debug: Template length: {localTemplate?.length || 0},
                    Sample data keys: {Object.keys(sampleData).length}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Help Documentation */}
      <Accordion className="space-y-2">
        <ColoredAccordionItem
          title="Handlebars Syntax Reference"
          variant="blue"
          icon={<BookOpen className="w-4 h-4" />}
        >
          <div className="space-y-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="font-medium mb-1">Variables:</div>
                <div className="font-mono bg-white dark:bg-gray-800 p-2 rounded">
                  {`{{variable}}`}
                </div>
              </div>
              <div>
                <div className="font-medium mb-1">Conditionals:</div>
                <div className="font-mono bg-white dark:bg-gray-800 p-2 rounded">
                  {`{{#if condition}}...{{/if}}`}
                </div>
              </div>
              <div>
                <div className="font-medium mb-1">Loops:</div>
                <div className="font-mono bg-white dark:bg-gray-800 p-2 rounded">
                  {`{{#each items}}...{{/each}}`}
                </div>
              </div>
              <div>
                <div className="font-medium mb-1">Comments:</div>
                <div className="font-mono bg-white dark:bg-gray-800 p-2 rounded">
                  {`{{!-- comment --}}`}
                </div>
              </div>
            </div>
          </div>
        </ColoredAccordionItem>

        <ColoredAccordionItem
          title="Available Variables"
          variant="green"
          icon={<Database className="w-4 h-4" />}
        >
          <div className="space-y-3">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {config.templateStructure.variables.map((variable, index) => (
                <div key={index} className="font-mono bg-white dark:bg-gray-800 p-2 rounded">
                  {`{{${variable}}}`}
                </div>
              ))}
            </div>
            <div>
              <div className="font-medium mb-1">Loop Variables:</div>
              <div className="flex flex-wrap gap-2">
                {config.templateStructure.loopVariables.map((variable, index) => (
                  <div key={index} className="font-mono bg-white dark:bg-gray-800 p-2 rounded">
                    {`{{#each ${variable}}}`}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </ColoredAccordionItem>

        <ColoredAccordionItem
          title="Dynamic Preview Form Generator"
          variant="purple"
          icon={<HelpCircle className="w-4 h-4" />}
        >
          <div className="space-y-1">
            <div>• <strong>Automatic Field Generation:</strong> Analyzes your Handlebars template to create appropriate form fields</div>
            <div>• <strong>Variable Detection:</strong> Extracts simple variables ({`{{variable}}`}), conditionals ({`{{#if condition}}`}), and loops ({`{{#each array}}`})</div>
            <div>• <strong>Smart Field Types:</strong> Text inputs for variables, checkboxes for conditionals, dynamic arrays for loops</div>
            <div>• <strong>Template Sample Data:</strong> Pre-populated with realistic data from {"template's"} built-in sampleData field</div>
            <div>• <strong>Real-time Preview:</strong> See exactly how your template renders with custom sample data</div>
            <div>• <strong>Array Management:</strong> Add/remove array items with proper field structure detection</div>
            <div>• <strong>Data Persistence:</strong> Sample data saved to template configuration for future use</div>
            <div>• <strong>Three-way Sync:</strong> Integrates with Visual Designer, Template Code, and Live Preview tabs</div>
          </div>
        </ColoredAccordionItem>

        <ColoredAccordionItem
          title="🔄 Live Template Preview with Automatic Sample Data"
          variant="blue"
          icon={<Eye className="w-4 h-4" />}
        >
          <div className="space-y-1">
            <div>• <strong>Split-Panel Design:</strong> Code editor on left, live preview on right for instant feedback</div>
            <div>• <strong>Automatic Preview:</strong> Sample data automatically applied when template loads - no manual action needed</div>
            <div>• <strong>Template-Based Data:</strong> Uses realistic sample data directly from bot template for authentic previews</div>
            <div>• <strong>Professional Editing:</strong> Syntax highlighting and advanced editing features for clean code</div>
            <div>• <strong>Real-time Updates:</strong> Preview updates automatically as you edit the template code</div>
            <div>• <strong>Smart Processing:</strong> {'{{#if}}'} conditions and {'{{#each}}'} loops rendered with realistic data</div>
            <div>• <strong>Production Styling:</strong> Preview shows exactly how customers will see the message</div>
            <div>• <strong>Custom Testing:</strong> Use {'"Preview with Custom Data"'} button to test with different sample data</div>
          </div>
        </ColoredAccordionItem>

        <ColoredAccordionItem
          title="🎯 Preview with Custom Data"
          variant="purple"
          icon={<Database className="w-4 h-4" />}
        >
          <div className="space-y-1">
            <div>• <strong>Test Different Scenarios:</strong> Try your template with various data to see all possibilities</div>
            <div>• <strong>Smart Form Generation:</strong> Automatically creates form fields based on your template variables</div>
            <div>• <strong>Easy Data Entry:</strong> Simple forms for text, checkboxes for conditions, lists for arrays</div>
            <div>• <strong>Template Data Included:</strong> Pre-filled with realistic sample data from your bot template</div>
            <div>• <strong>Instant Preview:</strong> See changes immediately in the preview as you modify data</div>
            <div>• <strong>Customer Perspective:</strong> Preview exactly what customers will see in conversations</div>
            <div>• <strong>No Technical Skills Needed:</strong> User-friendly interface for testing templates</div>
          </div>
        </ColoredAccordionItem>

        <ColoredAccordionItem
          title="Security Notice"
          variant="red"
          icon={<Shield className="w-4 h-4" />}
        >
          <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-red-600 dark:text-red-400 mt-0.5" />
            <div>
              <div className="font-medium mb-1">Security Notice:</div>
              <div>
                Avoid using raw Handlebars blocks ({`{{{}}}`}), partials, or unsafe helpers.
                Only use safe constructs like variables, conditionals, and loops.
              </div>
            </div>
          </div>
        </ColoredAccordionItem>
      </Accordion>
    </div>
  );
};

'use client';

import React from 'react';
import { useApiResponseDesignerStore } from '@/stores/apiResponseDesignerStore';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

interface HandlebarsPreviewProps {
  content: string;
}

export const HandlebarsPreview: React.FC<HandlebarsPreviewProps> = ({ content }) => {
  const { sampleData, syncTemplateToPreview } = useApiResponseDesignerStore();

  // Use the store's template processing function for consistency
  const processedContent = React.useMemo(() => {
    if (!content.trim()) {
      return 'No template content to preview';
    }

    // Use the same processing logic as the store for consistency
    return substituteVariables(content, sampleData);
  }, [content, sampleData]);

  // Simple variable substitution using actual sample data (same as store)
  const substituteVariables = (template: string, sampleData: Record<string, unknown>): string => {
    if (!template || !sampleData) {
      return template || 'No template content available';
    }

    let processedTemplate = template;

    // Process conditional blocks {{#if variable}}...{{/if}}
    const conditionalRegex = /\{\{#if\s+([^}]+)\}\}([\s\S]*?)\{\{\/if\}\}/g;
    processedTemplate = processedTemplate.replace(conditionalRegex, (_, condition, content) => {
      const conditionValue = sampleData[condition.trim()];
      // Show content if condition exists and is truthy
      if (conditionValue !== undefined && conditionValue !== null && conditionValue !== false && conditionValue !== '') {
        return substituteVariables(content, sampleData);
      }
      return '';
    });

    // Process {{#else}} blocks within conditionals
    const elseRegex = /\{\{else\}\}([\s\S]*?)(?=\{\{\/if\}\})/g;
    processedTemplate = processedTemplate.replace(elseRegex, (_, elseContent) => {
      return substituteVariables(elseContent, sampleData);
    });

    // Process loop blocks {{#each array}}...{{/each}}
    const loopRegex = /\{\{#each\s+([^}]+)\}\}([\s\S]*?)\{\{\/each\}\}/g;
    processedTemplate = processedTemplate.replace(loopRegex, (_, arrayName, content) => {
      const arrayData = sampleData[arrayName.trim()];
      if (Array.isArray(arrayData) && arrayData.length > 0) {
        // Process all items in the array (up to 5 for preview)
        return arrayData.slice(0, 5).map((item, index) => {
          const itemData = { ...sampleData, ...item, '@index': index + 1 };
          return substituteVariables(content, itemData);
        }).join('\n\n');
      }
      return '';
    });

    // Process simple variable substitution {{variable}}
    processedTemplate = processedTemplate.replace(/\{\{([^#\/][^}]*)\}\}/g, (_, variable) => {
      const varName = variable.trim();
      const value = sampleData[varName];

      if (value !== undefined && value !== null) {
        return value.toString();
      }

      // Return placeholder for unknown variables
      return `{{${varName}}}`;
    });

    return processedTemplate;
  };

  return (
    <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="flex justify-start">
        <div className="flex flex-col max-w-[80%] min-w-0">
          <div className="p-3 rounded-lg shadow-sm min-w-0 max-w-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700">
            <div
              style={{
                wordWrap: 'break-word',
                overflowWrap: 'break-word',
                wordBreak: 'normal'
              }}
            >
              <MarkdownRenderer>
                {processedContent}
              </MarkdownRenderer>
            </div>
            
            <div className="text-xs mt-2 text-gray-500 dark:text-gray-400">
              {new Date().toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper function to detect if content contains Handlebars syntax
export const hasHandlebarsSyntax = (content: string): boolean => {
  return /\{\{[^}]*\}\}/.test(content);
};

// Helper function to process Handlebars content for preview
export const processHandlebarsForPreview = (content: string): React.ReactElement => {
  return <HandlebarsPreview content={content} />;
};

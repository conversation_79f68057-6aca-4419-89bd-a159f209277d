'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, Eye, Trash2 } from 'lucide-react';
import { TemplateBlock } from '@/stores/apiResponseDesignerStore';

interface VisualBlockProps {
  block: TemplateBlock;
  onEdit: (block: TemplateBlock) => void;
  onDelete: (id: string) => void;
  isDragging?: boolean;
}

export const VisualBlock: React.FC<VisualBlockProps> = ({ 
  block, 
  onEdit, 
  onDelete, 
  isDragging 
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: block.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  // Render the visual representation that matches the exact template output
  const renderBlockPreview = () => {
    const nestPadding = (block.nestLevel || 0) * 16;
    
    switch (block.type) {
      case 'text':
        return (
          <div className="flex items-center space-x-2" style={{ paddingLeft: nestPadding }}>
            <span className="text-lg">📝</span>
            <span className={`${block.isBold ? 'font-bold' : ''} ${block.isItalic ? 'italic' : ''}`}>
              {block.content}
            </span>
          </div>
        );
      
      case 'variable':
        return (
          <div className="flex items-center space-x-2" style={{ paddingLeft: nestPadding }}>
            <span className="text-lg">🔗</span>
            <span className="font-mono text-blue-600 dark:text-blue-400">
              {`{{${block.content}}}`}
            </span>
          </div>
        );
      
      case 'conditional':
        return (
          <div className="space-y-1" style={{ paddingLeft: nestPadding }}>
            <div className="flex items-center space-x-2">
              <span className="text-lg">❓</span>
              <span className="font-mono text-purple-600 dark:text-purple-400">
                {`{{#if ${block.condition}}}`}
              </span>
            </div>
            <div className="pl-6 text-gray-700 dark:text-gray-300">
              {block.conditionContent}
            </div>
            <div className="font-mono text-purple-600 dark:text-purple-400">
              {`{{/if}}`}
            </div>
          </div>
        );
      
      case 'loop':
        return (
          <div className="space-y-1" style={{ paddingLeft: nestPadding }}>
            <div className="flex items-center space-x-2">
              <span className="text-lg">🔄</span>
              <span className="font-mono text-green-600 dark:text-green-400">
                {`{{#each ${block.loopVariable}}}`}
              </span>
            </div>
            <div className="pl-6 space-y-1">
              {block.loopContent?.map((subBlock, index) => (
                <div key={index} className="text-sm">
                  {renderSubBlock(subBlock)}
                </div>
              ))}
            </div>
            <div className="font-mono text-green-600 dark:text-green-400">
              {`{{/each}}`}
            </div>
          </div>
        );
      
      case 'field':
        return (
          <div className="flex items-center space-x-2" style={{ paddingLeft: nestPadding }}>
            <span className="text-lg">{block.fieldIcon || '📦'}</span>
            <span className="font-bold">**{block.fieldLabel}**:</span>
            <span className="font-mono text-blue-600 dark:text-blue-400">
              {`{{${block.fieldName}}}`}
            </span>
            {block.fieldType === 'currency' && <span className="text-green-600">💰</span>}
            {block.fieldType === 'rating' && <span className="text-yellow-500">⭐</span>}
            {block.fieldType === 'badge' && <span className="text-blue-500">🏷️</span>}
            {block.fieldType === 'number' && <span className="text-purple-500">📊</span>}
          </div>
        );
      
      case 'separator':
        return (
          <div className="flex items-center space-x-2" style={{ paddingLeft: nestPadding }}>
            <span className="text-lg">➖</span>
            <div className="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
            <span className="text-xs text-gray-400">{block.content || '---'}</span>
          </div>
        );
      
      case 'linebreak':
        return (
          <div className="h-4 flex items-center" style={{ paddingLeft: nestPadding }}>
            <span className="text-xs text-gray-400 italic">⏎ Line Break</span>
          </div>
        );
      
      default:
        return (
          <div className="flex items-center space-x-2" style={{ paddingLeft: nestPadding }}>
            <span className="text-lg">📄</span>
            <span>{block.content}</span>
          </div>
        );
    }
  };

  const renderSubBlock = (subBlock: TemplateBlock) => {
    switch (subBlock.type) {
      case 'field':
        return (
          <div className="flex items-center space-x-2">
            <span>{subBlock.fieldIcon || '📦'}</span>
            <span className="font-bold">**{subBlock.fieldLabel}**:</span>
            <span className="font-mono text-blue-600 dark:text-blue-400">
              {`{{${subBlock.fieldName}}}`}
            </span>
            {subBlock.fieldType === 'currency' && <span className="text-green-600">💰</span>}
            {subBlock.fieldType === 'rating' && <span className="text-yellow-500">⭐</span>}
          </div>
        );
      case 'separator':
        return (
          <div className="flex items-center space-x-2">
            <span>➖</span>
            <div className="flex-1 border-t border-gray-300 dark:border-gray-600"></div>
          </div>
        );
      default:
        return <span className="text-gray-600 dark:text-gray-400">{subBlock.content}</span>;
    }
  };

  const getBlockTypeColor = (type: TemplateBlock['type']) => {
    switch (type) {
      case 'text': return 'border-l-blue-500';
      case 'variable': return 'border-l-cyan-500';
      case 'conditional': return 'border-l-purple-500';
      case 'loop': return 'border-l-green-500';
      case 'field': return 'border-l-orange-500';
      case 'separator': return 'border-l-gray-500';
      case 'linebreak': return 'border-l-gray-300';
      default: return 'border-l-gray-400';
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all border-l-4 ${getBlockTypeColor(block.type)} ${
        isSortableDragging ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
      }`}
    >
      <div className="flex items-start space-x-3">
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="flex items-center justify-center w-6 h-6 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 cursor-grab active:cursor-grabbing mt-1 flex-shrink-0"
        >
          <GripVertical className="w-4 h-4" />
        </div>

        {/* Block Preview */}
        <div className="flex-1 min-w-0">
          {renderBlockPreview()}
          
          {/* Block Type Badge */}
          <div className="mt-2 flex items-center space-x-2">
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
              {block.type}
            </span>
            {block.order !== undefined && (
              <span className="text-xs text-gray-400">
                Order: {block.order}
              </span>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
          <button
            onClick={() => onEdit(block)}
            className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200 rounded hover:bg-blue-50 dark:hover:bg-blue-900/30"
            title="Edit block"
          >
            <Eye className="w-3 h-3" />
          </button>
          <button
            onClick={() => onDelete(block.id)}
            className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-200 rounded hover:bg-red-50 dark:hover:bg-red-900/30"
            title="Delete block"
          >
            <Trash2 className="w-3 h-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

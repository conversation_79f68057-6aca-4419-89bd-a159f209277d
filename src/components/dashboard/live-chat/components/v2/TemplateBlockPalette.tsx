'use client';

import React from 'react';
import { Palette, Code } from 'lucide-react';
import { useApiResponseDesignerStore, TemplateBlock } from '@/stores/apiResponseDesignerStore';

interface BlockPaletteItem {
  type: TemplateBlock['type'];
  label: string;
  icon: string;
  description: string;
}

const blockPaletteItems: BlockPaletteItem[] = [
  { type: 'text', label: 'Text Block', icon: '📝', description: 'Static text content' },
  { type: 'variable', label: 'Variable', icon: '🔗', description: 'Dynamic variable {{name}}' },
  { type: 'field', label: 'Field Display', icon: '🏷️', description: 'Formatted field with icon' },
  { type: 'conditional', label: 'Conditional', icon: '❓', description: '{{#if condition}}...{{/if}}' },
  { type: 'loop', label: 'Loop Block', icon: '🔄', description: '{{#each items}}...{{/each}}' },
  { type: 'separator', label: 'Separator', icon: '➖', description: 'Horizontal line' },
  { type: 'linebreak', label: 'Line Break', icon: '⏎', description: 'Empty line spacing' },
];

export const TemplateBlockPalette: React.FC = () => {
  const { 
    config, 
    addBlock, 
    updateConfig 
  } = useApiResponseDesignerStore();

  const fieldCount = config.templateStructure.blocks.filter(b => b.type === 'field').length + 
    config.templateStructure.blocks
      .filter(b => b.type === 'loop')
      .flatMap(b => b.loopContent?.filter(sub => sub.type === 'field') || []).length;

  const loopCount = config.templateStructure.blocks.filter(b => b.type === 'loop').length;
  const conditionalCount = config.templateStructure.blocks.filter(b => b.type === 'conditional').length;

  return (
    <div className="space-y-4 overflow-y-auto">
      <h3 className="text-lg font-medium flex items-center">
        <Palette className="w-5 h-5 mr-2" />
        Template Blocks
      </h3>
      
      <div className="space-y-2">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          Each block maps directly to Handlebars template syntax
        </p>
        <div className="space-y-2">
          {blockPaletteItems.map((item) => (
            <button
              key={item.type}
              onClick={() => addBlock(item.type)}
              className="w-full text-left p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
            >
              <div className="flex items-start space-x-3">
                <span className="text-lg mt-0.5">{item.icon}</span>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {item.label}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {item.description}
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Template Info */}
      <div className="space-y-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
          <Code className="w-4 h-4 mr-2" />
          Template Info
        </h4>
        <div className="space-y-2 text-xs text-gray-600 dark:text-gray-400">
          <div className="flex justify-between">
            <span>Blocks:</span>
            <span className="font-medium">{config.templateStructure.blocks.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Variables:</span>
            <span className="font-medium">{config.templateStructure.variables.length}</span>
          </div>
          <div className="flex justify-between">
            <span>Fields:</span>
            <span className="font-medium">{fieldCount}</span>
          </div>
          <div className="flex justify-between">
            <span>Loops:</span>
            <span className="font-medium">{loopCount}</span>
          </div>
          <div className="flex justify-between">
            <span>Conditionals:</span>
            <span className="font-medium">{conditionalCount}</span>
          </div>
        </div>
        
        {/* Settings */}
        <div className="space-y-2 pt-2 border-t border-gray-200 dark:border-gray-600">
          <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300">Settings</h5>
          <div className="space-y-2">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={config.preserveFormatting}
                onChange={(e) => updateConfig({ preserveFormatting: e.target.checked })}
                className="rounded text-blue-600 focus:ring-blue-500"
              />
              <span className="text-xs text-gray-600 dark:text-gray-400">
                Preserve Exact Formatting
              </span>
            </label>
            {/* Pagination option removed - not part of TemplateConfig interface */}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <h4 className="text-sm font-medium text-green-900 dark:text-green-100">
          Quick Actions
        </h4>
        <div className="space-y-2">
          <button
            onClick={() => addBlock('field')}
            disabled={fieldCount >= 5}
            className="w-full text-left p-2 bg-white dark:bg-gray-800 border border-green-200 dark:border-green-700 rounded text-xs hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <div className="flex items-center space-x-2">
              <span>🏷️</span>
              <span>Add Field ({fieldCount}/5)</span>
            </div>
          </button>
          <button
            onClick={() => addBlock('conditional')}
            className="w-full text-left p-2 bg-white dark:bg-gray-800 border border-green-200 dark:border-green-700 rounded text-xs hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <span>❓</span>
              <span>Add Conditional</span>
            </div>
          </button>
          <button
            onClick={() => addBlock('separator')}
            className="w-full text-left p-2 bg-white dark:bg-gray-800 border border-green-200 dark:border-green-700 rounded text-xs hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <span>➖</span>
              <span>Add Separator</span>
            </div>
          </button>
          <button
            onClick={() => addBlock('linebreak')}
            className="w-full text-left p-2 bg-white dark:bg-gray-800 border border-green-200 dark:border-green-700 rounded text-xs hover:bg-green-50 dark:hover:bg-green-900/30 transition-colors"
          >
            <div className="flex items-center space-x-2">
              <span>⏎</span>
              <span>Add Line Break</span>
            </div>
          </button>
        </div>
      </div>

      {/* Usage Tips */}
      <div className="space-y-2 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <h4 className="text-sm font-medium text-yellow-900 dark:text-yellow-100">
          💡 Tips
        </h4>
        <div className="text-xs text-yellow-800 dark:text-yellow-200 space-y-1">
          <div>• Drag blocks to reorder them</div>
          <div>• Use line breaks for spacing</div>
          <div>• Fields inside loops repeat for each item</div>
          <div>• Conditionals show/hide based on data</div>
          <div>• Maximum 5 fields allowed</div>
        </div>
      </div>
    </div>
  );
};

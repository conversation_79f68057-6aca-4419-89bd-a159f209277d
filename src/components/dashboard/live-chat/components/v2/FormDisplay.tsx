'use client';

import React, { memo } from 'react';

interface FormDisplayProps {
  formFields: any[];
  storeInVariable: string;
  onSubmit: (data: Record<string, any>) => void;
}

const FormDisplayComponent: React.FC<FormDisplayProps> = ({
  formFields,
  storeInVariable,
  onSubmit
}) => {
  // This component is intentionally minimal since forms are handled
  // by the sequential form system in ChatSimulatorV2
  // Inline forms are not displayed to prevent duplicate interfaces
return null;
  // return (
  //   <div className="mt-3">
  //     <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
  //       <p className="text-sm text-blue-700 dark:text-blue-300">
  //         📝 Form detected - Sequential form processing will handle this automatically
  //       </p>
  //       <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
  //         Please respond to the form prompts in the chat
  //       </p>
  //     </div>
  //   </div>
  // );
};

export const FormDisplay = memo(FormDisplayComponent);

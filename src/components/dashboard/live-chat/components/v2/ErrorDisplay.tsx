'use client';

import React, { memo, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { ErrorState } from '@/stores/chatSimulatorStore';

interface ErrorDisplayProps {
  error: ErrorState;
  onRecovery: () => void;
  onClear: () => void;
}

const ErrorDisplayComponent: React.FC<ErrorDisplayProps> = ({
  error,
  onRecovery,
  onClear
}) => {
  // Get error icon based on type
  const getErrorIcon = () => {
    switch (error.type) {
      case 'validation':
        return '⚠️';
      case 'api':
        return '🔌';
      case 'network':
        return '🌐';
      case 'flow':
        return '🔄';
      case 'system':
        return '⚙️';
      default:
        return '❌';
    }
  };

  // Get error color classes based on type
  const getErrorClasses = () => {
    const baseClasses = "border-l-4 p-4 mb-4";
    
    switch (error.type) {
      case 'validation':
        return `${baseClasses} bg-yellow-50 dark:bg-yellow-900/20 border-yellow-400 text-yellow-800 dark:text-yellow-200`;
      case 'api':
        return `${baseClasses} bg-blue-50 dark:bg-blue-900/20 border-blue-400 text-blue-800 dark:text-blue-200`;
      case 'network':
        return `${baseClasses} bg-purple-50 dark:bg-purple-900/20 border-purple-400 text-purple-800 dark:text-purple-200`;
      case 'flow':
        return `${baseClasses} bg-orange-50 dark:bg-orange-900/20 border-orange-400 text-orange-800 dark:text-orange-200`;
      case 'system':
        return `${baseClasses} bg-red-50 dark:bg-red-900/20 border-red-400 text-red-800 dark:text-red-200`;
      default:
        return `${baseClasses} bg-red-50 dark:bg-red-900/20 border-red-400 text-red-800 dark:text-red-200`;
    }
  };

  // Format timestamp
  const formatTimestamp = useCallback(() => {
    return error.timestamp.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }, [error.timestamp]);

  // Get recovery suggestions based on error type
  const getRecoverySuggestions = () => {
    switch (error.type) {
      case 'validation':
        return [
          'Check your input format',
          'Ensure required fields are filled',
          'Try a different value'
        ];
      case 'api':
        return [
          'Check your internet connection',
          'Verify API configuration',
          'Try again in a moment'
        ];
      case 'network':
        return [
          'Check your internet connection',
          'Refresh the page',
          'Try again later'
        ];
      case 'flow':
        return [
          'Restart the flow',
          'Check flow configuration',
          'Contact support if issue persists'
        ];
      case 'system':
        return [
          'Refresh the page',
          'Clear browser cache',
          'Contact technical support'
        ];
      default:
        return [
          'Try refreshing the page',
          'Contact support if issue persists'
        ];
    }
  };

  return (
    <div className={getErrorClasses()}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <span className="text-2xl mr-3">{getErrorIcon()}</span>
        </div>
        
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold capitalize">
              {error.type} Error
            </h4>
            <span className="text-xs opacity-75">
              {formatTimestamp()}
            </span>
          </div>
          
          <p className="mb-3 font-medium">
            {error.message}
          </p>
          
          {error.details && (
            <details className="mb-3">
              <summary className="cursor-pointer text-sm font-medium mb-2">
                Technical Details
              </summary>
              <pre className="text-xs bg-black/10 dark:bg-white/10 p-2 rounded overflow-x-auto">
                {error.details}
              </pre>
            </details>
          )}
          
          {/* Recovery suggestions */}
          <div className="mb-4">
            <p className="text-sm font-medium mb-2">💡 Suggestions:</p>
            <ul className="text-sm space-y-1">
              {getRecoverySuggestions().map((suggestion, index) => (
                <li key={index} className="flex items-start">
                  <span className="mr-2">•</span>
                  {suggestion}
                </li>
              ))}
            </ul>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center space-x-2">
            {error.recoverable && (
              <Button
                onClick={onRecovery}
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                🔧 Try Recovery
              </Button>
            )}
            
            <Button
              onClick={onClear}
              variant="outline"
              size="sm"
              className="border-current text-current hover:bg-current/10"
            >
              ✕ Dismiss
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export const ErrorDisplay = memo(ErrorDisplayComponent);

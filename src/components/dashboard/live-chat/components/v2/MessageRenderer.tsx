'use client';

import React, { memo, useMemo } from 'react';
import { ChatMessage } from '@/server/actions/bot-execution-actions';
import { CustomerInfo } from '@/components/ui/CustomerInfoForm';
import { useChatSimulatorStore } from '@/stores/chatSimulatorStore';
import { QuickReplyButtons } from './QuickReplyButtons';
import { FormDisplay } from './FormDisplay';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

// Remove global window interface - using Zustand store instead

interface MessageRendererProps {
  message: ChatMessage;
  customerInfo: CustomerInfo;
  organizationId: string;
  isLastMessage: boolean;
}

const MessageRendererComponent: React.FC<MessageRendererProps> = ({
  message,
  customerInfo: _customerInfo,
  organizationId: _organizationId,
  isLastMessage
}) => {
  const { conversationState } = useChatSimulatorStore();

  // Memoize message styling to prevent re-renders
  const messageClassName = useMemo(() => {
    const baseClasses = "p-3 rounded-lg shadow-sm min-w-0 max-w-full";

    if (message.type === 'user') {
      return `${baseClasses} bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white ml-auto`;
    }
    
    if (message.type === 'system') {
      if (message.metadata?.type === 'offline_indicator') {
        return `${baseClasses} bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-700 mx-auto`;
      }
      if (message.metadata?.type === 'error') {
        return `${baseClasses} bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-700 mx-auto`;
      }
      return `${baseClasses} bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 border border-yellow-200 dark:border-yellow-700 mx-auto`;
    }
    
    if (message.metadata?.type === 'offline_message') {
      return `${baseClasses} bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 border border-orange-200 dark:border-orange-700`;
    }
    
    return `${baseClasses} bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700`;
  }, [message.type, message.metadata?.type]);

  // Memoize timestamp formatting
  const formattedTime = useMemo(() => {
    return new Date(message.timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  }, [message.timestamp]);

  // Memoize content processing for variable substitution
  const processedContent = useMemo(() => {
    if (!conversationState?.variables || !message.content) {
      return message.content;
    }

    let content = message.content;

    // Check if content contains Handlebars control structures - if so, it should already be processed server-side
    const hasHandlebarsControls = /\{\{#(if|each|unless|with)\b|\{\{\/|\{\{\^|\{\{else\}\}/.test(content);

    if (hasHandlebarsControls) {
      // Content contains Handlebars templates - should be processed server-side
      // Only do minimal processing for any remaining simple variables
      return content;
    }

    // Simple variable substitution for basic templates only
    Object.entries(conversationState.variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
      content = content.replace(regex, String(value));
    });

    return content;
  }, [message.content, conversationState?.variables]);



  // Get button click handler from Zustand store
  const { handleButtonClick } = useChatSimulatorStore();

  // Handle quick reply buttons
  const renderQuickReplies = () => {
    if (message.metadata?.type !== 'options' || !message.metadata?.options) {
      return null;
    }

    return (
      <QuickReplyButtons
        options={message.metadata.options}
        disabled={!isLastMessage}
        onSelect={handleButtonClick}
      />
    );
  };

  // Handle form display
  const renderForm = () => {
    if (message.metadata?.type !== 'form' || !message.metadata?.formFields) {
      return null;
    }

    return (
      <FormDisplay
        formFields={message.metadata.formFields}
        storeInVariable={message.metadata.storeInVariable}
        onSubmit={(_data) => {
          // Handle form submission
        }}
      />
    );
  };

  return (
    <div className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
      <div className="flex flex-col max-w-[80%] min-w-0">
        {/* Message bubble */}
        <div className={messageClassName}>
          {/* Sender name for live chat messages */}
          {message.metadata?.liveChatMessage && message.metadata?.senderName && (
            <div className="text-xs opacity-75 mb-1">
              {message.metadata.senderName}
            </div>
          )}

          {/* Message content */}
          <div
            style={{
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              wordBreak: 'normal'
            }}
          >
            <MarkdownRenderer>
              {processedContent}
            </MarkdownRenderer>
          </div>
          {/* Quick reply buttons */}
          {renderQuickReplies()}

          {/* Form display */}
          {renderForm()}

          {/* Timestamp */}
          <div className={`text-xs mt-2 ${
            message.type === 'user'
              ? 'text-white/70'
              : 'text-gray-500 dark:text-gray-400'
          }`}>
            {formattedTime}
          </div>
        </div>

        {/* Message status indicators */}
        {message.metadata?.type === 'flow_completion' && (
          <div className="text-xs text-green-600 dark:text-green-400 mt-1 text-center">
            ✅ Flow completed successfully
          </div>
        )}

        {message.metadata?.type === 'flow_restart' && (
          <div className="text-xs text-blue-600 dark:text-blue-400 mt-1 text-center">
            🔄 Flow restarted
          </div>
        )}

        {message.metadata?.type === 'error_recovery' && (
          <div className="text-xs text-green-600 dark:text-green-400 mt-1 text-center">
            🔧 Error resolved
          </div>
        )}
      </div>
    </div>
  );
};

export const MessageRenderer = memo(MessageRendererComponent);

'use client';

import React, { memo } from 'react';
import { FormState } from '@/stores/chatSimulatorStore';

interface FormHandlerProps {
  form: FormState;
  onComplete: (data: Record<string, any>) => void;
  onCancel: () => void;
}

const FormHandlerComponent: React.FC<FormHandlerProps> = ({
  form,
  onComplete,
  onCancel
}) => {
  const currentField = form.formFields[form.currentFieldIndex];
  const progress = `${form.currentFieldIndex + 1}/${form.formFields.length}`;

  return (
    <div>
      {/* Form Progress Header */}
      
      {/* Current Field Info */}
      <div className="text-xs ">
      
        {currentField?.type === 'number' && (
          <div className="mt-2 text-xs">
            💡 Enter a valid number
            {!currentField.required && ' or type "skip" to skip'}
          </div>
        )}

        {currentField?.type === 'email' && (
          <div className="mt-2 text-xs">
            💡 Enter a valid email address
          </div>
        )}

        {currentField?.type === 'phone' && (
          <div className="mt-2 text-xs">
            💡 Enter a valid phone number
          </div>
        )}

        {!currentField?.required && currentField?.type !== 'number' && currentField?.type !== 'select' && (
          <div className="mt-2 text-xs">
            💡 Type any number to skip this optional field
          </div>
        )}
      </div>
    </div>
  );
};

export const FormHandler = memo(FormHandlerComponent);

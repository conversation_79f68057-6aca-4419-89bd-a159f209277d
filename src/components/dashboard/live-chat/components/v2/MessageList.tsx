'use client';

import React, { useEffect, useRef, memo, useMemo } from 'react';
import { ChatMessage } from '@/server/actions/bot-execution-actions';
import { CustomerInfo } from '@/components/ui/CustomerInfoForm';
import { MessageRenderer } from './MessageRenderer';
import { TypingIndicator } from './TypingIndicator';

interface MessageListProps {
  messages: ChatMessage[];
  isTyping: boolean;
  customerInfo: CustomerInfo;
  organizationId: string;
}

const MessageListComponent: React.FC<MessageListProps> = ({
  messages,
  isTyping,
  customerInfo,
  organizationId
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Memoize the rendered messages to prevent unnecessary re-renders
  const renderedMessages = useMemo(() => {
    return messages.map((message, index) => (
      <MessageRenderer
        key={message.id}
        message={message}
        customerInfo={customerInfo}
        organizationId={organizationId}
        isLastMessage={index === messages.length - 1}
      />
    ));
  }, [messages, customerInfo, organizationId]);

  // Handle empty state
  if (messages.length === 0 && !isTyping) {
    return (
      <div className="flex-1 flex items-center justify-center p-8">
        <div className="text-center">
          <div className="text-4xl mb-4">💬</div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Ready to start testing
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            The conversation will begin here once you start the flow.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="flex-1 overflow-y-auto p-4 pb-16 space-y-4"
      style={{ scrollBehavior: 'smooth' }}
    >
      {/* Rendered Messages */}
      {renderedMessages}

      {/* Typing Indicator */}
      {/*{isTyping && (*/}
      {/*  <TypingIndicator />*/}
      {/*)}*/}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
};

export const MessageList = memo(MessageListComponent);

'use client';

import React from 'react';
import { Eye, Code, RefreshCw } from 'lucide-react';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { useApiResponseDesignerStore } from '@/stores/apiResponseDesignerStore';
import { MarkdownRenderer } from '@/components/ui/MarkdownRenderer';

export const LivePreviewPanel: React.FC = () => {
  const {
    config,
    previewContent,
    generatedTemplate,
    sampleData,
    apiNodeData,
    regeneratePreview
  } = useApiResponseDesignerStore();

  // Get the actual template being used for preview
  const activeTemplate = config.customTemplate || generatedTemplate;

  // Count template elements
  const variableCount = (activeTemplate.match(/\{\{[^#\/][^}]*\}\}/g) || []).length;
  const conditionalCount = (activeTemplate.match(/\{\{#if\s+[^}]+\}\}/g) || []).length;
  const loopCount = (activeTemplate.match(/\{\{#each\s+[^}]+\}\}/g) || []).length;
  const templateLines = activeTemplate.split('\n').length;
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium flex items-center">
          <Eye className="w-5 h-5 mr-2" />
          Live Preview
        </h3>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="text-xs">
            Updates in Real-time
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={regeneratePreview}
            className="text-xs"
          >
            <RefreshCw className="w-3 h-3 mr-1" />
            Refresh
          </Button>
        </div>
      </div>
      
      <p className="text-sm text-gray-600 dark:text-gray-400">
        This preview shows exactly how the message will appear in the ChatSimulatorV2 interface with real-time variable substitution.
      </p>

      {/* Template Processing Info */}
      <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <Code className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
              Template Analysis
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {config.customTemplate && (
              <Badge variant="secondary" className="text-xs">
                Custom Template
              </Badge>
            )}
            <Badge variant="info" className="text-xs">
              Real-time Processing
            </Badge>
          </div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-blue-800 dark:text-blue-200">
          <div className="text-center">
            <div className="text-lg font-semibold">{templateLines}</div>
            <div>Lines</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold">{variableCount}</div>
            <div>Variables</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold">{loopCount}</div>
            <div>Loops</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-semibold">{conditionalCount}</div>
            <div>Conditionals</div>
          </div>
        </div>
        <div className="mt-2 text-xs text-blue-700 dark:text-blue-300">
          <strong>Source:</strong> {config.customTemplate ? 'Template Code Tab (Manual Edit)' : 'Visual Designer (Generated)'}
        </div>
      </div>
      
      {/* Exact ChatSimulatorV2 Message Preview */}
      <div className="bg-gray-50 dark:bg-gray-900 p-4 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex justify-start">
          <div className="flex flex-col max-w-[80%] min-w-0">
            {/* Message bubble with exact MessageRenderer styling */}
            <div className="p-3 rounded-lg shadow-sm min-w-0 max-w-full bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700">
              {/* Message content with exact MessageRenderer styling */}
              <div
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                  wordBreak: 'normal'
                }}
              >
                <MarkdownRenderer>
                  {previewContent}
                </MarkdownRenderer>
              </div>
              
              {/* Timestamp like in MessageRenderer */}
              <div className="text-xs mt-2 text-gray-500 dark:text-gray-400">
                {new Date().toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Variable Substitution Info */}
      <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2 mb-2">
          <Eye className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            Variable Substitution Rules
          </span>
        </div>
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <div className="font-medium mb-2">Sample API Data:</div>
              <div className="space-y-1 font-mono text-xs bg-white dark:bg-gray-700 p-2 rounded">
                <div><span className="text-blue-600">total:</span> {sampleData.total}</div>
                <div><span className="text-blue-600">query:</span> "{sampleData.query}"</div>
                <div><span className="text-blue-600">category:</span> "{sampleData.category}"</div>
                {Array.isArray(sampleData.products) && (
                  <div><span className="text-blue-600">products:</span> [{sampleData.products.length} items]</div>
                )}
                {Array.isArray(sampleData.items) && (
                  <div><span className="text-blue-600">items:</span> [{sampleData.items.length} items]</div>
                )}
                {apiNodeData && (
                  <div><span className="text-green-600">source:</span> API Node Data</div>
                )}
              </div>
            </div>
            <div>
              <div className="font-medium mb-2">Fallback Variables:</div>
              <div className="space-y-1 font-mono text-xs bg-white dark:bg-gray-700 p-2 rounded">
                <div><span className="text-green-600">{'{{name}}'}</span>: "Content will show here"</div>
                <div><span className="text-green-600">{'{{price}}'}</span>: "Content will show here"</div>
                <div><span className="text-green-600">{'{{status}}'}</span>: "Content will show here"</div>
                <div><span className="text-green-600">{'{{unknown}}'}</span>: "Content will show here"</div>
              </div>
            </div>
          </div>
          <div className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded text-xs">
            <strong>How it works:</strong> Variables are substituted with sample data if available,
            otherwise they show "Content will show here" as a placeholder.
            Conditionals and loops are processed with the sample data context.
          </div>
        </div>
      </div>

      {/* Preview Features */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
          <h4 className="text-sm font-medium text-green-900 dark:text-green-100 mb-2">
            ✅ Live Preview Features
          </h4>
          <div className="text-xs text-green-800 dark:text-green-200 space-y-1">
            <div>• Real-time template code synchronization</div>
            <div>• Universal Handlebars template support</div>
            <div>• Automatic variable substitution</div>
            <div>• Conditional block processing ({'{{#if}}'})</div>
            <div>• Loop iteration support ({'{{#each}}'} - first item only)</div>
            <div>• Exact ChatSimulatorV2 styling</div>
            <div>• Fallback values for unknown variables</div>
          </div>
        </div>

        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            � Template Processing
          </h4>
          <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
            <div>• Processes custom templates from Template Code tab</div>
            <div>• Falls back to Visual Designer generated templates</div>
            <div>• Updates automatically as you type (300ms debounce)</div>
            <div>• Handles complex nested structures</div>
            <div>• Shows "Content will show here" for unknown variables</div>
            <div>• Maintains exact markdown formatting</div>
          </div>
        </div>
      </div>
    </div>
  );
};

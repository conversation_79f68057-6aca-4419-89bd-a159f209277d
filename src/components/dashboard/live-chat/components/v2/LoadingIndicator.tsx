'use client';

import React, { memo } from 'react';

interface LoadingIndicatorProps {
  isTyping: boolean;
  customMessage?: string;
}

const LoadingIndicatorComponent: React.FC<LoadingIndicatorProps> = ({
  isTyping,
  customMessage
}) => {
  const getMessage = () => {
    if (customMessage) return customMessage;
    return isTyping ? '<PERSON><PERSON> is typing...' : 'Processing...';
  };

  return (
    <div className="flex items-center justify-center p-2 bg-blue-50 dark:bg-blue-900/20 border-b border-blue-200 dark:border-blue-800">
      <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <span className="text-sm">
          {getMessage()}
        </span>
      </div>
    </div>
  );
};

export const LoadingIndicator = memo(LoadingIndicatorComponent);

'use client';

import React, { useC<PERSON>back, memo, KeyboardEvent } from 'react';
import { Button } from '@/components/ui/Button';
import { FlowState, FormState, PaginationState, useChatSimulatorStore } from '@/stores/chatSimulatorStore';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import { logger } from '@/utils/logger';

// Pagination data interface for direct button navigation
interface PaginationData {
  targetPage?: number;
  command?: 'next' | 'previous' | 'exit';
}

interface UserInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (paginationData?: PaginationData) => void;
  disabled: boolean;
  placeholder: string;
  flowState: FlowState;
  activeForm: FormState | null;
  paginationState: PaginationState | null;
}

const UserInputComponent: React.FC<UserInputProps> = ({
  value,
  onChange,
  onSend,
  disabled,
  placeholder,
  flowState,
  activeForm,
  paginationState
}) => {
  // Create ref to inspect actual DOM element
  const textareaRef = React.useRef<HTMLTextAreaElement>(null);

  // Handle pagination navigation by passing data directly to onSend
  const handlePaginationNavigation = useCallback(async (direction: 'next' | 'previous') => {
    if (!paginationState?.isActive) return;

    try {
      // Calculate target page based on direction
      let targetPage: number;

      if (direction === 'next' && paginationState.hasNextPage) {
        targetPage = paginationState.currentPage + 1;
      } else if (direction === 'previous' && paginationState.hasPreviousPage) {
        targetPage = paginationState.currentPage - 1;
      } else {
        // Invalid navigation - button should be disabled but handle gracefully
        logger.warn(`📄 Invalid pagination navigation: ${direction} from page ${paginationState.currentPage}`);
        return;
      }
      
      // Pass pagination data directly to onSend - no setTimeout or input field manipulation needed
      onSend({
        targetPage,
        command: direction
      });

    } catch (error) {
      logger.error('Error triggering pagination navigation:', error);
    }
  }, [paginationState, onSend]);

  // Handle keyboard events
  const handleKeyDown = useCallback((e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!disabled && value.trim()) {
        onSend(); // No pagination data for text input
      }
    }
  }, [disabled, value, onSend]);

  // Handle send button click
  const handleSend = useCallback(() => {
    if (!disabled && value.trim()) {
      onSend(); // No pagination data for text input
    }
  }, [disabled, value, onSend]);

  // Determine if send button should be disabled
  const isSendDisabled = disabled || !value.trim();

  // Get input status indicator
  const getStatusIndicator = () => {
    if (flowState === 'error') {
      return (
        <div className="flex items-center text-red-600 dark:text-red-400 text-sm">
          <span className="mr-2">⚠️</span>
          Error state
        </div>
      );
    }

    if (flowState === 'completed') {
      return (
        <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
          <span className="mr-2">✅</span>
          Flow completed
        </div>
      );
    }

    if (activeForm) {
      const currentField = activeForm.formFields[activeForm.currentFieldIndex];
      const progress = `${activeForm.currentFieldIndex + 1}/${activeForm.formFields.length}`;
      return (
        <div className="flex items-center text-blue-600 dark:text-blue-400 text-sm">
          {progress}: {currentField?.label}
        </div>
      );
    }

    if (paginationState?.isActive) {
      return (
        <div className="flex items-center justify-between text-blue-600 dark:text-blue-400 text-sm font-medium">
          {/* Left side: Page info and results */}
          <div className="flex items-center">
            <span className="mr-2">📄</span>
            <span className="mr-3">Page {paginationState.currentPage} of {paginationState.totalPages}</span>
            <span className="text-xs bg-blue-100 dark:bg-blue-800 px-2 py-0.5 rounded">
              {paginationState.resultsCount} results
            </span>
          </div>

          {/* Right side: Navigation buttons */}
          <div className="flex items-center space-x-1">
            {/* Previous button */}
            <button
              onClick={() => handlePaginationNavigation('previous')}
              disabled={!paginationState.hasPreviousPage}
              aria-label="Previous page"
              className={`
                p-1 rounded transition-all duration-200 flex items-center justify-center
                ${paginationState.hasPreviousPage
                  ? 'text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-800/50 hover:text-blue-700 dark:hover:text-blue-300 cursor-pointer'
                  : 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                }
              `}
            >
              <ChevronLeftIcon className="w-4 h-4" />
            </button>

            {/* Next button */}
            <button
              onClick={() => handlePaginationNavigation('next')}
              disabled={!paginationState.hasNextPage}
              aria-label="Next page"
              className={`
                p-1 rounded transition-all duration-200 flex items-center justify-center
                ${paginationState.hasNextPage
                  ? 'text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-800/50 hover:text-blue-700 dark:hover:text-blue-300 cursor-pointer'
                  : 'text-gray-400 dark:text-gray-500 cursor-not-allowed'
                }
              `}
            >
              <ChevronRightIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      );
    }

    if (flowState === 'live_chat') {
      return (
        <div className="flex items-center text-green-600 dark:text-green-400 text-sm">
          <span className="mr-2">👤</span>
          Live chat active
        </div>
      );
    }

    if (flowState === 'handoff_pending') {
      return (
        <div className="flex items-center text-yellow-600 dark:text-yellow-400 text-sm">
          <span className="mr-2">⏳</span>
          Waiting for agent
        </div>
      );
    }

    return (
      <div className="flex items-center text-gray-600 dark:text-gray-400 text-sm">
        <span className="mr-2">💬</span>
        Bot conversation
      </div>
    );
  };

  return (
    <div className="p-4 bg-gray-50 dark:bg-gray-800">
      {/* Status indicator */}
      <div className="mb-2">
        {getStatusIndicator()}
      </div>

      {/* Input area */}
      <div className="flex items-stretch space-x-2">
        <div className="flex-1">
          <textarea
            ref={textareaRef}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            rows={1}
            className={`
              w-full h-full px-3 py-2 border rounded-lg resize-none
              focus:outline-none focus:ring-2 focus:border-transparent
              ${paginationState?.isActive
                ? 'focus:ring-blue-500 border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/10'
                : 'focus:ring-blue-500 border-gray-300 dark:border-gray-600'
              }
              ${disabled
                ? 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                : paginationState?.isActive
                  ? 'text-blue-900 dark:text-blue-100'
                  : 'bg-white dark:bg-gray-900 text-gray-900 dark:text-white'
              }
              ${paginationState?.isActive
                ? 'placeholder-blue-500 dark:placeholder-blue-400'
                : 'placeholder-gray-500 dark:placeholder-gray-400'
              }
            `}
            style={{
              minHeight: '40px',
              maxHeight: '120px'
            }}
          />
        </div>

        <Button
          onClick={handleSend}
          disabled={isSendDisabled}
          className={`
            px-4 py-2 rounded-lg font-medium transition-all flex-shrink-0 h-auto min-h-[40px]
            ${isSendDisabled
              ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
              : paginationState?.isActive
                ? 'bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg'
                : 'bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white'
            }
          `}
        >
          {paginationState?.isActive ? '📄 Navigate' : 'Send'}
        </Button>
      </div>

      {/* Help text for special modes */}
      {activeForm && (
        <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
          💡 Tip: Type {'"exit"'} to cancel the form
        </div>
      )}



      {flowState === 'completed' && (
        <div className="mt-2 text-xs text-gray-600 dark:text-gray-400">
          💡 Flow completed. Use the restart button to begin again.
        </div>
      )}
    </div>
  );
};

// Use memo with standard comparison - Zustand will handle reactivity
export const UserInput = memo(UserInputComponent);

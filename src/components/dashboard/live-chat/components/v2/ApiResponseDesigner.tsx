'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Palette, Save } from 'lucide-react';
import { useApiResponseDesignerStore, TemplateConfig } from '@/stores/apiResponseDesignerStore';
import { TemplateCodeEditor } from './TemplateCodeEditor';
import { BlockEditModal } from './BlockEditModal';
import { confirmDialog } from '@/components/ui/ConfirmDialog';


// Interface for the main component props
interface ApiResponseDesignerProps {
  onSave?: (config: TemplateConfig) => void;
  initialConfig?: TemplateConfig;
  apiNodeData?: {
    name?: string;
    displayFields?: (string | { name: string })[];
    responseStructure?: Record<string, unknown>;
    sampleData?: Record<string, unknown>;
    url?: string;
    method?: string;
    headers?: Record<string, unknown>;
    body?: string;
  };
}

// Main modal component using extracted components
interface ApiResponseDesignerModalProps extends ApiResponseDesignerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ApiResponseDesignerModal: React.FC<ApiResponseDesignerModalProps> = ({
  onSave,
  initialConfig,
  apiNodeData,
  isOpen,
  onClose
}) => {
  const {
    activeTab,
    setActiveTab,
    errors,
    clearErrors,
    initializeConfig,
    hasUnsavedTemplateChanges,
    applyTemplateChanges,
    discardTemplateChanges
  } = useApiResponseDesignerStore();

  // Initialize the store only once when modal first opens
  const [hasInitialized, setHasInitialized] = useState(false);

  useEffect(() => {
    if (isOpen && !hasInitialized) {
      initializeConfig(initialConfig, apiNodeData);
      clearErrors();
      setHasInitialized(true);
    }
  }, [isOpen, hasInitialized, initialConfig, apiNodeData, initializeConfig, clearErrors]);

  // Reset initialization flag when modal closes
  useEffect(() => {
    if (!isOpen) {
      setHasInitialized(false);
    }
  }, [isOpen]);

  const handleSave = useCallback((canClose: boolean = true) => {
    // Apply any pending template changes first to ensure Zustand state is up-to-date
    if (hasUnsavedTemplateChanges) {
      applyTemplateChanges();
    }

    // Get the current state after applying changes
    const currentState = useApiResponseDesignerStore.getState();
    const currentConfig = currentState.config;

    // Validate template for security and structure
    const validateTemplate = (template: string): string[] => {
      const errors: string[] = [];

      // Check for dangerous Handlebars features
      const dangerousPatterns = [
        /\{\{\{.*\}\}\}/g, // Raw blocks
        /\{\{>\s*\w+/g, // Partials
        /\{\{\s*lookup\s+/g, // Lookup helper
        /\{\{\s*with\s+/g, // With helper (can be dangerous)
        /\{\{\s*#unless\s+/g, // Unless blocks (keep simple)
      ];

      dangerousPatterns.forEach(pattern => {
        if (pattern.test(template)) {
          errors.push('Template contains potentially unsafe Handlebars constructs');
        }
      });

      // Check field name length
      const fieldMatches = template.match(/\{\{([^}#\/\s]+)\}\}/g);
      if (fieldMatches) {
        fieldMatches.forEach(match => {
          const fieldName = match.replace(/[{}]/g, '');
          if (fieldName.length > 20) {
            errors.push(`Field name "${fieldName}" exceeds 20 character limit`);
          }
        });
      }

      // Check field count using current template structure
      const fieldBlocks = currentConfig.templateStructure.blocks.filter(block => block.type === 'field');
      const loopFieldBlocks = currentConfig.templateStructure.blocks
        .filter(block => block.type === 'loop')
        .flatMap(block => block.loopContent?.filter(subBlock => subBlock.type === 'field') || []);

      const totalFieldCount = fieldBlocks.length + loopFieldBlocks.length;
      if (totalFieldCount > 5) {
        errors.push('Maximum 5 display fields allowed');
      }

      // Validate field names in template structure
      [...fieldBlocks, ...loopFieldBlocks].forEach(fieldBlock => {
        if (fieldBlock.fieldName && fieldBlock.fieldName.length > 20) {
          errors.push(`Field name "${fieldBlock.fieldName}" exceeds 20 character limit`);
        }
      });

      return errors;
    };

    const templateErrors = validateTemplate(currentConfig.customTemplate || currentState.generatedTemplate);
    if (templateErrors.length > 0) {
      // Set errors in store instead of local state
      useApiResponseDesignerStore.getState().setErrors(templateErrors);
      return;
    }

    // Extract display fields from current template structure
    const fieldBlocks = currentConfig.templateStructure.blocks.filter(block => block.type === 'field');
    const loopFieldBlocks = currentConfig.templateStructure.blocks
      .filter(block => block.type === 'loop')
      .flatMap(block => block.loopContent?.filter(subBlock => subBlock.type === 'field') || []);

    const allFieldBlocks = [...fieldBlocks, ...loopFieldBlocks];
    const updatedDisplayFields = allFieldBlocks.map(block => ({
      id: block.id,
      name: block.fieldName || '',
      label: block.fieldLabel || '',
      type: (block.fieldType || 'text') as any
    }));

    // Create final config with the most up-to-date customTemplate
    const finalConfig: TemplateConfig = {
      ...currentConfig,
      displayFields: updatedDisplayFields,
      customTemplate: currentConfig.customTemplate || currentState.generatedTemplate
    };

    onSave?.(finalConfig);
    clearErrors();
    if (canClose) {
      onClose();
    }
  }, [hasUnsavedTemplateChanges, applyTemplateChanges, onSave, clearErrors, onClose]);

  // Handle modal close with unsaved changes check using Zustand state
  const handleModalClose = useCallback(() => {
    if (hasUnsavedTemplateChanges) {
      confirmDialog({
        message: 'You have unsaved changes to the template. Do you want to apply these changes before closing?',
        header: 'Unsaved Template Changes',
        icon: 'pi pi-exclamation-triangle',
        accept: () => {
          // Apply changes using Zustand - this is synchronous and immediate
          applyTemplateChanges();
          handleSave()
          onClose();
        },
        reject: () => {
          // User chose to discard changes
          discardTemplateChanges();
          onClose();
        },
        acceptLabel: 'Apply & Close',
        rejectLabel: 'Discard & Close'
      });
    } else {
      onClose();
    }
  }, [hasUnsavedTemplateChanges, applyTemplateChanges, discardTemplateChanges, onClose]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => !open && handleModalClose()}
      title={
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center space-x-3">
            <Palette className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <span>API Response Template Designer</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-[#8178E8]/10 to-[#6964D3]/10 text-[#8178E8] border border-[#8178E8]/20">
              🎯
            </span>
          </div>
        </div>
      }
      size="full"
      className="api-response-designer-modal"
    >

      <div className="flex flex-col h-full">
        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {[
            { id: 'template', label: 'Template Code', icon: '📝' },
            // { id: 'preview', label: 'Live Preview', icon: '👁️' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span>{tab.icon}</span>
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
        
        <TemplateCodeEditor onApply={handleSave} />
        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Design templates with built-in response formatting
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={handleModalClose}>
              Cancel
            </Button>
            <Button
              onClick={()=>handleSave(true)}
              disabled={errors.length > 0}
              className="flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Block Edit Modal */}
      <BlockEditModal />
    </Dialog>
  );
};

// Main export component - trigger button that opens modal
export const ApiResponseDesigner: React.FC<ApiResponseDesignerProps> = (props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        onClick={handleOpenModal}
        className="flex items-center space-x-2"
      >
        <Palette className="w-4 h-4" />
        <span>Design API Template</span>
      </Button>

      <ApiResponseDesignerModal
        {...props}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </>
  );
};

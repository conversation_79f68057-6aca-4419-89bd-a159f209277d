'use client';

import React, { useState, useEffect, useCallback, memo } from 'react';
import { Database, Play, RotateCcw, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { Checkbox } from '@/components/ui/Checkbox';
import { useApiResponseDesignerStore } from '@/stores/apiResponseDesignerStore';
import { logger } from '@/utils/logger';
import {
  generateSampleDataForTemplate,
  applySampleDataToPreview,
  parseHandlebarsTemplate,
  extractArrayFields,
  type HandlebarsVariable,
  type ArrayItem,
  type SampleDataFormData,
} from '@/utils/sampleDataManager';
import useToast from "@components/ui/Toast";

// Types are now imported from sampleDataManager utility
type FormData = SampleDataFormData;

interface DynamicPreviewFormProps {
  // No props needed - component manages its own state
}

// Functions are now imported from sampleDataManager utility

const DynamicPreviewFormComponent: React.FC<DynamicPreviewFormProps> = () => {
  const { config, updateSampleData, syncTemplateToPreview, apiNodeData } = useApiResponseDesignerStore();
  const toast = useToast();
  // Dialog state
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [formData, setFormData] = useState<FormData>({});
  const [variables, setVariables] = useState<HandlebarsVariable[]>([]);
  const [arrayFields, setArrayFields] = useState<Record<string, string[]>>({});
  const [error, setError] = useState<string | null>(null);

  // Open dialog handler - memoized to prevent re-renders
  const handleOpenDialog = useCallback((): void => {
    setIsOpen(true);
    setError(null);
  }, []);

  // Close dialog handler - memoized to prevent re-renders
  const handleClose = useCallback((): void => {
    setIsOpen(false);
    setFormData({});
    setVariables([]);
    setArrayFields({});
    setError(null);
  }, []);

  // Parse template when modal opens or template changes
  useEffect(() => {
    if (isOpen && config.customTemplate) {
      const initializeModal = async () => {
        try {
          const parsedVars: HandlebarsVariable[] = parseHandlebarsTemplate(config.customTemplate as string);
          setVariables(parsedVars);

          // Extract array fields for each loop variable
          const fields: Record<string, string[]> = {};
          parsedVars.forEach((variable: HandlebarsVariable) => {
            if (variable.isLoop) {
              fields[variable.name] = extractArrayFields(config.customTemplate || '', variable.name);
            }
          });
          setArrayFields(fields);

          // Initialize form data with existing sample data or defaults
          await initializeFormData(parsedVars, fields);
          setError(null);

          // Automatically apply sample data to preview when modal opens
          setTimeout(async () => {
            try {
              await applySampleDataToPreview(
                config.customTemplate || '',
                apiNodeData,
                updateSampleData,
                syncTemplateToPreview
              );
            } catch (error) {
            }
          }, 100);

        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to parse template';
          setError(errorMessage);
          logger.error('Error parsing template:', err);
        }
      };

      initializeModal();
    }
  }, [isOpen, config.customTemplate, apiNodeData, updateSampleData, syncTemplateToPreview]);

  const initializeFormData = useCallback(async (vars: HandlebarsVariable[], fields: Record<string, string[]>): Promise<void> => {
    try {
      // Use the utility function to generate sample data
      const sampleData = await generateSampleDataForTemplate(config.customTemplate || '', apiNodeData);

      // Filter the sample data to only include variables that exist in the template
      const filteredData: FormData = {};
      vars.forEach((variable: HandlebarsVariable) => {
        if (sampleData[variable.name] !== undefined) {
          filteredData[variable.name] = sampleData[variable.name];
        }
      });

      setFormData(filteredData);
    } catch (error) {
      // Fallback to empty form data
      setFormData({});
    }
  }, [config.customTemplate, apiNodeData]);

  const handleInputChange = useCallback((varName: string, value: string | boolean): void => {
    setFormData((prev: FormData) => ({ ...prev, [varName]: value }));
  }, []);

  const handleArrayItemChange = useCallback((arrayName: string, index: number, field: string, value: string): void => {
    setFormData((prev: FormData) => {
      const currentArray: ArrayItem[] = (prev[arrayName] as ArrayItem[]) || [];
      const newArray: ArrayItem[] = [...currentArray];
      newArray[index] = { ...newArray[index], [field]: value };
      return { ...prev, [arrayName]: newArray };
    });
  }, []);

  const addArrayItem = useCallback((arrayName: string): void => {
    const fieldNames: string[] = arrayFields[arrayName] || ['name', 'value'];
    const newItem: ArrayItem = fieldNames.reduce((obj: ArrayItem, field: string) => {
      obj[field] = '';
      return obj;
    }, {});

    setFormData((prev: FormData) => {
      const currentArray: ArrayItem[] = (prev[arrayName] as ArrayItem[]) || [];
      return { ...prev, [arrayName]: [...currentArray, newItem] };
    });
  }, [arrayFields]);

  const removeArrayItem = useCallback((arrayName: string, index: number): void => {
    setFormData((prev: FormData) => {
      const currentArray: ArrayItem[] = (prev[arrayName] as ArrayItem[]) || [];
      const newArray: ArrayItem[] = currentArray.filter((_: ArrayItem, i: number) => i !== index);
      return { ...prev, [arrayName]: newArray };
    });
  }, []);

  const handlePreview = useCallback((): void => {
    try {
      // Update sample data in store
      updateSampleData(formData);

      // Regenerate preview with new data
      if (config.customTemplate) {
        syncTemplateToPreview(config.customTemplate);
      }
      toast.success("Sample Data update","Sample Data updated successfully");
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update preview';
      setError(errorMessage);
      logger.error('Error updating preview:', err);
    }
  }, [formData, config.customTemplate, updateSampleData, syncTemplateToPreview]);

  const handleReset = useCallback(async (): Promise<void> => {
    try {
      await initializeFormData(variables, arrayFields);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset form';
      setError(errorMessage);
      logger.error('Error resetting form:', err);
    }
  }, [variables, arrayFields, initializeFormData]);



  return (
    <>
      {/* Preview with Custom Data Button */}
      <Button
        onClick={handleOpenDialog}
        variant="outline"
        size="sm"
        className="text-xs bg-purple-600 hover:bg-purple-700 text-white border-purple-600"
        title="Test your template with custom sample data"
      >
        <Database className="w-3 h-3 mr-1" />
        Preview with Custom Data
      </Button>

      {/* Main Dialog */}
      <Dialog
        title="Preview with Custom Data"
        open={isOpen}
        onClose={handleClose}
        draggable
        size="large"
        className="max-w-4xl"
      >
        <div className="bg-white/90 dark:bg-[#1e1e28]/90 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] p-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Database className="w-5 h-5" />
                <h3 className="font-semibold">Preview with Custom Data</h3>
              </div>
            </div>
            <p className="text-sm text-white/80 mt-1">
              Test your template with different data to see how it will look to customers
            </p>
          </div>

          {/* Content Area */}
          <div className="p-6 bg-gray-50/50 dark:bg-gray-900/50 max-h-[70vh] overflow-y-auto">
            <div className="space-y-6">
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Fill in the form below to test how your template will look with different data.
                This helps you preview exactly what customers will see in their conversations.
              </div>

              {/* Control Buttons */}
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                  className="bg-green-600 hover:bg-green-700 text-white border-green-600"
                  disabled={variables.length === 0}
                >
                  <Play className="w-3 h-3 mr-1" />
                  Update Preview
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  disabled={variables.length === 0}
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Reset Form
                </Button>


              </div>
          
              {/* Form Fields */}
              <div className="space-y-6">
                {variables.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    No Handlebars variables found in the template.
                    Add variables like {`{{variable}}`}, {`{{#if condition}}`}, or {`{{#each array}}`} to see form fields here.
                  </div>
                ) : (
                  variables.map((variable: HandlebarsVariable) => (
                    <div key={variable.name} className="space-y-3">
                      {variable.type === 'boolean' ? (
                        // Boolean/Conditional Fields
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={variable.name}
                            checked={Boolean(formData[variable.name]) || false}
                            onChange={(checked: boolean) => handleInputChange(variable.name, checked)}
                          />
                          <Label htmlFor={variable.name} className="text-sm font-medium">
                            {variable.name} {variable.isConditional && <span className="text-blue-600">(Conditional)</span>}
                          </Label>
                        </div>
                      ) : variable.type === 'array' ? (
                        // Array Fields
                        <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <Label className="text-sm font-medium">
                              {variable.name} {variable.isLoop && <span className="text-green-600">(Loop Array)</span>}
                            </Label>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => addArrayItem(variable.name)}
                            >
                              <Plus className="w-3 h-3 mr-1" />
                              Add Item
                            </Button>
                          </div>

                          <div className="space-y-3">
                            {((formData[variable.name] as ArrayItem[]) || []).map((item: ArrayItem, index: number) => (
                              <div key={index} className="border border-gray-100 dark:border-gray-800 rounded p-3">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-xs font-medium text-gray-600">Item {index + 1}</span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => removeArrayItem(variable.name, index)}
                                    className="text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                  {(arrayFields[variable.name] || ['name', 'value']).map((field: string) => (
                                    <div key={field}>
                                      <Label htmlFor={`${variable.name}_${index}_${field}`} className="text-xs">
                                        {field}
                                      </Label>
                                      <Input
                                        id={`${variable.name}_${index}_${field}`}
                                        value={item[field] || ''}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                          handleArrayItemChange(variable.name, index, field, e.target.value)
                                        }
                                        placeholder={`Enter ${field}`}
                                        className="text-sm"
                                      />
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        // Text Fields
                        <div>
                          <Label htmlFor={variable.name} className="text-sm font-medium">
                            {variable.name}
                          </Label>
                          {variable.name.toLowerCase().includes('description') ||
                          variable.name.toLowerCase().includes('message') ||
                          variable.name.toLowerCase().includes('text') ? (
                            <Textarea
                              id={variable.name}
                              value={String(formData[variable.name] || '')}
                              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                                handleInputChange(variable.name, e.target.value)
                              }
                              placeholder={`Enter ${variable.name}`}
                              rows={3}
                            />
                          ) : (
                            <Input
                              id={variable.name}
                              value={String(formData[variable.name] || '')}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                handleInputChange(variable.name, e.target.value)
                              }
                              placeholder={`Enter ${variable.name}`}
                            />
                          )}
                        </div>
                      )}
                    </div>
                  ))
                )}
              </div>

              {/* Template Info */}
              {variables.length > 0 && (
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Template Analysis
                  </h4>
                  <div className="text-xs text-blue-800 dark:text-blue-200 space-y-1">
                    <div>• <strong>Total Variables:</strong> {variables.length}</div>
                    <div>• <strong>Text Variables:</strong> {variables.filter((v: HandlebarsVariable) => v.type === 'text').length}</div>
                    <div>• <strong>Conditional Variables:</strong> {variables.filter((v: HandlebarsVariable) => v.type === 'boolean').length}</div>
                    <div>• <strong>Loop Arrays:</strong> {variables.filter((v: HandlebarsVariable) => v.type === 'array').length}</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
              <p className="text-sm text-red-600 dark:text-red-400">⚠️ {error}</p>
            </div>
          )}
        </div>
      </Dialog>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
const DynamicPreviewFormButtonMemo = memo(DynamicPreviewFormComponent);

// Export the component
export { DynamicPreviewFormButtonMemo as DynamicPreviewFormButton };

'use client';

import React, { memo, useCallback } from 'react';

interface QuickReplyOption {
  id?: string;
  text: string;
  value?: string;
}

interface QuickReplyButtonsProps {
  options: QuickReplyOption[];
  onSelect: (value: string) => Promise<void>;
  disabled?: boolean;
}

const QuickReplyButtonsComponent: React.FC<QuickReplyButtonsProps> = ({
  options,
  onSelect,
  disabled = false
}) => {
  const handleSelect = useCallback(async (option: QuickReplyOption) => {
    if (disabled) return;

    // Use value if available, otherwise use text (matching v1 behavior)
    const selectedValue = option.value || option.text;
    await onSelect(selectedValue);
  }, [onSelect, disabled]);

  if (!options.length) {
    return null;
  }

  return (
    <div className="mt-3">
      <div className="space-y-2">
        {options.map((option, index) => (
          <button
            key={option.id || index}
            onClick={() => handleSelect(option)}
            disabled={disabled}
            className="
              block w-full text-left px-3 py-2 text-sm
              bg-gradient-to-r from-[#8178E8]/10 to-[#6964D3]/10
              hover:from-[#8178E8]/20 hover:to-[#6964D3]/20
              border border-[#8178E8]/30 hover:border-[#8178E8]/50
              rounded-lg transition-all duration-200
              disabled:opacity-50 disabled:cursor-not-allowed
              focus:outline-none focus:ring-2 focus:ring-[#8178E8]/50
            "
          >
            <span className="text-[#8178E8] dark:text-[#9d94f0] font-medium">
              {option.text}
            </span>
          </button>
        ))}
      </div>

      <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
        💡 Click a button or type your choice manually
      </p>
    </div>
  );
};

export const QuickReplyButtons = memo(QuickReplyButtonsComponent);

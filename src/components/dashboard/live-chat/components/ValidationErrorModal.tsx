'use client';

import React, { useState } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { AlertTriangle, AlertCircle, Variable, FileText, Link2, CheckCircle, Wand2, Loader2, Undo2 } from 'lucide-react';
import { IEnhancedFlowNode, IFlowVariable, IFlowConnection, BotResponseType, ValidationError } from '@/types/bot';
import { logger } from '@/utils/logger';

interface ValidationErrorModalProps {
  open: boolean;
  onClose: () => void;
  errors: ValidationError[];
  onFixErrors?: () => void;
  // Flow data for auto-fixing
  flowData?: {
    name: string;
    description: string;
    nodes: IEnhancedFlowNode[];
    connections: IFlowConnection[];
    variables: IFlowVariable[];
  };
  // Update functions for auto-fixing
  onFlowDataUpdate?: (updates: {
    nodes?: IEnhancedFlowNode[];
    connections?: IFlowConnection[];
    variables?: IFlowVariable[];
  }) => void;
  onRevalidate?: () => { isValid: boolean; errors: ValidationError[] };
}

interface ParsedError {
  nodeName: string;
  variableName: string;
  field?: string;
  originalMessage: string;
}

interface GroupedErrors {
  undefinedVariables: { [nodeName: string]: ParsedError[] };
  formFieldTypes: ValidationError[];
  conditionalLogic: ValidationError[];
  apiCallErrors: ValidationError[];
  nodeContent: ValidationError[];
  connections: ValidationError[];
  variables: ValidationError[];
  other: ValidationError[];
}

interface AutoFixResult {
  type: 'success' | 'partial' | 'failed';
  message: string;
  fixedCount: number;
  totalCount: number;
  details: string[];
}

interface FixOperation {
  type: 'variable_name' | 'missing_nodes' | 'orphaned_nodes' | 'empty_names' | 'duplicate_variables' | 'create_variables';
  description: string;
  canAutoFix: boolean;
  count: number;
}

// Constants for validation (matching backend)
const RESERVED_VARIABLE_NAMES = ['customer_name', 'customer_email'];
const VARIABLE_NAME_REGEX = /^[a-z][a-z0-9_]*$/;
const MAX_TEXT_LENGTH = 2000;
const MAX_NODE_NAME_LENGTH = 100;

// Enhanced error parsing for various validation error types
const parseUndefinedVariableError = (error: ValidationError): ParsedError | null => {
  // Match pattern: "Node 'NodeName' references undefined variable 'variableName'"
  const match = error.message.match(/Node '([^']+)' references undefined variable '([^']+)'/);
  if (match) {
    return {
      nodeName: match[1],
      variableName: match[2],
      field: error.field,
      originalMessage: error.message
    };
  }
  return null;
};

// Parse form field validation errors
const parseFormFieldError = (error: ValidationError): { fieldType?: string; path?: string; enumValues?: string[] } | null => {
  // Match pattern: "`fieldType` is not a valid enum value for path `path`"
  const enumMatch = error.message.match(/`([^`]+)` is not a valid enum value for path `([^`]+)`/);
  if (enumMatch) {
    return {
      fieldType: enumMatch[1],
      path: enumMatch[2]
    };
  }

  // Match pattern for missing required fields
  const requiredMatch = error.message.match(/Path `([^`]+)` is required/);
  if (requiredMatch) {
    return {
      path: requiredMatch[1]
    };
  }

  return null;
};

// Parse conditional validation errors
const parseConditionalError = (error: ValidationError): { operator?: string; field?: string; type?: string } | null => {
  // Match invalid operator errors
  const operatorMatch = error.message.match(/`([^`]+)` is not a valid enum value for path `operator`/);
  if (operatorMatch) {
    return { operator: operatorMatch[1] };
  }

  // Match missing field errors
  if (error.message.includes('Path `field` is required')) {
    return { field: 'missing' };
  }

  // Match missing type errors
  if (error.message.includes('Path `type` is required')) {
    return { type: 'missing' };
  }

  return null;
};

// Parse API call validation errors
const parseApiCallError = (error: ValidationError): {
  urlError?: boolean;
  methodError?: boolean;
  headersError?: boolean;
  bodyError?: boolean;
  timeoutError?: boolean;
  retriesError?: boolean;
  displayFieldsError?: boolean;
  arrayFieldError?: boolean;
} | null => {
  const message = error.message.toLowerCase?.();

  // Check for URL validation errors
  if (message.includes('api endpoint url') || message.includes('url is required') || message.includes('invalid url')) {
    return { urlError: true };
  }

  // Check for HTTP method errors
  if (message.includes('http method') || message.includes('method is required') || message.includes('invalid method')) {
    return { methodError: true };
  }

  // Check for headers validation errors
  if (message.includes('headers must be valid json') || message.includes('invalid headers') || message.includes('malformed json in headers')) {
    return { headersError: true };
  }

  // Check for request body errors
  if (message.includes('request body') || message.includes('invalid json in body') || message.includes('malformed body')) {
    return { bodyError: true };
  }

  // Check for timeout/retry errors
  if (message.includes('timeout') || message.includes('must be between')) {
    return { timeoutError: true };
  }

  if (message.includes('retries') || message.includes('retry')) {
    return { retriesError: true };
  }

  // Check for display fields errors
  if (message.includes('display fields') || message.includes('maximum 5 fields') || message.includes('field name')) {
    return { displayFieldsError: true };
  }

  // Check for array field errors
  if (message.includes('array field') || message.includes('list field')) {
    return { arrayFieldError: true };
  }

  return null;
};

// Auto-fix utility functions
const fixVariableName = (name: string): string => {
  // Convert to lowercase and replace invalid characters
  let fixed = name.toLowerCase?.()
    .replace(/[^a-z0-9_]/g, '_')  // Replace invalid chars with underscore
    .replace(/^[0-9]/, 'var_$&')  // Prefix with 'var_' if starts with number
    .replace(/_+/g, '_')          // Replace multiple underscores with single
    .replace(/^_|_$/g, '');       // Remove leading/trailing underscores

  // Ensure it starts with a letter
  if (!/^[a-z]/.test(fixed)) {
    fixed = 'var_' + fixed;
  }

  // Avoid reserved names
  if (RESERVED_VARIABLE_NAMES.includes(fixed)) {
    fixed = 'custom_' + fixed;
  }

  return fixed || 'variable';
};

const generateNodeName = (node: IEnhancedFlowNode, index: number): string => {
  const typeNames: Record<BotResponseType, string> = {
    [BotResponseType.TEXT]: 'Text Message',
    [BotResponseType.BUTTONS]: 'Button Choice',
    [BotResponseType.QUICK_REPLY]: 'Quick Reply',
    [BotResponseType.FORM]: 'Form Input',
    [BotResponseType.HANDOFF]: 'Agent Handoff',
    [BotResponseType.CONDITIONAL]: 'Conditional Logic',
    [BotResponseType.API_CALL]: 'API Call'
  };

  const baseName = typeNames[node.type] || 'Node';
  return `${baseName} ${index + 1}`;
};

const findNearestNode = (targetNode: IEnhancedFlowNode, allNodes: IEnhancedFlowNode[]): IEnhancedFlowNode | null => {
  if (allNodes.length <= 1) return null;

  let nearest = null;
  let minDistance = Infinity;

  allNodes.forEach(node => {
    if (node.id === targetNode.id) return;

    const distance = Math.sqrt(
      Math.pow(node.position.x - targetNode.position.x, 2) +
      Math.pow(node.position.y - targetNode.position.y, 2)
    );

    if (distance < minDistance) {
      minDistance = distance;
      nearest = node;
    }
  });

  return nearest;
};

// Group errors by type and node for better organization
const groupErrors = (errors: ValidationError[]): GroupedErrors => {
  const grouped: GroupedErrors = {
    undefinedVariables: {},
    formFieldTypes: [],
    conditionalLogic: [],
    apiCallErrors: [],
    nodeContent: [],
    connections: [],
    variables: [],
    other: []
  };

  errors.forEach(error => {
    // Try to parse as undefined variable error
    const parsedVar = parseUndefinedVariableError(error);
    if (parsedVar) {
      if (!grouped.undefinedVariables[parsedVar.nodeName]) {
        grouped.undefinedVariables[parsedVar.nodeName] = [];
      }
      grouped.undefinedVariables[parsedVar.nodeName].push(parsedVar);
      return;
    }

    // Check for form field type errors
    const formFieldError = parseFormFieldError(error);
    if (formFieldError) {
      grouped.formFieldTypes.push(error);
      return;
    }

    // Check for conditional logic errors
    const conditionalError = parseConditionalError(error);
    if (conditionalError) {
      grouped.conditionalLogic.push(error);
      return;
    }

    // Check for API call errors
    const apiCallError = parseApiCallError(error);
    if (apiCallError) {
      grouped.apiCallErrors.push(error);
      return;
    }

    // Categorize other errors
    if (error.message.includes('Circular reference detected') || error.message.includes('Conversation loop detected')) {
      grouped.other.push(error); // Will be handled specially in the UI
    } else if (error.message.includes('variable') && !error.nodeId) {
      grouped.variables.push(error);
    } else if (error.connectionId) {
      grouped.connections.push(error);
    } else if (error.nodeId) {
      grouped.nodeContent.push(error);
    } else {
      grouped.other.push(error);
    }
  });

  return grouped;
};

export const ValidationErrorModal: React.FC<ValidationErrorModalProps> = ({
  open,
  onClose,
  errors,
  onFixErrors,
  flowData,
  onFlowDataUpdate,
  onRevalidate
}) => {
  const [isFixing, setIsFixing] = useState(false);
  const [fixResult, setFixResult] = useState<AutoFixResult | null>(null);
  const [undoData, setUndoData] = useState<{
    nodes: IEnhancedFlowNode[];
    connections: IFlowConnection[];
    variables: IFlowVariable[];
  } | null>(null);

  const groupedErrors = groupErrors(errors);
  const hasUndefinedVariables = Object.keys(groupedErrors.undefinedVariables).length > 0;
  const totalErrors = errors.filter(e => e.type === 'error').length;
  const totalWarnings = errors.filter(e => e.type === 'warning').length;
  const canAutoFix = flowData && onFlowDataUpdate && onRevalidate;

  // Comprehensive auto-fix function
  const handleAutoFix = async () => {
    if (!canAutoFix || !flowData) return;

    setIsFixing(true);
    setFixResult(null);

    // Store current state for undo
    setUndoData({
      nodes: JSON.parse(JSON.stringify(flowData.nodes)),
      connections: JSON.parse(JSON.stringify(flowData.connections)),
      variables: JSON.parse(JSON.stringify(flowData.variables))
    });

    try {
      let updatedNodes = [...flowData.nodes];
      let updatedConnections = [...flowData.connections];
      let updatedVariables = [...flowData.variables];
      const fixDetails: string[] = [];
      let fixedCount = 0;

      // 1. Fix variable names
      const variableNameErrors = errors.filter(e =>
        e.message.includes('must start with a lowercase letter') ||
        e.message.includes('can only contain lowercase letters') ||
        e.message.includes('is reserved for system use')
      );

      for (const error of variableNameErrors) {
        const match = error.message.match(/Variable name '([^']+)'/);
        if (match) {
          const oldName = match[1];
          const newName = fixVariableName(oldName);

          // Update variable name
          updatedVariables = updatedVariables.map(v =>
            v.name === oldName ? { ...v, name: newName } : v
          );

          // Update all references in node content
          updatedNodes = updatedNodes.map(node => {
            const updatedNode = { ...node };
            if (updatedNode.content?.text) {
              updatedNode.content.text = updatedNode.content.text.replace(
                new RegExp(`\\{\\{${oldName}\\}\\}`, 'g'),
                `{{${newName}}}`
              );
            }
            if (updatedNode.content?.options) {
              updatedNode.content.options = updatedNode.content.options.map(option => ({
                ...option,
                text: option.text?.replace(
                  new RegExp(`\\{\\{${oldName}\\}\\}`, 'g'),
                  `{{${newName}}}`
                ) || option.text
              }));
            }
            return updatedNode;
          });

          fixDetails.push(`Fixed variable name: "${oldName}" → "${newName}"`);
          fixedCount++;
        }
      }

      // 2. Fix duplicate variable names
      const duplicateErrors = errors.filter(e => e.message.includes('Duplicate variable name'));
      const seenNames = new Set<string>();

      updatedVariables = updatedVariables.map((variable, index) => {
        if (seenNames.has(variable.name)) {
          let counter = 2;
          let newName = `${variable.name}_${counter}`;
          while (seenNames.has(newName) || updatedVariables.some(v => v.name === newName)) {
            counter++;
            newName = `${variable.name}_${counter}`;
          }
          seenNames.add(newName);
          fixDetails.push(`Fixed duplicate variable: "${variable.name}" → "${newName}"`);
          fixedCount++;
          return { ...variable, name: newName };
        }
        seenNames.add(variable.name);
        return variable;
      });

      // 3. Auto-designate start and end nodes
      const startNodeErrors = errors.filter(e => e.message.includes('exactly one start node'));
      const endNodeErrors = errors.filter(e => e.message.includes('at least one end node'));

      if (startNodeErrors.length > 0 && updatedNodes.length > 0) {
        // Remove all start node designations first
        updatedNodes = updatedNodes.map(node => ({ ...node, isStartNode: false }));
        // Designate first node as start
        updatedNodes[0].isStartNode = true;
        fixDetails.push(`Designated "${updatedNodes[0].name}" as start node`);
        fixedCount++;
      }

      if (endNodeErrors.length > 0 && updatedNodes.length > 0) {
        // Designate last node as end node if no end nodes exist
        const hasEndNode = updatedNodes.some(node => node.isEndNode);
        if (!hasEndNode) {
          updatedNodes[updatedNodes.length - 1].isEndNode = true;
          fixDetails.push(`Designated "${updatedNodes[updatedNodes.length - 1].name}" as end node`);
          fixedCount++;
        }
      }

      // 4. Generate names for nodes without names
      const emptyNameErrors = errors.filter(e => e.message.includes('is missing a name'));
      updatedNodes = updatedNodes.map((node, index) => {
        if (!node.name || node.name.trim() === '') {
          const newName = generateNodeName(node, index);
          fixDetails.push(`Generated name for node: "${newName}"`);
          fixedCount++;
          return { ...node, name: newName };
        }
        return node;
      });

      // 5. Fix form field type validation errors
      const formFieldErrors = groupedErrors.formFieldTypes;
      if (formFieldErrors.length > 0) {
        updatedNodes = updatedNodes.map(node => {
          if (node.content?.formFields) {
            const fixedFormFields = node.content.formFields.map((field: any) => {
              const fixedField = { ...field };

              // Fix invalid form field types
              const invalidTypeMapping: Record<string, string> = {
                'tel': 'text',
                'date': 'text',
                'datetime': 'text',
                'datetime-local': 'text',
                'time': 'text',
                'week': 'text',
                'month': 'text',
                'number': 'text',
                'range': 'text',
                'color': 'text',
                'file': 'text',
                'hidden': 'text',
                'image': 'text',
                'password': 'text',
                'radio': 'select',
                'checkbox': 'select',
                'button': 'text',
                'submit': 'text',
                'reset': 'text'
              };

              if (field.type && invalidTypeMapping[field.type]) {
                fixedField.type = invalidTypeMapping[field.type];
                fixDetails.push(`Fixed form field type: "${field.type}" → "${fixedField.type}"`);
                fixedCount++;
              }

              // Add missing required properties
              if (!field.id) {
                fixedField.id = `field_${Math.random().toString(36).substr(2, 9)}`;
                fixDetails.push(`Added missing ID to form field`);
                fixedCount++;
              }

              if (!field.name && field.label) {
                fixedField.name = field.label.toLowerCase?.().replace(/[^a-z0-9]/g, '_').replace(/_+/g, '_').replace(/^_|_$/g, '');
                fixDetails.push(`Added missing name to form field: "${fixedField.name}"`);
                fixedCount++;
              }

              return fixedField;
            });

            return {
              ...node,
              content: {
                ...node.content,
                formFields: fixedFormFields
              }
            };
          }
          return node;
        });
      }

      // 6. Fix conditional node validation errors
      const conditionalErrors = groupedErrors.conditionalLogic;
      if (conditionalErrors.length > 0) {
        updatedNodes = updatedNodes.map(node => {
          if (node.type === 'conditional' && node.content?.conditions) {
            const fixedConditions = node.content.conditions.map((condition: any) => {
              const fixedCondition = { ...condition };

              // Fix invalid operators by mapping to valid ones
              if (condition.operator) {
                const operatorMapping: Record<string, string> = {
                  'exists': 'contains',
                  'greater_than': 'contains',
                  'less_than': 'contains',
                  'not_equals': 'contains',
                  'not_contains': 'contains',
                  'is_empty': 'contains',
                  'is_not_empty': 'contains',
                  'in': 'contains',
                  'and': 'or',
                  'not': 'or'
                };

                if (operatorMapping[condition.operator]) {
                  fixedCondition.operator = operatorMapping[condition.operator];
                  fixDetails.push(`Fixed condition operator: "${condition.operator}" → "${fixedCondition.operator}"`);
                  fixedCount++;
                }
              }

              // Add missing required fields
              if (!condition.field) {
                fixedCondition.field = 'user_input';
                fixDetails.push(`Added missing field to condition: "user_input"`);
                fixedCount++;
              }

              if (!condition.type) {
                fixedCondition.type = 'contains';
                fixDetails.push(`Added missing type to condition: "contains"`);
                fixedCount++;
              }

              if (!condition.value) {
                fixedCondition.value = '';
                fixDetails.push(`Added missing value to condition`);
                fixedCount++;
              }

              return fixedCondition;
            });

            return {
              ...node,
              content: {
                ...node.content,
                conditions: fixedConditions
              }
            };
          }
          return node;
        });
      }

      // 7. Fix API call node validation errors
      const apiCallErrors = groupedErrors.apiCallErrors;
      if (apiCallErrors.length > 0) {
        updatedNodes = updatedNodes.map(node => {
          if (node.type === 'api_call' && node.content) {
            const fixedContent = { ...node.content };

            // Fix missing URL
            if (!fixedContent.url || !fixedContent.url.trim()) {
              fixedContent.url = 'https://api.example.com/endpoint';
              fixDetails.push(`Added default API endpoint URL`);
              fixedCount++;
            }

            // Fix missing HTTP method
            if (!fixedContent.method) {
              fixedContent.method = 'GET';
              fixDetails.push(`Set default HTTP method to GET`);
              fixedCount++;
            }

            // Fix invalid HTTP method
            const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
            if (fixedContent.method && !validMethods.includes(fixedContent.method)) {
              fixedContent.method = 'GET';
              fixDetails.push(`Fixed invalid HTTP method to GET`);
              fixedCount++;
            }

            // Fix missing or invalid timeout
            if (!fixedContent.timeout || fixedContent.timeout < 1000 || fixedContent.timeout > 300000) {
              fixedContent.timeout = 30000;
              fixDetails.push(`Set timeout to 30 seconds`);
              fixedCount++;
            }

            // Fix missing or invalid retries
            if (fixedContent.retries === undefined || fixedContent.retries < 0 || fixedContent.retries > 10) {
              fixedContent.retries = 3;
              fixDetails.push(`Set retries to 3 attempts`);
              fixedCount++;
            }

            // Fix invalid headers (ensure it's an object)
            if (fixedContent.headers && typeof fixedContent.headers === 'string') {
              try {
                fixedContent.headers = JSON.parse(fixedContent.headers);
                fixDetails.push(`Fixed headers JSON format`);
                fixedCount++;
              } catch {
                fixedContent.headers = {};
                fixDetails.push(`Reset invalid headers to empty object`);
                fixedCount++;
              }
            } else if (!fixedContent.headers) {
              fixedContent.headers = {};
            }

            // Fix display fields validation
            if (fixedContent.displayFields && Array.isArray(fixedContent.displayFields)) {
              // Limit to 5 fields maximum
              if (fixedContent.displayFields.length > 5) {
                fixedContent.displayFields = fixedContent.displayFields.slice(0, 5);
                fixDetails.push(`Limited display fields to maximum of 5`);
                fixedCount++;
              }

              // Truncate field names to 20 characters
              const truncatedFields = fixedContent.displayFields.map((field: string) => {
                if (typeof field === 'string' && field.length > 20) {
                  return field.substring(0, 20);
                }
                return field;
              });

              if (JSON.stringify(truncatedFields) !== JSON.stringify(fixedContent.displayFields)) {
                fixedContent.displayFields = truncatedFields;
                fixDetails.push(`Truncated field names to 20 characters maximum`);
                fixedCount++;
              }
            }

            // Note: maxDisplayItems property was removed as it doesn't exist in IFlowNodeContent interface

            return {
              ...node,
              content: fixedContent
            };
          }
          return node;
        });
      }

      // 6. Create missing variables
      const undefinedVarNames = new Set<string>();
      Object.values(groupedErrors.undefinedVariables).forEach(nodeErrors => {
        nodeErrors.forEach(error => {
          undefinedVarNames.add(error.variableName);
        });
      });

      undefinedVarNames.forEach(varName => {
        // Only create if it doesn't already exist
        if (!updatedVariables.some(v => v.name === varName)) {
          updatedVariables.push({
            name: varName,
            type: 'string',
            defaultValue: '',
            description: `Auto-created variable for ${varName}`,
            required: false
          });
          fixDetails.push(`Created missing variable: "${varName}"`);
          fixedCount++;
        }
      });

      // Apply all fixes
      onFlowDataUpdate({
        nodes: updatedNodes,
        connections: updatedConnections,
        variables: updatedVariables
      });

      // Re-validate to check results
      setTimeout(() => {
        const validationResult = onRevalidate();
        const remainingErrors = validationResult.errors.filter(e => e.type === 'error').length;

        setFixResult({
          type: remainingErrors === 0 ? 'success' : fixedCount > 0 ? 'partial' : 'failed',
          message: remainingErrors === 0
            ? 'All fixable issues have been resolved!'
            : fixedCount > 0
              ? `Fixed ${fixedCount} issues. ${remainingErrors} issues require manual attention.`
              : 'No issues could be automatically fixed.',
          fixedCount,
          totalCount: totalErrors,
          details: fixDetails
        });
        setIsFixing(false);
      }, 100);

    } catch (error) {
      logger.error('Auto-fix error:', error);
      setFixResult({
        type: 'failed',
        message: 'An error occurred while trying to fix the issues.',
        fixedCount: 0,
        totalCount: totalErrors,
        details: []
      });
      setIsFixing(false);
    }
  };

  // Undo auto-fixes
  const handleUndo = () => {
    if (undoData && onFlowDataUpdate) {
      onFlowDataUpdate({
        nodes: undoData.nodes,
        connections: undoData.connections,
        variables: undoData.variables
      });
      setUndoData(null);
      setFixResult(null);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={onClose}
      title="Flow Validation Issues"
      size="large"
      footer={
        <div className="flex flex-col gap-4 w-full">
          {/* Auto-fix result display */}
          {fixResult && (
            <div className={`p-3 rounded-lg border ${
              fixResult.type === 'success'
                ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                : fixResult.type === 'partial'
                  ? 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                  : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
            }`}>
              <div className="flex items-start gap-3">
                <CheckCircle className={`w-5 h-5 mt-0.5 ${
                  fixResult.type === 'success'
                    ? 'text-green-600 dark:text-green-400'
                    : fixResult.type === 'partial'
                      ? 'text-yellow-600 dark:text-yellow-400'
                      : 'text-red-600 dark:text-red-400'
                }`} />
                <div className="flex-1">
                  <p className={`text-sm font-medium ${
                    fixResult.type === 'success'
                      ? 'text-green-900 dark:text-green-100'
                      : fixResult.type === 'partial'
                        ? 'text-yellow-900 dark:text-yellow-100'
                        : 'text-red-900 dark:text-red-100'
                  }`}>
                    {fixResult.message}
                  </p>
                  {fixResult.details.length > 0 && (
                    <div className="mt-2">
                      <p className={`text-xs ${
                        fixResult.type === 'success'
                          ? 'text-green-700 dark:text-green-300'
                          : fixResult.type === 'partial'
                            ? 'text-yellow-700 dark:text-yellow-300'
                            : 'text-red-700 dark:text-red-300'
                      }`}>
                        Changes made:
                      </p>
                      <ul className={`text-xs mt-1 space-y-1 ${
                        fixResult.type === 'success'
                          ? 'text-green-600 dark:text-green-400'
                          : fixResult.type === 'partial'
                            ? 'text-yellow-600 dark:text-yellow-400'
                            : 'text-red-600 dark:text-red-400'
                      }`}>
                        {fixResult.details.map((detail, index) => (
                          <li key={index}>• {detail}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Footer buttons */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {totalErrors > 0 && (
                <span className="text-red-600 dark:text-red-400">
                  {totalErrors} error{totalErrors !== 1 ? 's' : ''}
                </span>
              )}
              {totalErrors > 0 && totalWarnings > 0 && <span className="mx-2">•</span>}
              {totalWarnings > 0 && (
                <span className="text-yellow-600 dark:text-yellow-400">
                  {totalWarnings} warning{totalWarnings !== 1 ? 's' : ''}
                </span>
              )}
            </div>
            <div className="flex gap-3">
              {/* Undo button */}
              {undoData && (
                <Button
                  variant="outline"
                  onClick={handleUndo}
                  className="text-gray-600 border-gray-200 hover:bg-gray-50"
                >
                  <Undo2 className="w-4 h-4 mr-2" />
                  Undo Changes
                </Button>
              )}

              {/* Auto-fix button */}
              {canAutoFix && totalErrors > 0 && (
                <Button
                  variant="outline"
                  onClick={handleAutoFix}
                  disabled={isFixing}
                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                >
                  {isFixing ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Fixing...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      Auto-Fix Issues
                    </>
                  )}
                </Button>
              )}

              {/* Legacy fix button */}
              {onFixErrors && !canAutoFix && (
                <Button
                  variant="outline"
                  onClick={onFixErrors}
                  className="text-blue-600 border-blue-200 hover:bg-blue-50"
                >
                  Help Me Fix These
                </Button>
              )}

              <Button onClick={onClose}>
                Got It
              </Button>
            </div>
          </div>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Header with summary */}
        <div className="flex items-start gap-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex-shrink-0">
            <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-2">
              Your flow has validation issues that need attention
            </h3>
            <p className="text-sm text-red-700 dark:text-red-300">
              These issues will prevent your flow from working correctly and may confuse your customers. 
              Please review and fix the problems below before publishing your flow.
            </p>
          </div>
        </div>

        {/* Undefined Variables Section */}
        {hasUndefinedVariables && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Variable className="w-5 h-5 text-orange-600 dark:text-orange-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Undefined Variables
              </h4>
            </div>
            
            <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
              <p className="text-sm text-orange-800 dark:text-orange-200 mb-4">
                The following nodes reference variables that don't exist. Customers will see the raw variable syntax instead of actual values.
              </p>
              
              <div className="space-y-4">
                {Object.entries(groupedErrors.undefinedVariables).map(([nodeName, nodeErrors]) => (
                  <div key={nodeName} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-orange-200 dark:border-orange-700">
                    <div className="flex items-center gap-2 mb-3">
                      <FileText className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                      <span className="font-medium text-gray-900 dark:text-white">
                        Node: {nodeName}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm text-gray-700 dark:text-gray-300 mb-2">
                        Missing variables:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {nodeErrors.map((error, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 text-xs font-medium rounded-md"
                          >
                            {error.variableName}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                  How to fix this:
                </h5>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Go to the Variables tab and create the missing variables</li>
                  <li>• Or edit the affected nodes and remove the undefined variable references</li>
                  <li>• Make sure variable names match exactly (case-sensitive)</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Form Field Type Errors */}
        {groupedErrors.formFieldTypes.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-purple-600 dark:text-purple-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Form Field Type Issues
              </h4>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
              <p className="text-sm text-purple-800 dark:text-purple-200 mb-4">
                Some form fields use invalid field types that aren't supported by the validation system.
              </p>

              <div className="space-y-3">
                {groupedErrors.formFieldTypes.map((error, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700">
                    <p className="text-sm text-purple-800 dark:text-purple-200">
                      {error.message}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                  How to fix this:
                </h5>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Use only supported field types: text, email, select, textarea</li>
                  <li>• Replace 'tel' and 'date' types with 'text' and add placeholder guidance</li>
                  <li>• Replace 'number' types with 'text' for better compatibility</li>
                  <li>• Use the Auto-Fix button to automatically correct these issues</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Conditional Logic Errors */}
        {groupedErrors.conditionalLogic.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Conditional Logic Issues
              </h4>
            </div>

            <div className="bg-indigo-50 dark:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-800 rounded-lg p-4">
              <p className="text-sm text-indigo-800 dark:text-indigo-200 mb-4">
                Conditional nodes have invalid operators or missing required fields.
              </p>

              <div className="space-y-3">
                {groupedErrors.conditionalLogic.map((error, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-indigo-200 dark:border-indigo-700">
                    <p className="text-sm text-indigo-800 dark:text-indigo-200">
                      {error.message}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                  How to fix this:
                </h5>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Use only valid operators: equals, contains, starts_with, ends_with, regex, intent_match</li>
                  <li>• Ensure all conditions have required fields: type, field, value</li>
                  <li>• Check that field names reference existing variables</li>
                  <li>• Use the Auto-Fix button to automatically correct these issues</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* API Call Errors */}
        {groupedErrors.apiCallErrors.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Link2 className="w-5 h-5 text-cyan-600 dark:text-cyan-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                API Integration Issues
              </h4>
            </div>

            <div className="bg-cyan-50 dark:bg-cyan-900/20 border border-cyan-200 dark:border-cyan-800 rounded-lg p-4">
              <p className="text-sm text-cyan-800 dark:text-cyan-200 mb-4">
                API call nodes have configuration issues that will prevent them from working correctly.
              </p>

              <div className="space-y-3">
                {groupedErrors.apiCallErrors.map((error, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-cyan-200 dark:border-cyan-700">
                    <p className="text-sm text-cyan-800 dark:text-cyan-200">
                      {error.message}
                    </p>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
                  How to fix this:
                </h5>
                <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Ensure API endpoint URL is complete and valid (https://...)</li>
                  <li>• Use valid HTTP methods: GET, POST, PUT, DELETE, PATCH</li>
                  <li>• Format headers as valid JSON: {"{"}"Authorization": "Bearer token"{"}"}</li>
                  <li>• Format request body as valid JSON for POST/PUT requests</li>
                  <li>• Set timeout between 1-300 seconds (default: 30)</li>
                  <li>• Set retries between 0-10 attempts (default: 3)</li>
                  <li>• Limit display fields to maximum 5 fields (20 chars each)</li>
                  <li>• Use the Auto-Fix button to automatically correct these issues</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Other Error Categories */}
        {groupedErrors.nodeContent.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FileText className="w-5 h-5 text-red-600 dark:text-red-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Node Content Issues
              </h4>
            </div>

            <div className="space-y-3">
              {groupedErrors.nodeContent.map((error, index) => (
                <div key={index} className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {error.message}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {groupedErrors.connections.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Link2 className="w-5 h-5 text-red-600 dark:text-red-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Connection Issues
              </h4>
            </div>

            <div className="space-y-3">
              {groupedErrors.connections.map((error, index) => (
                <div key={index} className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {error.message}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {groupedErrors.variables.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Variable className="w-5 h-5 text-red-600 dark:text-red-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Variable Definition Issues
              </h4>
            </div>

            <div className="space-y-3">
              {groupedErrors.variables.map((error, index) => (
                <div key={index} className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    {error.message}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {groupedErrors.other.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                Other Issues
              </h4>
            </div>

            <div className="space-y-3">
              {groupedErrors.other.map((error, index) => {
                const isCircularReference = error.message.includes('Circular reference detected');
                const isConversationLoop = error.message.includes('Conversation loop detected');

                if (isCircularReference) {
                  return (
                    <div key={index} className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-red-800 dark:text-red-200">
                            {error.message}
                          </p>
                          <div className="mt-2 p-2 bg-red-100 dark:bg-red-800/30 rounded border border-red-200 dark:border-red-700">
                            <p className="text-xs text-red-700 dark:text-red-300 font-medium mb-1">
                              💡 Fix suggestions:
                            </p>
                            <ul className="text-xs text-red-600 dark:text-red-400 space-y-1">
                              <li>• Add user interaction points (buttons, forms, conditionals)</li>
                              <li>• Ensure multiple exit paths from the loop</li>
                              <li>• Consider if this loop provides value to users</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                } else if (isConversationLoop) {
                  return (
                    <div key={index} className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                      <div className="flex items-start space-x-3">
                        <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                          <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            {error.message}
                          </p>
                          <div className="mt-2 p-2 bg-yellow-100 dark:bg-yellow-800/30 rounded border border-yellow-200 dark:border-yellow-700">
                            <p className="text-xs text-yellow-700 dark:text-yellow-300 font-medium mb-1">
                              ✅ This is a valid conversation pattern, but ensure:
                            </p>
                            <ul className="text-xs text-yellow-600 dark:text-yellow-400 space-y-1">
                              <li>• Users can easily exit the loop when needed</li>
                              <li>• The loop provides clear value to users</li>
                              <li>• There are multiple conversation paths available</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                } else {
                  return (
                    <div key={index} className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                      <p className="text-sm text-red-800 dark:text-red-200">
                        {error.message}
                      </p>
                    </div>
                  );
                }
              })}
            </div>
          </div>
        )}

        {/* Success message if no errors */}
        {errors.length === 0 && (
          <div className="flex items-center gap-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />
            <div>
              <h3 className="text-lg font-semibold text-green-900 dark:text-green-100">
                All validation checks passed!
              </h3>
              <p className="text-sm text-green-700 dark:text-green-300">
                Your flow is ready to be published and used.
              </p>
            </div>
          </div>
        )}
      </div>
    </Dialog>
  );
};

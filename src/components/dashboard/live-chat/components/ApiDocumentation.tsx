'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export const ApiDocumentation: React.FC = () => {
  const [activeSection, setActiveSection] = useState<'format' | 'variables' | 'examples' | 'troubleshooting'>('format');

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'format':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  📤 Request Format
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-x-auto">
{`{
  "conversationId": "conv_123",
  "userId": "user_456",
  "message": "User's message",
  "variables": {
    "user_name": "<PERSON>",
    "email": "<EMAIL>",
    "previous_response": "API data"
  },
  "context": {
    "currentNode": "node_789",
    "flowId": "flow_abc",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}`}
                  </pre>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  📥 Response Format
                </h4>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  <pre className="text-xs text-gray-700 dark:text-gray-300 overflow-x-auto">
{`{
  "success": true,
  "data": {
    "message": "API response message",
    "variables": {
      "api_result": "success",
      "customer_id": "12345",
      "account_balance": 1500.00
    },
    "metadata": {
      "processing_time": 150,
      "api_version": "v2.1"
    }
  },
  "nextNode": "optional_next_node_id"
}`}
                  </pre>
                </div>
              </div>
            </div>

            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
                📋 Requirements & Best Practices
              </h4>
              <div className="text-xs text-blue-700 dark:text-blue-300 space-y-2">
                <div><strong>Headers:</strong> Content-Type: application/json is required</div>
                <div><strong>Timeout:</strong> APIs should respond within configured timeout (default: 30 seconds)</div>
                <div><strong>Error Handling:</strong> Return {`{"success": false, "error": "Error message"}`} for failures</div>
                <div><strong>Variables:</strong> Use the variables object to store data for use in subsequent nodes</div>
                <div><strong>Security:</strong> Always validate and sanitize input data</div>
                <div><strong>Rate Limiting:</strong> Implement appropriate rate limiting on your API endpoints</div>
              </div>
            </div>
          </div>
        );

      case 'variables':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                🔧 Variable Substitution System
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Use double curly braces {`{{variable_name}}`} to insert dynamic values from the conversation flow into your API requests.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  🏗️ System Variables
                </h5>
                <div className="space-y-2">
                  {[
                    { name: 'user_id', description: 'Current user ID' },
                    { name: 'conversation_id', description: 'Current conversation ID' },
                    { name: 'user_message', description: 'User\'s current message' },
                    { name: 'timestamp', description: 'Current timestamp (ISO 8601)' },
                    { name: 'flow_id', description: 'Current flow ID' },
                    { name: 'node_id', description: 'Current node ID' }
                  ].map((variable) => (
                    <div key={variable.name} className="p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                      <div className="font-mono text-xs text-purple-600 dark:text-purple-400">{`{{${variable.name}}}`}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">{variable.description}</div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                  📝 Usage Examples
                </h5>
                <div className="space-y-3">
                  <div>
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">URL with variables:</div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded font-mono text-xs">
                      https://api.example.com/users/{`{{user_id}}`}/profile
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Header with variables:</div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded font-mono text-xs">
                      Authorization: Bearer {`{{api_token}}`}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Request body with variables:</div>
                    <div className="bg-gray-50 dark:bg-gray-800 p-2 rounded">
                      <pre className="text-xs font-mono">
{`{
  "user_id": "{{user_id}}",
  "message": "{{user_message}}",
  "timestamp": "{{timestamp}}"
}`}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'examples':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                🎯 Common Use Case Examples
              </h4>
            </div>

            <div className="grid grid-cols-1 gap-6">
              {/* Customer Lookup Example */}
              <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h5 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3">
                  👤 Customer Lookup API
                </h5>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-2">Configuration:</div>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <div className="space-y-1 text-xs">
                        <div><strong>URL:</strong> https://api.crm.com/customers/{`{{user_id}}`}</div>
                        <div><strong>Method:</strong> GET</div>
                        <div><strong>Headers:</strong> Authorization: Bearer {`{{api_key}}`}</div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-blue-800 dark:text-blue-200 mb-2">Expected Response:</div>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <pre className="text-xs">
{`{
  "success": true,
  "data": {
    "variables": {
      "customer_name": "John Doe",
      "account_status": "active",
      "tier": "premium"
    }
  }
}`}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>

              {/* Order Creation Example */}
              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h5 className="text-sm font-medium text-green-900 dark:text-green-100 mb-3">
                  🛒 Order Creation API
                </h5>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-green-800 dark:text-green-200 mb-2">Configuration:</div>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <div className="space-y-1 text-xs">
                        <div><strong>URL:</strong> https://api.shop.com/orders</div>
                        <div><strong>Method:</strong> POST</div>
                        <div><strong>Body:</strong></div>
                        <pre className="text-xs mt-1">
{`{
  "customer_id": "{{user_id}}",
  "items": "{{selected_items}}",
  "total": "{{cart_total}}"
}`}
                        </pre>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-green-800 dark:text-green-200 mb-2">Expected Response:</div>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <pre className="text-xs">
{`{
  "success": true,
  "data": {
    "variables": {
      "order_id": "ORD-12345",
      "status": "confirmed",
      "tracking_number": "TRK789"
    }
  }
}`}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notification Example */}
              <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 rounded-lg">
                <h5 className="text-sm font-medium text-purple-900 dark:text-purple-100 mb-3">
                  📧 Send Notification API
                </h5>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-purple-800 dark:text-purple-200 mb-2">Configuration:</div>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <div className="space-y-1 text-xs">
                        <div><strong>URL:</strong> https://api.notify.com/send</div>
                        <div><strong>Method:</strong> POST</div>
                        <div><strong>Body:</strong></div>
                        <pre className="text-xs mt-1">
{`{
  "to": "{{user_email}}",
  "subject": "Order Update",
  "message": "{{notification_message}}"
}`}
                        </pre>
                      </div>
                    </div>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-purple-800 dark:text-purple-200 mb-2">Flow Integration:</div>
                    <div className="bg-white dark:bg-gray-800 p-3 rounded border text-xs">
                      <div className="space-y-1">
                        <div>• Use after order confirmation</div>
                        <div>• Pass order details to next node</div>
                        <div>• Handle success/failure paths</div>
                        <div>• Store notification status</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'troubleshooting':
        return (
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
                🔧 Troubleshooting Guide
              </h4>
            </div>

            <div className="space-y-4">
              {/* Common Issues */}
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <h5 className="text-sm font-medium text-red-900 dark:text-red-100 mb-3">
                  ❌ Common Issues & Solutions
                </h5>
                <div className="space-y-3">
                  {[
                    {
                      issue: "Variable not found error",
                      solution: "Check variable name spelling and ensure it's defined in the Variables tab",
                      example: "{{user_id}} → Verify 'user_id' exists in flow variables"
                    },
                    {
                      issue: "API timeout errors",
                      solution: "Increase timeout value or optimize your API endpoint performance",
                      example: "Default 30s → Try 60s for slow endpoints"
                    },
                    {
                      issue: "Authentication failures",
                      solution: "Verify API keys and tokens are correctly configured in headers",
                      example: "Authorization: Bearer {{api_token}} → Check token validity"
                    },
                    {
                      issue: "JSON parsing errors",
                      solution: "Ensure request body is valid JSON format with proper variable syntax",
                      example: "Use JSON validator and check variable placement"
                    }
                  ].map((item, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 p-3 rounded border">
                      <div className="text-xs font-medium text-red-800 dark:text-red-200 mb-1">{item.issue}</div>
                      <div className="text-xs text-red-700 dark:text-red-300 mb-1">{item.solution}</div>
                      <div className="text-xs text-red-600 dark:text-red-400 font-mono">{item.example}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Best Practices */}
              <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <h5 className="text-sm font-medium text-green-900 dark:text-green-100 mb-3">
                  ✅ Best Practices
                </h5>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs font-medium text-green-800 dark:text-green-200 mb-2">API Design:</div>
                    <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
                      <li>• Keep response times under 5 seconds</li>
                      <li>• Use consistent error response format</li>
                      <li>• Implement proper HTTP status codes</li>
                      <li>• Include meaningful error messages</li>
                      <li>• Support idempotent operations</li>
                    </ul>
                  </div>
                  <div>
                    <div className="text-xs font-medium text-green-800 dark:text-green-200 mb-2">Flow Integration:</div>
                    <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
                      <li>• Test APIs before adding to flow</li>
                      <li>• Handle both success and error cases</li>
                      <li>• Use descriptive variable names</li>
                      <li>• Document API dependencies</li>
                      <li>• Monitor API performance</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Testing Tips */}
              <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <h5 className="text-sm font-medium text-yellow-900 dark:text-yellow-100 mb-3">
                  🧪 Testing Tips
                </h5>
                <div className="text-xs text-yellow-700 dark:text-yellow-300 space-y-2">
                  <div><strong>1. Use Test Connection:</strong> Always test your API configuration before saving</div>
                  <div><strong>2. Mock Variables:</strong> Test with sample data to verify variable substitution</div>
                  <div><strong>3. Error Scenarios:</strong> Test how your API handles invalid inputs</div>
                  <div><strong>4. Rate Limits:</strong> Verify your API can handle expected traffic</div>
                  <div><strong>5. Flow Testing:</strong> Test the complete conversation flow end-to-end</div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          📋 API Integration Documentation
        </h3>
      </div>

      {/* Navigation Tabs */}
      <div className="flex flex-wrap gap-2 mb-6 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
        {[
          { key: 'format', label: '📋 Format', icon: '📋' },
          { key: 'variables', label: '🔧 Variables', icon: '🔧' },
          { key: 'examples', label: '🎯 Examples', icon: '🎯' },
          { key: 'troubleshooting', label: '🔧 Troubleshooting', icon: '🔧' }
        ].map((tab) => (
          <Button
            key={tab.key}
            onClick={() => setActiveSection(tab.key as any)}
            variant={activeSection === tab.key ? 'default' : 'outline'}
            size="sm"
            className={`text-xs ${
              activeSection === tab.key
                ? 'bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white border-0'
                : 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            {tab.label}
          </Button>
        ))}
      </div>

      {/* Content */}
      <div className="min-h-96">
        {renderSectionContent()}
      </div>
    </Card>
  );
};

'use client';

import React, { useRef, useState } from 'react';
import { Textarea } from '@/components/ui/Textarea';
import { Label } from '@/components/ui/Label';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Button } from '@/components/ui/Button';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { VariableReferenceSection } from './VariableReferenceSection';
import { BotResponseType, IFlowVariable, IResponseOption } from '@/types/bot';

// Component-specific constants
const MIN_BUTTON_MESSAGE_LENGTH = 1;
const MAX_BUTTON_MESSAGE_LENGTH = 1000;
const MAX_BUTTON_OPTIONS = 10;
const MAX_BUTTON_TEXT_LENGTH = 50;
const DEFAULT_PLACEHOLDER = "Enter the message that will appear above the buttons. Use {{variable_name}} to personalize...";
const HELP_TEXT = "This message appears before the button options. Use variables to personalize the button prompt.";

// Component-specific interfaces
interface ButtonMessageValidation {
  isValid: boolean;
  length: number;
  remaining: number;
  message: string;
}

/**
 * ButtonsNodeEditor Component
 * 
 * Self-contained component that handles buttons node editing including:
 * - Button message input with character count and validation
 * - Button option management (add, edit, remove, reorder)
 * - Button text and value configuration with validation
 * - Variable insertion with cursor positioning
 * - Variable storage configuration
 * - Button-specific guidance and validation
 * 
 * Encapsulates its own validation logic, button management, and state.
 */
export const ButtonsNodeEditor: React.FC = () => {
  const { currentNode, updateNode, variables } = useNodeEditorStore();

  // Internal state for UI concerns
  const [isFocused, setIsFocused] = useState(false);
  const [cursorPosition, setCursorPosition] = useState<number>(0);
  const [newButtonText, setNewButtonText] = useState('');
  const [newButtonValue, setNewButtonValue] = useState('');

  // Internal ref for textarea management
  const buttonMessageRef = useRef<HTMLTextAreaElement>(null);

  if (!currentNode || currentNode.type !== BotResponseType.BUTTONS) {
    return null;
  }

  // Internal validation function - encapsulated within component
  const validateButtonMessage = (message: string): ButtonMessageValidation => {
    const length = message.length;
    const isValidLength = length >= MIN_BUTTON_MESSAGE_LENGTH && length <= MAX_BUTTON_MESSAGE_LENGTH;
    const remaining = MAX_BUTTON_MESSAGE_LENGTH - length;

    let validationMessage = '';
    if (length === 0) {
      validationMessage = 'Button message is required';
    } else if (length < MIN_BUTTON_MESSAGE_LENGTH) {
      validationMessage = `Message must be at least ${MIN_BUTTON_MESSAGE_LENGTH} character`;
    } else if (length > MAX_BUTTON_MESSAGE_LENGTH) {
      validationMessage = `Message exceeds maximum length by ${Math.abs(remaining)} characters`;
    }

    return {
      isValid: isValidLength,
      length,
      remaining,
      message: validationMessage
    };
  };

  // Internal helper to validate button option
  const validateButtonOption = (text: string, value: string): { isValid: boolean; message: string } => {
    if (!text.trim()) {
      return { isValid: false, message: 'Button text is required' };
    }
    if (text.length > MAX_BUTTON_TEXT_LENGTH) {
      return { isValid: false, message: `Button text exceeds ${MAX_BUTTON_TEXT_LENGTH} characters` };
    }
    if (!value.trim()) {
      return { isValid: false, message: 'Button value is required' };
    }
    return { isValid: true, message: '' };
  };

  // Internal helper to get current button message
  const getCurrentButtonMessage = (): string => {
    return currentNode.content?.text || '';
  };

  // Internal helper to get current button options
  const getCurrentButtonOptions = (): IResponseOption[] => {
    return currentNode.content?.options || [];
  };

  // Internal helper to format character count display
  const formatCharacterCount = (length: number, remaining: number): string => {
    if (remaining < 0) {
      return `${length}/${MAX_BUTTON_MESSAGE_LENGTH} (${Math.abs(remaining)} over)`;
    }
    return `${length}/${MAX_BUTTON_MESSAGE_LENGTH}`;
  };

  // Internal helper to get character count color
  const getCharacterCountColor = (remaining: number): string => {
    if (remaining < 0) {
      return 'text-red-500 dark:text-red-400';
    } else if (remaining < 50) {
      return 'text-orange-500 dark:text-orange-400';
    }
    return 'text-gray-500 dark:text-gray-400';
  };

  // Internal event handlers
  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    updateNode({
      content: {
        ...currentNode.content,
        text: newMessage
      }
    });
  };

  const handleTextareaFocus = () => {
    setIsFocused(true);
  };

  const handleTextareaBlur = () => {
    setIsFocused(false);
  };

  const handleCursorPositionChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.target.selectionStart || 0);
  };

  const handleKeyUp = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    setCursorPosition(e.currentTarget.selectionStart || 0);
  };

  // Internal variable insertion with scroll preservation
  const handleVariableClick = (variableName: string) => {
    const textarea = buttonMessageRef.current;
    const currentMessage = getCurrentButtonMessage();
    
    if (textarea) {
      // Store current scroll position and cursor position
      const scrollTop = textarea.scrollTop;
      const selectionStart = textarea.selectionStart || 0;
      const selectionEnd = textarea.selectionEnd || 0;
      
      // Insert variable at cursor position
      const variableText = `{{${variableName}}}`;
      const newValue = currentMessage.substring(0, selectionStart) + variableText + currentMessage.substring(selectionEnd);
      
      // Update the node content
      updateNode({
        content: {
          ...currentNode.content,
          text: newValue
        }
      });
      
      // Restore focus and cursor position after React re-render
      setTimeout(() => {
        if (textarea) {
          textarea.focus();
          // Set cursor position after the inserted variable
          const newCursorPosition = selectionStart + variableText.length;
          textarea.setSelectionRange(newCursorPosition, newCursorPosition);
          // Restore scroll position to prevent jumping to bottom
          textarea.scrollTop = scrollTop;
          setCursorPosition(newCursorPosition);
        }
      }, 0);
    } else {
      // Fallback: append to end if no textarea ref
      const variableText = `{{${variableName}}}`;
      updateNode({
        content: {
          ...currentNode.content,
          text: currentMessage + variableText
        }
      });
    }
  };

  // Internal button option management
  const handleAddButton = () => {
    const validation = validateButtonOption(newButtonText, newButtonValue);
    if (!validation.isValid) return;

    const currentOptions = getCurrentButtonOptions();
    if (currentOptions.length >= MAX_BUTTON_OPTIONS) return;

    const newOption: IResponseOption = {
      id: crypto.randomUUID(),
      text: newButtonText.trim(),
      value: newButtonValue.trim()
    };

    updateNode({
      content: {
        ...currentNode.content,
        options: [...currentOptions, newOption]
      }
    });

    // Reset form
    setNewButtonText('');
    setNewButtonValue('');
  };

  const handleRemoveButton = (index: number) => {
    const currentOptions = getCurrentButtonOptions();
    const updatedOptions = currentOptions.filter((_, i) => i !== index);
    updateNode({
      content: {
        ...currentNode.content,
        options: updatedOptions
      }
    });
  };

  const handleUpdateButton = (index: number, text: string, value: string) => {
    const currentOptions = getCurrentButtonOptions();
    const updatedOptions = currentOptions.map((option, i) =>
      i === index ? { ...option, text, value } : option
    );
    updateNode({
      content: {
        ...currentNode.content,
        options: updatedOptions
      }
    });
  };

  const currentMessage = getCurrentButtonMessage();
  const messageValidation = validateButtonMessage(currentMessage);
  const currentOptions = getCurrentButtonOptions();
  const buttonValidation = validateButtonOption(newButtonText, newButtonValue);

  return (
    <div className="space-y-4">
      {/* Buttons Node Information */}
      <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-3 flex items-center">
          🔘 Button Options Configuration
        </h4>
        <p className="text-xs text-blue-700 dark:text-blue-300 mb-3">
          This node presents users with clickable button options. Configure the message and button choices below.
        </p>
        <div className="text-xs text-blue-600 dark:text-blue-400">
          User selections can be stored in variables for use in subsequent nodes.
        </div>
      </div>

      {/* Button Message Input */}
      <div>
        <div className="flex items-center justify-between mb-2">
          <Label htmlFor="buttonMessage" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Button Message
          </Label>
          <span className={`text-xs ${getCharacterCountColor(messageValidation.remaining)}`}>
            {formatCharacterCount(messageValidation.length, messageValidation.remaining)}
          </span>
        </div>
        
        <Textarea
          id="buttonMessage"
          ref={buttonMessageRef}
          value={currentMessage}
          onChange={handleMessageChange}
          onFocus={handleTextareaFocus}
          onBlur={handleTextareaBlur}
          onSelect={handleCursorPositionChange}
          onKeyUp={handleKeyUp}
          placeholder={DEFAULT_PLACEHOLDER}
          rows={3}
          className={`mt-1 ${
            !messageValidation.isValid
              ? 'border-red-300 dark:border-red-600 focus:border-red-500 dark:focus:border-red-500'
              : isFocused
                ? 'border-blue-300 dark:border-blue-600'
                : ''
          }`}
          maxLength={MAX_BUTTON_MESSAGE_LENGTH}
        />
        
        {!messageValidation.isValid && (
          <p className="text-xs text-red-500 dark:text-red-400 mt-1">
            {messageValidation.message}
          </p>
        )}
        
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {HELP_TEXT}
        </p>
      </div>

      {/* Available Variables Reference */}
      <VariableReferenceSection
        onVariableClick={handleVariableClick}
        title="Available Variables for Button Message:"
      />

      {/* Button Options Configuration */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Button Options ({currentOptions.length}/{MAX_BUTTON_OPTIONS})
          </h4>
        </div>

        {/* Add New Button */}
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Add New Button</h5>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="buttonText" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Button Text
              </Label>
              <Input
                id="buttonText"
                value={newButtonText}
                onChange={(e) => setNewButtonText(e.target.value)}
                placeholder="e.g., Yes, No, Maybe"
                className="mt-1"
                maxLength={MAX_BUTTON_TEXT_LENGTH}
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Text displayed on the button ({newButtonText.length}/{MAX_BUTTON_TEXT_LENGTH})
              </p>
            </div>

            <div>
              <Label htmlFor="buttonValue" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Button Value
              </Label>
              <Input
                id="buttonValue"
                value={newButtonValue}
                onChange={(e) => setNewButtonValue(e.target.value)}
                placeholder="e.g., yes, no, maybe"
                className="mt-1"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Value stored when button is clicked
              </p>
            </div>
          </div>

          <div className="flex items-center justify-between mt-4">
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {!buttonValidation.isValid && buttonValidation.message}
            </div>

            <Button
              onClick={handleAddButton}
              disabled={!buttonValidation.isValid || currentOptions.length >= MAX_BUTTON_OPTIONS}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Add Button
            </Button>
          </div>
        </div>

        {/* Current Buttons List */}
        {currentOptions.length > 0 && (
          <div className="space-y-2">
            <h5 className="text-sm font-medium text-gray-900 dark:text-white">Current Buttons</h5>
            {currentOptions.map((option, index) => (
              <div
                key={option.id}
                className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg"
              >
                <div className="flex-1 grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Text</Label>
                    <Input
                      value={option.text}
                      onChange={(e) => handleUpdateButton(index, e.target.value, option.value)}
                      className="mt-1 h-8 text-sm"
                      maxLength={MAX_BUTTON_TEXT_LENGTH}
                    />
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-600 dark:text-gray-400">Value</Label>
                    <Input
                      value={option.value}
                      onChange={(e) => handleUpdateButton(index, option.text, e.target.value)}
                      className="mt-1 h-8 text-sm"
                    />
                  </div>
                </div>
                <Button
                  onClick={() => handleRemoveButton(index)}
                  variant="outline"
                  size="sm"
                  className="ml-4 text-red-600 hover:text-red-700 border-red-300 hover:border-red-400"
                >
                  Remove
                </Button>
              </div>
            ))}
          </div>
        )}

        {currentOptions.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No button options configured yet.</p>
            <p className="text-xs mt-1">Add buttons above to give users choices.</p>
          </div>
        )}
      </div>

      {/* Variable Storage Configuration */}
      <div>
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
          Store Response In Variable
        </Label>
        <Select
          value={(currentNode.content?.storeInVariable as string) || ''}
          onChange={(value) => {
            const updatedContent = { ...currentNode.content };
            if (value === '' || value === null) {
              delete updatedContent.storeInVariable;
            } else {
              updatedContent.storeInVariable = value as string;
            }
            updateNode({ content: updatedContent });
          }}
          options={[
            { label: "Don't store in variable", value: '' },
            { label: 'customer_name (system)', value: 'customer_name' },
            { label: 'customer_email (system)', value: 'customer_email' },
            ...variables.map((variable: IFlowVariable) => ({
              label: `${variable.name} (${variable.type})`,
              value: variable.name
            }))
          ]}
          placeholder="Select variable to store button selection"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Choose a variable to store the user's button selection for later use in the conversation.
        </p>
      </div>

      {/* Development Analysis */}
      {process.env.NODE_ENV === 'development' && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-2">
            📊 Buttons Analysis (Dev Mode)
          </h5>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p><strong>Message Length:</strong> {messageValidation.length} characters</p>
            <p><strong>Remaining:</strong> {messageValidation.remaining} characters</p>
            <p><strong>Button Options:</strong> {currentOptions.length}/{MAX_BUTTON_OPTIONS}</p>
            <p><strong>Cursor Position:</strong> {cursorPosition}</p>
            <p><strong>Is Focused:</strong> {isFocused ? 'Yes' : 'No'}</p>
            <p><strong>Storage Variable:</strong> {currentNode.content?.storeInVariable || 'None'}</p>
          </div>
        </div>
      )}
    </div>
  );
};

import React, { useState, memo, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { CustomerInfoForm, CustomerInfo } from '@/components/ui/CustomerInfoForm';
import { useToast } from '@/components/ui/Toast';
import {
  initializeBotConversationWithFallback,
  ConversationState,
  BotResponse
} from '@/server/actions/bot-execution-actions';
import {ChatSimulator} from "@components/dashboard/live-chat/components/ChatSimulator";
import { useChatSimulatorStore } from '@/stores/chatSimulatorStore';

interface BotTestManagerProps {
  flowId: string;
  organizationId: string;
  hasUnsavedChanges: boolean;
  flowName?: string;
}

const BotTestManagerComponent: React.FC<BotTestManagerProps> = ({
  flowId,
  organizationId,
  hasUnsavedChanges,
  flowName
}) => {
  const { success, error: toastError } = useToast();

  // Get reset and verification functions from Zustand store
  const { reset: resetChatSimulator, verifyReset } = useChatSimulatorStore();

  // Dialog and flow state
  const [isOpen, setIsOpen] = useState(false);
  const [showCustomerForm, setShowCustomerForm] = useState(true);
  const [isInitializingConversation, setIsInitializingConversation] = useState(false);

  // Customer and conversation state
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [conversationState, setConversationState] = useState<ConversationState | null>(null);
  const [botResponse, setBotResponse] = useState<BotResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Open dialog and show customer info form - memoized to prevent re-renders
  const handleOpenDialog = useCallback(() => {
    if (hasUnsavedChanges) {
      return; // Button should be disabled, but extra safety
    }

    setIsOpen(true);
    setShowCustomerForm(true);
    setError(null);
  }, [hasUnsavedChanges]);

  // Handle customer information submission - memoized to prevent re-renders
  const handleCustomerInfoSubmit = useCallback(async (info: CustomerInfo) => {
    try {
      setIsInitializingConversation(true);
      setCustomerInfo(info);
      
      // Initialize bot conversation with fallback mechanism
      const result = await initializeBotConversationWithFallback(organizationId, info);

      if (result.success && result.data) {
        // Check if this is a ConversationState (normal bot) or BotResponse (fallback handoff)
        if ('_id' in result.data) {
          // Normal bot conversation
          setConversationState(result.data as ConversationState);
          setBotResponse(null);
        } else {
          // Fallback handoff or offline message
          const botResp = result.data as BotResponse;
          setBotResponse(botResp);
          setConversationState(null);

          if (botResp.handoffTriggered) {
            success(`Welcome ${info.name}! You're being connected to a live agent.`);
          } else if (botResp.offlineMessageShown) {
            success(`Welcome ${info.name}! We've shown you our offline message.`);
          }
        }

        setShowCustomerForm(false);
        setError(null);
      } else {
        setError(result.error || 'Failed to start conversation');
        toastError('Initialization failed', result.error || 'Failed to start conversation');
      }
    } catch (error: any) {
      console.error('Error initializing conversation with customer info:', error);
      setError(error.message || 'Failed to start conversation');
      toastError('Error', error.message || 'Failed to start conversation');
    } finally {
      setIsInitializingConversation(false);
    }
  }, [organizationId, success, toastError]);

  // Reset to customer form - memoized to prevent infinite re-renders
  const handleReset = useCallback(() => {
    // Reset all chat simulator state (pagination, forms, messages, etc.)
    resetChatSimulator();

    // Verify the reset was successful
    const resetSuccessful = verifyReset();
    if (resetSuccessful) {
      console.log('✅ Chat simulator reset verified successfully');
    } else {
      console.warn('⚠️ Chat simulator reset verification failed - some state may persist');
    }

    // Reset local component state
    setConversationState(null);
    setBotResponse(null);
    setCustomerInfo(null);
    setShowCustomerForm(true);
    setError(null);
  }, [resetChatSimulator, verifyReset]);

  // Memoize the flow status text to prevent infinite re-renders
  const flowStatusText = useMemo(() => {
    if (!flowName) return null;
    return showCustomerForm ? `Preparing to test: ${flowName}` : `Testing: ${flowName}`;
  }, [flowName, showCustomerForm]);

  // Close dialog - memoized to prevent re-renders
  const handleClose = useCallback(() => {
    // Reset all chat simulator state when closing modal
    resetChatSimulator();

    // Verify the reset was successful
    const resetSuccessful = verifyReset();
    if (resetSuccessful) {
      console.log('✅ Chat simulator reset on close verified successfully');
    } else {
      console.warn('⚠️ Chat simulator reset on close verification failed - some state may persist');
    }

    // Reset local component state
    setIsOpen(false);
    setConversationState(null);
    setBotResponse(null);
    setCustomerInfo(null);
    setShowCustomerForm(true);
    setError(null);
  }, [resetChatSimulator, verifyReset]);

  return (
    <>
      {/* Test Bot Button */}
      <Button
        onClick={handleOpenDialog}
        disabled={hasUnsavedChanges}
        className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border-0 shadow-lg"
        title={hasUnsavedChanges ? 'Save your flow first to test it' : 'Test your chat bot flow'}
      >
        ▶️ Test Bot
      </Button>

      {/* Main Dialog */}
      <Dialog
        showHeader={false}
        draggable
        open={isOpen}
        onClose={handleClose}
        className="max-w-md mx-auto"
      >
        <div className="bg-white/90 dark:bg-[#1e1e28]/90 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl shadow-2xl overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] p-4 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <h3 className="font-semibold">
                  {showCustomerForm ? 'Customer Information' : 'Chat Bot Test'}
                </h3>
              </div>
              <div className="flex items-center space-x-2">
                {!showCustomerForm && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={handleReset}
                    className="text-white hover:bg-white/20 p-1 text-xs"
                    title="Start over with new customer info"
                  >
                    🔄 Reset
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleClose}
                  className="text-white hover:bg-white/20 p-1"
                >
                  ✕
                </Button>
              </div>
            </div>
            {flowStatusText && (
              <p className="text-sm text-white/80 mt-1">
                {flowStatusText}
              </p>
            )}
          </div>

          {/* Content Area */}
          {showCustomerForm ? (
            /* Customer Information Collection */
            <div className="p-6 bg-gray-50/50 dark:bg-gray-900/50">
              <CustomerInfoForm
                onSubmit={handleCustomerInfoSubmit}
                isLoading={isInitializingConversation}
                title="Welcome! Let's get started"
                subtitle="Please provide your information to begin testing the bot"
                submitButtonText="Start Bot Test"
              />
            </div>
          ) : (
            /* Chat Interface */
            customerInfo && (conversationState || botResponse) && (
              <ChatSimulator
                conversationState={conversationState}
                botResponse={botResponse}
                customerInfo={customerInfo}
                organizationId={organizationId}
              />
            )
          )}

          {/* Error Display */}
          {error && (
            <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
              <p className="text-sm text-red-600 dark:text-red-400">⚠️ {error}</p>
            </div>
          )}
        </div>
      </Dialog>
    </>
  );
};

// Memoize the component to prevent unnecessary re-renders
export const BotTestManager = memo(BotTestManagerComponent);

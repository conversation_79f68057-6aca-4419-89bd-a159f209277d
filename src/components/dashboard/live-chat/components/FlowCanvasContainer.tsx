'use client';

import React from 'react';
import { IEnhancedFlowNode, IFlowConnection, BotResponseType } from '@/types/bot';
import { useNodeEditorStore } from '@/stores/nodeEditorStore';
import { FlowCanvas } from './FlowCanvas';
import { FlowCanvasModern } from './FlowCanvasModern';


interface FlowCanvasContainerProps {
  nodes: IEnhancedFlowNode[];
  connections: IFlowConnection[];
  selectedNode: IEnhancedFlowNode | null;
  selectedNodes: string[];
  canvasScale: number;
  canvasOffset: { x: number; y: number };
  isConnecting: boolean;
  connectionSource: string | null;
  onNodeSelect: (node: IEnhancedFlowNode) => void;
  onMultiNodeSelect: (nodeIds: string[]) => void;
  onNodeUpdate: (nodeId: string, updates: Partial<IEnhancedFlowNode>) => void;
  onMultiNodeUpdate: (updates: { nodeId: string; position: { x: number; y: number } }[]) => void;
  onNodeDelete: (nodeId: string) => void;
  onMultiNodeDelete: (nodeIds: string[]) => void;
  onConnectionCreate: (sourceNodeId: string, targetNodeId: string) => void;
  onConnectionDelete: (connectionId: string) => void;
  onStartConnection: (nodeId: string) => void;
  onEndConnection: (targetNodeId: string) => void;
  onCancelConnection: () => void;
  onScaleChange: (scale: number) => void;
  onOffsetChange: (offset: { x: number; y: number }) => void;
  onAddNode: (type: BotResponseType, position?: { x: number; y: number }) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

export const FlowCanvasContainer: React.FC<FlowCanvasContainerProps> = (props) => {
  const { flowCanvasDesign } = useNodeEditorStore();

  // Conditionally render the appropriate FlowCanvas
  return flowCanvasDesign === 'modern' ? (
    <FlowCanvasModern {...props} />
  ) : (
    <FlowCanvas {...props} />
  );
};

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useToast } from '@/components/ui/Toast';
import {
  validateBotFlowPrerequisites,
  setAgentOnlineStatus,
  getAgentStatus,
  getOnlineAgents,
  type FlowValidation,
  type AgentStatus
} from '@/server/actions/bot-first-chat-actions';
import { logger } from '@/utils/logger';

interface LiveChatTestStatusProps {
  organizationId: string;
}

export function LiveChatTestStatus({ organizationId }: LiveChatTestStatusProps) {
  const { success, error } = useToast();
  const [flowValidation, setFlowValidation] = useState<FlowValidation | null>(null);
  const [agentStatus, setAgentStatus] = useState<AgentStatus | null>(null);
  const [onlineAgents, setOnlineAgents] = useState<AgentStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runTests = async () => {
    setIsLoading(true);
    try {
      // Test 1: Validate flow prerequisites
      const flowResult = await validateBotFlowPrerequisites(organizationId);
      if (flowResult.success && flowResult.data) {
        setFlowValidation(flowResult.data);
      } else {
        const errorMessage = 'error' in flowResult ? flowResult.error : 'Unknown error';
        error('Flow validation failed', errorMessage);
      }

      // Test 2: Get current agent status
      const statusResult = await getAgentStatus(organizationId);
      if (statusResult.success && statusResult.data) {
        setAgentStatus(statusResult.data);
      }

      // Test 3: Get online agents
      const agentsResult = await getOnlineAgents(organizationId);
      if (agentsResult.success && agentsResult.data) {
        setOnlineAgents(agentsResult.data);
      }
      success('Tests completed! Check console for details.');

    } catch (err: any) {
      logger.error('❌ Test error:', err);
      error('Test failed', err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAgentStatus = async () => {
    try {
      const newStatus = !agentStatus?.isOnline;
      const result = await setAgentOnlineStatus(organizationId, newStatus);
      
      if (result.success && result.data) {
        setAgentStatus(result.data);
        
        // Refresh online agents list
        const agentsResult = await getOnlineAgents(organizationId);
        if (agentsResult.success && agentsResult.data) {
          setOnlineAgents(agentsResult.data);
        }
      } else {
        error('Failed to update status', result.error || 'Unknown error');
      }
    } catch (err: any) {
      error('Error updating status', err.message);
    }
  };

  useEffect(() => {
    runTests();
  }, [organizationId]);

  return (
    <div className="space-y-4">
      <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            🧪 Live Chat System Test Status
          </h3>
          <Button
            onClick={runTests}
            disabled={isLoading}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            {isLoading ? 'Testing...' : '🔄 Run Tests'}
          </Button>
        </div>

        {/* Flow Validation Status */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            🤖 Bot Flow Validation
          </h4>
          {flowValidation ? (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Active Bot:</span>
                  <span className={`ml-2 ${flowValidation.hasActiveBot ? 'text-green-600' : 'text-red-600'}`}>
                    {flowValidation.hasActiveBot ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Published Flow:</span>
                  <span className={`ml-2 ${flowValidation.hasPublishedFlow ? 'text-green-600' : 'text-red-600'}`}>
                    {flowValidation.hasPublishedFlow ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Available Agents:</span>
                  <span className={`ml-2 ${onlineAgents.length > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {onlineAgents.length > 0 ? '✅ Yes' : '❌ No'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Online Agents:</span>
                  <span className="ml-2 text-blue-600">
                    {onlineAgents.length}
                  </span>
                </div>
              </div>
              <div className="mt-2 pt-2 border-t border-blue-200 dark:border-blue-700">
                <span className="font-medium">Bot Name:</span>
                <span className="ml-2 text-blue-800 dark:text-blue-300">
                  {flowValidation.botName || 'N/A'}
                </span>
              </div>
            </div>
          ) : (
            <div className="text-gray-500">Loading flow validation...</div>
          )}
        </div>

        {/* Agent Status */}
        <div className="mb-6">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            👨‍💼 Agent Status
          </h4>
          <div className="flex items-center justify-between bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${agentStatus?.isOnline ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              <div>
                <div className="font-medium">
                  Status: {agentStatus?.isOnline ? 'Online' : 'Offline'}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {agentStatus?.isOnline
                    ? `Active conversations: ${agentStatus.conversationCount}`
                    : 'Not receiving new conversations'
                  }
                </div>
              </div>
            </div>
            <Button
              onClick={toggleAgentStatus}
              className={`${
                agentStatus?.isOnline
                  ? 'bg-red-500 hover:bg-red-600'
                  : 'bg-green-500 hover:bg-green-600'
              } text-white`}
            >
              {agentStatus?.isOnline ? '🔴 Go Offline' : '🟢 Go Online'}
            </Button>
          </div>
        </div>

        {/* Online Agents List */}
        <div>
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            🌐 Online Agents ({onlineAgents.length})
          </h4>
          {onlineAgents.length > 0 ? (
            <div className="space-y-2">
              {onlineAgents.map((agent, index) => (
                <div key={index} className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-green-900 dark:text-green-400">
                        {agent.userName}
                      </div>
                      <div className="text-sm text-green-700 dark:text-green-500">
                        Conversations: {agent.conversationCount}
                      </div>
                    </div>
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              No agents currently online
            </div>
          )}
        </div>

        {/* Test Summary */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <div className="font-medium mb-1">Test Results Summary:</div>
            <div>• Bot Flow: {flowValidation?.hasActiveBot && flowValidation?.hasPublishedFlow ? '✅ Ready' : '❌ Not Ready'}</div>
            <div>• Agent System: {agentStatus?.isOnline ? '✅ Online' : '⚠️ Offline'}</div>
            <div>• Overall Status: {
              flowValidation?.hasActiveBot && flowValidation?.hasPublishedFlow && onlineAgents.length > 0
                ? '✅ Fully Operational'
                : flowValidation?.hasActiveBot && flowValidation?.hasPublishedFlow
                ? '⚠️ Bot Ready, No Agents'
                : onlineAgents.length > 0
                ? '⚠️ Agents Ready, No Bot'
                : '❌ Not Ready'
            }</div>
          </div>
        </div>
      </Card>
    </div>
  );
}

'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { logger } from '@/utils/logger';
import { useNotificationSound } from '@/hooks';
import { useTypingIndicator } from '@/hooks/useTypingIndicator';
import { TypingIndicator } from '@/components/ui/TypingIndicator';
import { CreateTicketModal } from './CreateTicketModal';
import { getExistingTicketForConversation } from '@/server/actions/support';

// Import types from the main ConversationsTab file
import type { Conversation } from '../tabs/ConversationsTab';

interface ChatWindowProps {
  conversation: Conversation;
  onSendMessage: (content: string) => void;
  onCloseChat: (conversationId: string, reason?: string) => Promise<void>;
  organizationId: string;
  typingUsers?: Array<{
    userId: string;
    userType: 'agent' | 'customer';
    userName: string;
    timestamp: Date;
  }>;
  onTicketCreated?: (ticketData: any) => void;
}

export function ChatWindow({
  conversation,
  onSendMessage,
  onCloseChat,
  organizationId,
  typingUsers = [],
  onTicketCreated
}: ChatWindowProps) {
  const [message, setMessage] = useState('');
  const [isClosing, setIsClosing] = useState(false);
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  const [isUserAtBottom, setIsUserAtBottom] = useState(true);
  const [hasNewMessages, setHasNewMessages] = useState(false);
  const [newMessageCount, setNewMessageCount] = useState(0);
  const [showCreateTicketModal, setShowCreateTicketModal] = useState(false);
  const [existingTicket, setExistingTicket] = useState<{
    id: string;
    title: string;
    status: string;
    priority: string;
    createdAt: Date;
  } | null>(null);
  const [checkingTicket, setCheckingTicket] = useState(false);

  // Typing indicator for agent
  const {
    handleTyping,
    onMessageSent
  } = useTypingIndicator({
    conversationId: conversation.id,
    organizationId,
    userType: 'agent',
    userName: 'Agent', // This could be passed from props if needed
    enabled: conversation.status === 'active'
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const chatWindowRef = useRef<HTMLDivElement>(null);
  const newMessageButtonRef = useRef<HTMLDivElement>(null);
  const typingIndicatorRef = useRef<HTMLDivElement>(null);

  // Track previous message count to detect new messages and customer messages
  const previousMessageCountRef = useRef<number>(conversation.messages.length);
  const previousCustomerMessageCountRef = useRef<number>(
    conversation.messages.filter(msg => msg.type === 'customer').length
  );
  // Track message count when user scrolled up (for accurate counting)
  const messageCountWhenScrolledUpRef = useRef<number>(0);

  // Initialize notification sound for incoming customer messages
  const { playNotification } = useNotificationSound({
    volume: 0.7,
    enabled: true,
    debounceMs: 2000 // Prevent rapid-fire notifications for agents
  });

  // Check if user is at the bottom of the messages container
  const checkIfUserAtBottom = useCallback(() => {
    if (!messagesContainerRef.current) return true;

    const container = messagesContainerRef.current;
    const threshold = 100; // pixels from bottom to consider "at bottom"
    const isAtBottom = container.scrollHeight - container.scrollTop - container.clientHeight <= threshold;

    return isAtBottom;
  }, []);

  // Scroll to bottom function - contained within messages container only
  const scrollToBottom = useCallback((behavior: 'smooth' | 'auto' = 'smooth') => {
    const container = messagesContainerRef.current;
    if (!container) return;

    // Use scrollTo on the container instead of scrollIntoView to prevent page body scroll
    try {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: behavior
      });
    } catch (error) {
      // Fallback for browsers that don't support smooth scrolling
      container.scrollTop = container.scrollHeight;
    }

    setHasNewMessages(false);
    setNewMessageCount(0);
    setIsUserAtBottom(true);
    // Reset the reference point when user returns to bottom
    messageCountWhenScrolledUpRef.current = 0;
  }, []);

  // Function to position the "New messages" button at the center of the chat window
  const positionNewMessageButton = useCallback(() => {
    if (!chatWindowRef.current || !newMessageButtonRef.current) return;

    const chatWindow = chatWindowRef.current;
    const button = newMessageButtonRef.current;

    // Get chat window dimensions and position
    const chatRect = chatWindow.getBoundingClientRect();

    // Calculate the center position relative to the chat window
    const centerX = chatRect.left + (chatRect.width / 2);

    // Position the button at the calculated center
    button.style.position = 'fixed';
    button.style.left = `${centerX}px`;
    button.style.transform = 'translateX(-50%)';
    button.style.bottom = '160px'; // Adjust as needed for spacing from input
    button.style.zIndex = '50';
  }, []);

  // Function to position the typing indicator at the bottom left of the chat window
  const positionTypingIndicator = useCallback(() => {
    if (!chatWindowRef.current || !typingIndicatorRef.current) return;

    const chatWindow = chatWindowRef.current;
    const indicator = typingIndicatorRef.current;

    // Get chat window dimensions and position
    const chatRect = chatWindow.getBoundingClientRect();

    // Position the typing indicator at bottom left with some padding
    indicator.style.position = 'fixed';
    indicator.style.left = `${chatRect.left + 16}px`; // 16px padding from left
    indicator.style.bottom = '100px'; // Position above the input area
    indicator.style.zIndex = '40';
  }, []);

  // Position button on mount and when window resizes
  useEffect(() => {
    positionNewMessageButton();
    positionTypingIndicator();

    const handleResize = () => {
      positionNewMessageButton();
      positionTypingIndicator();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [positionNewMessageButton, positionTypingIndicator]);

  // Reposition button when it becomes visible
  useEffect(() => {
    if (hasNewMessages && !isUserAtBottom && newMessageCount > 0) {
      // Small delay to ensure DOM is updated
      setTimeout(positionNewMessageButton, 10);
    }
  }, [hasNewMessages, isUserAtBottom, newMessageCount, positionNewMessageButton]);

  // Reposition typing indicator when it becomes visible
  useEffect(() => {
    const customerTypingUsers = typingUsers.filter(user => user.userType === 'customer');
    if (customerTypingUsers.length > 0) {
      // Small delay to ensure DOM is updated
      setTimeout(positionTypingIndicator, 10);
    }
  }, [typingUsers, positionTypingIndicator]);

  // Handle scroll events to track user position
  const handleScroll = useCallback(() => {
    const isAtBottom = checkIfUserAtBottom();
    const wasAtBottom = isUserAtBottom;
    setIsUserAtBottom(isAtBottom);

    // If user just scrolled up from bottom, record current message count
    if (wasAtBottom && !isAtBottom) {
      messageCountWhenScrolledUpRef.current = conversation.messages.length;
      setNewMessageCount(0); // Reset count when user first scrolls up
    }

    // Clear new message indicator if user scrolls to bottom
    if (isAtBottom && hasNewMessages) {
      setHasNewMessages(false);
      setNewMessageCount(0);
      messageCountWhenScrolledUpRef.current = 0;
    }
  }, [checkIfUserAtBottom, hasNewMessages, isUserAtBottom, conversation.messages.length]);

  // Smart auto-scroll: only scroll if user is at bottom or on initial load
  useEffect(() => {
    const currentMessageCount = conversation.messages.length;
    const previousMessageCount = previousMessageCountRef.current;

    // On initial load or when user is at bottom, auto-scroll
    if (previousMessageCount === 0 || isUserAtBottom) {
      scrollToBottom('smooth');
    } else if (currentMessageCount > previousMessageCount) {
      // New messages arrived while user is scrolled up - show indicator
      setHasNewMessages(true);

      // Calculate accurate new message count since user scrolled up
      if (messageCountWhenScrolledUpRef.current > 0) {
        const newCount = currentMessageCount - messageCountWhenScrolledUpRef.current;
        setNewMessageCount(Math.max(0, newCount));
      } else {
        // If no reference point, just count the difference
        setNewMessageCount(currentMessageCount - previousMessageCount);
      }
    }

    // Update message count ref
    previousMessageCountRef.current = currentMessageCount;
  }, [conversation.messages, isUserAtBottom, scrollToBottom]);

  // Add scroll event listener to messages container
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    container.addEventListener('scroll', handleScroll);

    return () => {
      container.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // Detect new customer messages and play notification sound
  useEffect(() => {
    const currentCustomerMessages = conversation.messages.filter(msg => msg.type === 'customer');
    const currentCustomerMessageCount = currentCustomerMessages.length;
    const previousCustomerMessageCount = previousCustomerMessageCountRef.current;

    // Check if there are new customer messages (but not on initial load)
    if (currentCustomerMessageCount > previousCustomerMessageCount && previousCustomerMessageCount > 0) {
      // Get the new customer messages
      const newCustomerMessages = currentCustomerMessages.slice(previousCustomerMessageCount);

      if (newCustomerMessages.length > 0) {
        playNotification();
      }
    }

    // Update the ref for next comparison
    previousCustomerMessageCountRef.current = currentCustomerMessageCount;
  }, [conversation.messages, playNotification]);

  // Check for existing ticket when conversation changes
  useEffect(() => {
    const checkExistingTicket = async () => {
      if (!conversation?.id || !organizationId) return;

      setCheckingTicket(true);
      try {
        const result = await getExistingTicketForConversation(conversation.id, organizationId);
        if (result.success && result.data) {
          setExistingTicket(result.data);
        } else {
          setExistingTicket(null);
        }
      } catch (error) {
        console.error('Error checking for existing ticket:', error);
        setExistingTicket(null);
      } finally {
        setCheckingTicket(false);
      }
    };

    checkExistingTicket();
  }, [conversation?.id, organizationId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSendMessage(message.trim());
      setMessage('');
      onMessageSent(); // Clear typing indicator when message is sent
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(e.target.value);
    handleTyping(); // Trigger typing indicator
  };

  const handleCloseChat = async () => {
    setIsClosing(true);
    try {
      await onCloseChat(conversation.id);
      setShowCloseConfirm(false);
    } catch (error) {
      logger.error('Error closing chat:', error);
    } finally {
      setIsClosing(false);
    }
  };

  const formatMessageTime = (date: Date): string => {
    return new Date(date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'waiting': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'resolved': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <div ref={chatWindowRef} className="h-full flex flex-col">
      {/* Add CSS animations */}
      <style dangerouslySetInnerHTML={{
        __html: `
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
          }
        `
      }} />

      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {conversation.customerName}
            </h3>
            {conversation.customerEmail && (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {conversation.customerEmail}
              </p>
            )}
          </div>
          <div className="flex items-center gap-3">
            <Badge className={getStatusColor(conversation.status)}>
              {conversation.status}
            </Badge>
            {conversation.status === 'active' && (
              <div className="flex items-center gap-2">
                {checkingTicket ? (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="w-4 h-4 border-2 border-gray-300 border-t-[#8178E8] rounded-full animate-spin" />
                    Checking...
                  </div>
                ) : existingTicket ? (
                  <div className="flex items-center gap-2">
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Ticket Created
                    </Badge>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      <div className="font-medium">#{existingTicket.id.slice(-6)}</div>
                      <div className="text-xs">{existingTicket.status}</div>
                    </div>
                  </div>
                ) : (
                  <Button
                    onClick={() => setShowCreateTicketModal(true)}
                    size="sm"
                    variant="outline"
                    className="text-[#8178E8] border-[#8178E8] hover:bg-[#8178E8]/10 hover:border-[#6964D3]"
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
                    </svg>
                    Create Ticket
                  </Button>
                )}
              </div>
            )}
            {conversation.status === 'active' && (
              <div className="relative">
                {!showCloseConfirm ? (
                  <Button
                    onClick={() => setShowCloseConfirm(true)}
                    size="sm"
                    variant="outline"
                    className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900/20"
                  >
                    <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Close Chat
                  </Button>
                ) : (
                  <div className="flex items-center gap-2 p-2 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                    <span className="text-sm text-red-700 dark:text-red-300">Close chat?</span>
                    <Button
                      onClick={handleCloseChat}
                      disabled={isClosing}
                      size="sm"
                      className="bg-red-600 hover:bg-red-700 text-white text-xs px-2 py-1"
                    >
                      {isClosing ? (
                        <div className="flex items-center gap-1">
                          <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Closing...
                        </div>
                      ) : (
                        'Yes'
                      )}
                    </Button>
                    <Button
                      onClick={() => setShowCloseConfirm(false)}
                      disabled={isClosing}
                      size="sm"
                      variant="outline"
                      className="text-xs px-2 py-1"
                    >
                      Cancel
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto p-4 pb-20 space-y-4 relative"
        onScroll={handleScroll}
        style={{
          scrollBehavior: 'smooth',
          overscrollBehavior: 'contain', // Prevent scroll chaining to parent
          isolation: 'isolate' // Create new stacking context
        }}
      >
        {/* New messages indicator - JavaScript positioned at center of chat window */}
        {hasNewMessages && !isUserAtBottom && newMessageCount > 0 && (
          <div
            ref={newMessageButtonRef}
            style={{
              animation: 'fadeIn 0.3s ease-in-out'
            }}
          >
            <button
              onClick={() => scrollToBottom('smooth')}
              className="bg-[#8178E8] text-white px-4 py-2 rounded-full shadow-lg hover:bg-[#6964D3] hover:shadow-xl transition-all duration-200 flex items-center gap-2 text-sm"
              title="Scroll to latest messages"
              style={{
                animation: 'pulse 2s infinite'
              }}
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
              New messages
              <span className="bg-white text-[#8178E8] text-xs px-2 py-1 rounded-full ml-1 font-medium">
                {newMessageCount}
              </span>
            </button>
          </div>
        )}

        {/* Messages are already sorted by backend in chronological order (oldest first) */}
        {conversation.messages.map((msg, index) => (
            <div
              key={`${msg.id}-${index}`}
              className={`flex ${msg.type === 'customer' ? 'justify-start' : 'justify-end'}`}
            >
              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  msg.type === 'customer'
                    ? 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
                    : 'bg-[#8178E8] text-white'
                }`}
              >
                <p className="text-sm">{msg.content}</p>
                <p className={`text-xs mt-1 ${
                  msg.type === 'customer'
                    ? 'text-gray-500 dark:text-gray-400'
                    : 'text-white/70'
                }`}>
                  {msg.senderName} • {formatMessageTime(msg.sentAt)}
                </p>
              </div>
            </div>
          ))}

        {/* Invisible element to scroll to */}
        <div ref={messagesEndRef} />
      </div>

      {/* JavaScript-positioned Typing Indicator - positioned like new messages button */}
      {(() => {
        const customerTypingUsers = typingUsers.filter(user => user.userType === 'customer');



        return customerTypingUsers.length > 0 && (
          <div
            ref={typingIndicatorRef}
            style={{
              animation: 'fadeIn 0.3s ease-in-out'
            }}
          >
            <TypingIndicator
              typingUsers={customerTypingUsers}
              className="max-w-xs lg:max-w-md shadow-lg"
            />
          </div>
        );
      })()}

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        {conversation.status === 'closed' ? (
          <div className="text-center py-4">
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              ✅ This conversation has been closed. No further messages can be sent.
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex space-x-2">
            <Input
              value={message}
              onChange={handleInputChange}
              placeholder="Type your message..."
              className="flex-1"
              disabled={isClosing}
            />
            <Button type="submit" disabled={!message.trim() || isClosing}>
              Send
            </Button>
          </form>
        )}
      </div>

      {/* Create Ticket Modal */}
      <CreateTicketModal
        isOpen={showCreateTicketModal}
        onClose={() => setShowCreateTicketModal(false)}
        conversationId={conversation.id}
        organizationId={organizationId}
        customerInfo={{
          name: conversation.customerName,
          email: conversation.customerEmail
        }}
        chatHistory={conversation.messages}
        onTicketCreated={(ticketData) => {
          onTicketCreated?.(ticketData);
          setShowCreateTicketModal(false);
          // Update existing ticket state to reflect the newly created ticket
          setExistingTicket({
            id: ticketData.id,
            title: ticketData.title,
            status: ticketData.status,
            priority: ticketData.priority,
            createdAt: ticketData.createdAt
          });
        }}
      />
    </div>
  );
}

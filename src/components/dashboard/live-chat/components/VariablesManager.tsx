'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Select } from '@/components/ui/Select';
import { Switch } from '@/components/ui/Switch';
import { IFlowVariable } from '@/types/bot';

interface VariablesManagerProps {
  variables: IFlowVariable[];
  onVariablesChange: (variables: IFlowVariable[]) => void;
}

// Reserved system variable names
const RESERVED_VARIABLE_NAMES = ['customer_name', 'customer_email'];

// Variable validation functions
const validateVariableName = (name: string): { isValid: boolean; error?: string } => {
  // Check if empty
  if (!name.trim()) {
    return { isValid: false, error: 'Variable name is required' };
  }

  // Check for reserved names
  if (RESERVED_VARIABLE_NAMES.includes(name.toLowerCase())) {
    return { isValid: false, error: `Variable name '${name}' is reserved for system use` };
  }

  // Check naming convention: only lowercase letters, numbers, underscores, must start with letter
  const nameRegex = /^[a-z][a-z0-9_]*$/;
  if (!nameRegex.test(name)) {
    if (!/^[a-z]/.test(name)) {
      return { isValid: false, error: 'Variable names must start with a letter' };
    }
    return { isValid: false, error: 'Variable names can only contain lowercase letters, numbers, and underscores' };
  }

  return { isValid: true };
};

const checkDuplicateName = (name: string, variables: IFlowVariable[], currentIndex?: number): boolean => {
  return variables.some((variable, index) =>
    variable.name === name && index !== currentIndex
  );
};

export const VariablesManager: React.FC<VariablesManagerProps> = ({
  variables,
  onVariablesChange
}) => {
  const [validationErrors, setValidationErrors] = useState<Record<number, string>>({});
  const addVariable = (template?: Partial<IFlowVariable>) => {
    // Generate a valid default name
    let defaultName = template?.name;
    if (!defaultName) {
      let counter = variables.length + 1;
      do {
        defaultName = `variable_${counter}`;
        counter++;
      } while (checkDuplicateName(defaultName, variables));
    }

    const newVariable: IFlowVariable = {
      name: defaultName,
      type: template?.type || 'string',
      defaultValue: template?.defaultValue || '',
      required: template?.required || false
    };

    // Validate the new variable name
    const validation = validateVariableName(newVariable.name);
    if (!validation.isValid) {
      // Set validation error for the new variable
      const newIndex = variables.length;
      setValidationErrors(prev => ({
        ...prev,
        [newIndex]: validation.error!
      }));
    }

    onVariablesChange([...variables, newVariable]);
  };

  const updateVariable = (index: number, updates: Partial<IFlowVariable>) => {
    const updatedVariables = [...variables];
    updatedVariables[index] = { ...updatedVariables[index], ...updates };

    // If name is being updated, validate it
    if (updates.name !== undefined) {
      const newName = updates.name;
      const validation = validateVariableName(newName);
      const isDuplicate = checkDuplicateName(newName, updatedVariables, index);

      let errorMessage = '';
      if (!validation.isValid) {
        errorMessage = validation.error!;
      } else if (isDuplicate) {
        errorMessage = `Variable name '${newName}' already exists`;
      }

      // Update validation errors
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        if (errorMessage) {
          newErrors[index] = errorMessage;
        } else {
          delete newErrors[index];
        }
        return newErrors;
      });
    }

    onVariablesChange(updatedVariables);
  };

  const deleteVariable = (index: number) => {
    const updatedVariables = variables.filter((_, i) => i !== index);

    // Clean up validation errors - shift indices down for remaining variables
    setValidationErrors(prev => {
      const newErrors: Record<number, string> = {};
      Object.entries(prev).forEach(([key, value]) => {
        const errorIndex = parseInt(key);
        if (errorIndex < index) {
          // Keep errors for variables before the deleted one
          newErrors[errorIndex] = value;
        } else if (errorIndex > index) {
          // Shift down errors for variables after the deleted one
          newErrors[errorIndex - 1] = value;
        }
        // Skip the error for the deleted variable (errorIndex === index)
      });
      return newErrors;
    });

    onVariablesChange(updatedVariables);
  };

  const createFirstVariable = () => {
    addVariable({
      name: 'user_name',
      type: 'string',
      defaultValue: '',
      required: true
    });
  };

  return (
    <Card className="p-6 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl transition-all duration-300 ease-in-out">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              📊 Flow Variables
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Define variables to store and use data throughout your conversation flow
            </p>
            <div className="mt-2 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded">
              <p className="text-xs text-green-800 dark:text-green-200">
                <strong>System Variables:</strong> <code>customer_name</code> and <code>customer_email</code> are automatically available and populated with customer information collected before chat initiation.
              </p>
            </div>
          </div>
          <Button
            onClick={() => addVariable()}
            className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
          >
            ➕ Add Variable
          </Button>
        </div>

        {variables.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Variables Defined
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Variables can store user input and be used throughout the conversation
            </p>
            <Button
              onClick={createFirstVariable}
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0"
            >
              Create First Variable
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {variables.map((variable, index) => (
              <Card key={index} className="p-4 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl hover:shadow-lg transition-all duration-300 ease-in-out">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label className="text-xs text-gray-600 dark:text-gray-400">Name</Label>
                    <Input
                      value={variable.name}
                      onChange={(e) => updateVariable(index, { name: e.target.value })}
                      className={`mt-1 ${validationErrors[index] ? 'border-red-500 focus:border-red-500' : ''}`}
                      placeholder="variable_name"
                    />
                    {validationErrors[index] && (
                      <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                        {validationErrors[index]}
                      </p>
                    )}
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600 dark:text-gray-400">Type</Label>
                    <Select
                      value={variable.type}
                      onChange={(value) => updateVariable(index, { type: value as any })}
                      options={[
                        { label: 'String', value: 'string' },
                        { label: 'Number', value: 'number' },
                        { label: 'Boolean', value: 'boolean' },
                        { label: 'Array', value: 'array' },
                        { label: 'Object', value: 'object' }
                      ]}
                      placeholder="Select type"
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600 dark:text-gray-400">Default Value</Label>
                    <Input
                      value={variable.defaultValue || ''}
                      onChange={(e) => updateVariable(index, { defaultValue: e.target.value })}
                      className="mt-1"
                      placeholder="Default value"
                    />
                  </div>
                  <div className="flex items-end space-x-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={variable.required}
                        onChange={(e) => updateVariable(index, { required: e.value })}
                      />
                      <Label className="text-xs text-gray-600 dark:text-gray-400">Required</Label>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteVariable(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      🗑️
                    </Button>
                  </div>
                </div>

                {/* Variable Usage Examples */}
                <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <h4 className="text-xs font-medium text-blue-900 dark:text-blue-100 mb-2">
                    💡 Usage Examples
                  </h4>
                  <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
                    <div><strong>In messages:</strong> {`{{${variable.name}}}`}</div>
                    <div><strong>In conditions:</strong> {`${variable.name} === "value"`}</div>
                    <div><strong>In API calls:</strong> Include in request payload</div>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Variable Documentation */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            📋 Variable Reference
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                Available Variables
              </h4>
              <div className="space-y-1">
                {/* System Variables */}
                {RESERVED_VARIABLE_NAMES.map((sysVar) => (
                  <div key={sysVar} className="text-xs text-gray-600 dark:text-gray-400">
                    <code className="bg-green-200 dark:bg-green-700 px-1 rounded">
                      {sysVar}
                    </code>
                    <span className="ml-2 text-green-600 dark:text-green-400">(system)</span>
                  </div>
                ))}

                {/* Flow Variables */}
                {variables.map((variable, index) => (
                  <div key={index} className="text-xs text-gray-600 dark:text-gray-400">
                    <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">
                      {variable.name}
                    </code>
                    <span className="ml-2">({variable.type})</span>
                    {variable.required && (
                      <span className="ml-1 text-red-500">*</span>
                    )}
                    {validationErrors[index] && (
                      <span className="ml-1 text-red-500">⚠️</span>
                    )}
                  </div>
                ))}

                {variables.length === 0 && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Only system variables are currently available. Add flow variables to extend functionality.
                  </p>
                )}
              </div>
            </div>
              <div>
                <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Naming Rules
                </h4>
                <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400">
                  <div>• Must start with a letter (a-z)</div>
                  <div>• Only lowercase letters, numbers, and underscores</div>
                  <div>• No spaces, hyphens, or special characters</div>
                  <div>• Cannot use reserved names: <code className="bg-red-100 dark:bg-red-900 px-1 rounded text-red-600 dark:text-red-400">customer_name</code>, <code className="bg-red-100 dark:bg-red-900 px-1 rounded text-red-600 dark:text-red-400">customer_email</code></div>
                </div>

                <h4 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2 mt-4">
                  Variable Types
                </h4>
                <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                  <div><strong>String:</strong> Text values (names, emails)</div>
                  <div><strong>Number:</strong> Numeric values (age, quantity)</div>
                  <div><strong>Boolean:</strong> True/false values</div>
                  <div><strong>Array:</strong> Lists of values</div>
                  <div><strong>Object:</strong> Complex data structures</div>
                </div>
              </div>
            </div>
          </div>
        {/* Quick Templates */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            🚀 Quick Templates
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => addVariable({
                name: 'user_phone',
                type: 'string',
                defaultValue: '',
                required: false
              })}
              className="text-left justify-start"
            >
              📱 Phone Variable
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => addVariable({
                name: 'order_total',
                type: 'number',
                defaultValue: '0',
                required: false
              })}
              className="text-left justify-start"
            >
              💰 Order Total
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => addVariable({
                name: 'inquiry_type',
                type: 'string',
                defaultValue: '',
                required: false
              })}
              className="text-left justify-start"
            >
              📝 Inquiry Type
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import { Textarea } from '@/components/ui/Textarea';
import { ColorPicker } from '@/components/ui/ColorPicker';
import { Checkbox } from '@/components/ui/Checkbox';
import { initializeLiveChat, completeLiveChatSetup } from '@/server/actions/live-chat-actions';

interface LiveChatSetupWizardProps {
  organizationId: string;
  existingConfig?: any;
}

export function LiveChatSetupWizard({ organizationId, existingConfig }: LiveChatSetupWizardProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(existingConfig ? 2 : 1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Step 1: Basic Setup
  const [basicSetup, setBasicSetup] = useState({
    companyName: '',
    welcomeMessage: 'Hi! How can we help you today?'
  });

  // Step 2: Branding & Settings
  const [branding, setBranding] = useState({
    primaryColor: '#8178E8',
    secondaryColor: '#6964D3',
    accentColor: '#9333EA',
    backgroundColor: '#FFFFFF',
    textColor: '#1F2937',
    fontFamily: 'Inter, sans-serif',
    logoUrl: ''
  });

  const [settings, setSettings] = useState({
    welcomeMessage: 'Hi! How can we help you today?',
    offlineMessage: 'We\'re currently offline. Please leave a message and we\'ll get back to you.',
    placeholderText: 'Type your message...',
    enableFileUpload: true,
    enableEmojis: true,
    enableTypingIndicator: true, // Fixed typo: was "showTypeingIndicator"
    autoAssignment: true,
    requireCustomerEmail: false, // Default to optional for backward compatibility
    businessHours: {
      enabled: false,
      timezone: 'UTC',
      schedule: {
        monday: { enabled: true, start: '09:00', end: '17:00' },
        tuesday: { enabled: true, start: '09:00', end: '17:00' },
        wednesday: { enabled: true, start: '09:00', end: '17:00' },
        thursday: { enabled: true, start: '09:00', end: '17:00' },
        friday: { enabled: true, start: '09:00', end: '17:00' },
        saturday: { enabled: false, start: '09:00', end: '17:00' },
        sunday: { enabled: false, start: '09:00', end: '17:00' },
      },
    },
    responseTime: {
      target: 5, // minutes
      autoResponse: {
        enabled: false,
        message: 'Thanks for your message! We\'ll respond shortly.',
        delay: 30, // seconds
      },
    },
  });

  // Widget Configuration
  const [widget, setWidget] = useState({
    position: 'bottom-right' as 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left',
    size: 'medium' as 'small' | 'medium' | 'large',
    theme: 'light' as 'light' | 'dark' | 'auto',
    showAgentPhotos: true,
    showOnlineStatus: true,
    enableSounds: true,
  });

  // Analytics & Integration
  const [analytics, setAnalytics] = useState({
    enabled: true,
    trackVisitorInfo: true,
    trackPageViews: false,
    retentionDays: 90,
  });

  const [integration, setIntegration] = useState({
    allowedDomains: [] as string[],
    webhookUrl: '',
    customCSS: '',
    customJS: '',
  });

  const handleStep1Submit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const result = await initializeLiveChat(organizationId, basicSetup);

      if (result.success) {
        setCurrentStep(2);
      } else {
        setError('error' in result ? result.error : 'Failed to initialize live chat');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStep2Submit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const result = await completeLiveChatSetup(organizationId, {
        branding: {
          ...branding,
          companyName: basicSetup.companyName
        },
        settings,
        widget,
        analytics,
        integration,
        enableLiveChat: true
      });

      if (result.success) {
        router.push(`/dashboard/organization/${organizationId}/live-chat`);
      } else {
        setError('error' in result ? result.error : 'Failed to complete live chat setup');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Generate simplified widget configuration for preview (client-side only)
  const getWidgetConfig = () => {
    return {
      appId: 'demo-app-id', // This will be replaced with actual appId after setup
      apiKey: 'demo-api-key', // This will be replaced with actual API key after setup
      enableSounds: widget.enableSounds,
      showAgentPhotos: widget.showAgentPhotos,
      showOnlineStatus: widget.showOnlineStatus,
      welcomeMessage: settings.welcomeMessage,
      offlineMessage: settings.offlineMessage,
      placeholderText: settings.placeholderText
    };
  };



  if (currentStep === 1) {
    return (
      <Card className="max-w-2xl mx-auto p-6">
        <div className="space-y-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Initialize Live Chat
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Let's get started with your live chat setup
            </p>
          </div>

          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <p className="text-red-800 dark:text-red-200 text-sm">{error}</p>
            </div>
          )}

          <form onSubmit={handleStep1Submit} className="space-y-4">
            <div>
              <Label htmlFor="companyName">Company Name</Label>
              <Input
                id="companyName"
                value={basicSetup.companyName}
                onChange={(e) => setBasicSetup(prev => ({ ...prev, companyName: e.target.value }))}
                placeholder="Enter your company name"
                required
              />
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                This will be displayed in your live chat widget
              </p>
            </div>

            <div>
              <Label htmlFor="welcomeMessage">Welcome Message</Label>
              <Textarea
                id="welcomeMessage"
                value={basicSetup.welcomeMessage}
                onChange={(e) => setBasicSetup(prev => ({ ...prev, welcomeMessage: e.target.value }))}
                placeholder="Hi! How can we help you today?"
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isLoading || !basicSetup.companyName}
              >
                {isLoading ? 'Initializing...' : 'Continue'}
              </Button>
            </div>
          </form>
        </div>
      </Card>
    );
  }

  return (
    <Card className="max-w-4xl mx-auto p-6">
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Customize Your Live Chat
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Configure the appearance and behavior of your live chat widget
          </p>
        </div>

        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <p className="text-red-800 dark:text-red-200 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleStep2Submit} className="space-y-8">
          {/* Branding & Appearance Section */}
          <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl p-6 hover:shadow-lg transition-all duration-300">
            <div className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] bg-clip-text text-transparent">
              <h3 className="text-xl font-semibold mb-6">Branding & Appearance</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Color Configuration */}
              <div className="space-y-6">
                <div className="bg-gradient-to-br from-gray-50/50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-900/50 rounded-xl p-4 border border-gray-200/50 dark:border-gray-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-[#8178E8] to-[#6964D3] rounded-full mr-2"></div>
                    Color Palette
                  </h4>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="primaryColor" className="text-xs font-medium text-gray-700 dark:text-gray-300">Primary Color</Label>
                      <div className="mt-1">
                        <ColorPicker
                          id="primaryColor"
                          value={branding.primaryColor}
                          onChange={(value) => setBranding(prev => ({ ...prev, primaryColor: value }))}
                          placeholder="#8178E8"
                          className="w-10 h-10"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="secondaryColor" className="text-xs font-medium text-gray-700 dark:text-gray-300">Secondary Color</Label>
                      <div className="mt-1">
                        <ColorPicker
                          id="secondaryColor"
                          value={branding.secondaryColor}
                          onChange={(value) => setBranding(prev => ({ ...prev, secondaryColor: value }))}
                          placeholder="#6964D3"
                          className="w-10 h-10"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="accentColor" className="text-xs font-medium text-gray-700 dark:text-gray-300">Accent Color</Label>
                      <div className="mt-1">
                        <ColorPicker
                          id="accentColor"
                          value={branding.accentColor}
                          onChange={(value) => setBranding(prev => ({ ...prev, accentColor: value }))}
                          placeholder="#9333EA"
                          className="w-10 h-10"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="backgroundColor" className="text-xs font-medium text-gray-700 dark:text-gray-300">Background</Label>
                      <div className="mt-1">
                        <ColorPicker
                          id="backgroundColor"
                          value={branding.backgroundColor}
                          onChange={(value) => setBranding(prev => ({ ...prev, backgroundColor: value }))}
                          placeholder="#FFFFFF"
                          className="w-10 h-10"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Theme Selection */}
                <div className="bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-4 border border-blue-200/50 dark:border-blue-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mr-2"></div>
                    Theme Mode
                  </h4>
                  <select
                    id="widgetTheme"
                    value={widget.theme}
                    onChange={(e) => setWidget(prev => ({ ...prev, theme: e.target.value as any }))}
                    className="w-full px-3 py-2 text-sm bg-white/70 dark:bg-gray-800/70 border border-blue-200/50 dark:border-blue-600/50 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500/50 transition-all"
                  >
                    <option value="light">Light Theme</option>
                    <option value="dark">Dark Theme</option>
                    <option value="auto">Auto (follows system)</option>
                  </select>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                    Controls the overall appearance of your chat widget
                  </p>
                </div>
              </div>

              {/* Typography & Assets */}
              <div className="space-y-6">
                <div className="bg-gradient-to-br from-purple-50/50 to-pink-50/50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-4 border border-purple-200/50 dark:border-purple-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-2"></div>
                    Typography
                  </h4>
                  <div>
                    <Label htmlFor="fontFamily" className="text-xs font-medium text-gray-700 dark:text-gray-300">Font Family</Label>
                    <select
                      id="fontFamily"
                      value={branding.fontFamily}
                      onChange={(e) => setBranding(prev => ({ ...prev, fontFamily: e.target.value }))}
                      className="w-full px-3 py-2 mt-1 text-sm bg-white/70 dark:bg-gray-800/70 border border-purple-200/50 dark:border-purple-600/50 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-purple-500/50 transition-all"
                    >
                      <option value="Inter, sans-serif">Inter (Recommended)</option>
                      <option value="Arial, sans-serif">Arial</option>
                      <option value="Helvetica, sans-serif">Helvetica</option>
                      <option value="Georgia, serif">Georgia</option>
                      <option value="Times New Roman, serif">Times New Roman</option>
                      <option value="Courier New, monospace">Courier New</option>
                    </select>
                  </div>
                </div>

                <div className="bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl p-4 border border-green-200/50 dark:border-green-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full mr-2"></div>
                    Brand Assets
                  </h4>
                  <div>
                    <Label htmlFor="logoUrl" className="text-xs font-medium text-gray-700 dark:text-gray-300">Logo URL (Optional)</Label>
                    <Input
                      id="logoUrl"
                      value={branding.logoUrl}
                      onChange={(e) => setBranding(prev => ({ ...prev, logoUrl: e.target.value }))}
                      placeholder="https://example.com/logo.png"
                      className="mt-1 bg-white/70 dark:bg-gray-800/70 border-green-200/50 dark:border-green-600/50 rounded-lg focus:ring-2 focus:ring-green-500/50 transition-all"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                      Display your company logo in the chat header
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Settings & Behavior Section */}
          <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl p-6 hover:shadow-lg transition-all duration-300">
            <div className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] bg-clip-text text-transparent">
              <h3 className="text-xl font-semibold mb-6">Chat Settings & Behavior</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Messages Configuration */}
              <div className="space-y-6">
                <div className="bg-gradient-to-br from-amber-50/50 to-orange-50/50 dark:from-amber-900/20 dark:to-orange-900/20 rounded-xl p-4 border border-amber-200/50 dark:border-amber-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-amber-500 to-orange-500 rounded-full mr-2"></div>
                    Customer Messages
                  </h4>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="welcomeMsg" className="text-xs font-medium text-gray-700 dark:text-gray-300">Welcome Message</Label>
                      <Textarea
                        id="welcomeMsg"
                        value={settings.welcomeMessage}
                        onChange={(e) => setSettings(prev => ({ ...prev, welcomeMessage: e.target.value }))}
                        rows={2}
                        maxLength={500}
                        className="mt-1 bg-white/70 dark:bg-gray-800/70 border-amber-200/50 dark:border-amber-600/50 rounded-lg focus:ring-2 focus:ring-amber-500/50 transition-all text-sm"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {settings.welcomeMessage.length}/500 characters
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="offlineMsg" className="text-xs font-medium text-gray-700 dark:text-gray-300">Offline Message</Label>
                      <Textarea
                        id="offlineMsg"
                        value={settings.offlineMessage}
                        onChange={(e) => setSettings(prev => ({ ...prev, offlineMessage: e.target.value }))}
                        rows={2}
                        maxLength={500}
                        className="mt-1 bg-white/70 dark:bg-gray-800/70 border-amber-200/50 dark:border-amber-600/50 rounded-lg focus:ring-2 focus:ring-amber-500/50 transition-all text-sm"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {settings.offlineMessage.length}/500 characters
                      </p>
                    </div>

                    <div>
                      <Label htmlFor="placeholder" className="text-xs font-medium text-gray-700 dark:text-gray-300">Placeholder Text</Label>
                      <Input
                        id="placeholder"
                        value={settings.placeholderText}
                        onChange={(e) => setSettings(prev => ({ ...prev, placeholderText: e.target.value }))}
                        maxLength={100}
                        className="mt-1 bg-white/70 dark:bg-gray-800/70 border-amber-200/50 dark:border-amber-600/50 rounded-lg focus:ring-2 focus:ring-amber-500/50 transition-all text-sm"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        {settings.placeholderText.length}/100 characters
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Features & Behavior */}
              <div className="space-y-6">
                <div className="bg-gradient-to-br from-cyan-50/50 to-blue-50/50 dark:from-cyan-900/20 dark:to-blue-900/20 rounded-xl p-4 border border-cyan-200/50 dark:border-cyan-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-full mr-2"></div>
                    Chat Features
                  </h4>

                  <div className="space-y-4">
                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-cyan-100/50 dark:border-cyan-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="enableFileUpload"
                        checked={settings.enableFileUpload}
                        onChange={(checked) => setSettings(prev => ({ ...prev, enableFileUpload: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Enable file upload</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Allow customers to send files and images</p>
                          </div>
                        }
                      />
                    </div>

                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-cyan-100/50 dark:border-cyan-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="enableEmojis"
                        checked={settings.enableEmojis}
                        onChange={(checked) => setSettings(prev => ({ ...prev, enableEmojis: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Enable emojis</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Show emoji picker in chat</p>
                          </div>
                        }
                      />
                    </div>

                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-cyan-100/50 dark:border-cyan-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="enableTypingIndicator"
                        checked={settings.enableTypingIndicator}
                        onChange={(checked) => setSettings(prev => ({ ...prev, enableTypingIndicator: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Show typing indicator</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Display when agents are typing</p>
                          </div>
                        }
                      />
                    </div>

                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-cyan-100/50 dark:border-cyan-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="autoAssignment"
                        checked={settings.autoAssignment}
                        onChange={(checked) => setSettings(prev => ({ ...prev, autoAssignment: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Auto-assign conversations</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Automatically assign new chats to available agents</p>
                          </div>
                        }
                      />
                    </div>

                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-cyan-100/50 dark:border-cyan-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="requireCustomerEmail"
                        checked={settings.requireCustomerEmail}
                        onChange={(checked) => setSettings(prev => ({ ...prev, requireCustomerEmail: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Require customer email before chat</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Make email address mandatory for customers to start a chat session</p>
                          </div>
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Widget Features */}
                <div className="bg-gradient-to-br from-violet-50/50 to-purple-50/50 dark:from-violet-900/20 dark:to-purple-900/20 rounded-xl p-4 border border-violet-200/50 dark:border-violet-700/50">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full mr-2"></div>
                    Widget Features
                  </h4>

                  <div className="space-y-4">
                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-violet-100/50 dark:border-violet-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="showAgentPhotos"
                        checked={widget.showAgentPhotos}
                        onChange={(checked) => setWidget(prev => ({ ...prev, showAgentPhotos: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Show agent photos</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Display agent profile pictures in chat</p>
                          </div>
                        }
                      />
                    </div>

                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-violet-100/50 dark:border-violet-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="showOnlineStatus"
                        checked={widget.showOnlineStatus}
                        onChange={(checked) => setWidget(prev => ({ ...prev, showOnlineStatus: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Show online status</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Display when agents are online/offline</p>
                          </div>
                        }
                      />
                    </div>

                    <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-violet-100/50 dark:border-violet-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all">
                      <Checkbox
                        id="enableSounds"
                        checked={widget.enableSounds}
                        onChange={(checked) => setWidget(prev => ({ ...prev, enableSounds: checked }))}
                        label={
                          <div className="flex-1">
                            <span className="text-sm font-medium text-gray-900 dark:text-white">Enable sounds</span>
                            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Play notification sounds for new messages</p>
                          </div>
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Auto Response Section */}
            <div className="mt-8 bg-gradient-to-br from-rose-50/50 to-pink-50/50 dark:from-rose-900/20 dark:to-pink-900/20 rounded-xl p-4 border border-rose-200/50 dark:border-rose-700/50">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                <div className="w-2 h-2 bg-gradient-to-r from-rose-500 to-pink-500 rounded-full mr-2"></div>
                Auto Response System
              </h4>

              <div className="p-3 rounded-lg bg-white/50 dark:bg-gray-800/50 border border-rose-100/50 dark:border-rose-800/50 hover:bg-white/70 dark:hover:bg-gray-800/70 transition-all mb-4">
                <Checkbox
                  id="enableAutoResponse"
                  checked={settings.responseTime.autoResponse.enabled}
                  onChange={(checked) => setSettings(prev => ({
                    ...prev,
                    responseTime: {
                      ...prev.responseTime,
                      autoResponse: {
                        ...prev.responseTime.autoResponse,
                        enabled: checked
                      }
                    }
                  }))}
                  label={
                    <div className="flex-1">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">Enable auto response</span>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Send automatic responses to new messages</p>
                    </div>
                  }
                />
              </div>

              {settings.responseTime.autoResponse.enabled && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
                  <div>
                    <Label htmlFor="autoResponseMsg" className="text-xs font-medium text-gray-700 dark:text-gray-300">Auto Response Message</Label>
                    <Textarea
                      id="autoResponseMsg"
                      value={settings.responseTime.autoResponse.message}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        responseTime: {
                          ...prev.responseTime,
                          autoResponse: {
                            ...prev.responseTime.autoResponse,
                            message: e.target.value
                          }
                        }
                      }))}
                      rows={2}
                      maxLength={500}
                      className="mt-1 bg-white/70 dark:bg-gray-800/70 border-rose-200/50 dark:border-rose-600/50 rounded-lg focus:ring-2 focus:ring-rose-500/50 transition-all text-sm"
                    />
                  </div>
                  <div>
                    <Label htmlFor="autoResponseDelay" className="text-xs font-medium text-gray-700 dark:text-gray-300">Delay (seconds)</Label>
                    <Input
                      id="autoResponseDelay"
                      type="number"
                      min="0"
                      max="300"
                      value={settings.responseTime.autoResponse.delay}
                      onChange={(e) => setSettings(prev => ({
                        ...prev,
                        responseTime: {
                          ...prev.responseTime,
                          autoResponse: {
                            ...prev.responseTime.autoResponse,
                            delay: parseInt(e.target.value) || 0
                          }
                        }
                      }))}
                      className="mt-1 bg-white/70 dark:bg-gray-800/70 border-rose-200/50 dark:border-rose-600/50 rounded-lg focus:ring-2 focus:ring-rose-500/50 transition-all text-sm"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Widget Integration Preview Section */}
          <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl p-6 hover:shadow-lg transition-all duration-300">
            <div className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] bg-clip-text text-transparent">
              <h3 className="text-xl font-semibold mb-6">Widget Integration Preview</h3>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Essential Configuration */}
              <div className="bg-gradient-to-br from-indigo-50/50 to-blue-50/50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-xl p-4 border border-indigo-200/50 dark:border-indigo-700/50">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <div className="w-2 h-2 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full mr-2"></div>
                  Essential Widget Configuration
                </h4>
                <div className="bg-white/70 dark:bg-gray-900/70 rounded-lg p-3 border border-indigo-100/50 dark:border-indigo-800/50">
                  <pre className="text-xs text-gray-800 dark:text-gray-200 overflow-x-auto">
                    <code>{JSON.stringify(getWidgetConfig(), null, 2)}</code>
                  </pre>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-3 leading-relaxed">
                  Only client-side configuration shown. Theme, analytics, and business hours are loaded automatically from your server configuration.
                </p>
              </div>

              {/* Integration Code */}
              <div className="bg-gradient-to-br from-emerald-50/50 to-teal-50/50 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-xl p-4 border border-emerald-200/50 dark:border-emerald-700/50">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full mr-2"></div>
                  Simple Integration Code
                </h4>
                <div className="bg-white/70 dark:bg-gray-900/70 rounded-lg p-3 border border-emerald-100/50 dark:border-emerald-800/50">
                  <pre className="text-xs text-gray-800 dark:text-gray-200 overflow-x-auto">
                    <code>{`<!-- NewInstance Live Chat Widget -->
<script src="https://widget.newinstance.com/widget.js"></script>
<script>
  const widget = new NewInstanceWidget.ChatWidgetVanilla({
    appId: "your-app-id",
    apiKey: "your-api-key"
  });
  widget.mount('newinstance-chat-widget');
</script>
<div id="newinstance-chat-widget"></div>`}</code>
                  </pre>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-3 leading-relaxed">
                  The widget automatically loads all configuration (theme, messages, settings) from your server using the appId and apiKey.
                </p>
              </div>
            </div>

            {/* Integration Note */}
            <div className="mt-6 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/50 dark:border-blue-700/50 rounded-xl p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-0.5">
                  <div className="w-5 h-5 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1">
                    Integration Note
                  </h4>
                  <p className="text-sm text-blue-800 dark:text-blue-200 leading-relaxed">
                    After setup completion, you'll receive your actual appId and apiKey. The widget will automatically load all your configuration settings from the server, including the branding, theme, and behavior settings you've configured above.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setCurrentStep(1)}
            >
              Back
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
            >
              {isLoading ? 'Setting up...' : 'Complete Setup & Enable Live Chat'}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
}

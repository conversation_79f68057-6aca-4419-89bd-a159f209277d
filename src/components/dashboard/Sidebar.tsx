'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { classNames } from 'primereact/utils';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { getOrganizationById } from '@/server/actions/user-actions';
import { logger } from '@/utils/logger';

type NavItem = {
  label: string;
  path: string;
  icon: string;
  badge?: string | number;
  badgeVariant?: 'success' | 'warning' | 'danger' | 'info' | 'default';
};

type SidebarProps = {
  onClose?: () => void;
  isMobile?: boolean;
};

export default function Sidebar({ onClose, isMobile = false }: SidebarProps) {
  const params = useParams();
  const pathname = usePathname();
  const organizationId = params.id as string;

  const [orgName, setOrgName] = useState('Organization');
  const [orgInitial, setOrgInitial] = useState('O');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  // Get organization name from server
  useEffect(() => {
    const fetchOrgName = async () => {
      setIsLoading(true);
      try {
        const response = await getOrganizationById(organizationId);

        if (response.success && response.data) {
          setOrgName(response.data.name);
          setOrgInitial(response.data.name[0] || 'O');
        } else {
          setError(response.error?.includes('access denied') ? 'Access denied' : "Unknown error");
          setOrgName(`Organization ${organizationId.slice(0, 4)}`);
          setOrgInitial('O');
        }
      } catch (error) {
        setOrgName(`Organization ${organizationId.slice(0, 4)}`);
        setOrgInitial('O');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrgName();
  }, [organizationId]);

  // Create navigation items
  const navItems: NavItem[] = [
    {
      label: 'Dashboard',
      path: `/dashboard/organization/${organizationId}`,
      icon: 'pi pi-home'
    },
    {
      label: 'App Manager',
      path: `/dashboard/organization/${organizationId}/app-manager/apps`,
      icon: 'pi pi-server'
    },
    {
      label: 'Support',
      path: `/dashboard/organization/${organizationId}/support`,
      icon: 'pi pi-comments'
    },
    {
      label: 'Live Chat',
      path: `/dashboard/organization/${organizationId}/live-chat`,
      icon: 'pi pi-comment',
      // badge: 'Setup' // This will be dynamic based on setup status
    },
    {
      label: 'Billing',
      path: `/dashboard/organization/${organizationId}/billing`,
      icon: 'pi pi-credit-card'
    },
    {
      label: 'Settings',
      path: `/dashboard/organization/${organizationId}/settings`,
      icon: 'pi pi-cog'
    }
  ];

  // Check if a nav item is active
  const isActive = (item: NavItem) => {
    if (item.path === `/dashboard/organization/${organizationId}`) {
      return pathname === item.path;
    }
    return pathname.includes(item.path);
  };

  return (
    <div className={`
      flex flex-col h-full
      bg-white dark:bg-[#1a1a2e]
      shadow-lg
      ${isMobile ? 'rounded-r-xl' : 'border-r border-gray-200 dark:border-gray-800'}
    `}>
      {/* Organization header */}
      <div className="p-4 flex items-center gap-3">
        <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-indigo-600 to-purple-600 flex items-center justify-center text-white font-semibold text-lg shadow-md">
          {orgInitial}
        </div>
        <div className="flex-1 min-w-0">
          <h2 className="text-lg font-bold truncate text-gray-900 dark:text-white">
            {isLoading ? 'Loading...' : error ? <span className="text-red-500">{error}</span> : orgName}
          </h2>
        </div>
        {isMobile && (
          <Button
            icon="pi pi-times"
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            onClick={onClose}
          />
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4">
        <ul className="space-y-1.5">
          {navItems.map((item) => {
            const active = isActive(item);
            return (
              <li key={item.path}>
                <Link href={item.path} onClick={isMobile ? onClose : undefined}>
                  <div
                    className={classNames(
                      'flex items-center justify-between px-4 py-2.5 rounded-xl transition-all duration-200',
                      active
                        ? 'bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 text-indigo-700 dark:text-indigo-300 font-medium shadow-sm'
                        : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div className={classNames(
                        'flex items-center justify-center w-9 h-9 rounded-lg',
                        active
                          ? 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-300'
                          : 'text-gray-500 dark:text-gray-400'
                      )}>
                        <i className={`${item.icon} text-lg`}></i>
                      </div>
                      <span>{item.label}</span>
                    </div>

                    {item.badge && (
                      <Badge
                        variant={item.badgeVariant || 'default'}
                        className="ml-2"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-800">
        <Link href="/dashboard">
          <div className="flex items-center gap-3 px-4 py-2.5 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-xl transition-all duration-200">
            <div className="flex items-center justify-center w-9 h-9 rounded-lg text-gray-500 dark:text-gray-400">
              <i className="pi pi-arrow-left text-lg"></i>
            </div>
            <span>Back to Dashboard</span>
          </div>
        </Link>
      </div>
    </div>
  );
}

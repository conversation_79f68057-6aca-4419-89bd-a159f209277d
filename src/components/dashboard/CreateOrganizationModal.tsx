'use client';

import { useState, forwardRef, useImperative<PERSON>andle } from 'react';
import { createOrganization } from '@/server/actions/user-actions';
import { Select } from '@/components/ui/Select';
import { ErrorAlert } from '@/components/ui/Alert';
import { useToast } from '@/components/ui/Toast';
import { Input } from '../ui/Input';
import { Textarea } from '../ui/Textarea';
import { logger } from '@/utils/logger';

interface CreateOrganizationModalProps {
  onClose: () => void;
  onSuccess: (organization: any) => void;
}

export interface CreateOrganizationModalRef {
  handleSubmit: () => Promise<void>;
}

const industries = [
  { label: 'Technology', value: 'technology' },
  { label: 'Healthcare', value: 'healthcare' },
  { label: 'Finance', value: 'finance' },
  { label: 'Education', value: 'education' },
  { label: 'Retail', value: 'retail' },
  { label: 'Real Estate', value: 'realEstate' },
  { label: 'Manufacturing', value: 'manufacturing' },
  { label: 'Entertainment', value: 'entertainment' },
  { label: 'Hospitality', value: 'hospitality' },
  { label: 'Transportation', value: 'transportation' },
  { label: 'Construction', value: 'construction' },
  { label: 'Other', value: 'other' },
];

const CreateOrganizationModal = forwardRef<CreateOrganizationModalRef, CreateOrganizationModalProps>(
  ({ onClose, onSuccess }, ref) => {
    const [name, setName] = useState('');
    const [industry, setIndustry] = useState<string | null>(null);
    const [description, setDescription] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    const { success, error: showError } = useToast();

    const handleSubmit = async (e?: React.FormEvent) => {
      if (e) e.preventDefault();

      if (!name) {
        setError('Organization name is required');
        return;
      }

      if (!industry) {
        setError('Please select an industry');
        return;
      }

      setIsLoading(true);
      setError('');

      try {
        const result = await createOrganization({
          name,
          industry,
          description
        });

        if (result.success && 'data' in result) {
          onSuccess(result.data);
        } else {
          setError('error' in result ? result.error : 'Failed to create organization');
        }
      } catch (err) {
        logger.error('Error creating organization:', err);
        setError('An error occurred. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      handleSubmit
    }));

    // Render just the form content, not the modal container
    return (
      <div>
        {error && (
          <ErrorAlert className="mb-4">
            {error}
          </ErrorAlert>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Organization Name <span className="text-red-500">*</span>
            </label>
            <Input
              id="name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-2 rounded-lg border border-transparent focus:border-[#8178E8] focus:outline-none focus:ring-2 focus:ring-[#8178E8]/50 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              placeholder="Enter organization name"
              disabled={isLoading}
              required
            />
          </div>

          <div className="mb-4">
            <label htmlFor="industry" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Industry <span className="text-red-500">*</span>
            </label>
            <Select
              options={industries}
              value={industry}
              onChange={(value) => setIndustry(value as string)}
              placeholder="Select an industry"
              disabled={isLoading}
            />
          </div>

          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full px-4 py-2 rounded-lg border border-transparent focus:border-[#8178E8] focus:outline-none focus:ring-2 focus:ring-[#8178E8]/50 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
              placeholder="Briefly describe your organization (optional)"
              disabled={isLoading}
            />
          </div>
        </form>

        <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mt-4">
          After creating your organization, you'll be able to add team members and access New Instance services.
        </p>
      </div>
    );
  }
);

CreateOrganizationModal.displayName = 'CreateOrganizationModal';

export default CreateOrganizationModal;

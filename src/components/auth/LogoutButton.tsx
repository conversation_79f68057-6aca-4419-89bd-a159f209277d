'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {logoutUser} from "@server/actions/user-auth";
import { logger } from '@/utils/logger';

interface LogoutButtonProps {
  className?: string;
  variant?: 'default' | 'icon' | 'text';
}

export default function LogoutButton({
  className = '',
  variant = 'default'
}: LogoutButtonProps) {
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      const result = await logoutUser();

      if (result.success) {
        // Redirect to login page
        router.push('/auth?mode=login');
      } else {
        setIsLoggingOut(false);
      }
    } catch (error) {
      logger.error('Error during logout:', error);
      setIsLoggingOut(false);
    }
  };

  // Different button styles based on variant
  if (variant === 'icon') {
    return (
      <button
        onClick={handleLogout}
        disabled={isLoggingOut}
        className={`p-2 rounded-full hover:bg-[#F3F3F3] dark:hover:bg-[#2a2a38] transition-colors ${className}`}
        aria-label="Logout"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
          <polyline points="16 17 21 12 16 7"></polyline>
          <line x1="21" y1="12" x2="9" y2="12"></line>
        </svg>
      </button>
    );
  }

  if (variant === 'text') {
    return (
      <button
        onClick={handleLogout}
        disabled={isLoggingOut}
        className={`text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-[#6964D3] dark:hover:text-[#8178E8] transition-colors ${className}`}
      >
        {isLoggingOut ? 'Logging out...' : 'Logout'}
      </button>
    );
  }

  // Default button style
  return (
    <button
      onClick={handleLogout}
      disabled={isLoggingOut}
      className={`px-4 py-2 bg-[#F3F3F3] dark:bg-[#2a2a38] rounded-lg text-[#5E5E5E] dark:text-[#C6C6C6] hover:bg-[#E0D7FF] dark:hover:bg-[#3a3a48] transition-colors ${className}`}
    >
      {isLoggingOut ? 'Logging out...' : 'Logout'}
    </button>
  );
}

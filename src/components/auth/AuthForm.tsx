import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  registerUser,
  loginUser,
} from '@/server/actions/user-auth';
import { Password } from 'primereact/password';
import { InputText } from 'primereact/inputtext';
import { Checkbox } from '@/components/ui/Checkbox';
import { ErrorAlert, SuccessAlert } from '@/components/ui/Alert';
import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import { logger } from '@/utils/logger';

type AuthMode = 'login' | 'signup' | 'verify';

// Define types for server action responses
interface LoginResponse {
  success: boolean;
  error?: string;
  needsVerification?: boolean;
  verificationToken?: string;
  data?: {
    name: string;
    email: string;
  };
}

// First, let's define the validation functions that were in utils/auth

// Function to validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Function to validate password strength
const isStrongPassword = (password: string): boolean => {
  // Password should be at least 8 characters, include number and special character
  const passwordRegex = /^(?=.*[0-9])(?=.*[!@#$%^&*])(?=.{8,})/;
  return passwordRegex.test(password);
};

export default function AuthForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectPath = searchParams.get('redirect') || '/dashboard';

  const [mode, setMode] = useState<AuthMode>(
    (searchParams.get('mode') as AuthMode) || 'login'
  );
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [company, setCompany] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [formHeight, setFormHeight] = useState<number | undefined>(undefined);
  const [rememberMe, setRememberMe] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const loginFormRef = useRef<HTMLDivElement>(null);
  const signupFormRef = useRef<HTMLDivElement>(null);
  const verifyFormRef = useRef<HTMLDivElement>(null);
  const formContainerRef = useRef<HTMLDivElement>(null);

  // Set initial mode based on URL parameter
  useEffect(() => {
    const urlMode = searchParams.get('mode') as AuthMode | null;
    if (urlMode && (urlMode === 'login' || urlMode === 'signup')) {
      setMode(urlMode);
    }

    // If plan parameter exists, pre-select signup mode
    const plan = searchParams.get('plan');
    if (plan) {
      setMode('signup');
    }
  }, [searchParams]);

  // Update form height when mode changes
  useEffect(() => {
    if (mode === 'login' && loginFormRef.current) {
      setFormHeight(loginFormRef.current.scrollHeight);
    } else if (mode === 'signup' && signupFormRef.current) {
      setFormHeight(signupFormRef.current.scrollHeight);
    } else if (mode === 'verify' && verifyFormRef.current) {
      setFormHeight(verifyFormRef.current.scrollHeight);
    }
  }, [mode]);

  // Update form height on window resize
  useEffect(() => {
    const updateFormHeight = () => {
      if (mode === 'login' && loginFormRef.current) {
        setFormHeight(loginFormRef.current.scrollHeight);
      } else if (mode === 'signup' && signupFormRef.current) {
        setFormHeight(signupFormRef.current.scrollHeight);
      } else if (mode === 'verify' && verifyFormRef.current) {
        setFormHeight(verifyFormRef.current.scrollHeight);
      }
    };

    window.addEventListener('resize', updateFormHeight);
    return () => window.removeEventListener('resize', updateFormHeight);
  }, [mode]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) {
      value = value.charAt(0);
    }

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto focus next input
    if (value && index < 5) {
      const nextInput = document.getElementById(`otp-${index + 1}`);
      if (nextInput) {
        nextInput.focus();
      }
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Handle backspace - move to previous input
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
      }
    }
  };

  const handleResendOtp = async () => {
    // Note: This function is no longer used as we now redirect to the dedicated VerifyForm component
    // which handles OTP resending through the server action directly
    setIsLoading(true);
    setError('');
    try {
      setSuccessMessage('Please use the verification page to resend the code');
      setTimeout(() => {
        // Redirect to verify page with mode parameter
        if (email) {
          router.push('/auth?mode=login');
        }
      }, 2000);
    } catch (err) {
      logger.error('Error redirecting:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = (): boolean => {
    setError('');

    if (mode === 'login' || mode === 'signup') {
      if (!isValidEmail(email)) {
        setError('Please enter a valid email address');
        return false;
      }

      if (password.length < 8) {
        setError('Password must be at least 8 characters');
        return false;
      }

      if (mode === 'signup' && !isStrongPassword(password)) {
        setError('Password must include at least one number and one special character');
        return false;
      }

      if (mode === 'signup' && (!name || !company)) {
        setError('Please fill in all required fields');
        return false;
      }

      if (mode === 'signup' && !termsAccepted) {
        setError('You must accept the Terms of Service and Privacy Policy');
        return false;
      }
    }

    if (mode === 'verify') {
      const otpValue = otp.join('');
      if (otpValue.length !== 6 || !/^\d+$/.test(otpValue)) {
        setError('Please enter a valid 6-digit verification code');
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      if (mode === 'login') {
        const result = await loginUser({
          email,
          password,
          rememberMe
        }) as LoginResponse;

        if (result.success) {
          setSuccessMessage('Login successful! Redirecting...');
          setTimeout(() => {
            // Use the redirectPath from the URL parameter if available
            router.push(redirectPath);
          }, 1000);
        } else if (result.needsVerification && result.verificationToken) {
          // Redirect to verification page with token
          router.push(`/verify?token=${result.verificationToken}&redirect=${encodeURIComponent(redirectPath)}`);
        } else {
          setError(result.error || 'Login failed. Please try again.');
        }
      } else if (mode === 'signup') {
        const result = await registerUser({
          name,
          email,
          company,
          password
        });

        if (result.success && result.verificationToken) {
          setSuccessMessage('Registration successful! Check your email for verification.');
          // Redirect to verification page with token
          router.push(`/verify?token=${result.verificationToken}&redirect=${encodeURIComponent(redirectPath)}`);
        } else {
          setError(result.error || 'Registration failed. Please try again.');
        }
      } else if (mode === 'verify') {
        // This mode is not used as we redirect to VerifyForm
        setError('Please use the verification page to verify your email');
      }
    } catch (error) {
      logger.error('Auth error:', error);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="py-12 md:py-20 bg-white dark:bg-[#0a0a14] relative overflow-hidden min-h-screen flex items-center animate-fadeIn">
      {/* Background elements with animation */}
      <div className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl animate-float"></div>

      <div className="max-w-7xl mx-auto px-6 relative z-10 w-full">
        <div className="flex flex-col lg:flex-row gap-12 items-center">
          {/* Left side with branding/value prop */}
          <div className="lg:w-1/2 max-w-lg animate-slideInLeft">
            <div className="mb-8 flex items-center">
              <Image
                src="/assets/logos/purple-cloud-yellow-dots.svg"
                alt="New Instance Logo"
                width={50}
                height={50}
                className="mr-3 animate-pulse-subtle"
              />
              <span className="text-2xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
            </div>

            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold font-[family-name:var(--font-jakarta)] leading-tight mb-6 bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent animate-gradient">
              {mode === 'login' ? 'Welcome back!' : mode === 'signup' ? 'Join thousands of teams' : 'Verify your account'}
            </h1>

            <p className="text-lg mb-8 text-[#4B4B4B] dark:text-[#C6C6C6] animate-fadeIn animation-delay-300">
              {mode === 'login'
                ? 'Sign in to access your dashboard and manage your cloud operations.'
                : mode === 'signup'
                ? 'Create your account and start streamlining your business operations today.'
                : 'We sent a verification code to your email. Enter it below to verify your account.'}
            </p>

            <div className="hidden lg:block">
              <div className="flex gap-4 items-center mb-8 animate-fadeIn animation-delay-400">
                <div className="w-12 h-12 bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-[#6964D3]">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-base">Secure Authentication</h3>
                  <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Two-factor authentication for enhanced security</p>
                </div>
              </div>

              <div className="flex gap-4 items-center animate-fadeIn animation-delay-500">
                <div className="w-12 h-12 bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-full flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-[#BE9544]">
                    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-base">Data Protection</h3>
                  <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Your data is encrypted and securely stored</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right side with auth form */}
          <div className="lg:w-1/2 w-full max-w-md animate-slideInRight">
            <div className="glass-card dark:glass-dark p-8 md:p-10 rounded-2xl shadow-xl border border-[#E0D7FF] dark:border-[#1e1e28] animate-pulse-subtle">
              {/* Header */}
              {mode !== 'verify' && (
                <div className="flex mb-8 bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-xl p-1 relative">
                  {/* Animated background pill that slides */}
                  <div
                    className={`absolute inset-y-1 w-1/2 bg-white dark:bg-[#0f0f18] rounded-lg shadow-sm transition-all duration-300 ease-in-out ${
                      mode === 'signup' ? 'translate-x-full' : 'translate-x-0'
                    }`}
                    aria-hidden="true"
                  ></div>

                  <button
                    className={`flex-1 py-2 text-sm md:text-base font-medium rounded-lg transition-all duration-300 relative z-10 ${
                      mode === 'login' ? 'text-[#424098] dark:text-white scale-105' : 'text-[#8178E8]/70 dark:text-gray-400'
                    }`}
                    onClick={() => {
                      setMode('login');
                      setError('');
                      setSuccessMessage('');
                    }}
                    type="button"
                  >
                    Sign In
                  </button>
                  <button
                    className={`flex-1 py-2 text-sm md:text-base font-medium rounded-lg transition-all duration-300 relative z-10 ${
                      mode === 'signup' ? 'text-[#424098] dark:text-white scale-105' : 'text-[#8178E8]/70 dark:text-gray-400'
                    }`}
                    onClick={() => {
                      setMode('signup');
                      setError('');
                      setSuccessMessage('');
                    }}
                    type="button"
                  >
                    Sign Up
                  </button>
                </div>
              )}

              {/* Error and Success Messages */}
              {error && (
                <ErrorAlert className="mb-6 animate-shakeX">
                  {error}
                </ErrorAlert>
              )}

              {successMessage && (
                <SuccessAlert className="mb-6 animate-bounceIn">
                  {successMessage}
                </SuccessAlert>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit}>
                <div
                  ref={formContainerRef}
                  className="height-transition"
                  style={{ height: formHeight ? `${formHeight}px` : 'auto' }}
                >
                  {mode === 'login' && (
                    <div
                      ref={loginFormRef}
                      className="animate-slideInUp"
                      onAnimationEnd={() => {
                        if (loginFormRef.current) {
                          setFormHeight(loginFormRef.current.scrollHeight);
                        }
                      }}
                    >
                      <div className="mb-5 animate-fadeIn">
                        <label htmlFor="email" className="block text-sm font-medium mb-2">Email</label>
                        <InputText
                          id="email"
                          type="email"
                          className="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          placeholder="<EMAIL>"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          disabled={isLoading}
                          required
                          pt={{
                            root: { className: 'w-full' }
                          }}
                        />
                      </div>

                      <div className="mb-3 animate-fadeIn animation-delay-100">
                        <div className="flex justify-between mb-2">
                          <label htmlFor="password" className="block text-sm font-medium">Password</label>
                          <Link href="/forgot-password" className="text-sm text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white transition-colors">
                            Forgot password?
                          </Link>
                        </div>
                        <Password
                          id="password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          toggleMask
                          feedback={false}
                          className="w-full rounded-xl"
                          inputClassName="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          disabled={isLoading}
                          required
                        />
                      </div>

                      <div className="animate-fadeIn animation-delay-200">
                        <Checkbox
                          id="remember"
                          checked={rememberMe}
                          onChange={setRememberMe}
                          label="Remember me for 30 days"
                          disabled={isLoading}
                          className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]"
                        />
                      </div>
                    </div>
                  )}

                  {mode === 'signup' && (
                    <div
                      ref={signupFormRef}
                      className="animate-slideInUp"
                      onAnimationEnd={() => {
                        if (signupFormRef.current) {
                          setFormHeight(signupFormRef.current.scrollHeight);
                        }
                      }}
                    >
                      <div className="mb-5 animate-fadeIn">
                        <label htmlFor="name" className="block text-sm font-medium mb-2">Full Name</label>
                        <InputText
                          id="name"
                          className="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          placeholder="John Doe"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          disabled={isLoading}
                          required
                          pt={{
                            root: { className: 'w-full' }
                          }}
                        />
                      </div>

                      <div className="mb-5 animate-fadeIn animation-delay-100">
                        <label htmlFor="signup-email" className="block text-sm font-medium mb-2">Work Email</label>
                        <InputText
                          id="signup-email"
                          type="email"
                          className="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          placeholder="<EMAIL>"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          disabled={isLoading}
                          required
                          pt={{
                            root: { className: 'w-full' }
                          }}
                        />
                      </div>

                      <div className="mb-5 animate-fadeIn animation-delay-200">
                        <label htmlFor="company" className="block text-sm font-medium mb-2">Company Name</label>
                        <InputText
                          id="company"
                          className="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          placeholder="Company Inc."
                          value={company}
                          onChange={(e) => setCompany(e.target.value)}
                          disabled={isLoading}
                          required
                          pt={{
                            root: { className: 'w-full' }
                          }}
                        />
                      </div>

                      <div className="mb-4 animate-fadeIn animation-delay-300">
                        <label htmlFor="signup-password" className="block text-sm font-medium mb-2">Password</label>
                        <Password
                          id="signup-password"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          toggleMask
                          feedback
                          promptLabel="Enter password"
                          weakLabel="Too simple"
                          mediumLabel="Average complexity"
                          strongLabel="Complex password"
                          className="w-full rounded-xl"
                          inputClassName="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          disabled={isLoading}
                          required
                        />
                        <p className="mt-1 text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">
                          Must be at least 8 characters with a number and special character
                        </p>
                      </div>

                      <div className="mb-6 animate-fadeIn animation-delay-400">
                        <label htmlFor="confirm-password" className="block text-sm font-medium mb-2">Confirm Password</label>
                        <Password
                          id="confirm-password"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          toggleMask
                          feedback={false}
                          className="w-full rounded-xl"
                          inputClassName="w-full px-4 py-3 rounded-xl bg-[#F3F3F3] dark:bg-[#1e1e28] border border-transparent focus:border-[#8178E8] focus:outline-none transition-all hover:shadow-md"
                          disabled={isLoading}
                          required
                        />
                      </div>

                      <div className="mb-6 animate-fadeIn animation-delay-500">
                        <Checkbox
                          id="terms"
                          checked={termsAccepted}
                          onChange={setTermsAccepted}
                          className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]"
                          disabled={isLoading}
                          label={
                            <span>
                              I agree to the <Link href="/terms" className="text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white">Terms of Service</Link> and <Link href="/privacy" className="text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white">Privacy Policy</Link>
                            </span>
                          }
                          required
                        />
                      </div>
                    </div>
                  )}

                  {mode === 'verify' && (
                    <div
                      ref={verifyFormRef}
                      className="animate-slideInUp"
                      onAnimationEnd={() => {
                        if (verifyFormRef.current) {
                          setFormHeight(verifyFormRef.current.scrollHeight);
                        }
                      }}
                    >
                      <div className="mb-6 text-center animate-fadeIn animation-delay-100">
                        <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-2">
                          We sent a 6-digit code to <span className="font-medium text-black dark:text-white">{email}</span>
                        </p>
                        <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mb-6">
                          The code expires in 10 minutes
                        </p>

                        <div className="flex justify-center gap-2 mb-6 animate-fadeIn animation-delay-200">
                          {otp.map((digit, index) => (
                            <input
                              key={index}
                              id={`otp-${index}`}
                              type="text"
                              className="w-12 h-14 text-center text-xl font-medium bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-xl border border-transparent focus:border-[#8178E8] focus:outline-none transition-colors"
                              maxLength={1}
                              value={digit}
                              onChange={(e) => handleOtpChange(index, e.target.value)}
                              onKeyDown={(e) => handleOtpKeyDown(index, e)}
                              autoFocus={index === 0}
                              pattern="[0-9]*"
                              inputMode="numeric"
                              disabled={isLoading}
                            />
                          ))}
                        </div>

                        <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] animate-fadeIn animation-delay-300">
                          Didn&#39;t receive the code?
                          <button
                            type="button"
                            className="text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white ml-1 font-medium"
                            onClick={handleResendOtp}
                            disabled={isLoading}
                          >
                            Resend code
                          </button>
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-end space-x-3 border-none p-0 mt-3">
                  <button
                    type="submit"
                    className={`w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white py-3.5 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all btn-hover-fx mb-6 ${isLoading ? 'opacity-70 cursor-not-allowed animate-pulse-subtle hover:animate-none' : 'animate-pulse-subtle hover:animate-none'}`}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {mode === 'login' ? 'Signing In...' : mode === 'signup' ? 'Creating Account...' : 'Verifying...'}
                      </div>
                    ) : (
                      mode === 'login' ? 'Sign In' : mode === 'signup' ? 'Create Account' : 'Verify Account'
                    )}
                  </button>
                </div>

                {/* OAuth options */}
                <div className="relative flex items-center justify-center mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-[#E0D7FF] dark:border-[#1e1e28]"></div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

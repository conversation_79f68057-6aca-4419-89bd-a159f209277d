import {useState, useEffect} from 'react';
import Image from 'next/image';
import {useRouter, useSearchParams} from 'next/navigation';
import {verifyOtp, checkVerificationToken, sendOtpForToken} from '@/server/actions/user-auth';
import {InputOtp, InputOtpChangeEvent} from '@/components/ui/InputOtp';
import { logger } from '@/utils/logger';


export default function VerifyForm() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const token = searchParams.get('token');
    const redirectPath = searchParams.get('redirect') || '/dashboard';

    const [maskedEmail, setMaskedEmail] = useState<string>('');
    const [otp, setOtp] = useState<string>('');
    const [isLoading, setIsLoading] = useState(false);
    const [isTokenValid, setIsTokenValid] = useState(false);
    const [error, setError] = useState('');
    const [successMessage, setSuccessMessage] = useState('');
    const [resendDisabled, setResendDisabled] = useState(false);
    const [resendCountdown, setResendCountdown] = useState(0);

    // Check token validity and get masked email
    useEffect(() => {
        if (!token) {
            router.push('/auth?mode=signup');
            return;
        }

        const validateToken = async () => {
            setIsLoading(true);
            try {
                const tokenResult = await checkVerificationToken(token);
                if (tokenResult.success && tokenResult.email) {
                    setMaskedEmail(tokenResult.email);
                    setIsTokenValid(true);
                    // Send OTP when we first validate the token
                    await handleSendOtp();
                } else {
                    setError(tokenResult.error || 'Invalid verification token. Please try again.');
                    setIsTokenValid(false);
                    // Redirect after showing error
                    setTimeout(() => {
                        router.push('/auth?mode=signup');
                    }, 3000);
                }
            } catch (err) {
                logger.error('Error validating token:', err);
                setError('An error occurred. Redirecting to signup...');
                setIsTokenValid(false);
                setTimeout(() => {
                    router.push('/auth?mode=signup');
                }, 3000);
            } finally {
                setIsLoading(false);
            }
        };

        validateToken();
    }, [token, router]);

    // Handle countdown for resend button
    useEffect(() => {
        if (resendCountdown > 0) {
            const timer = setTimeout(() => {
                setResendCountdown(resendCountdown - 1);
            }, 1000);
            return () => clearTimeout(timer);
        } else if (resendCountdown === 0 && resendDisabled) {
            setResendDisabled(false);
        }
    }, [resendCountdown, resendDisabled]);

    const handleSendOtp = async () => {
        if (!token || !isTokenValid) return;

        setIsLoading(true);
        setError('');
        try {
            const result = await sendOtpForToken(token);
            if (result.success) {
                setSuccessMessage(`Verification code sent to ${maskedEmail}`);
                // Enable resend button cooldown
                setResendDisabled(true);
                setResendCountdown(60);
            } else {
                setError(result.error || 'Failed to send verification code. Please try again.');
            }
        } catch (err) {
            logger.error('Error sending OTP:', err);
            setError('An error occurred. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleOtpChange = (e: InputOtpChangeEvent) => {
        setOtp(e.value?.toString() || '');

        // Clear error when user is typing
        if (error) setError('');
    };

    const handleResendOtp = async () => {
        if (resendDisabled || !token || !isTokenValid) return;
        await handleSendOtp();
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (otp.length !== 6) {
            setError('Please enter all 6 digits of the verification code');
            return;
        }

        if (!token || !isTokenValid) {
            setError('Invalid verification token. Please try again.');
            return;
        }

        setIsLoading(true);
        setError('');

        try {
            const result = await verifyOtp(token, otp);
            if (result.success) {
                setSuccessMessage('Email verified successfully! Redirecting...');
                setTimeout(() => {
                    router.push(redirectPath);
                }, 1500);
            } else {
                setError(result.error || 'Invalid verification code. Please try again.');
            }
        } catch (err) {
            logger.error('Error verifying OTP:', err);
            setError('An error occurred. Please try again.');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <section
            className="py-12 md:py-20 bg-white dark:bg-[#0a0a14] relative overflow-hidden min-h-screen flex items-center animate-fadeIn">
            {/* Background elements with animation */}
            <div
                className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
            <div
                className="absolute bottom-0 left-0 w-1/3 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl animate-float"></div>

            <div className="max-w-7xl mx-auto px-6 relative z-10 w-full">
                <div className="flex flex-col lg:flex-row gap-12 items-center">
                    {/* Left side with branding/value prop */}
                    <div className="lg:w-1/2 max-w-lg animate-slideInLeft">
                        <div className="mb-8 flex items-center">
                            <Image
                                src="/assets/logos/purple-cloud-yellow-dots.svg"
                                alt="New Instance Logo"
                                width={50}
                                height={50}
                                className="mr-3 animate-pulse-subtle"
                            />
                            <span
                                className="text-2xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
                        </div>

                        <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold font-[family-name:var(--font-jakarta)] leading-tight mb-6 bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent animate-gradient">
                            Verify your email
                        </h1>

                        <p className="text-lg mb-8 text-[#4B4B4B] dark:text-[#C6C6C6] animate-fadeIn animation-delay-300">
                            We&#39;ve sent a 6-digit verification code to {maskedEmail ? maskedEmail : 'your email'}. Enter it below
                            to complete your account setup.
                        </p>

                        <div className="hidden lg:block">
                            <div className="flex gap-4 items-center mb-8 animate-fadeIn animation-delay-400">
                                <div
                                    className="w-12 h-12 bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                         fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                                         strokeLinejoin="round" className="text-[#6964D3]">
                                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                        <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-base">Email Verification</h3>
                                    <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Ensures the security of
                                        your account</p>
                                </div>
                            </div>

                            <div className="flex gap-4 items-center animate-fadeIn animation-delay-500">
                                <div
                                    className="w-12 h-12 bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-full flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                         fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                                         strokeLinejoin="round" className="text-[#BE9544]">
                                        <path
                                            d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                        <polyline points="22,6 12,13 2,6"></polyline>
                                    </svg>
                                </div>
                                <div>
                                    <h3 className="font-semibold text-base">Check Your Inbox</h3>
                                    <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Look for an email from New
                                        Instance</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right side with verification form */}
                    <div className="lg:w-1/2 w-full max-w-md animate-slideInRight">
                        <div
                            className="glass-card dark:glass-dark p-8 md:p-10 rounded-2xl shadow-xl border border-[#E0D7FF] dark:border-[#1e1e28] animate-pulse-subtle">

                            {/* Error and Success Messages */}
                            {error && (
                                <div
                                    className="mb-6 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30 text-red-700 dark:text-red-400 rounded-lg text-sm animate-shakeX">
                                    {error}
                                </div>
                            )}

                            {successMessage && (
                                <div
                                    className="mb-6 p-3 bg-green-100 dark:bg-green-900/20 border border-green-200 dark:border-green-800/30 text-green-700 dark:text-green-400 rounded-lg text-sm animate-bounceIn">
                                    {successMessage}
                                </div>
                            )}

                            {/* Form */}
                            <form onSubmit={handleSubmit}>
                                <div className="mb-8">
                                    <label className="block text-sm font-medium mb-4 text-center">Enter Verification
                                        Code</label>

                                    <div className="flex justify-center mb-4">
                                        <InputOtp
                                            value={otp}
                                            onChange={handleOtpChange}
                                            disabled={isLoading || !isTokenValid}
                                            length={6}
                                            error={error ? '' : undefined}
                                            className="animate-fadeIn"
                                        />
                                    </div>
                                </div>

                                <button
                                    type="submit"
                                    className={`w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white py-3.5 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all btn-hover-fx mb-4 ${isLoading || !isTokenValid ? 'opacity-70 cursor-not-allowed' : 'animate-pulse-subtle hover:animate-none'}`}
                                    disabled={isLoading || !isTokenValid}
                                >
                                    {isLoading ? (
                                        <div className="flex items-center justify-center">
                                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle className="opacity-25" cx="12" cy="12" r="10"
                                                        stroke="currentColor" strokeWidth="4"></circle>
                                                <path className="opacity-75" fill="currentColor"
                                                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            Verifying...
                                        </div>
                                    ) : 'Verify Code'}
                                </button>

                                <div className="text-center">
                                    <button
                                        type="button"
                                        onClick={handleResendOtp}
                                        disabled={resendDisabled || isLoading || !isTokenValid}
                                        className={`text-sm text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white transition-colors ${resendDisabled || !isTokenValid ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105 transition-transform'}`}
                                    >
                                        {resendDisabled && resendCountdown > 0
                                            ? `Resend code in ${resendCountdown}s`
                                            : 'Didn\'t receive a code? Resend'}
                                    </button>
                                </div>

                                <div
                                    className="mt-6 pt-6 border-t border-[#E0D7FF] dark:border-[#1e1e28] text-center animate-fadeIn animation-delay-500">
                                    <button
                                        type="button"
                                        onClick={() => router.push('/auth?mode=signup')}
                                        className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-[#424098] dark:hover:text-white transition-colors hover:scale-105 transition-transform"
                                    >
                                        Return to sign up
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}

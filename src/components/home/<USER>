'use client';

import Link from "next/link";
import { useState, useEffect } from "react";
import { getPublicPricingPlans } from "@/server/actions/pricing";
import { TimeUnit } from "@/constants/pricing";
import {capitalizeWords} from "@utils/helper";
import { logger } from '@/utils/logger';

// Define types for our pricing data
interface Feature {
  _id: string;
  name: string;
  description?: string;
}

interface Product {
  _id: string;
  name: string;
  description?: string;
  category?: 'core' | 'addon' | 'premium';
}

interface PricingPlan {
  _id: string;
  name: string;
  code: string;
  description?: string;
  products: Product[]; // Multiple products
  productItems?: Array<{
    productId: string;
    features: string[];
  }>; // Add this field for product-specific features
  amountUSD: number;
  amountNGN?: number;
  timeUnit: TimeUnit;
  duration: number;
  features: Feature[];
  isPerUser: boolean;
  active: boolean;
  discountPercentage?: number;
  discountActive?: boolean;
}

type PricingData = Awaited<ReturnType<typeof getPublicPricingPlans>>;



// Get the main product from a pricing plan
const getMainProduct = (plan: PricingPlan): Product | undefined => {
  // Use the first product from the products array
  if (plan.products && plan.products.length > 0) {
    return plan.products[0];
  }

  // If no products at all, return undefined
  return undefined;
};


// Sort plans by product category and price
const sortPlans = (plans: PricingPlan[]): PricingPlan[] => {
  return [...plans].sort((a, b) => {
    // Get the main products for comparison
    const productA = getMainProduct(a);
    const productB = getMainProduct(b);

    // First sort by product category if available
    if (productA?.category && productB?.category) {
      if (productA.category !== productB.category) {
        // core first, then addon, then premium
        const order: Record<string, number> = { core: 1, addon: 2, premium: 3 };
        return order[productA.category] - order[productB.category];
      }
    }

    // Then sort by price
    return a.amountUSD - b.amountUSD;
  });
};
export default function Pricing() {
  const [isYearly, setIsYearly] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pricingData, setPricingData] = useState<PricingData['data']>({
    monthly: [],
    yearly: [],
    products: [],
    yearlyDiscount: 0
  });

  // Fetch pricing data from the server
  useEffect(() => {
    const fetchPricing = async () => {
      setLoading(true);
      try {
        // Always provide the required timeUnit parameter
        const timeUnit = isYearly ? TimeUnit.YEAR : TimeUnit.MONTH;
        const response = await getPublicPricingPlans({ timeUnit });
        if (response.success && response.data) {
          setPricingData(response.data);
        } else {
          setError(response.error || 'Failed to load pricing data');
        }
      } catch (err) {
        logger.error('Error fetching pricing:', err);
        setError('An error occurred while loading pricing data');
      } finally {
        setLoading(false);
      }
    };

    fetchPricing();
  }, [isYearly]); // Re-fetch when the selected period changes

  // Get the plans to display based on selected period
  const plansToDisplay = isYearly ? pricingData?.yearly : pricingData?.monthly;

  // Sort plans by price (starter, professional, then premium)
  const sortedPlans = sortPlans(plansToDisplay || []);

  // Get all plans for display instead of just top 3
  const displayPlans = sortedPlans;

  // Format price based on currency and period
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };


  // Get period label (month or year)
  const periodLabel = isYearly ? 'year' : 'month';

  // Calculate discount percentage based on plan's settings
  const getDiscountText = (plan: PricingPlan): string | null => {
    // If the plan has an active discount, use that
    if (plan.discountActive && plan.discountPercentage) {
      return `Save ${plan.discountPercentage}%`;
    }

    // Otherwise, use the yearly discount percentage from the server
    if (isYearly && pricingData?.yearlyDiscount && pricingData.yearlyDiscount > 0) {
      return `Save ${pricingData.yearlyDiscount}%`;
    }

    return null;
  };

  // Handle switching between monthly and yearly plans
  const handlePeriodChange = (yearly: boolean) => {
    if (yearly !== isYearly) {
      setIsYearly(yearly);
      setLoading(true); // Will trigger a new fetch via useEffect
    }
  };

  // Generate the discount label text for the yearly toggle
  const getYearlyDiscountLabel = () => {
    if (pricingData?.yearlyDiscount && pricingData.yearlyDiscount > 0) {
      return <span className="text-[#BE9544] text-xs ml-1">(Save {pricingData.yearlyDiscount}%)</span>;
    }
    return null;
  };

  return (
    <section id="pricing" className="py-24 bg-white dark:bg-[#0a0a14] relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-6 relative z-10">
        <div className="text-center mb-16">
          <span className="inline-block bg-gradient-to-r from-[#F0DAB8]/30 to-[#D1AB66]/30 text-[#BE9544] px-4 py-1 rounded-full text-sm font-medium mb-4">
            Pricing
          </span>
          <h2 className="text-3xl md:text-4xl font-bold font-[family-name:var(--font-jakarta)] mb-4">
            Flexible plans for every business
          </h2>
          <p className="text-[#4B4B4B] dark:text-[#C6C6C6] max-w-2xl mx-auto">
            Pay only for what you use with our transparent, per-user pricing model
          </p>
        </div>

        {/* Pricing Toggle */}
        <div className="flex justify-center items-center mb-12">
          <span
            className={`text-sm font-medium mr-3 ${
              !isYearly ? 'text-[#6964D3] dark:text-[#8178E8]' : 'text-[#4B4B4B] dark:text-[#C6C6C6]'
            } cursor-pointer`}
            onClick={() => handlePeriodChange(false)}
          >
            Monthly
          </span>
          <div
            className={`w-14 h-7 bg-[#E0D7FF] dark:bg-[#1e1e28] rounded-full p-1 flex items-center cursor-pointer`}
            onClick={() => handlePeriodChange(!isYearly)}
          >
            <div
              className={`w-5 h-5 rounded-full bg-[#8178E8] transform duration-300 ease-in-out ${
                isYearly ? 'translate-x-7' : 'translate-x-0'
              }`}
            ></div>
          </div>
          <span
            className={`text-sm font-medium ml-3 ${
              isYearly ? 'text-[#6964D3] dark:text-[#8178E8]' : 'text-[#4B4B4B] dark:text-[#C6C6C6]'
            } cursor-pointer`}
            onClick={() => handlePeriodChange(true)}
          >
            Annual  {getYearlyDiscountLabel()}
          </span>
        </div>

        {loading ? (
          <div className="grid md:grid-cols-3 gap-8">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse bg-white dark:bg-[#1e1e28] rounded-2xl h-96"></div>
            ))}
          </div>
        ) : error ? (
          <div className="text-center text-red-500 dark:text-red-400">
            {error}
          </div>
        ) : (
          <div className="flex flex-wrap justify-center">
            {/* Calculate featured plan index - usually we want a middle plan in each row to be featured */}
            {/* For visual balance, we'll feature the 2nd plan in the first row when there are 3+ plans */}
            {displayPlans.map((plan, index) => {

              // Design logic: Feature the 2nd plan when there are 3+ plans
              const featuredPlanIndex = 1; // Always highlight the second plan for consistency
              const isProfessional = index === featuredPlanIndex;

              // Get any applicable discount text
              const discountText = getDiscountText(plan);

              // Get the main product for category styling
              const mainProduct = getMainProduct(plan);
              const category = mainProduct?.category || 'core';

              // Determine if this is the premium tier for styling
              const isPremium = category === 'premium' || (displayPlans.length >= 3 && index === 2);

              // Responsive width classes based on number of plans
              const totalPlans = displayPlans.length;
              let widthClasses = "w-full sm:w-2/3 md:w-1/2 lg:w-1/3 xl:w-1/4 px-4 mb-8";

              // For 3 plans, make them equal width and the middle one slightly larger
              if (totalPlans === 3) {
                widthClasses = "w-full md:w-1/3 px-4 mb-8";
              }
              // For 4 plans, make them all equal width
              else if (totalPlans === 4) {
                widthClasses = "w-full sm:w-1/2 lg:w-1/4 px-4 mb-8";
              }

              return (
                <div key={plan._id} className={widthClasses}>
                  <div
                    className={`relative h-full ${
                      isProfessional 
                        ? 'bg-gradient-to-b from-white to-[#F9F8FF] dark:from-[#1e1e28] dark:to-[#252444] rounded-2xl shadow-xl border-2 border-[#B2A5FF] dark:border-[#6964D3] md:scale-105 md:-my-4 z-10'
                        : 'bg-white dark:bg-[#1e1e28] rounded-2xl shadow-lg border border-[#E0D7FF] dark:border-[#1e1e28]'
                    } overflow-hidden hover-card flex flex-col`}
                  >
                    {isProfessional && (
                      <div className="absolute top-0 left-0 w-full bg-[#8178E8] text-white py-1.5 text-xs font-medium text-center">
                        MOST POPULAR
                      </div>
                    )}
                    {plan.discountActive && plan.discountPercentage && (
                      <div
                        className={`absolute top-0 right-0 ${
                          isProfessional ? 'mt-7' : 'mt-0'
                        } bg-[#BE9544] text-white px-2 py-1 text-xs font-medium rounded-bl-lg`}
                      >
                        {plan.discountPercentage}% OFF
                      </div>
                    )}
                    <div className={`p-8 ${isProfessional ? 'pt-12' : ''} flex-grow`}>
                      <h3 className="text-xl font-semibold mb-2 font-[family-name:var(--font-jakarta)]">
                        {capitalizeWords(plan.name)}
                      </h3>
                      <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mb-4">{plan.description}</p>
                      <div className="mb-6">
                        <span className="text-4xl font-bold">{formatPrice(plan.amountUSD)}</span>
                        <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">
                          {plan.isPerUser ? '/user' : ''}/{periodLabel}
                        </span>
                        {discountText && (
                          <span className="ml-2 inline-block bg-[#F0DAB8]/30 text-[#BE9544] px-2 py-0.5 rounded-full text-xs font-medium">
                            {discountText}
                          </span>
                        )}
                      </div>
                      <div className="space-y-6 mt-6">
                        {/* Group features by product */}
                        {plan.productItems && plan.productItems.map((productItem: {productId: string; features: string[]}, productIndex: number) => {
                          // Find the corresponding product
                          const product = plan.products?.find(p =>
                            String(p._id) === String(productItem.productId)
                          );

                          if (!product) return null;

                          // Get features for this product
                          const productFeatures = plan.features?.filter(feature =>
                            productItem.features.some((featureId: string) =>
                              String(feature._id) === String(featureId)
                            )
                          );

                          if (!productFeatures || productFeatures.length === 0) return null;

                          return (
                            <div key={productIndex} className="space-y-3">
                              {/* Product name as header */}
                              <h4 className={`text-base font-semibold ${
                                isPremium 
                                  ? 'text-[#BE9544] dark:text-[#D1AB66]'
                                  : 'text-[#6964D3] dark:text-[#8178E8]'
                              } border-b border-dashed ${
                                isPremium 
                                  ? 'border-[#BE9544]/30 dark:border-[#D1AB66]/30'
                                  : 'border-[#6964D3]/30 dark:border-[#8178E8]/30'
                              } pb-1 mb-2`}>
                                {product.name}
                              </h4>
                              <div className="pl-2">
                                {productFeatures.map((feature, featureIndex) => (
                                  <div key={featureIndex} className="flex items-start mb-2.5">
                                    <div className={`w-5 h-5 rounded-full ${
                                      isPremium
                                        ? 'bg-[#F0DAB8]/50 dark:bg-[#BE9544]/20' 
                                        : 'bg-[#E0D7FF] dark:bg-[#6964D3]/20'
                                    } flex items-center justify-center mt-0.5 mr-3 flex-shrink-0`}>
                                      <svg className={`w-3 h-3 ${
                                        isPremium ? 'text-[#BE9544]' : 'text-[#6964D3]'
                                      }`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd"></path>
                                      </svg>
                                    </div>
                                    <span className="text-sm text-[#4B4B4B] dark:text-[#C6C6C6]">{feature.name}</span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          );
                        })}
                         <Link
                        href={`/auth?mode=signup&plan=${plan.code}`}
                        className={`!mb-[30px] block text-center ${
                          isProfessional
                            ? 'bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white hover:shadow-lg btn-hover-fx'
                            : isPremium 
                              ? 'bg-gradient-to-r from-[#D1AB66] to-[#BE9544] text-white hover:shadow-lg btn-hover-fx'
                              : 'bg-[#F3F3F3] dark:bg-[#0f0f18] text-[#6964D3] dark:text-[#B2A5FF] hover:bg-[#E0D7FF] dark:hover:bg-[#6964D3]/20'
                        } py-3 rounded-xl font-medium transition-all w-full mb-6`}
                      >
                        Start Free Trial
                      </Link>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Pro-rate Notice */}
        <div className="mt-12 text-center">
          <div className="inline-block bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-lg px-4 py-3 max-w-xl">
            <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
              All plans include pro-rated billing, allowing you to add users anytime during your billing cycle and only pay for what you use.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

import Link from "next/link";

export default function CTA() {
  return (
    <section className="py-24 bg-white dark:bg-[#0a0a14] relative overflow-hidden">
      {/* Background gradient blobs */}
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-br from-[#E0D7FF]/30 to-transparent rounded-full blur-3xl"></div>
      <div className="absolute -bottom-40 -right-40 w-80 h-80 bg-gradient-to-tr from-[#F0DAB8]/20 to-transparent rounded-full blur-3xl"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-6">
        <div className="bg-gradient-to-br from-[#6964D3]/10 to-[#BE9544]/10 dark:from-[#6964D3]/20 dark:to-[#BE9544]/20 rounded-3xl p-10 md:p-16 overflow-hidden relative">
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-[#8178E8]/40 to-transparent rounded-full blur-2xl"></div>
          <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-[#BE9544]/30 to-transparent rounded-full blur-2xl"></div>

          <div className="relative z-10 flex flex-col md:flex-row items-center justify-between gap-10">
            <div className="max-w-2xl">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 font-[family-name:var(--font-jakarta)]">
                Ready to transform your operations?
              </h2>
              <p className="text-[#4B4B4B] dark:text-[#C6C6C6] mb-8 text-lg">
                Join thousands of companies using our platform to streamline their infrastructure. Start your free trial today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/auth?mode=signup"
                  className="btn-primary text-center"
                >
                  Start Free Trial
                </Link>
                <Link
                  href="/contact"
                  className="btn-secondary text-center"
                >
                  Contact Sales
                </Link>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-md p-6 rounded-xl">
                <div className="text-3xl md:text-4xl font-bold text-[#6964D3] mb-2">97%</div>
                <div className="text-sm text-[#4B4B4B] dark:text-[#C6C6C6]">Customer satisfaction rate</div>
              </div>
              <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-md p-6 rounded-xl">
                <div className="text-3xl md:text-4xl font-bold text-[#BE9544] mb-2">45%</div>
                <div className="text-sm text-[#4B4B4B] dark:text-[#C6C6C6]">Reduction in cloud costs</div>
              </div>
              <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-md p-6 rounded-xl">
                <div className="text-3xl md:text-4xl font-bold text-[#6964D3] mb-2">3x</div>
                <div className="text-sm text-[#4B4B4B] dark:text-[#C6C6C6]">Deployment speed increase</div>
              </div>
              <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-md p-6 rounded-xl">
                <div className="text-3xl md:text-4xl font-bold text-[#BE9544] mb-2">24/7</div>
                <div className="text-sm text-[#4B4B4B] dark:text-[#C6C6C6]">Expert support available</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

import Link from "next/link";
import HeroSvg from "./HeroSvg";

export default function Hero() {
  return (
    <section className="pt-24 pb-32 relative overflow-hidden">
      {/* Background blobs */}
      <div className="absolute top-[-300px] right-[-200px] w-[600px] h-[600px] rounded-full bg-[#E0D7FF]/30 blur-3xl"></div>
      <div className="absolute bottom-[-200px] left-[-100px] w-[400px] h-[400px] rounded-full bg-[#F0DAB8]/20 blur-3xl"></div>
      
      <div className="max-w-7xl mx-auto px-6 relative z-10">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <div className="inline-block bg-gradient-to-r from-[#8178E8]/20 to-[#998EF8]/20 px-4 py-2 rounded-full mb-6">
              <span className="text-sm font-medium bg-gradient-to-r from-[#8178E8] to-[#998EF8] bg-clip-text text-transparent">
                Multi-Service Business Platform
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold font-[family-name:var(--font-jakarta)] leading-tight mb-6 bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent">
              Elevate Your <br />Business Operations
            </h1>
            <p className="text-lg mb-8 text-[#4B4B4B] dark:text-[#C6C6C6] max-w-lg">
              An intelligent multi-service platform engineered to streamline your business with next-generation 
              customer support and advanced error analytics.
            </p>
            <div className="flex flex-wrap gap-4">
              <Link 
                href="/sign-up" 
                className="bg-gradient-to-r from-[#8C82F6] to-[#6964D3] text-white px-8 py-3.5 rounded-full font-medium shadow-lg hover:shadow-xl transition-all btn-hover-fx"
              >
                Start Free Trial
              </Link>
              <Link 
                href="#demo" 
                className="bg-white dark:bg-[#1e1e28] border border-[#8C82F6] text-[#6964D3] px-8 py-3.5 rounded-full font-medium shadow hover:shadow-md transition-all"
              >
                Book Demo
              </Link>
            </div>
          </div>
          <div className="relative h-[400px] flex items-center justify-center">
            {/* Main illustration */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-[300px] h-[300px] rounded-full bg-[#8C82F6]/20 blur-3xl absolute"></div>
              <div className="relative z-10 w-full h-full flex items-center justify-center">
                <HeroSvg />
              </div>
            </div>

            {/* Floating elements */}
            <div className="absolute top-1/4 left-10 glass-card p-3 rounded-xl shadow-lg animate-float">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                <span className="text-xs font-medium">Live Support</span>
              </div>
            </div>
            <div className="absolute bottom-1/4 right-10 glass-card p-3 rounded-xl shadow-lg animate-float delay-200">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-[#D1AB66] rounded-full"></div>
                <span className="text-xs font-medium">Error Analytics</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 
import React from 'react';

export default function HeroSvg() {
  return (
    <svg
      width="450"
      height="350"
      viewBox="0 0 450 350"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="relative z-10 w-full h-full"
    >
      {/* Main Cloud Platform */}
      <g filter="url(#filter0_d)">
        <path
          d="M225 50C128.5 50 110 120 110 180C110 240 170 280 225 280C280 280 340 240 340 180C340 120 321.5 50 225 50Z"
          fill="url(#paint0_linear)"
        />
      </g>

      {/* Upper Cloud with Pulse Effect */}
      <g className="animate-pulse">
        <path
          d="M165 100C147.5 100 140 110 140 125C140 140 150 150 165 150C180 150 190 140 190 125C190 110 182.5 100 165 100Z"
          fill="url(#paint1_linear)"
          fillOpacity="0.8"
        />
      </g>

      {/* Middle Cloud with Different Animation */}
      <g className="animate-bounce" style={{ animationDuration: '3s' }}>
        <path
          d="M285 130C272.5 130 265 140 265 150C265 160 275 170 285 170C295 170 305 160 305 150C305 140 297.5 130 285 130Z"
          fill="url(#paint2_linear)"
          fillOpacity="0.8"
        />
      </g>

      {/* Lower Supporting Cloud */}
      <path
        d="M225 220C207.5 220 200 230 200 245C200 260 210 270 225 270C240 270 250 260 250 245C250 230 242.5 220 225 220Z"
        fill="url(#paint3_linear)"
        fillOpacity="0.6"
      />

      {/* Support Chat Icon */}
      <g filter="url(#filter1_d)">
        <circle cx="180" cy="170" r="25" fill="white" />
        <path
          d="M192 177H182L176 183V177H172C170.9 177 170 176.1 170 175V163C170 161.9 170.9 161 172 161H192C193.1 161 194 161.9 194 163V175C194 176.1 193.1 177 192 177Z"
          fill="#8178E8"
        />
      </g>

      {/* Error Logging Icon */}
      <g filter="url(#filter2_d)">
        <circle cx="270" cy="170" r="25" fill="white" />
        <path
          d="M262 170L278 170M270 162L270 178"
          stroke="#D1AB66"
          strokeWidth="3"
          strokeLinecap="round"
        />
      </g>

      {/* Connection Lines */}
      <path
        d="M180 170L225 150L270 170"
        stroke="url(#paint4_linear)"
        strokeDasharray="4 4"
        strokeWidth="2"
      />

      {/* Data Flow Circles - Will be animated */}
      <circle className="animate-ping" cx="190" cy="165" r="3" fill="#8178E8" fillOpacity="0.8" />
      <circle className="animate-ping" cx="215" cy="155" r="3" fill="#8178E8" fillOpacity="0.8" style={{ animationDelay: "0.5s" }} />
      <circle className="animate-ping" cx="240" cy="160" r="3" fill="#D1AB66" fillOpacity="0.8" style={{ animationDelay: "1s" }} />
      <circle className="animate-ping" cx="260" cy="165" r="3" fill="#D1AB66" fillOpacity="0.8" style={{ animationDelay: "1.5s" }} />

      {/* Subtle Decoration Elements */}
      <circle cx="140" cy="200" r="5" fill="#E0D7FF" fillOpacity="0.5" />
      <circle cx="310" cy="200" r="5" fill="#F0DAB8" fillOpacity="0.5" />
      <circle cx="225" cy="90" r="7" fill="#CABDFF" fillOpacity="0.5" />

      {/* Connection Dots Pattern */}
      <circle cx="160" cy="220" r="2" fill="white" fillOpacity="0.7" />
      <circle cx="170" cy="230" r="2" fill="white" fillOpacity="0.7" />
      <circle cx="180" cy="240" r="2" fill="white" fillOpacity="0.7" />
      <circle cx="270" cy="240" r="2" fill="white" fillOpacity="0.7" />
      <circle cx="280" cy="230" r="2" fill="white" fillOpacity="0.7" />
      <circle cx="290" cy="220" r="2" fill="white" fillOpacity="0.7" />

      {/* Definitions for gradients and filters */}
      <defs>
        <filter id="filter0_d" x="90" y="40" width="270" height="270" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
          <feOffset dy="10" />
          <feGaussianBlur stdDeviation="10" />
          <feColorMatrix type="matrix" values="0 0 0 0 0.506 0 0 0 0 0.471 0 0 0 0 0.91 0 0 0 0.4 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
        <filter id="filter1_d" x="150" y="144" width="60" height="60" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2.5" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
        <filter id="filter2_d" x="240" y="144" width="60" height="60" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="2.5" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape" />
        </filter>
        <linearGradient id="paint0_linear" x1="110" y1="50" x2="340" y2="280" gradientUnits="userSpaceOnUse">
          <stop stopColor="#8C82F6" />
          <stop offset="1" stopColor="#524C90" />
        </linearGradient>
        <linearGradient id="paint1_linear" x1="140" y1="100" x2="190" y2="150" gradientUnits="userSpaceOnUse">
          <stop stopColor="#B2A5FF" />
          <stop offset="1" stopColor="#6964D3" />
        </linearGradient>
        <linearGradient id="paint2_linear" x1="265" y1="130" x2="305" y2="170" gradientUnits="userSpaceOnUse">
          <stop stopColor="#B2A5FF" />
          <stop offset="1" stopColor="#6964D3" />
        </linearGradient>
        <linearGradient id="paint3_linear" x1="200" y1="220" x2="250" y2="270" gradientUnits="userSpaceOnUse">
          <stop stopColor="#F0DAB8" />
          <stop offset="1" stopColor="#BE9544" />
        </linearGradient>
        <linearGradient id="paint4_linear" x1="180" y1="160" x2="270" y2="160" gradientUnits="userSpaceOnUse">
          <stop stopColor="#8C82F6" />
          <stop offset="0.5" stopColor="#CABDFF" />
          <stop offset="1" stopColor="#D1AB66" />
        </linearGradient>
      </defs>
    </svg>
  );
} 
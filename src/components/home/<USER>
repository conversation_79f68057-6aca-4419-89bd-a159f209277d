import Image from "next/image";
import Link from "next/link";

export default function Services() {
  return (
    <section id="services" className="py-24 bg-gradient-to-b from-[#F3F3F3] to-white dark:from-[#0f0f18] dark:to-[#0a0a14]">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <span className="inline-block bg-[#F0DAB8]/30 text-[#BE9544] px-4 py-1 rounded-full text-sm font-medium mb-4">
            Services
          </span>
          <h2 className="text-3xl md:text-4xl font-bold font-[family-name:var(--font-jakarta)] mb-4">
            Powerful integrations for any business
          </h2>
          <p className="text-[#4B4B4B] dark:text-[#C6C6C6] max-w-2xl mx-auto">
            Deploy our solution across your digital ecosystem with minimal integration effort
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Service 1 */}
          <div className="relative">
            <div className="absolute -top-8 -left-8 w-64 h-64 bg-[#E0D7FF]/30 dark:bg-[#424098]/20 rounded-full blur-3xl"></div>
            <div className="relative z-10 bg-white dark:bg-[#1e1e28] p-8 rounded-2xl shadow-xl border border-[#E0D7FF] dark:border-[#424098]/20">
              <div className="flex justify-between items-start mb-6">
                <h3 className="text-2xl font-semibold font-[family-name:var(--font-jakarta)] text-[#424098] dark:text-[#B2A5FF]">
                  Full-Page Widget
                </h3>
                <Image 
                  src="/assets/logos/purple-cloud-yellow-dots.svg" 
                  alt="Widget logo" 
                  width={48} 
                  height={48}
                />
              </div>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-8">
                A comprehensive interface where representatives can interact with clients,
                delivered as an iframe that can be embedded on any website.
              </p>
              <div className="glass-card dark:glass-dark p-6 rounded-xl mb-8">
                <div className="flex gap-3 mb-3">
                  <div className="w-3 h-3 bg-[#F0777C] rounded-full"></div>
                  <div className="w-3 h-3 bg-[#F0DA78] rounded-full"></div>
                  <div className="w-3 h-3 bg-[#7BF078] rounded-full"></div>
                </div>
                <div className="h-40 w-full bg-[#0a0a14] dark:bg-[#0f0f18] rounded-lg flex items-center justify-center">
                  <span className="text-xs text-white/50">Representative Interface Preview</span>
                </div>
              </div>
              <Link 
                href="#demo" 
                className="text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white font-medium flex items-center transition-colors"
              >
                <span>View Documentation</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2">
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </Link>
            </div>
          </div>
          
          {/* Service 2 */}
          <div className="relative">
            <div className="absolute -top-8 -right-8 w-64 h-64 bg-[#F0DAB8]/30 dark:bg-[#BE9544]/20 rounded-full blur-3xl"></div>
            <div className="relative z-10 bg-white dark:bg-[#1e1e28] p-8 rounded-2xl shadow-xl border border-[#F0DAB8] dark:border-[#BE9544]/20">
              <div className="flex justify-between items-start mb-6">
                <h3 className="text-2xl font-semibold font-[family-name:var(--font-jakarta)] text-[#BE9544]">
                  Chat Bubble Widget
                </h3>
                <Image 
                  src="/assets/logos/white-cloud-gradient-purple.svg" 
                  alt="Widget logo" 
                  width={48} 
                  height={48}
                />
              </div>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-8">
                A lightweight embeddable widget that can be integrated into any customer-facing
                application, enabling end users to initiate real-time support chats.
              </p>
              <div className="glass-card dark:glass-dark p-6 rounded-xl mb-8 relative">
                <div className="h-40 w-full rounded-lg">
                  <div className="absolute bottom-10 right-10 bg-[#6964D3] w-14 h-14 rounded-full shadow-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                  </div>
                  <div className="absolute bottom-28 right-10 bg-white dark:bg-[#1e1e28] w-64 rounded-lg shadow-xl p-4 opacity-0 hover:opacity-100 transition-opacity">
                    <div className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">Chat Preview</div>
                  </div>
                </div>
              </div>
              <Link 
                href="#demo" 
                className="text-[#BE9544] hover:text-[#A88027] dark:hover:text-white font-medium flex items-center transition-colors"
              >
                <span>View Documentation</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="ml-2">
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 
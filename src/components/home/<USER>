export default function Features() {
  return (
    <section id="features" className="py-24 bg-white dark:bg-[#0a0a14]">
      <div className="max-w-7xl mx-auto px-6">
        <div className="text-center mb-16">
          <span className="inline-block bg-[#E0D7FF] dark:bg-[#6964D3]/30 text-[#6964D3] dark:text-[#B2A5FF] px-4 py-1 rounded-full text-sm font-medium mb-4">
            Features
          </span>
          <h2 className="text-3xl md:text-4xl font-bold font-[family-name:var(--font-jakarta)] mb-4">
            Designed for the modern enterprise
          </h2>
          <p className="text-[#4B4B4B] dark:text-[#C6C6C6] max-w-2xl mx-auto">
            Powerful tools built to streamline your operations and enhance customer experiences
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {/* Feature 1 */}
          <div className="group bg-[#F3F3F3] dark:bg-[#1e1e28] p-8 rounded-2xl hover:shadow-xl transition-all hover:-translate-y-1">
            <div className="w-14 h-14 bg-gradient-to-br from-[#B2A5FF] to-[#8C82F6] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 font-[family-name:var(--font-jakarta)]">
              Customer Support Module
            </h3>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
              Real-time chat, intelligent ticketing system, and comprehensive agent performance analytics.
            </p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                AI-powered conversation routing
              </li>
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Multi-channel support integration
              </li>
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Automated customer sentiment analysis
              </li>
            </ul>
          </div>
          
          {/* Feature 2 */}
          <div className="group bg-[#F3F3F3] dark:bg-[#1e1e28] p-8 rounded-2xl hover:shadow-xl transition-all hover:-translate-y-1">
            <div className="w-14 h-14 bg-gradient-to-br from-[#B2A5FF] to-[#8C82F6] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="m21 2-2 2m-3 3-2 2m-3 3-2 2m-3 3-2 2"></path><path d="M10 8V6a2 2 0 1 0-4 0v2"></path><path d="M18 11V9a2 2 0 1 0-4 0v2"></path><path d="M21 16v-3a2 2 0 0 0-2-2h-1"></path><path d="M3 16v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-4"></path><path d="M14 16v.01"></path></svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 font-[family-name:var(--font-jakarta)]">
              Error Logging Service
            </h3>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
              Comprehensive error tracking with predictive analytics and automated resolution workflows.
            </p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Real-time error detection & alerting
              </li>
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Pattern recognition with ML
              </li>
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Automated troubleshooting suggestions
              </li>
            </ul>
          </div>
          
          {/* Feature 3 */}
          <div className="group bg-[#F3F3F3] dark:bg-[#1e1e28] p-8 rounded-2xl hover:shadow-xl transition-all hover:-translate-y-1">
            <div className="w-14 h-14 bg-gradient-to-br from-[#B2A5FF] to-[#8C82F6] rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect width="18" height="18" x="3" y="3" rx="2"></rect><path d="M3 9h18"></path><path d="M9 21V9"></path></svg>
            </div>
            <h3 className="text-xl font-semibold mb-3 font-[family-name:var(--font-jakarta)]">
              Modular Architecture
            </h3>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
              Infinitely extensible platform with the ability to add customized service modules on demand.
            </p>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Microservices-based architecture
              </li>
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                API-first development approach
              </li>
              <li className="flex items-center text-[#4B4B4B] dark:text-[#C6C6C6]">
                <span className="text-[#8C82F6] mr-2">✓</span>
                Custom module development
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
} 
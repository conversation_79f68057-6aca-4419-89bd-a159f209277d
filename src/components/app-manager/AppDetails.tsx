'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { getAppDetails } from '@/server/actions/app-manager';
import { RegenerateAppSecretDialog } from './RegenerateAppSecretDialog';
import ComprehensiveAppDeleteDialog from './ComprehensiveAppDeleteDialog';
import { Dialog } from '../ui/Dialog';
import { logger } from '@/utils/logger';

interface AppDetailsProps {
  organizationId: string;
  appId: string;
}

interface AppDetail {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
  modules: string[];
  apiKey: string;
  createdAt: string;
  updatedAt: string;
}

export default function AppDetails({ organizationId, appId }: AppDetailsProps) {
  const { success, error } = useToast();
  const [rotateKeyDialogVisible, setRotateKeyDialogVisible] = useState(false);
  const [comprehensiveDeleteDialogVisible, setComprehensiveDeleteDialogVisible] = useState(false);
  const [regenerateSecretDialogVisible, setRegenerateSecretDialogVisible] = useState(false);
  const [appDetails, setAppDetails] = useState<AppDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchAppDetails();
  }, [appId]);

  const fetchAppDetails = async () => {
    setIsLoading(true);
    try {
      const response = await getAppDetails(appId);
      if (response.success && response.data) {
        setAppDetails(response.data);
      } else {
        error('Error', response.error || 'Failed to fetch app details');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text);
    success('Copied', message);
  };

  const handleRotateKey = () => {
    // In a real app, you would call the API to rotate the key
    setRotateKeyDialogVisible(false);
    success('API Key Rotated', 'Your new API key has been generated');
    // Refresh app details
    fetchAppDetails();
  };

  const handleDeleteSuccess = () => {
    // Redirect to apps list after successful deletion
    window.location.href = `/dashboard/organization/${organizationId}/app-manager/apps`;
  };

  const handleDeleteClose = () => {
    setComprehensiveDeleteDialogVisible(false);
  };

  return (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      ) : !appDetails ? (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-800 dark:text-red-300 text-sm">
            <i className="pi pi-exclamation-triangle mr-2"></i>
            {"App not found or you don't have permission to view it."}
          </p>
        </div>
      ) : (
        <>
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold">{appDetails.name}</h1>
              <div className="flex items-center mt-1">
                <span className="text-sm text-gray-500 dark:text-gray-400 font-mono">{appId}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-1 p-1 h-auto"
                  onClick={() => copyToClipboard(appId, 'App ID copied to clipboard')}
                >
                  <i className="pi pi-copy text-xs"></i>
                </Button>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                className="text-yellow-600 border-yellow-300 hover:bg-yellow-50 dark:text-yellow-400 dark:border-yellow-800 dark:hover:bg-yellow-900/20"
                onClick={() => setRotateKeyDialogVisible(true)}
              >
                <i className="pi pi-refresh mr-2"></i>
                Rotate API Key
              </Button>
              <Button
                variant="outline"
                className="text-blue-600 border-blue-300 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-800 dark:hover:bg-blue-900/20"
                onClick={() => setRegenerateSecretDialogVisible(true)}
              >
                <i className="pi pi-key mr-2"></i>
                Regenerate App Secret
              </Button>
              <Button
                variant="outline"
                className="text-red-600 border-red-300 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                onClick={() => setComprehensiveDeleteDialogVisible(true)}
              >
                <i className="pi pi-trash mr-2"></i>
                Delete App
              </Button>
            </div>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <p className="text-yellow-800 dark:text-yellow-300 text-sm">
              <i className="pi pi-info-circle mr-2"></i>
              We never store AppSecret. Re-enter in Settings if lost.
            </p>
          </div>
        </>
      )}

      {!isLoading && appDetails && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {appDetails.modules.includes('secret-key-manager') && (
            <Link
              href={`/dashboard/organization/${organizationId}/app-manager/apps/${appId}/secret-keys`}
              className="block"
            >
              <Card className="h-full p-6 hover:shadow-md transition-shadow">
                <div className="flex flex-col items-center text-center">
                  <i className="pi pi-key text-3xl text-indigo-600 dark:text-indigo-400 mb-4"></i>
                  <h3 className="text-lg font-medium mb-2">Secret Key Manager</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Manage encrypted environment variables
                  </p>
                </div>
              </Card>
            </Link>
          )}

          { (
            <Link
              href={`/dashboard/organization/${organizationId}/app-manager/apps/${appId}/bug-watch`}
              className="block"
            >
              <Card className="h-full p-6 hover:shadow-md transition-shadow">
                <div className="flex flex-col items-center text-center">
                  <i className="pi pi-exclamation-circle text-3xl text-indigo-600 dark:text-indigo-400 mb-4"></i>
                  <h3 className="text-lg font-medium mb-2">Bug Watch</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Bug and Logs Management System
                  </p>
                </div>
              </Card>
            </Link>
          )}

          {(!appDetails.modules || appDetails.modules.length === 0) && (
            <div className="col-span-2 bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No modules enabled</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {"This app doesn't have any modules enabled yet."}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Rotate API Key Dialog */}
      <Dialog
        open={rotateKeyDialogVisible}
        onOpenChange={setRotateKeyDialogVisible}
        title="Rotate API Key"
        footer={
          <div className="flex justify-end space-x-2">
            <Button variant="ghost" onClick={() => setRotateKeyDialogVisible(false)}>Cancel</Button>
            <Button variant="primary" onClick={handleRotateKey}>Rotate Key</Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-300">
            Are you sure you want to rotate the API key for this app? The current key will be invalidated immediately.
          </p>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <p className="text-yellow-800 dark:text-yellow-300 text-sm">
              <i className="pi pi-exclamation-triangle mr-2"></i>
              <strong>Warning:</strong> This action cannot be undone. All services using the current API key will need to be updated.
            </p>
          </div>
        </div>
      </Dialog>

      {/* ✅ COMPREHENSIVE APP DELETION DIALOG */}
      <ComprehensiveAppDeleteDialog
        app={appDetails ? {
          id: appId,
          name: appDetails.name,
          description: appDetails.description
        } : null}
        organizationId={organizationId}
        isOpen={comprehensiveDeleteDialogVisible}
        onClose={handleDeleteClose}
        onSuccess={handleDeleteSuccess}
      />

      {/* Regenerate App Secret Dialog */}
      <RegenerateAppSecretDialog
        open={regenerateSecretDialogVisible}
        onOpenChange={setRegenerateSecretDialogVisible}
        appId={appId}
        organizationId={organizationId}
      />
    </div>
  );
}

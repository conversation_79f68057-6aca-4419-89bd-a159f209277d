'use client';

import { useState } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';
import { useToast } from '@/components/ui/Toast';
import { generateAppSecret, generateKeyCheck } from '@/utils/encryption';
import { generateAndEncryptMEK } from '@/utils/master-key-manager';
import { updateKeyCheck } from '@/server/actions/app-manager';

interface RegenerateAppSecretDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  appId: string;
  organizationId: string;
}

/**
 * Dialog component for regenerating an App Secret
 */
export function RegenerateAppSecretDialog({
  open,
  onOpenChange,
  appId,
  organizationId
}: RegenerateAppSecretDialogProps) {
  const { success, error } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [newAppSecret, setNewAppSecret] = useState<string | null>(null);
  const [step, setStep] = useState<'confirm' | 'generated'>('confirm');

  const handleRegenerate = async () => {
    try {
      setIsLoading(true);

      // Generate a new App Secret
      const appSecret = generateAppSecret();

      // Generate a new keyCheck value
      const keyCheck = await generateKeyCheck(appId, appSecret);

      // FRONTEND-ONLY: Generate and encrypt new MEK with new App Secret
      const mekData = await generateAndEncryptMEK(appSecret, appId);

      // Update the keyCheck value and store new frontend-generated MEK on the server
      const response = await updateKeyCheck({
        appId,
        keyCheck,
        organizationId,
        // Frontend-generated MEK fields (Three-Key Model)
        encryptedMasterKey: mekData.encryptedMasterKey,
        masterKeyIV: mekData.masterKeyIV,
        masterKeyTag: mekData.masterKeyTag
      });

      if (response.success) {
        setNewAppSecret(appSecret);
        setStep('generated');
        success('Success', 'App Secret Key have been regenerated successfully');
      } else {
        error('Error', response.error || 'Failed to regenerate App Secret');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    success('Copied', 'App Secret copied to clipboard');
  };

  const handleClose = () => {
    setNewAppSecret(null);
    setStep('confirm');
    onOpenChange(false);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          handleClose();
        } else {
          onOpenChange(isOpen);
        }
      }}
      title={step === 'confirm' ? 'Regenerate App Secret' : 'New App Secret Generated'}
      footer={
        step === 'confirm' ? (
          <div className="flex justify-end space-x-2">
            <Button variant="ghost" onClick={handleClose} disabled={isLoading}>Cancel</Button>
            <Button
              variant="destructive"
              onClick={handleRegenerate}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin mr-2">
                    <i className="pi pi-spinner"></i>
                  </span>
                  Regenerating...
                </>
              ) : (
                'Regenerate App Secret'
              )}
            </Button>
          </div>
        ) : (
          <div className="flex justify-end space-x-2">
            <Button onClick={handleClose}>Done</Button>
          </div>
        )
      }
    >
      {step === 'confirm' && (
        <div className="space-y-4">
          <Alert severity="warn">
            <div className="font-medium">Warning: This action cannot be undone</div>
            <p className="mt-1">
              Regenerating your App Secret will invalidate the previous secret. All applications using the old App Secret will need to be updated.
            </p>
          </Alert>

          <p className="text-sm text-gray-600 dark:text-gray-300">
            Your App Secret is used to encrypt environment variables and authenticate API requests. Make sure to update all applications using this App Secret after regeneration.
          </p>
        </div>
      )}

      {step === 'generated' && newAppSecret && (
        <div className="space-y-4">
          <Alert severity="success">
            <div className="font-medium">App Secret Regenerated Successfully</div>
            <p className="mt-1">
              Your new App Secret has been generated. Make sure to save it securely as it will not be shown again.
            </p>
          </Alert>

          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md">
            <div className="flex justify-between items-center">
              <code className="text-sm font-mono break-all">{newAppSecret}</code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(newAppSecret)}
              >
                <i className="pi pi-copy"></i>
              </Button>
            </div>
          </div>

          <p className="text-sm text-gray-600 dark:text-gray-300">
            Remember to update all applications using this App Secret. Your App Secret is never stored on our servers and cannot be recovered if lost.
          </p>
        </div>
      )}
    </Dialog>
  );
}

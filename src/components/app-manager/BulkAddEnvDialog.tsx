'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';
import CodeEditor from '@uiw/react-textarea-code-editor';
import { EnvVarType, EnvironmentVariable } from '@/types/environment-variables';
import { decryptValueWithMEK } from '@/utils/encryption';
import { logger } from '@/utils/logger';

interface BulkAddEnvDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddEnvs: (envs: BulkEnvFormData) => void;
  appSecret: string;
  appId: string; // App ID for MEK-based encryption
  organizationId: string; // Organization ID for server actions
  existingEnvs?: EnvironmentVariable[]; // Optional list of existing environment variables for editing
  cachedMasterKey?: CryptoK<PERSON> | null; // Pre-decrypted MEK from parent component
}

export interface BulkEnvFormData {
  envText: string;
  parsedEnvs: {
    key: string;
    value: string;
    type: EnvVarType;
  }[];
  existingEnvIds?: string[]; // IDs of existing environment variables being edited
  isEdit?: boolean; // Flag to indicate if this is an edit operation
}

/**
 * Dialog component for bulk adding or editing environment variables
 */
export function BulkAddEnvDialog({
  open,
  onOpenChange,
  onAddEnvs,
  appSecret,
  appId,
  organizationId,
  existingEnvs = [],
  cachedMasterKey = null
}: BulkAddEnvDialogProps) {
  // State
  const [parseError, setParseError] = useState<string | null>(null);
  const [parsedEnvs, setParsedEnvs] = useState<{ key: string; value: string; type: EnvVarType }[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);

  // Form setup
  const form = useForm<{ envText: string }>({
    defaultValues: {
      envText: ''
    },
    mode: 'onChange'
  });

  // Load existing environment variables when the dialog opens
  useEffect(() => {
    if (open && existingEnvs && existingEnvs.length > 0) {
      loadExistingEnvs();
    } else if (!open) {
      // Reset form when dialog is closed
      resetForm();
    }
  }, [open, cachedMasterKey]);

  // Function to load and decrypt existing environment variables
  const loadExistingEnvs = async () => {
    if (!existingEnvs || existingEnvs.length === 0) {
      return;
    }

    // Check if we have a cached MEK from the parent component
    if (!cachedMasterKey) {
      logger.error('No cached master key available for bulk edit');
      setParseError('Master key not available. Please re-enter edit mode in the main interface.');
      return;
    }

    setIsLoading(true);
    setIsEditMode(true);
    try {
      // Decrypt all environment variables using the cached MEK
      const decryptedEnvs = await Promise.all(
        existingEnvs.map(async (env) => {
          try {
            // Check if we have all required MEK-based encryption fields
            if (env.value && env.iv && env.tag && cachedMasterKey) {
              const decryptedValue = await decryptValueWithMEK(
                env.value,
                env.iv,
                env.tag,
                cachedMasterKey
              );
              return {
                key: env.key,
                value: decryptedValue,
                type: env.type
              };
            } else {
              throw new Error('Missing required MEK-based encryption fields (iv/tag)');
            }
          } catch (err) {
            logger.error(`Failed to decrypt ${env.key}:`, err);
            return {
              key: env.key,
              value: '# DECRYPTION_ERROR',
              type: env.type
            };
          }
        })
      );

      // Convert to KEY=VALUE format
      const envText = decryptedEnvs.map(env => {
        // Format the value based on its type
        let formattedValue = env.value;

        // For JSON values, stringify them
        if (env.type === 'JSON' && formattedValue !== '# DECRYPTION_ERROR') {
          try {
            // Try to parse and re-stringify to ensure valid JSON
            formattedValue = JSON.stringify(JSON.parse(formattedValue));
          } catch (err) {
            logger.error(`Failed to format JSON for ${env.key}:`, err);
          }
        }

        return `${env.key}=${formattedValue}`;
      }).join('\n');

      // Set the form value
      form.setValue('envText', envText);

      // Parse the environment variables
      handleParse();
    } catch (err) {
      logger.error('Failed to load existing environment variables:', err);
      setParseError('Failed to load existing environment variables');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    form.reset({
      envText: ''
    });
    setParseError(null);
    setParsedEnvs([]);
    setIsEditMode(false);
  };

  const parseEnvText = (text: string): { success: boolean; envs?: { key: string; value: string; type: EnvVarType }[]; error?: string } => {
    if (!text.trim()) {
      return { success: false, error: 'Please enter environment variables' };
    }

    const lines = text.split('\n').filter(line => line.trim() !== '');
    const envs: { key: string; value: string; type: EnvVarType }[] = [];
    const errors: string[] = [];

    lines.forEach((line, index) => {
      // Skip comments and empty lines
      if (line.trim().startsWith('#') || line.trim() === '') {
        return;
      }

      // Check for KEY=VALUE format
      const match = line.match(/^([^=]+)=(.*)$/);
      if (!match) {
        errors.push(`Line ${index + 1}: Invalid format. Expected KEY=VALUE`);
        return;
      }

      const key = match[1].trim();
      const value = match[2].trim();

      if (!key) {
        errors.push(`Line ${index + 1}: Key cannot be empty`);
        return;
      }

      // Determine the type based on the value
      let type: EnvVarType = 'String';
      if (value === 'true' || value === 'false') {
        type = 'Boolean';
      } else if (!isNaN(Number(value)) && value !== '') {
        type = 'Number';
      } else if ((value.startsWith('{') && value.endsWith('}')) ||
                (value.startsWith('[') && value.endsWith(']'))) {
        try {
          JSON.parse(value);
          type = 'JSON';
        } catch (e) {
          // If it's not valid JSON, treat it as a string
          type = 'String';
        }
      }

      envs.push({ key, value, type });
    });

    if (errors.length > 0) {
      return { success: false, error: errors.join('\n') };
    }

    return { success: true, envs };
  };

  const handleParse = () => {
    const envText = form.getValues('envText');
    const result = parseEnvText(envText);

    if (result.success && result.envs) {
      setParsedEnvs(result.envs);
      setParseError(null);
    } else {
      setParsedEnvs([]);
      setParseError(result.error || 'Failed to parse environment variables');
    }
  };

  const handleSubmit = () => {
    const envText = form.getValues('envText');
    const result = parseEnvText(envText);

    if (result.success && result.envs && result.envs.length > 0) {
      // If we're in edit mode, collect the IDs of existing environment variables
      const existingEnvIds = isEditMode && existingEnvs
        ? existingEnvs
            .filter(env => result.envs?.some(parsedEnv => parsedEnv.key === env.key))
            .map(env => env.id)
        : [];

      onAddEnvs({
        envText,
        parsedEnvs: result.envs,
        existingEnvIds,
        isEdit: isEditMode
      });
      resetForm();
      onOpenChange(false);
    } else {
      setParseError(result.error || 'Failed to parse environment variables');
    }
  };

  const handleCancel = () => {
    resetForm();
    onOpenChange(false);
  };

  const dialogFooter = (
    <div className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 sm:space-x-2">
      <Button
        variant="ghost"
        onClick={handleCancel}
        disabled={isLoading}
        className="w-full sm:w-auto"
      >
        Cancel
      </Button>
      <Button
        type="button"
        variant="outline"
        onClick={handleParse}
        disabled={isLoading}
        className="w-full sm:w-auto"
      >
        {isLoading ? (
          <>
            <span className="animate-spin mr-2">
              <i className="pi pi-spinner"></i>
            </span>
            Loading...
          </>
        ) : (
          'Parse & Preview'
        )}
      </Button>
      <Button
        type="button"
        onClick={handleSubmit}
        disabled={parsedEnvs.length === 0 || isLoading}
        className="w-full sm:w-auto"
      >
        {isEditMode ? `Update ${parsedEnvs.length} Variables` : `Add ${parsedEnvs.length} Variables`}
      </Button>
    </div>
  );

  // Main render
  return (
    <Dialog
      title={isEditMode ? "Bulk Edit Environment Variables" : "Bulk Add Environment Variables"}
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          handleCancel();
        } else {
          onOpenChange(isOpen);
        }
      }}
      footer={dialogFooter}
      size="large"
    >
      <div className="space-y-4">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          </div>
        ) : (
          <>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {isEditMode
                ? "Edit your environment variables below. Each line should be in KEY=VALUE format."
                : "Enter environment variables in KEY=VALUE format, one per line. Comments can be added with #."}
            </p>

            {isEditMode ? (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-4">
                <p className="text-green-800 dark:text-green-300 text-sm">
                  <i className="pi pi-unlock mr-2"></i>
                  Values have been decrypted using MEK-based encryption for editing. They will be re-encrypted when you save changes.
                </p>
              </div>
            ) : (
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                <p className="text-blue-800 dark:text-blue-300 text-sm">
                  <i className="pi pi-info-circle mr-2"></i>
                  Variables will be encrypted. Types will be automatically detected.
                </p>
              </div>
            )}

            <Controller
              control={form.control}
              name="envText"
              rules={{ required: 'Environment variables are required' }}
              render={({ field, fieldState }) => (
                <>
                  <div className="overflow-x-auto">
                    <CodeEditor
                      value={field.value}
                      language="properties"
                      placeholder={`DATABASE_URL=postgres://localhost:5432/mydb
API_KEY=your-api-key
DEBUG_MODE=true
MAX_CONNECTIONS=100
# This is a comment
CONFIG_SETTINGS={"timeout": 30, "retries": 3}`}
                      onChange={(e) => field.onChange(e.target.value)}
                      padding={15}
                      style={{
                        fontSize: 14,
                        backgroundColor: "transparent",
                        fontFamily: 'ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace',
                        minHeight: "200px",
                        maxHeight: "50vh",
                        width: "100%"
                      }}
                      data-color-mode="dark"
                    />
                  </div>
                  {fieldState.error && (
                    <p className="text-sm text-red-500 mt-1">{fieldState.error.message}</p>
                  )}
                </>
              )}
            />

            {parseError && (
              <Alert severity="error" className="mt-2">
                <pre className="whitespace-pre-wrap text-sm">{parseError}</pre>
              </Alert>
            )}

            {parsedEnvs.length > 0 && (
              <div className="mt-4">
                <h3 className="text-sm font-medium mb-2">Preview ({parsedEnvs.length} variables):</h3>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  {/* Desktop Table - Hidden on small screens */}
                  <div className="hidden md:block">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Key</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Value</th>
                          <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Type</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                        {parsedEnvs.map((env, index) => (
                          <tr key={index}>
                            <td className="px-4 py-2 text-sm font-mono">{env.key}</td>
                            <td className="px-4 py-2 text-sm font-mono truncate max-w-xs">{env.value}</td>
                            <td className="px-4 py-2 text-sm">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                env.type === 'String' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                                env.type === 'Number' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                env.type === 'Boolean' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' :
                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                              }`}>
                                {env.type}
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>

                  {/* Mobile Card View - Shown only on small screens */}
                  <div className="md:hidden space-y-3 p-3">
                    {parsedEnvs.map((env, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-white dark:bg-gray-900"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="font-mono text-sm font-medium text-gray-900 dark:text-white break-all">
                            {env.key}
                          </div>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            env.type === 'String' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                            env.type === 'Number' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                            env.type === 'Boolean' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' :
                            'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                          }`}>
                            {env.type}
                          </span>
                        </div>
                        <div className="font-mono text-xs text-gray-500 dark:text-gray-400 break-all">
                          {env.value}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </Dialog>
  );
}

'use client';

import { ConfirmDialog, confirmDialog } from '@/components/ui/ConfirmDialog';

interface DeleteConfirmOptions {
  itemType: string;
  itemName: string;
  onConfirm: () => void;
  onCancel?: () => void;
  message?: string;
}

export function showDeleteConfirmDialog({
  itemType,
  itemName,
  onConfirm,
  onCancel,
  message
}: DeleteConfirmOptions) {
  confirmDialog({
    header: `Delete ${itemType}`,
    message: message || `Are you sure you want to delete the ${itemType.toLowerCase?.()} "${itemName}"? This action cannot be undone.`,
    icon: 'pi pi-exclamation-triangle',
    acceptLabel: 'Delete',
    rejectLabel: 'Cancel',
    acceptClassName: 'p-button-danger',
    accept: onConfirm,
    reject: onCancel
  });
}

export function DeleteConfirmDialogProvider() {
  return <ConfirmDialog />;
}

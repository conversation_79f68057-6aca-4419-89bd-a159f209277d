'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import {AddInheritanceDialog, InheritanceManagerResponse} from './AddInheritanceDialog';
import { DeleteConfirmDialogProvider, showDeleteConfirmDialog } from './DeleteConfirmDialog';
import { SelectiveRemovalDialog } from './SelectiveRemovalDialog';
import { getApps, getInheritanceLinks, removeInheritanceLink, verifyAppSecret, getMasterKey } from '@/server/actions/app-manager';
import { MEKCache, enterEditMode } from '@/utils/master-key-manager';
import { verifyAppSecret as clientVerifyAppSecret } from '@/utils/encryption';
import EnvironmentSelector, { Environment } from '@/components/common/EnvironmentSelector';
import { logger } from '@/utils/logger';

interface InheritanceManagerProps {
  organizationId: string;
  appId: string;
}

interface App {
  id: string;
  name: string;
  description?: string;
}



interface GroupedInheritanceLink {
  sourceAppId: string;
  sourceAppName: string;
  allKeys: string[];
  links: InheritanceManagerResponse['data'][];
  earliestDate: string;
  totalKeys: number;
}



export default function InheritanceManager({ organizationId, appId }: InheritanceManagerProps) {
  const { success, error } = useToast();
  const [addInheritanceDialogOpen, setAddInheritanceDialogOpen] = useState(false);
  const [apps, setApps] = useState<App[]>([]);
  const [groupedInheritanceLinks, setGroupedInheritanceLinks] = useState<GroupedInheritanceLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);

  // Environment selection state
  const [selectedEnvironment, setSelectedEnvironment] = useState<Environment>('development');

  // Selective removal dialog state
  const [selectiveRemovalDialogOpen, setSelectiveRemovalDialogOpen] = useState(false);
  const [selectedGroupForRemoval, setSelectedGroupForRemoval] = useState<GroupedInheritanceLink | null>(null);

  // Edit Mode state (following SecretKeyManager pattern)
  const [isEditMode, setIsEditMode] = useState(false);
  const [appSecretInput, setAppSecretInput] = useState('');
  const [showAppSecretInput, setShowAppSecretInput] = useState(false);
  const [editModeTimeoutId, setEditModeTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [cachedMasterKey, setCachedMasterKey] = useState<CryptoKey | null>(null);

  // Constants
  const EDIT_MODE_TIMEOUT = 15 * 60 * 1000; // 15 minutes in milliseconds

  // Fetch apps and inheritance links on component mount and when environment changes
  useEffect(() => {
    fetchData();
  }, [appId, organizationId, selectedEnvironment]);

  // Check for cached MEK on component mount
  useEffect(() => {
    const checkCachedMEK = async () => {
      try {
        if (MEKCache.isValid(appId)) {
          const cachedMEK = await MEKCache.retrieve(appId);
          if (cachedMEK) {
            // Restore edit mode with cached MEK
            setCachedMasterKey(cachedMEK);
            setIsEditMode(true);

            // Set timeout based on remaining cache time
            const remainingTime = MEKCache.getRemainingTime(appId);
            if (remainingTime > 0) {
              const timeoutId = setTimeout(() => {
                exitEditMode();
                success('Edit Mode', 'Edit mode has been automatically disabled due to inactivity');
              }, remainingTime);

              setEditModeTimeoutId(timeoutId);
            }

          }
        }
      } catch (error) {
        logger.error('Failed to restore cached MEK:', error);
      }
    };

    checkCachedMEK();
  }, [appId]);

  // Reset edit mode when component unmounts
  useEffect(() => {
    return () => {
      if (editModeTimeoutId) {
        clearTimeout(editModeTimeoutId);
      }
    };
  }, [editModeTimeoutId]);

  // Function to enter edit mode with MEK retrieval
  const enterEditModeWithMEK = async (secret: string) => {
    try {
      // Clear any existing timeout
      if (editModeTimeoutId) {
        clearTimeout(editModeTimeoutId);
      }

      // Use the enterEditMode utility to retrieve and decrypt MEK
      const editModeResult = await enterEditMode(
        appId,
        organizationId,
        secret,
        getMasterKey
      );

      if (!editModeResult.success) {
        throw new Error(editModeResult.error || 'Failed to enter edit mode');
      }

      // Set the cached MEK and enable edit mode
      setCachedMasterKey(editModeResult.masterKey!);
      setIsEditMode(true);
      setShowAppSecretInput(false);

      // Set a timeout to automatically exit edit mode
      const timeoutId = setTimeout(() => {
        exitEditMode();
        success('Edit Mode', 'Edit mode has been automatically disabled due to inactivity');
      }, EDIT_MODE_TIMEOUT);

      setEditModeTimeoutId(timeoutId);

      success('Edit Mode Enabled', 'You can now add inheritance links');
    } catch (err: any) {
      error('Edit Mode Error', err.message || 'Failed to enter edit mode');
      throw err;
    }
  };

  // Function to exit edit mode
  const exitEditMode = () => {
    // Clear the timeout
    if (editModeTimeoutId) {
      clearTimeout(editModeTimeoutId);
      setEditModeTimeoutId(null);
    }

    // Clear MEK cache
    MEKCache.clear(appId);

    // Reset state
    setIsEditMode(false);
    setAppSecretInput('');
    setCachedMasterKey(null);
  };

  // Function to handle app secret submission
  const handleAppSecretSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!appSecretInput.trim()) {
      error('Invalid Input', 'Please enter the App Secret');
      return;
    }

    setIsVerifying(true);

    try {
      // Get the keyCheck value from the server
      const response = await verifyAppSecret({
        appId,
        organizationId
      });

      if (response.success && response.data?.keyCheck) {
        // Verify the app secret on the client side
        const isValid = await clientVerifyAppSecret(
          response.data.keyCheck,
          appId,
          appSecretInput.trim()
        );

        if (isValid) {
          // If verification is successful, enter edit mode with MEK retrieval
          await enterEditModeWithMEK(appSecretInput.trim());
        } else {
          // If verification fails, show an error
          error('Invalid App Secret', 'The App Secret you entered is invalid. Please try again.');
        }
      } else {
        // If we couldn't get the keyCheck value, show an error
        error('Error', response?.error || 'Failed to verify App Secret');
      }
    } catch (err: any) {
      error('Error', err.message || 'Failed to verify App Secret');
    } finally {
      setIsVerifying(false);
    }
  };

  // Function to reset edit mode timeout when user is active
  const resetEditModeTimeout = () => {
    if (isEditMode && editModeTimeoutId) {
      clearTimeout(editModeTimeoutId);

      const timeoutId = setTimeout(() => {
        exitEditMode();
        success('Edit Mode', 'Edit mode has been automatically disabled due to inactivity');
      }, EDIT_MODE_TIMEOUT);

      setEditModeTimeoutId(timeoutId);
    }
  };

  // Function to process inheritance links (backend already groups them)
  const groupInheritanceLinks = (links: InheritanceManagerResponse['data'][]): GroupedInheritanceLink[] => {
    // Backend already returns grouped data, so we just need to transform it to our interface
    return links.map(link => ({
      sourceAppId: link.sourceApp.id,
      sourceAppName: link.sourceApp.name,
      allKeys: [...link.emvKeys].sort(), // Backend provides emvKeys array
      links: link.links, // Backend provides individual link records
      earliestDate: link.createdAt,
      totalKeys: link.emvKeys.length
    })).sort((a, b) => a.sourceAppName.localeCompare(b.sourceAppName));
  };

  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Fetch apps
      const appsResponse = await getApps(organizationId);
      if (appsResponse.success && appsResponse.data) {
        // Filter out the current app
        const filteredApps = appsResponse.data.filter((app) => app.id !== appId);
        setApps(filteredApps);
      } else {
        error('Error', appsResponse.error || 'Failed to fetch apps');
      }

      // Fetch inheritance links for the selected environment
      const linksResponse = await getInheritanceLinks(appId, selectedEnvironment);
      if (linksResponse.success && linksResponse.data) {

        // Group the inheritance links by source app
        const grouped = groupInheritanceLinks(linksResponse.data);

        setGroupedInheritanceLinks(grouped);
      } else {
        error('Error', linksResponse.error || 'Failed to fetch inheritance links');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddInheritance = async () => {
    fetchData();
  };

  // Smart removal logic based on number of keys
  const handleSmartRemoval = (groupedLink: GroupedInheritanceLink) => {
    if (groupedLink.totalKeys === 1) {
      // Single key - direct removal with confirmation
      showDeleteConfirmDialog({
        itemType: 'Inheritance Link',
        itemName: `${groupedLink.sourceAppName} (${groupedLink.allKeys[0]})`,
        message: `Are you sure you want to remove the inheritance of "${groupedLink.allKeys[0]}" from "${groupedLink.sourceAppName}"?`,
        onConfirm: () => handleRemoveAllKeysFromSource(groupedLink)
      });
    } else {
      // Multiple keys - open selective removal dialog
      setSelectedGroupForRemoval(groupedLink);
      setSelectiveRemovalDialogOpen(true);
    }
  };

  // Remove all keys from a source app
  const handleRemoveAllKeysFromSource = async (groupedLink: GroupedInheritanceLink) => {
    try {
      // Remove all inheritance links for this source app
      const removePromises = groupedLink.links.map(link =>
        removeInheritanceLink({
          id: link.id,
          environment: selectedEnvironment
        })
      );

      const results = await Promise.all(removePromises);
      const failedRemovals = results.filter(result => !result.success);

      if (failedRemovals.length === 0) {
        success(
          'Inheritance Removed',
          `Successfully removed all ${groupedLink.totalKeys} inherited keys from ${groupedLink.sourceAppName}`
        );
      } else {
        error(
          'Removal Failed',
          `Failed to remove ${failedRemovals.length} inheritance links. Please try again.`
        );
      }

      // Refresh the data after removal
      fetchData();
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    }
  };

  // Remove specific keys from a source app
  const handleRemoveSpecificKeys = async (keysToRemove: string[], removeAll: boolean) => {
    if (!selectedGroupForRemoval) return;
    try {
      if (removeAll) {
        await handleRemoveAllKeysFromSource(selectedGroupForRemoval);
        return;
      }

      // Find which individual InheritanceLink records to remove
      // Each link in the links array represents a single key (link.emvKey)
      const linksToRemove = selectedGroupForRemoval.links.filter(link => {
        return keysToRemove.includes(link.emvKey);
      });

      if (linksToRemove.length === 0) {
        error('No Links Found', 'No inheritance links found for the selected keys.');
        return;
      }
      logger.dir(linksToRemove,{
        depth: Infinity,
      })
      const removePromises = linksToRemove.map(link =>
        removeInheritanceLink({
          id: link.id,
          environment: selectedEnvironment
        })
      );

      const results = await Promise.all(removePromises);
      const failedRemovals = results.filter(result => !result.success);

      if (failedRemovals.length === 0) {
        success(
          'Keys Removed',
          `Successfully removed ${keysToRemove.length} inherited key${keysToRemove.length !== 1 ? 's' : ''} from ${selectedGroupForRemoval.sourceAppName}`
        );
      } else {
        const successfulRemovals = results.length - failedRemovals.length;
        if (successfulRemovals > 0) {
          error(
            'Partial Removal',
            `Successfully removed ${successfulRemovals} key${successfulRemovals !== 1 ? 's' : ''}, but failed to remove ${failedRemovals.length} key${failedRemovals.length !== 1 ? 's' : ''}. Please try again.`
          );
        } else {
          error(
            'Removal Failed',
            `Failed to remove ${failedRemovals.length} inheritance link${failedRemovals.length !== 1 ? 's' : ''}. Please try again.`
          );
        }
      }

      // Refresh the data after removal
      fetchData();
    } catch (err: any) {
      logger.error('Error in handleRemoveSpecificKeys:', err);
      error('Error', err.message || 'An unexpected error occurred');
    }
  };

  return (
    <div className="space-y-6">
      <DeleteConfirmDialogProvider />

      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <h2 className="text-xl font-semibold">Inheritance Manager</h2>
          <EnvironmentSelector
            value={selectedEnvironment}
            onChange={setSelectedEnvironment}
          />
        </div>
        <div className="flex items-center space-x-3">
          {isEditMode ? (
            <>
              <div className="flex items-center text-green-600 dark:text-green-400">
                <i className="pi pi-check-circle mr-2"></i>
                <span className="text-sm font-medium">Edit Mode Active</span>
              </div>
              <Button
                variant="outline"
                onClick={exitEditMode}
                className="text-gray-600 dark:text-gray-400"
              >
                Exit Edit Mode
              </Button>
              <Button onClick={() => {
                resetEditModeTimeout();
                setAddInheritanceDialogOpen(true);
              }}>
                <i className="pi pi-plus mr-2"></i>
                Add Inheritance
              </Button>
            </>
          ) : (
            <>
              {!showAppSecretInput ? (
                <Button
                  onClick={() => setShowAppSecretInput(true)}
                  className="bg-indigo-600 hover:bg-indigo-700"
                >
                  <i className="pi pi-unlock mr-2"></i>
                  Enter Edit Mode
                </Button>
              ) : (
                <form onSubmit={handleAppSecretSubmit} className="flex items-center space-x-2">
                  <input
                    type="password"
                    value={appSecretInput}
                    onChange={(e) => setAppSecretInput(e.target.value)}
                    placeholder="Enter App Secret"
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                    autoFocus
                  />
                  <Button type="submit" size="sm" disabled={isVerifying}>
                    {isVerifying ? (
                      <>
                        <i className="pi pi-spin pi-spinner mr-1"></i>
                        Verifying...
                      </>
                    ) : (
                      <>
                        <i className="pi pi-check mr-1"></i>
                        Unlock
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setShowAppSecretInput(false);
                      setAppSecretInput('');
                    }}
                  >
                    <i className="pi pi-times"></i>
                  </Button>
                </form>
              )}
            </>
          )}
        </div>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
        <p className="text-blue-800 dark:text-blue-300 text-sm">
          <i className="pi pi-info-circle mr-2"></i>
          {isEditMode ? (
            <>
              <strong>Edit Mode Active:</strong> You can now add inheritance links. MEK-based inheritance allows secure sharing of encrypted environment variables between applications.
            </>
          ) : (
            <>
              <strong>Enter Edit Mode Required:</strong> To add inheritance links, you must first enter Edit Mode by providing this {"app's"} App Secret. This enables secure MEK-based inheritance.
            </>
          )}
        </p>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <Card>
          <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium">Current Inheritance Links</h3>
          </div>

          {groupedInheritanceLinks.length === 0 ? (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
              <p>No inheritance links found for this app in <span className="font-medium">{selectedEnvironment}</span> environment.</p>
              <p className="text-sm mt-2">Add inheritance links to share environment variables from other apps.</p>
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden md:block overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Source App</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Inherited Keys</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Date Linked</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {groupedInheritanceLinks.map((groupedLink) => (
                      <tr key={groupedLink.sourceAppId} className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-4 text-sm font-medium text-gray-900 dark:text-white">
                          <div className="flex flex-col">
                            <span>{groupedLink.sourceAppName}</span>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {groupedLink.totalKeys} key{groupedLink.totalKeys !== 1 ? 's' : ''}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm">
                          <div className="flex flex-wrap gap-1">
                            {groupedLink.allKeys.map((key: string) => (
                              <Badge key={key} className="mr-1 mb-1" variant="info">
                                {key}
                              </Badge>
                            ))}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                          {new Date(groupedLink.earliestDate).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-4 text-sm">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                              onClick={() => handleSmartRemoval(groupedLink)}
                            >
                              {groupedLink.totalKeys === 1 ? 'Remove' : 'Manage'}
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Mobile Card View */}
              <div className="md:hidden space-y-4 p-4">
                {groupedInheritanceLinks.map((groupedLink) => (
                  <div key={groupedLink.sourceAppId} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{groupedLink.sourceAppName}</h4>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {groupedLink.totalKeys} key{groupedLink.totalKeys !== 1 ? 's' : ''} • {new Date(groupedLink.earliestDate).toLocaleDateString()}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 flex-shrink-0"
                        onClick={() => handleSmartRemoval(groupedLink)}
                      >
                        {groupedLink.totalKeys === 1 ? 'Remove' : 'Manage'}
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {groupedLink.allKeys.map((key: string) => (
                        <Badge key={key} className="mr-1 mb-1" variant="info">
                          {key}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </Card>
      )}

      {/* Add Inheritance Dialog */}
      <AddInheritanceDialog
        organizationId={organizationId}
        targetAppId={appId}
        environment={selectedEnvironment}
        open={addInheritanceDialogOpen}
        onOpenChange={setAddInheritanceDialogOpen}
        onAddInheritance={handleAddInheritance}
        availableApps={apps}
        cachedMasterKey={cachedMasterKey}
        isEditMode={isEditMode}
      />

      {/* Selective Removal Dialog */}
      <SelectiveRemovalDialog
        open={selectiveRemovalDialogOpen}
        onOpenChange={setSelectiveRemovalDialogOpen}
        groupedLink={selectedGroupForRemoval}
        onRemoveKeys={handleRemoveSpecificKeys}
      />
    </div>
  );
}

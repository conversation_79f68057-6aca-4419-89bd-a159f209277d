'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Tooltip } from '@/components/ui/Tooltip';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { getApps } from '@/server/actions/app-manager';
import ComprehensiveAppDeleteDialog from './ComprehensiveAppDeleteDialog';

interface App {
  id: string;
  name: string;
  description?: string;
  modules: string[];
  apiKey: string;
  createdAt: string;
  updatedAt: string;
}

interface AppsListProps {
  organizationId: string;
}

export default function AppsList({ organizationId }: AppsListProps) {
  const { success, error } = useToast();
  const [comprehensiveDeleteDialogVisible, setComprehensiveDeleteDialogVisible] = useState(false);
  const [appToDelete, setAppToDelete] = useState<App | null>(null);
  const [apps, setApps] = useState<App[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchApps();
  }, [organizationId]);

  const fetchApps = async () => {
    setIsLoading(true);
    try {
      const response = await getApps(organizationId);
      if (response.success && response.data) {
        setApps(response.data);
      } else {
        error('Error', response.error || 'Failed to fetch apps');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClick = (app: App) => {
    setAppToDelete(app);
    setComprehensiveDeleteDialogVisible(true);
  };

  const handleDeleteSuccess = () => {
    // Refresh the apps list after successful deletion
    fetchApps();
  };

  const handleDeleteClose = () => {
    setComprehensiveDeleteDialogVisible(false);
    setAppToDelete(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <h2 className="text-xl font-semibold">Your Applications</h2>
          <Tooltip content="Apps can enable any combination of services. Add or remove modules per app.">
            <span className="ml-2 text-gray-400 cursor-help">
              <i className="pi pi-info-circle text-sm"></i>
            </span>
          </Tooltip>
        </div>
        {apps.length !== 0 &&<Link href={`/dashboard/organization/${organizationId}/app-manager/apps/new`}>
          <Button>
            <i className="pi pi-plus mr-2"></i>
            Create New App
          </Button>
        </Link>}
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      ) : apps.length === 0 ? (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-8 text-center">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No applications found</h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            Create your first application to get started with App Manager.
          </p>
          <Link href={`/dashboard/organization/${organizationId}/app-manager/apps/new`}>
            <Button>
              <i className="pi pi-plus mr-2"></i>
              Create New App
            </Button>
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {apps.map((app) => (
            <Card key={app.id} className="overflow-hidden hover:shadow-md transition-shadow">
              <div className="p-5">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">{app.name}</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 font-mono">{app.id}</p>
                  </div>
                  <div className="flex items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                      onClick={() => handleDeleteClick(app)}
                    >
                      <i className="pi pi-trash"></i>
                    </Button>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-xs text-gray-500 dark:text-gray-400 uppercase font-semibold mb-2">Modules Enabled</div>
                  <div className="flex flex-wrap gap-2">
                    {app.modules && app.modules.map((module) => (
                      <Badge key={module} variant="info">{module}</Badge>
                    ))}
                    {(!app.modules || app.modules.length === 0) && (
                      <span className="text-sm text-gray-500 dark:text-gray-400">No modules enabled</span>
                    )}
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Last Modified: {new Date(app.updatedAt).toLocaleDateString()}
                  </div>
                  <Link href={`/dashboard/organization/${organizationId}/app-manager/apps/${app.id}`}>
                    <Button variant="primary" size="sm">
                      <i className="pi pi-eye mr-1"></i>
                      View
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* ✅ COMPREHENSIVE APP DELETION DIALOG */}
      <ComprehensiveAppDeleteDialog
        app={appToDelete}
        organizationId={organizationId}
        isOpen={comprehensiveDeleteDialogVisible}
        onClose={handleDeleteClose}
        onSuccess={handleDeleteSuccess}
      />
    </div>
  );
}

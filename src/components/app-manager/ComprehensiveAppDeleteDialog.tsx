'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { getAppDeletionImpact, deleteApp } from '@/server/actions/app-manager';

interface App {
  id: string;
  name: string;
  description?: string;
}

interface DeletionImpact {
  app: {
    id: string;
    name: string;
    description?: string;
  };
  impact: {
    environmentVariables: number;
    sourceInheritanceLinks: number;
    targetInheritanceLinks: number;
    affectedApps: Array<{ id: string; name: string }>;
    sourceApps: Array<{ id: string; name: string }>;
  };
}

interface ComprehensiveAppDeleteDialogProps {
  app: App | null;
  organizationId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function ComprehensiveAppDeleteDialog({
  app,
  organizationId,
  isOpen,
  onClose,
  onSuccess
}: ComprehensiveAppDeleteDialogProps) {
  const { success, error } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletionImpact, setDeletionImpact] = useState<DeletionImpact | null>(null);
  const [confirmationText, setConfirmationText] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Reset state when dialog opens/closes
  useEffect(() => {
    if (isOpen && app) {
      setConfirmationText('');
      setShowConfirmation(false);
      setDeletionImpact(null);
      fetchDeletionImpact();
    } else {
      setDeletionImpact(null);
      setConfirmationText('');
      setShowConfirmation(false);
    }
  }, [isOpen, app]);

  const fetchDeletionImpact = async () => {
    if (!app) return;

    setIsLoading(true);
    try {
      const response = await getAppDeletionImpact({
        appId: app.id,
        organizationId
      });

      if (response.success && response.data) {
        setDeletionImpact(response.data);
      } else {
        error('Error', response.error || 'Failed to analyze deletion impact');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!app || !deletionImpact) return;

    // Validate confirmation text
    if (confirmationText !== app.name) {
      error('Error', 'App name confirmation does not match');
      return;
    }

    setIsDeleting(true);
    try {
      const response = await deleteApp({
        appId: app.id,
        organizationId
      });

      if (response.success) {
        const summary = response.data?.deletionSummary;
        success(
          'App Deleted Successfully',
          `"${app.name}" and ${summary?.totalItemsRemoved || 'all'} related items have been permanently removed.`
        );
        onSuccess();
        onClose();
      } else {
        error('Error', response.error || 'Failed to delete app');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  const totalImpactedItems = deletionImpact ? 
    deletionImpact.impact.environmentVariables + 
    deletionImpact.impact.sourceInheritanceLinks + 
    deletionImpact.impact.targetInheritanceLinks : 0;

  const isConfirmationValid = confirmationText === app?.name;

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      title="⚠️ Delete App - Comprehensive Data Removal"
      size="large"
      footer={
        <div className="flex justify-end space-x-2">
          <Button 
            variant="ghost" 
            onClick={onClose}
            disabled={isDeleting}
          >
            Cancel
          </Button>
          {showConfirmation && (
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isDeleting || !isConfirmationValid}
            >
              {isDeleting ? (
                <>
                  <span className="animate-spin mr-2">
                    <i className="pi pi-spinner"></i>
                  </span>
                  Deleting...
                </>
              ) : (
                'Permanently Delete App'
              )}
            </Button>
          )}
          {!showConfirmation && deletionImpact && (
            <Button
              variant="destructive"
              onClick={() => setShowConfirmation(true)}
              disabled={isLoading}
            >
              I Understand - Proceed to Delete
            </Button>
          )}
        </div>
      }
    >
      <div className="space-y-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
            <span className="ml-3 text-gray-600">Analyzing deletion impact...</span>
          </div>
        ) : deletionImpact ? (
          <>
            {/* Critical Warning */}
            <div className="bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-start">
                <i className="pi pi-exclamation-triangle text-red-600 text-xl mr-3 mt-1"></i>
                <div>
                  <h3 className="text-red-800 dark:text-red-300 font-semibold text-lg mb-2">
                    Critical: This action cannot be undone
                  </h3>
                  <p className="text-red-700 dark:text-red-400 text-sm">
                    Deleting <strong>"{app?.name}"</strong> will permanently remove the app and all associated data. 
                    This includes environment variables, inheritance relationships, and will affect other apps that depend on this one.
                  </p>
                </div>
              </div>
            </div>

            {/* Impact Summary */}
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <i className="pi pi-info-circle mr-2"></i>
                Deletion Impact Summary
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Environment Variables:</span>
                    <Badge variant={deletionImpact.impact.environmentVariables > 0 ? "danger" : "secondary"}>
                      {deletionImpact.impact.environmentVariables}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Inheritance Links:</span>
                    <Badge variant={
                      (deletionImpact.impact.sourceInheritanceLinks + deletionImpact.impact.targetInheritanceLinks) > 0
                        ? "danger" : "secondary"
                    }>
                      {deletionImpact.impact.sourceInheritanceLinks + deletionImpact.impact.targetInheritanceLinks}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Affected Apps:</span>
                    <Badge variant={deletionImpact.impact.affectedApps.length > 0 ? "danger" : "secondary"}>
                      {deletionImpact.impact.affectedApps.length}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Total Items:</span>
                    <Badge variant={totalImpactedItems > 0 ? "danger" : "secondary"}>
                      {totalImpactedItems + 1} {/* +1 for the app itself */}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            {/* Affected Apps Warning */}
            {deletionImpact.impact.affectedApps.length > 0 && (
              <div className="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                <h4 className="font-semibold text-orange-800 dark:text-orange-300 mb-2 flex items-center">
                  <i className="pi pi-exclamation-triangle mr-2"></i>
                  Apps That Will Lose Inherited Variables
                </h4>
                <div className="space-y-1">
                  {deletionImpact.impact.affectedApps.map((affectedApp) => (
                    <div key={affectedApp.id} className="text-sm text-orange-700 dark:text-orange-400">
                      • <strong>{affectedApp.name}</strong> will lose inherited environment variables
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Confirmation Input */}
            {showConfirmation && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <h4 className="font-semibold text-red-800 dark:text-red-300 mb-3">
                  Final Confirmation Required
                </h4>
                <p className="text-sm text-red-700 dark:text-red-400 mb-3">
                  To confirm deletion, type the app name <strong>"{app?.name}"</strong> exactly as shown:
                </p>
                <input
                  type="text"
                  value={confirmationText}
                  onChange={(e) => setConfirmationText(e.target.value)}
                  placeholder={`Type "${app?.name}" to confirm`}
                  className="w-full px-3 py-2 border border-red-300 dark:border-red-700 rounded-md 
                           focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-red-900/30"
                  disabled={isDeleting}
                />
                {confirmationText && !isConfirmationValid && (
                  <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                    App name does not match. Please type exactly: "{app?.name}"
                  </p>
                )}
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500">Failed to load deletion impact analysis.</p>
          </div>
        )}
      </div>
    </Dialog>
  );
}

'use client';

import { useState } from 'react';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';

interface GroupedInheritanceLink {
  sourceAppId: string;
  sourceAppName: string;
  allKeys: string[];
  links: any[];
  earliestDate: string;
  totalKeys: number;
}

interface SelectiveRemovalDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  groupedLink: GroupedInheritanceLink | null;
  onRemoveKeys: (keysToRemove: string[], removeAll: boolean) => Promise<void>;
}

export function SelectiveRemovalDialog({
  open,
  onOpenChange,
  groupedLink,
  onRemoveKeys
}: SelectiveRemovalDialogProps) {
  const { error } = useToast();
  const [selectedKeys, setSelectedKeys] = useState<Set<string>>(new Set());
  const [isRemoving, setIsRemoving] = useState(false);

  if (!groupedLink) return null;

  const handleKeyToggle = (key: string) => {
    const newSelected = new Set(selectedKeys);
    if (newSelected.has(key)) {
      newSelected.delete(key);
    } else {
      newSelected.add(key);
    }
    setSelectedKeys(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedKeys.size === groupedLink.allKeys.length) {
      setSelectedKeys(new Set());
    } else {
      setSelectedKeys(new Set(groupedLink.allKeys));
    }
  };

  const handleRemoveSelected = async () => {
    if (selectedKeys.size === 0) {
      error('Selection Required', 'Please select at least one key to remove');
      return;
    }

    setIsRemoving(true);
    try {
      await onRemoveKeys(Array.from(selectedKeys), false);
      setSelectedKeys(new Set());
      onOpenChange(false);
    } catch (err) {
      // Error handling is done in parent component
    } finally {
      setIsRemoving(false);
    }
  };

  const handleRemoveAll = async () => {
    setIsRemoving(true);
    try {
      await onRemoveKeys(groupedLink.allKeys, true);
      setSelectedKeys(new Set());
      onOpenChange(false);
    } catch (err) {
      // Error handling is done in parent component
    } finally {
      setIsRemoving(false);
    }
  };

  const handleCancel = () => {
    setSelectedKeys(new Set());
    onOpenChange(false);
  };

  const dialogFooter = (
    <div className="flex flex-col space-y-2">
      <Button
        onClick={handleRemoveSelected}
        disabled={selectedKeys.size === 0 || isRemoving}
        className="w-full bg-red-600 hover:bg-red-700 text-white"
      >
        {isRemoving ? (
          <>
            <i className="pi pi-spin pi-spinner mr-2"></i>
            Removing...
          </>
        ) : (
          <>
            <i className="pi pi-trash mr-2"></i>
            Remove Selected ({selectedKeys.size})
          </>
        )}
      </Button>

      <Button
        onClick={handleRemoveAll}
        disabled={isRemoving}
        variant="outline"
        className="w-full border-red-300 text-red-600 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
      >
        {isRemoving ? (
          <>
            <i className="pi pi-spin pi-spinner mr-2"></i>
            Removing...
          </>
        ) : (
          <>
            <i className="pi pi-times-circle mr-2"></i>
            Remove All Keys
          </>
        )}
      </Button>

      <Button
        onClick={handleCancel}
        disabled={isRemoving}
        variant="ghost"
        className="w-full"
      >
        Cancel
      </Button>
    </div>
  );

  return (
    <Dialog
      title="Remove Inherited Keys"
      open={open}
      onOpenChange={onOpenChange}
      footer={dialogFooter}
      size="default"
    >
      <div className="space-y-4">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            Select which keys to remove from <strong>{groupedLink.sourceAppName}</strong>:
          </p>

          <div className="space-y-2 max-h-60 overflow-y-auto">
            {groupedLink.allKeys.map((key) => (
              <label
                key={key}
                className="flex items-center space-x-3 p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer"
              >
                <input
                  type="checkbox"
                  checked={selectedKeys.has(key)}
                  onChange={() => handleKeyToggle(key)}
                  className="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                />
                <Badge variant="info" className="flex-1">
                  {key}
                </Badge>
              </label>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSelectAll}
            className="text-indigo-600 hover:text-indigo-700"
          >
            {selectedKeys.size === groupedLink.allKeys.length ? 'Deselect All' : 'Select All'}
          </Button>
          <span className="text-sm text-gray-500">
            {selectedKeys.size} of {groupedLink.allKeys.length} selected
          </span>
        </div>
      </div>
    </Dialog>
  );
}

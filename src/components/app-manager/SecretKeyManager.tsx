'use client';

import {useState, useEffect, useCallback} from 'react';
import Link from 'next/link';
import {Card} from '@/components/ui/Card';
import {Button} from '@/components/ui/Button';
import {Badge} from '@/components/ui/Badge';
import {Input} from '@/components/ui/Input';
import {useToast} from '@/components/ui/Toast';
import {AddEnvDialog, EnvFormData} from './AddEnvDialog';
import {BulkAddEnvDialog, BulkEnvFormData} from './BulkAddEnvDialog';
import {DeleteConfirmDialogProvider, showDeleteConfirmDialog} from './DeleteConfirmDialog';
import {verifyAppSecret as clientVerifyAppSecret, decryptValueWithMEK, encryptValueWithMEK} from '@/utils/encryption';
import {enterEditMode, MEKCache} from '@/utils/master-key-manager';
import {getMasterKey} from '@/server/actions/app-manager';

import {
  getEnvironmentVariables,
  createEnvironmentVariable,
  updateEnvironmentVariable,
  deleteEnvironmentVariable,
  verifyAppSecret,
  bulkCreateOrUpdateEnvironmentVariables
} from '@/server/actions/app-manager';
import {
  EnvVarType,
  EnvVarEnvironment,
  EnvironmentVariable,
  InheritedSource
} from '@/types/environment-variables';
import EnvironmentSelector, {Environment} from '@/components/common/EnvironmentSelector';
import {logger} from '@/utils/logger';

// Helper function to convert client EnvVarType to server EnvVarType
// This is needed because the client and server types have the same values but are defined differently
const convertEnvVarType = (type: EnvVarType): any => {
  // Just pass the string value through, the server will validate it
  return type;
};

interface SecretKeyManagerProps {
  organizationId: string;
  appId: string;
}

interface GroupedInheritedSource extends InheritedSource {
  isExpanded: boolean;
  totalVariables: number;
}

export default function SecretKeyManager({organizationId, appId}: SecretKeyManagerProps) {
  const {success, error} = useToast();
  const [addEnvDialogOpen, setAddEnvDialogOpen] = useState(false);
  const [bulkAddEnvDialogOpen, setBulkAddEnvDialogOpen] = useState(false);
  const [editEnvDialogOpen, setEditEnvDialogOpen] = useState(false);
  const [selectedEnv, setSelectedEnv] = useState<EnvFormData | null>(null);
  const [ownEnvs, setOwnEnvs] = useState<EnvironmentVariable[]>([]);
  const [groupedInheritedEnvs, setGroupedInheritedEnvs] = useState<GroupedInheritedSource[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // Environment selection state
  const [selectedEnvironment, setSelectedEnvironment] = useState<Environment>('development');
  
  // Edit Mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [appSecret, setAppSecret] = useState('');
  const [appSecretInput, setAppSecretInput] = useState('');
  const [showAppSecretInput, setShowAppSecretInput] = useState(false);
  const [editModeTimeoutId, setEditModeTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [cachedMasterKey, setCachedMasterKey] = useState<CryptoKey | null>(null);
  
  // Constants
  const EDIT_MODE_TIMEOUT = 15 * 60 * 1000; // 15 minutes in milliseconds
  
  // Fetch environment variables on component mount and when environment changes
  useEffect(() => {
    fetchEnvironmentVariables();
  }, [appId, selectedEnvironment]);
  
  // Check for cached MEK on component mount
  useEffect(() => {
    const checkCachedMEK = async () => {
      try {
        if (MEKCache.isValid(appId)) {
          const cachedMEK = await MEKCache.retrieve(appId);
          if (cachedMEK) {
            // Restore edit mode with cached MEK
            setCachedMasterKey(cachedMEK);
            setIsEditMode(true);
            
            // Set timeout based on remaining cache time
            const remainingTime = MEKCache.getRemainingTime(appId);
            if (remainingTime > 0) {
              const timeoutId = setTimeout(() => {
                exitEditMode();
                success('Edit Mode', 'Edit mode has been automatically disabled due to inactivity');
              }, remainingTime);
              
              setEditModeTimeoutId(timeoutId);
            }
            
          }
        }
      } catch (error) {
      }
    };
    
    checkCachedMEK();
  }, [appId]);
  
  // Reset edit mode when component unmounts
  useEffect(() => {
    return () => {
      if (editModeTimeoutId) {
        clearTimeout(editModeTimeoutId);
      }
    };
  }, [editModeTimeoutId]);
  
  // Function to enter edit mode with MEK retrieval
  const enterEditModeWithMEK = async (secret: string) => {
    try {
      // Clear any existing timeout
      if (editModeTimeoutId) {
        clearTimeout(editModeTimeoutId);
      }
      
      // Use the enterEditMode utility to retrieve and decrypt MEK
      const editModeResult = await enterEditMode(
        appId,
        organizationId,
        secret,
        getMasterKey
      );
      
      if (!editModeResult.success) {
        throw new Error(editModeResult.error || 'Failed to enter edit mode');
      }
      
      // Set the app secret, cached MEK, and enable edit mode
      setAppSecret(secret);
      setCachedMasterKey(editModeResult.masterKey!);
      setIsEditMode(true);
      setShowAppSecretInput(false);
      
      // Set a timeout to automatically exit edit mode
      const timeoutId = setTimeout(() => {
        exitEditMode();
        success('Edit Mode', 'Edit mode has been automatically disabled due to inactivity');
      }, EDIT_MODE_TIMEOUT);
      
      setEditModeTimeoutId(timeoutId);
      
      success('Edit Mode Enabled', 'You can now add, edit, and delete environment variables');
    } catch (err: any) {
      error('Edit Mode Error', err.message || 'Failed to enter edit mode');
      throw err;
    }
  };
  
  // Function to exit edit mode
  const exitEditMode = () => {
    // Clear the timeout
    if (editModeTimeoutId) {
      clearTimeout(editModeTimeoutId);
      setEditModeTimeoutId(null);
    }
    
    // Clear MEK cache
    MEKCache.clear(appId);
    
    // Reset state
    setAppSecret('');
    setIsEditMode(false);
    setAppSecretInput('');
    setCachedMasterKey(null);
  };
  
  // Function to handle app secret submission
  const handleAppSecretSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!appSecretInput.trim()) {
      error('Error', 'App Secret is required');
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get the keyCheck value from the server
      const response = await verifyAppSecret({
        appId,
        organizationId
      });
      
      if (response.success && response.data?.keyCheck) {
        // Verify the app secret on the client side
        const isValid = await clientVerifyAppSecret(
          response.data.keyCheck,
          appId,
          appSecretInput.trim()
        );
        
        if (isValid) {
          // If verification is successful, enter edit mode with MEK retrieval
          await enterEditModeWithMEK(appSecretInput.trim());
        } else {
          // If verification fails, show an error
          error('Invalid App Secret', 'The App Secret you entered is invalid. Please try again.');
        }
      } else {
        // If we couldn't get the keyCheck value, show an error
        error('Error', response?.error || 'Failed to verify App Secret');
      }
    } catch (err: any) {
      error('Error', err?.message || 'An unexpected error occurred while verifying the App Secret');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Function to reset the edit mode timeout
  const resetEditModeTimeout = useCallback(() => {
    if (isEditMode) {
      // Clear existing timeout
      if (editModeTimeoutId) {
        clearTimeout(editModeTimeoutId);
      }
      
      // Set a new timeout
      const timeoutId = setTimeout(() => {
        exitEditMode();
        success('Edit Mode', 'Edit mode has been automatically disabled due to inactivity');
      }, EDIT_MODE_TIMEOUT);
      
      setEditModeTimeoutId(timeoutId);
    }
  }, [isEditMode, editModeTimeoutId, EDIT_MODE_TIMEOUT]);
  
  // Function to process inherited variables into grouped format with expansion state
  const processInheritedVariables = (inheritedVars: InheritedSource[]): GroupedInheritedSource[] => {
    
    // Group inherited variables by sourceApp.id to handle duplicates
    const groupedBySourceApp = new Map<string, GroupedInheritedSource>();
    
    inheritedVars.forEach(source => {
      const sourceAppId = source.sourceApp.id;
      
      if (groupedBySourceApp.has(sourceAppId)) {
        // Merge envVars arrays for the same source app
        const existing = groupedBySourceApp.get(sourceAppId)!;
        existing.envVars = [...existing.envVars, ...source.envVars];
      } else {
        // Create new grouped entry
        groupedBySourceApp.set(sourceAppId, {
          sourceApp: source.sourceApp,
          envVars: [...source.envVars], // Create a copy of the array
          isExpanded: true, // Default to expanded for better UX
          totalVariables: 0 // Will be calculated after deduplication
        });
      }
    });
    
    // Deduplicate environment variables within each source app and calculate totals
    groupedBySourceApp.forEach(group => {
      // Remove duplicate environment variables based on their ID
      const uniqueEnvVars = group.envVars.filter((envVar, index, array) =>
        array.findIndex(v => v.id === envVar.id) === index
      );
      
      group.envVars = uniqueEnvVars;
      group.totalVariables = uniqueEnvVars.length;

    });
    
    // Convert Map to array and sort by source app name
    const result = Array.from(groupedBySourceApp.values())
      .sort((a, b) => a.sourceApp.name.localeCompare(b.sourceApp.name));
    
    return result;
  };
  
  // Function to toggle expansion state of a source app group
  const toggleSourceExpansion = (sourceAppId: string) => {
    setGroupedInheritedEnvs(prev =>
      prev.map(source =>
        source.sourceApp.id === sourceAppId
          ? {...source, isExpanded: !source.isExpanded}
          : source
      )
    );
  };
  
  // Function to expand all source app groups
  const expandAllSources = () => {
    setGroupedInheritedEnvs(prev =>
      prev.map(source => ({...source, isExpanded: true}))
    );
  };
  
  // Function to collapse all source app groups
  const collapseAllSources = () => {
    setGroupedInheritedEnvs(prev =>
      prev.map(source => ({...source, isExpanded: false}))
    );
  };
  
  const fetchEnvironmentVariables = async () => {
    setIsLoading(true);
    try {
      // Convert Environment to EnvVarEnvironment
      const envVarEnvironment = selectedEnvironment as EnvVarEnvironment;
      const response = await getEnvironmentVariables(appId, envVarEnvironment);
      if (response.success && response.data) {
        setOwnEnvs(response.data.ownVars || []);
        
        // Process inherited variables into grouped format
        const grouped = processInheritedVariables(response.data.inheritedVars || []);
        setGroupedInheritedEnvs(grouped);
      } else {
        error('Error', response.error || 'Failed to fetch environment variables');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleAddEnv = async (data: EnvFormData) => {
    try {
      resetEditModeTimeout();
      
      // Use the stored app secret in edit mode, or the one provided in the form
      const secretToUse = isEditMode ? appSecret : data.appSecret;
      
      // Use secure MEK-based encryption with cached MEK
      if (!cachedMasterKey) {
        throw new Error('Master key not available. Please re-enter edit mode.');
      }
      const encryptedData = await encryptValueWithMEK(data.value, cachedMasterKey);
      
      // Create the environment variable with MEK-based encryption fields
      const response = await createEnvironmentVariable({
        key: data.key,
        value: encryptedData.value,
        type: convertEnvVarType(data.type),
        description: data.description,
        appId: appId,
        environment: selectedEnvironment as EnvVarEnvironment,
        // Include MEK-based encryption fields
        iv: encryptedData.iv,
        tag: encryptedData.tag
      });
      
      if (response.success) {
        success('ENV Added', `${data.key} has been added successfully`);
        fetchEnvironmentVariables(); // Refresh the list
      } else {
        error('Error', response.error || 'Failed to add environment variable');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    }
  };
  
  const handleEditEnv = async (data: EnvFormData) => {
    if (!selectedEnv) return;
    
    try {
      resetEditModeTimeout();
      
      // Use the stored app secret in edit mode, or the one provided in the form
      const secretToUse = isEditMode ? appSecret : data.appSecret;
      
      // Use secure MEK-based encryption with cached MEK
      if (!cachedMasterKey) {
        throw new Error('Master key not available. Please re-enter edit mode.');
      }
      const encryptedData = await encryptValueWithMEK(data.value, cachedMasterKey);
      
      // Find the original environment variable to get its ID
      const originalEnv = ownEnvs.find(env => env.key === data.key);
      if (!originalEnv) {
        error('Error', 'Could not find the original environment variable');
        return;
      }
      
      // Update the environment variable with MEK-based encryption fields
      const response = await updateEnvironmentVariable({
        id: originalEnv.id,
        value: encryptedData.value,
        type: data.type ? convertEnvVarType(data.type) : undefined,
        description: data.description,
        organizationId: organizationId,
        // Include MEK-based encryption fields
        iv: encryptedData.iv,
        tag: encryptedData.tag
      });
      
      if (response.success) {
        success('ENV Updated', `${data.key} has been updated successfully`);
        fetchEnvironmentVariables(); // Refresh the list
        setEditEnvDialogOpen(false); // Close the dialog
      } else {
        error('Error', response.error || 'Failed to update environment variable');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    }
  };
  
  const handleDeleteEnv = async (env: EnvironmentVariable) => {
    try {
      resetEditModeTimeout();
      
      // Delete the environment variable
      const response = await deleteEnvironmentVariable({
        id: env.id,
        organizationId: organizationId
      });
      
      if (response.success) {
        success('ENV Deleted', `${env.key} has been deleted successfully`);
        fetchEnvironmentVariables(); // Refresh the list
      } else {
        error('Error', response.error || 'Failed to delete environment variable');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    }
  };
  
  const openEditDialog = async (env: EnvironmentVariable) => {
    try {
      resetEditModeTimeout();
      
      // Only attempt to decrypt if in Edit Mode and we have the cached MEK
      // and all required MEK-based encryption fields are present
      if (isEditMode && cachedMasterKey && env.value && env.iv && env.tag) {
        try {
          
          // Use secure MEK-based decryption with cached MEK
          const decryptedValue = await decryptValueWithMEK(
            env.value,
            env.iv!,
            env.tag!,
            cachedMasterKey
          )
          // Create an EnvFormData object with the decrypted value
          const formData: EnvFormData = {
            key: env.key,
            type: env.type,
            value: decryptedValue,
            appSecret: '',
            description: env.description || '',
            skipAppSecret: true
          };
          
          // Set the selected env with the decrypted value
          setSelectedEnv(formData);
        } catch (decryptError) {
          
          const formData: EnvFormData = {
            key: env.key,
            type: env.type,
            value: '',
            appSecret: '',
            description: env.description || '',
            skipAppSecret: true
          };
          
          setSelectedEnv(formData);
        }
      } else {
        // If not in Edit Mode or missing encryption fields, just set the selected env without decrypting
        const formData: EnvFormData = {
          key: env.key,
          type: env.type,
          value: '',
          appSecret: '',
          description: env.description || '',
          skipAppSecret: true
        };
        
        setSelectedEnv(formData);
      }
      
      // Open the dialog after setting the selected env
      setEditEnvDialogOpen(true);
    } catch (err: any) {
      error('Error', 'An unexpected error occurred. Please try again.');
      logger.error('Error in openEditDialog:', err);
    }
  };
  
  const openDeleteDialog = (env: EnvironmentVariable) => {
    showDeleteConfirmDialog({
      itemType: 'Environment Variable',
      itemName: env.key,
      onConfirm: () => handleDeleteEnv(env)
    });
  };
  
  const handleBulkAddEnvs = async (data: BulkEnvFormData) => {
    try {
      resetEditModeTimeout();
      
      // Check if we have cached MEK
      if (!cachedMasterKey) {
        throw new Error('Master key not available. Please re-enter edit mode.');
      }
      
      // Prepare variables for bulk create or update
      const variables = await Promise.all(
        data.parsedEnvs.map(async (env) => {
          // Use secure MEK-based encryption with cached MEK
          const encryptedData = await encryptValueWithMEK(env.value, cachedMasterKey);
          
          // Find the corresponding existing env if it exists
          const existingEnv = ownEnvs.find(e => e.key === env.key);
          
          return {
            key: env.key,
            value: encryptedData.value,
            type: convertEnvVarType(env.type),
            description: existingEnv?.description || '',
            id: existingEnv?.id, // Include ID if it's an existing variable
            // Include MEK-based encryption fields
            iv: encryptedData.iv,
            tag: encryptedData.tag
          };
        })
      );
      
      // Call the bulk create or update function
      const response = await bulkCreateOrUpdateEnvironmentVariables({
        appId,
        organizationId,
        environment: selectedEnvironment as EnvVarEnvironment,
        variables
      });
      
      if (response.success) {
        const summary = response.data?.summary;
        if (summary) {
          // Show success message with details
          const messages = [];
          
          if (summary.created > 0) {
            messages.push(`Created ${summary.created} new variables`);
          }
          
          if (summary.updated > 0) {
            messages.push(`Updated ${summary.updated} existing variables`);
          }
          
          success('ENVs Processed', messages.join(', '));
          
          if (summary.failure > 0) {
            error('Some Operations Failed', `Failed to process ${summary.failure} environment variables`);
          }
        } else {
          success('ENVs Processed', 'Environment variables processed successfully');
        }
      } else {
        error('Error', response.error || 'Failed to process environment variables');
      }
      
      // Refresh the list
      fetchEnvironmentVariables();
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    }
  };
  
  return (
    <div className="space-y-6">
      <DeleteConfirmDialogProvider/>
      
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div className="flex flex-col sm:flex-row sm:items-center gap-4">
          <h3 className="text-xl font-semibold">Environment:</h3>
          <EnvironmentSelector
            showLabel={false}
            value={selectedEnvironment}
            onChange={setSelectedEnvironment}
          />
        </div>
        <div className="flex flex-wrap gap-2">
          {isEditMode ? (
            <>
              <Button
                onClick={() => setAddEnvDialogOpen(true)}
                className="flex-grow md:flex-grow-0"
              >
                <i className="pi pi-plus mr-2"></i>
                <span className="hidden sm:inline">Add New ENV</span>
                <span className="sm:hidden">Add</span>
              </Button>
              <Button
                onClick={() => {
                  // Open the bulk dialog for editing existing variables
                  setBulkAddEnvDialogOpen(true);
                }}
                className="flex-grow md:flex-grow-0"
              >
                <i className="pi pi-list mr-2"></i>
                <span className="hidden sm:inline">Bulk Edit</span>
                <span className="sm:hidden">Bulk</span>
              </Button>
              <Button
                variant="outline"
                onClick={exitEditMode}
                className="flex-grow md:flex-grow-0"
              >
                <i className="pi pi-lock mr-2"></i>
                <span className="hidden sm:inline">Exit Edit Mode</span>
                <span className="sm:hidden">Exit</span>
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setShowAppSecretInput(true)}
              className="flex-grow md:flex-grow-0"
            >
              <i className="pi pi-unlock mr-2"></i>
              <span className="hidden sm:inline">Enter Edit Mode</span>
              <span className="sm:hidden">Edit Mode</span>
            </Button>
          )}
          <Link
            href={`/dashboard/organization/${organizationId}/app-manager/apps/${appId}/inheritance`}
            className="flex-grow md:flex-grow-0"
          >
            <Button
              variant="outline"
              className="w-full"
            >
              <i className="pi pi-link mr-2"></i>
              <span className="hidden sm:inline">Manage Inheritance</span>
              <span className="sm:hidden">Inheritance</span>
            </Button>
          </Link>
        </div>
      </div>
      
      {isEditMode && (
        <div
          className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-4">
          <p className="text-green-800 dark:text-green-300 text-sm">
            <i className="pi pi-unlock mr-2"></i>
            <strong>Edit Mode Active:</strong> You can now add, edit, and delete environment variables. Edit mode will
            automatically disable after 15 minutes of inactivity.
          </p>
        </div>
      )}
      
      {showAppSecretInput && !isEditMode && (
        <div
          className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-4">
          <form onSubmit={handleAppSecretSubmit} className="space-y-4">
            <p className="text-yellow-800 dark:text-yellow-300 text-sm mb-2">
              <i className="pi pi-key mr-2"></i>
              Enter your App Secret to enable Edit Mode. This will allow you to add, edit, and delete environment
              variables.
            </p>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input
                containerClass="w-full sm:max-w-[75%]"
                type="password"
                placeholder="Enter your App Secret"
                value={appSecretInput}
                onChange={(e) => setAppSecretInput(e.target.value)}
                className="flex-1"
              />
              <div className="flex gap-2">
                <Button
                  type="submit"
                  disabled={isLoading}
                  isLoading={isLoading}
                  className="flex-1 sm:flex-none"
                >
                  {isLoading ? (
                    <>
                      Verifying...
                    </>
                  ) : (
                    <>
                      <i className="pi pi-unlock mr-2"></i>
                      Unlock
                    </>
                  )}
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => setShowAppSecretInput(false)}
                  className="flex-1 sm:flex-none"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </form>
        </div>
      )}
      
      {!showAppSecretInput && !isEditMode && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
          <p className="text-blue-800 dark:text-blue-300 text-sm">
            <i className="pi pi-info-circle mr-2"></i>
            All environment variables are encrypted in your browser with your AppSecret. We never see or store your
            plaintext values.
          </p>
        </div>
      )}
      
      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <>
          <Card>
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium">Your ENVs</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Environment variables for this application
              </p>
            </div>
            <div className="overflow-x-auto">
              {ownEnvs.length === 0 ? (
                <div className="p-8 text-center">
                  <p className="text-gray-500 dark:text-gray-400">
                    No environment variables found. Click {"Add New ENV"} to create one.
                  </p>
                </div>
              ) : (
                <div className="min-w-full">
                  {/* Desktop Table - Hidden on small screens */}
                  <table className="w-full hidden md:table">
                    <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Key</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Type</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Version
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Last
                        Updated
                      </th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Actions
                      </th>
                    </tr>
                    </thead>
                    <tbody>
                    {ownEnvs.map((env) => (
                      <tr key={env.id}
                          className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-4 text-sm font-medium text-gray-900 dark:text-white">
                          <div className="flex items-center">
                            <i className="pi pi-lock text-gray-400 mr-2"></i>
                            {env.key}
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                          <Badge variant={env.type === 'JSON' ? 'info' : 'warning'}>
                            {env.type}
                          </Badge>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">{env.version}</td>
                        <td className="px-4 py-4 text-sm text-gray-500 dark:text-gray-400">
                          {new Date(env.updatedAt).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-4 text-sm">
                          {isEditMode ? (
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openEditDialog(env)}
                              >
                                Edit
                              </Button>
                              <span className="text-gray-300 dark:text-gray-600">•</span>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                onClick={() => openDeleteDialog(env)}
                              >
                                Delete
                              </Button>
                            </div>
                          ) : (
                            <div className="text-gray-400 dark:text-gray-600 text-xs italic">
                              Enter Edit Mode to modify
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                    </tbody>
                  </table>
                  
                  {/* Mobile Card View - Shown only on small screens */}
                  <div className="md:hidden space-y-4 px-4 py-2">
                    {ownEnvs.map((env) => (
                      <div
                        key={env.id}
                        className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800"
                      >
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center">
                            <i className="pi pi-lock text-gray-400 mr-2"></i>
                            <span className="font-medium text-gray-900 dark:text-white">{env.key}</span>
                          </div>
                          <Badge variant={env.type === 'JSON' ? 'info' : 'warning'}>
                            {env.type}
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-500 dark:text-gray-400 mb-3">
                          <div>
                            <span className="font-medium">Version:</span> {env.version}
                          </div>
                          <div>
                            <span className="font-medium">Updated:</span> {new Date(env.updatedAt).toLocaleDateString()}
                          </div>
                        </div>
                        
                        {isEditMode ? (
                          <div
                            className="flex items-center space-x-2 mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openEditDialog(env)}
                              className="flex-1"
                            >
                              <i className="pi pi-pencil mr-1"></i> Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex-1 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                              onClick={() => openDeleteDialog(env)}
                            >
                              <i className="pi pi-trash mr-1"></i> Delete
                            </Button>
                          </div>
                        ) : (
                          <div
                            className="text-gray-400 dark:text-gray-600 text-xs italic mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                            Enter Edit Mode to modify
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
          
          {groupedInheritedEnvs.length > 0 && (
            <Card>
              <div className="p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div>
                    <div className="flex items-center space-x-3">
                      <h3 className="text-lg font-medium">Inherited ENVs</h3>
                      <Badge variant="info" className="text-xs">
                        {groupedInheritedEnvs.reduce((total, source) => total + source.totalVariables, 0)} total
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Variables from {groupedInheritedEnvs.length} source
                      app{groupedInheritedEnvs.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={expandAllSources}
                      className="text-xs"
                    >
                      <i className="pi pi-plus mr-1"></i>
                      Expand All
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={collapseAllSources}
                      className="text-xs"
                    >
                      <i className="pi pi-minus mr-1"></i>
                      Collapse All
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {groupedInheritedEnvs.map((source) => (
                  <div key={source.sourceApp.id} className="p-4 sm:p-6">
                    {/* Collapsible Header */}
                    <button
                      onClick={() => toggleSourceExpansion(source.sourceApp.id)}
                      className="w-full flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg p-2 -m-2 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex items-center space-x-2">
                          <i
                            className={`pi ${source.isExpanded ? 'pi-chevron-down' : 'pi-chevron-right'} text-gray-400 transition-transform duration-200`}></i>
                          <h4 className="text-md font-medium text-gray-900 dark:text-white">
                            {source.sourceApp.name}
                          </h4>
                        </div>
                        <Badge variant="info" className="text-xs">
                          {source.totalVariables} variable{source.totalVariables !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="default" className="text-xs">
                          Inherited
                        </Badge>
                      </div>
                    </button>
                    
                    {/* Collapsible Content */}
                    <div
                      className={`mt-4 transition-all duration-300 ease-in-out ${source.isExpanded ? 'opacity-100 max-h-none' : 'opacity-0 max-h-0 overflow-hidden'}`}>
                      <div className="overflow-x-auto">
                        {/* Desktop Table - Hidden on small screens */}
                        <table className="w-full hidden md:table">
                          <thead>
                          <tr className="border-b border-gray-200 dark:border-gray-700">
                            <th
                              className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Key
                            </th>
                            <th
                              className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Type
                            </th>
                            <th
                              className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Environment
                            </th>
                            <th
                              className="px-4 py-2 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Last
                              Updated
                            </th>
                          </tr>
                          </thead>
                          <tbody>
                          {source.envVars.map((env) => (
                            <tr key={env.id}
                                className="border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800">
                              <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                                <div className="flex items-center">
                                  <i className="pi pi-lock text-gray-400 mr-2"></i>
                                  {env.key}
                                </div>
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                                <Badge variant={env.type === 'JSON' ? 'info' : 'warning'}>
                                  {env.type}
                                </Badge>
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                                <Badge variant="default" className="text-xs">
                                  {env.environment}
                                </Badge>
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400">
                                {new Date(env.updatedAt).toLocaleDateString()}
                              </td>
                            </tr>
                          ))}
                          </tbody>
                        </table>
                        
                        {/* Mobile Card View - Shown only on small screens */}
                        <div className="md:hidden space-y-3">
                          {source.envVars.map((env) => (
                            <div
                              key={env.id}
                              className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-800"
                            >
                              <div className="flex justify-between items-start mb-2">
                                <div className="flex items-center">
                                  <i className="pi pi-lock text-gray-400 mr-2"></i>
                                  <span className="font-medium text-gray-900 dark:text-white">{env.key}</span>
                                </div>
                                <Badge variant={env.type === 'JSON' ? 'info' : 'warning'}>
                                  {env.type}
                                </Badge>
                              </div>
                              
                              <div
                                className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                                <div className="flex items-center space-x-2">
                                  <Badge variant="default" className="text-xs">
                                    {env.environment}
                                  </Badge>
                                  <span>•</span>
                                  <span>Updated: {new Date(env.updatedAt).toLocaleDateString()}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )}
        </>
      )}
      
      {/* Add New ENV Dialog */}
      <AddEnvDialog
        open={addEnvDialogOpen}
        onOpenChange={setAddEnvDialogOpen}
        onAddEnv={handleAddEnv}
        initialData={isEditMode ? {
          key: '',
          type: 'String',
          value: '',
          appSecret: '',
          description: '',
          skipAppSecret: true
        } : undefined}
      />
      
      {/* Edit ENV Dialog */}
      {selectedEnv && (
        <AddEnvDialog
          open={editEnvDialogOpen}
          onOpenChange={setEditEnvDialogOpen}
          onAddEnv={handleEditEnv}
          initialData={selectedEnv}
          isEditing={true}
        />
      )}
      
      {/* Bulk Add/Edit ENV Dialog */}
      <BulkAddEnvDialog
        open={bulkAddEnvDialogOpen}
        onOpenChange={setBulkAddEnvDialogOpen}
        onAddEnvs={handleBulkAddEnvs}
        appSecret={appSecret}
        appId={appId}
        organizationId={organizationId}
        existingEnvs={isEditMode ? ownEnvs : undefined}
        cachedMasterKey={cachedMasterKey}
      />
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { Dialog } from '@/components/ui/Dialog';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Switch } from '@/components/ui/Switch';
import { Alert } from '@/components/ui/Alert';
import CodeEditor from '@uiw/react-textarea-code-editor';
import { EnvVarType } from '@/types/environment-variables';
import { logger } from '@/utils/logger';

// Types and interfaces
interface AddEnvDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddEnv: (data: EnvFormData) => void;
  initialData?: EnvFormData;
  isEditing?: boolean;
}

export interface EnvFormData {
  key: string;
  type: EnvVarType;
  value: string;
  appSecret: string;
  description?: string;
  skipAppSecret?: boolean; // Flag to indicate if App Secret field should be skipped
}

/**
 * Dialog component for adding or editing environment variables
 */
export function AddEnvDialog({
  open,
  onOpenChange,
  onAddEnv,
  initialData,
  isEditing = false
}: AddEnvDialogProps) {
  // State
  const [jsonEditorExpanded, setJsonEditorExpanded] = useState(false);
  const [valueError, setValueError] = useState<string | null>(null);

  // Form setup
  const form = useForm<EnvFormData>({
    defaultValues: initialData || {
      key: '',
      type: 'String',
      value: '',
      appSecret: '',
      description: ''
    },
    mode: 'onChange'
  });

  // Update form values when initialData changes or dialog opens
  useEffect(() => {
    if (open && initialData) {
      // Reset the form with the new initialData
      form.reset(initialData);

      // If it's JSON type, check if we need to format it
      if (initialData.type === 'JSON' && initialData.value) {
        try {
          // Try to parse and format the JSON
          const formattedJson = formatJson(initialData.value);
          form.setValue('value', formattedJson);
        } catch (e) {
          // If it's not valid JSON, just use the value as is
          logger.error('Failed to format JSON:', e);
        }
      }
    }
  }, [open, initialData, form]);

  // Get the current type selection
  const selectedType = form.watch('type');

  // Handlers
  const validateValue = (value: string, type: string): boolean => {
    setValueError(null);
    if (type === 'Number') {
      if (isNaN(Number(value))) {
        setValueError('Please enter a valid number');
        return false;
      }
    } else if (type === 'Boolean') {
      if (value !== 'true' && value !== 'false') {
        setValueError('Boolean value must be true or false');
        return false;
      }
    } else if (type === 'JSON') {
      try {
        JSON.parse(value);
      } catch (e) {
        setValueError('Please enter valid JSON');
        return false;
      }
    }

    return true;
  };

  const formatJson = (jsonString: string): string => {
    try {
      return JSON.stringify(JSON.parse(jsonString), null, 2);
    } catch (e) {
      return jsonString;
    }
  };

  const resetForm = () => {
    // If we're editing and have initialData, reset to that instead of empty values
    if (isEditing && initialData) {
      form.reset(initialData);
    } else {
      // Otherwise, reset to empty values
      form.reset({
        key: '',
        type: 'String',
        value: '',
        appSecret: '',
        description: ''
      });
    }
    setJsonEditorExpanded(false);
    setValueError(null);
  };

  const handleSubmit = (data: EnvFormData) => {
    // Validate the value based on type
    if (!validateValue(data.value, data.type)) {
      return;
    }

    // Format JSON if needed
    if (data.type === 'JSON') {
      data.value = formatJson(data.value);
    }

    // Submit data
    onAddEnv(data);
    resetForm();
    onOpenChange(false);
  };

  const handleCancel = () => {
    // Don't reset the form here, just close the dialog
    // The form will be reset when the dialog is reopened
    onOpenChange(false);
  };

  const toggleJsonEditor = () => {
    if (jsonEditorExpanded) {
      setJsonEditorExpanded(false);
    } else {
      // When expanding, try to format the JSON nicely
      try {
        const currentValue = form.getValues('value');
        if (currentValue && currentValue.trim() !== '') {
          form.setValue('value', formatJson(currentValue));
        }
        setJsonEditorExpanded(true);
      } catch (e) {
        setValueError('Warning: Your JSON appears to be invalid. Please correct it in the editor.');
        setJsonEditorExpanded(true);
      }
    }
  };

  // UI components
  const renderValueField = () => {
    return (
      <Controller
        control={form.control}
        name="value"
        rules={{ required: 'Value is required' }}
        render={({ field, fieldState }) => (
          <>
            {selectedType === 'Boolean' && (
              <div className="flex items-center space-x-2">
                <Switch
                  checked={field.value === 'true'}
                  onChange={(e) => field.onChange(e.value ? 'true' : 'false')}
                />
                <span className="text-sm font-medium">
                  {field.value === 'true' ? 'True' : 'False'}
                </span>
              </div>
            )}

            {selectedType === 'JSON' && (
              jsonEditorExpanded ? (
                <div className="border border-gray-300 dark:border-gray-700 rounded-md overflow-hidden">
                  <div className="bg-gray-100 dark:bg-gray-800 px-2 sm:px-4 py-2 border-b border-gray-300 dark:border-gray-700 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">JSON Editor</span>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          try {
                            field.onChange(formatJson(field.value));
                          } catch (e) {
                            setValueError('Please enter valid JSON before formatting');
                          }
                        }}
                        className="flex-1 sm:flex-none"
                      >
                        Format JSON
                      </Button>
                    </div>
                  </div>
                  <div className="overflow-x-auto">
                    <CodeEditor
                      value={field.value}
                      language="json"
                      placeholder='{"key": "value"}'
                      onChange={(e) => {
                        field.onChange(e.target.value);
                        validateValue(e.target.value, selectedType);
                      }}
                      padding={15}
                      style={{
                        fontSize: 14,
                        backgroundColor: "transparent",
                        fontFamily: 'ui-monospace, SFMono-Regular, SF Mono, Consolas, Liberation Mono, Menlo, monospace',
                        minHeight: "200px",
                        maxHeight: "50vh",
                        width: "100%"
                      }}
                      data-color-mode="dark"
                    />
                  </div>
                </div>
              ) : (
                <div className="relative">
                  <Input
                    {...field}
                    className="font-mono pr-10"
                    placeholder='{"key": "value"}'
                    onChange={(e) => {
                      field.onChange(e);
                      validateValue(e.target.value, selectedType);
                    }}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 p-0"
                    onClick={() => {
                      try {
                        field.onChange(JSON.stringify(JSON.parse(field.value), null, 0));
                      } catch (e) {
                        setValueError('Please enter valid JSON before formatting');
                      }
                    }}
                    title="Format JSON"
                  >
                    <i className="pi pi-code text-xs"></i>
                  </Button>
                </div>
              )
            )}

            {selectedType === 'Number' && (
              <Input
                {...field}
                type="number"
                placeholder="0"
                onChange={(e) => {
                  field.onChange(e);
                  validateValue(e.target.value, selectedType);
                }}
              />
            )}

            {selectedType === 'String' && (
              <Input
                {...field}
                placeholder="Enter value"
              />
            )}

            {/* Display validation errors */}
            {valueError && (
              <Alert severity="error" className="mt-2 py-2">
                {valueError}
              </Alert>
            )}

            {/* Field-specific help text */}
            {selectedType === 'JSON' && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Enter valid JSON. Use the expand button for a larger editor.
              </p>
            )}
            {selectedType === 'Number' && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Enter a numeric value.
              </p>
            )}
            {selectedType === 'Boolean' && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Toggle the switch to set true or false.
              </p>
            )}

            {fieldState.error && (
              <p className="text-sm text-red-500 mt-1">{fieldState.error.message}</p>
            )}
          </>
        )}
      />
    );
  };

  const dialogFooter = (
    <div className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 sm:space-x-2">
      <Button
        variant="ghost"
        onClick={handleCancel}
        className="w-full sm:w-auto"
      >
        Cancel
      </Button>
      <Button
        type="submit"
        form="add-env-form"
        className="w-full sm:w-auto"
      >
        {form.getValues('skipAppSecret') ? 'Save' : 'Encrypt & Save'}
      </Button>
    </div>
  );

  // Main render
  return (
    <Dialog
      title={isEditing ? "Edit Environment Variable" : "Add New ENV"}
      open={open}
      onOpenChange={(isOpen) => {
        // Simply pass the open state to the parent component
        onOpenChange(isOpen);
      }}
      footer={dialogFooter}
      size={jsonEditorExpanded ? 'large' : 'default'}
    >
      <form id="add-env-form" onChange={() => {
        if(form.getValues("type") !=="JSON") {
          setValueError(null);
        }
      }} onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {/* Key Field */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Key <span className="text-red-500">*</span>
          </label>
          <Controller
            control={form.control}
            name="key"
            rules={{ required: 'Key is required' }}
            render={({ field, fieldState }) => (
              <>
                <Input
                  {...field}
                  placeholder="e.g., DATABASE_URL"
                  disabled={isEditing}
                  readOnly={isEditing}
                />
                {fieldState.error && (
                  <p className="text-sm text-red-500 mt-1">{fieldState.error.message}</p>
                )}
              </>
            )}
          />
        </div>

        {/* Type Field */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Type
          </label>
          <Controller
            control={form.control}
            name="type"
            render={({ field, fieldState }) => (
              <>
                <Select
                  value={field.value}
                  onChange={(value) => field.onChange(value)}
                  options={[
                    { label: 'String', value: 'String' },
                    { label: 'Number', value: 'Number' },
                    { label: 'Boolean', value: 'Boolean' },
                    { label: 'JSON', value: 'JSON' },
                  ]}
                />
                {fieldState.error && (
                  <p className="text-sm text-red-500 mt-1">{fieldState.error.message}</p>
                )}
              </>
            )}
          />
        </div>

        {/* Value Field */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Value <span className="text-red-500">*</span>
            </label>
            {selectedType === 'JSON' && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={toggleJsonEditor}
              >
                {jsonEditorExpanded ? 'Collapse' : 'Expand'} <i className={`pi ${jsonEditorExpanded ? 'pi-compress' : 'pi-expand'} ml-1`}></i>
              </Button>
            )}
          </div>
          {renderValueField()}
        </div>

        {/* Description Field */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Description
          </label>
          <Controller
            control={form.control}
            name="description"
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Optional description for this environment variable"
              />
            )}
          />
        </div>

        {/* Information about Edit Mode when skipAppSecret is true */}
        {form.getValues('skipAppSecret') && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
            <p className="text-blue-800 dark:text-blue-300 text-sm">
              <i className="pi pi-info-circle mr-2"></i>
              Using App Secret from your active Edit Mode session for encryption. Your App Secret is never stored on our servers.
            </p>
          </div>
        )}
      </form>
    </Dialog>
  );
}

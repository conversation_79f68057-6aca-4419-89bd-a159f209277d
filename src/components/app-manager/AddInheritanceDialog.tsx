'use client';

import {useState, useEffect} from 'react';
import {Controller, useForm} from 'react-hook-form';
import {Dialog} from '@/components/ui/Dialog';
import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {Select} from '@/components/ui/Select';
import {Checkbox} from '@/components/ui/Checkbox';
import {Badge} from '@/components/ui/Badge';
import {Alert} from '@/components/ui/Alert';
import {useToast} from '@/components/ui/Toast';
import {verifyAppSecret as clientVerifyAppSecret} from '@/utils/encryption';
import {
  getEnvironmentVariables,
  getInheritanceLinks,
  verifyAppSecret,
  createMEKInheritance,
  getMasterKey
} from '@/server/actions/app-manager';
import {decryptMasterKey, reEncryptMEKForInheritance} from '@/utils/master-key-manager';
import { logger } from '@/utils/logger';

export type InheritanceManagerResponse = Awaited<ReturnType<typeof getInheritanceLinks>>
export type InheritanceFormData = InheritanceManagerResponse['data'][number]


interface AddInheritanceDialogProps {
  open: boolean;
  organizationId: string;
  targetAppId: string;
  environment: 'development' | 'staging' | 'production';
  onOpenChange: (open: boolean) => void;
  onAddInheritance: () => void;
  availableApps: Array<{ id: string; name: string }>;
  cachedMasterKey: CryptoKey | null;
  isEditMode: boolean;
}


interface EnvironmentVariable {
  id: string;
  key: string;
  type: string;
  description?: string;
  environment: string;
  version: number;
  createdAt: string;
  updatedAt: string;
}

interface AvailableKey {
  key: string;
  type: string;

  value?: string; // Encrypted value (not shown to user)
  description?: string;
  lastUpdated?: string;
}

// Group keys by type for better organization
type GroupedKeys = {
  [type: string]: AvailableKey[];
};

export function AddInheritanceDialog({
                                       open,
                                       onOpenChange,
                                       organizationId,
                                       targetAppId,
                                       environment,
                                       onAddInheritance,
                                       availableApps,
                                       cachedMasterKey,
                                     }: AddInheritanceDialogProps) {
  const {success, error} = useToast();
  const [availableKeys, setAvailableKeys] = useState<AvailableKey[]>([]);
  const [groupedKeys, setGroupedKeys] = useState<GroupedKeys>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('all');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSecretVerified, setIsSecretVerified] = useState(false);
  const [verificationError, setVerificationError] = useState<string | null>(null);
  const [existingInheritedKeys, setExistingInheritedKeys] = useState<string[]>([]);

  const form = useForm<InheritanceFormData>({
    defaultValues: {
      sourceAppId: '',
      sourceAppSecret: '',
      selectedKeys: [] // This will be used as emvKeys in the API call
    }
  });

  // Group keys by type whenever availableKeys changes
  useEffect(() => {
    const grouped = availableKeys.reduce((acc, key) => {
      if (!acc[key.type]) {
        acc[key.type] = [];
      }
      acc[key.type].push(key);
      return acc;
    }, {} as GroupedKeys);

    setGroupedKeys(grouped);
  }, [availableKeys]);

  const handleSubmit = async (data: InheritanceFormData) => {
    // Only proceed if the Source App Secret has been verified
    if (!isSecretVerified) {
      error('Verification Required', 'Please verify the Source App Secret before proceeding');
      return;
    }

    // Check if we have the cached target MEK
    if (!cachedMasterKey) {
      error('Edit Mode Required', 'Target app MEK not available. Please ensure Edit Mode is active.');
      return;
    }

    try {
      // Step 1: Get the source app's encrypted MEK
      const sourceMEKResponse = await getMasterKey({
        appId: data.sourceAppId,
        organizationId
      });

      if (!sourceMEKResponse.success || !sourceMEKResponse.data) {
        throw new Error(sourceMEKResponse.error || 'Failed to retrieve source app MEK');
      }

      // Step 2: Decrypt the source app's MEK using the source app secret
      const sourceMEK = await decryptMasterKey(
        sourceMEKResponse.data.encryptedMasterKey,
        sourceMEKResponse.data.masterKeyIV,
        sourceMEKResponse.data.masterKeyTag,
        data.sourceAppSecret,
        data.sourceAppId
      );

      // Step 3: Re-encrypt the source MEK with the target MEK
      const reEncryptedMEK = await reEncryptMEKForInheritance(sourceMEK, cachedMasterKey);

      // Step 4: Create the MEK-based inheritance link
      const inheritanceResponse = await createMEKInheritance({
        targetAppId,
        sourceAppId: data.sourceAppId,
        organizationId,
        emvKeys: data.selectedKeys,
        environment: environment,
        encryptedMasterKey: reEncryptedMEK.encryptedMasterKey,
        masterKeyIV: reEncryptedMEK.masterKeyIV,
        masterKeyTag: reEncryptedMEK.masterKeyTag
      });

      if (!inheritanceResponse.success) {
        throw new Error(inheritanceResponse.error || 'Failed to create inheritance link');
      }

      success('Inheritance Created', inheritanceResponse.data?.message || 'inheritance link created successfully');

      // Reset form and close dialog
      form.reset();
      setAvailableKeys([]);
      setSearchTerm('');
      setSelectedType('all');
      setIsSecretVerified(false);
      setVerificationError(null);
      onOpenChange(false);

      // Refresh the parent component data
      onAddInheritance();
    } catch (err: any) {
      logger.error('MEK inheritance creation error:', err);
      error('Inheritance Error', err.message || 'Failed to create MEK-based inheritance');
    }
  };

  const handleCancel = () => {
    form.reset();
    setAvailableKeys([]);
    setSearchTerm('');
    setSelectedType('all');
    setIsSecretVerified(false);
    setVerificationError(null);
    setExistingInheritedKeys([]);
    onOpenChange(false);
  };

  const handleSourceAppChange = async (appId: string) => {
    form.setValue('sourceAppId', appId);
    form.setValue('selectedKeys', []);
    form.setValue('sourceAppSecret', ''); // Clear the app secret when changing source app
    setIsSecretVerified(false); // Reset verification status
    setVerificationError(null);
    setAvailableKeys([]); // Clear available keys until secret is verified
    setIsLoading(true);

    try {
      // Fetch existing inherited keys from this source app to prevent duplicates
      await fetchExistingInheritedKeys(appId);
      setIsLoading(false);
    } catch (error) {
      logger.error('Error fetching app details:', error);
      setIsLoading(false);
    }
  };

  // Function to fetch existing inherited keys from a source app
  const fetchExistingInheritedKeys = async (sourceAppId: string) => {
    try {
      // Get existing inheritance links for the target app
      const response = await getInheritanceLinks(targetAppId, environment);

      if (response.success && response.data) {
        // Find all keys already inherited from this specific source app
        const existingKeys = response.data
          .filter((link: any) => link.sourceApp.id === sourceAppId)
          .flatMap((link: any) => link.emvKeys);

        setExistingInheritedKeys(existingKeys);
      } else {
        setExistingInheritedKeys([]);
      }
    } catch (error) {
      logger.error('Error fetching existing inherited keys:', error);
      setExistingInheritedKeys([]);
    }
  };

  // Function to verify the source app secret
  const verifySourceAppSecret = async () => {
    const sourceAppId = form.getValues('sourceAppId');
    const sourceAppSecret = form.getValues('sourceAppSecret');

    if (!sourceAppId || !sourceAppSecret) {
      setVerificationError('Source App ID and Secret are required');
      return;
    }

    setIsVerifying(true);
    setVerificationError(null);

    try {
      // Get the keyCheck value from the server
      const response = await verifyAppSecret({
        appId: sourceAppId,
        organizationId // This will be filled in by the server based on the app ID
      });
      if (response.success && response.data?.keyCheck) {
        // Verify the app secret on the client side
        const isValid = await clientVerifyAppSecret(
          response.data.keyCheck,
          sourceAppId,
          sourceAppSecret.trim()
        );

        if (isValid) {
          // If verification is successful, fetch environment variables
          setIsSecretVerified(true);
          success('Verification Successful', 'Source App Secret verified successfully');

          // Now fetch the environment variables
          await fetchEnvironmentVariables(sourceAppId);
        } else {
          // If verification fails, show an error
          setIsSecretVerified(false);
          setVerificationError('Invalid Source App Secret. Please check and try again.');
          error('Verification Failed', 'The Source App Secret you entered is invalid. Please try again.');
        }
      } else {
        // If we couldn't get the keyCheck value, show an error
        setIsSecretVerified(false);
        setVerificationError(response?.error || 'Failed to verify Source App Secret');
        error('Verification Error', response?.error || 'Failed to verify Source App Secret');
      }
    } catch (err: any) {
      logger.error('Error verifying app secret:', err);
      setIsSecretVerified(false);
      setVerificationError(err?.message || 'An unexpected error occurred');
      error('Verification Error', err?.message || 'An unexpected error occurred');
    } finally {
      setIsVerifying(false);
    }
  };

  // Function to fetch environment variables after secret verification
  const fetchEnvironmentVariables = async (appId: string) => {
    setIsLoading(true);

    try {
      // Fetch environment variables from the source app
      const response = await getEnvironmentVariables(appId);

      if (response.success && response.data) {
        // Transform the environment variables to the format we need
        const envVars = response.data.ownVars.map((env: EnvironmentVariable) => ({
          key: env.key,
          type: env.type,
          description: env.description || '',
          lastUpdated: new Date(env.updatedAt).toLocaleDateString()
        }));

        setAvailableKeys(envVars);
      } else {
        logger.error('Failed to fetch environment variables:', response.error);
        setAvailableKeys([]);
        error('Error', response.error || 'Failed to fetch environment variables');
      }
    } catch (err: any) {
      logger.error('Error fetching environment variables:', err);
      setAvailableKeys([]);
      error('Error', err?.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const toggleKeySelection = (key: string) => {
    // Prevent selection of already inherited keys
    if (existingInheritedKeys.includes(key)) {
      error('Duplicate Key', `The key "${key}" is already inherited from this source app. Remove the existing inheritance first to re-inherit this key.`);
      return;
    }

    const currentKeys = form.getValues('selectedKeys') || [];
    const index = currentKeys.indexOf(key);

    if (index === -1) {
      form.setValue('selectedKeys', [...currentKeys, key]);
    } else {
      const newKeys = [...currentKeys];
      newKeys.splice(index, 1);
      form.setValue('selectedKeys', newKeys);
    }
  };

  // Select all keys of a specific type or all keys if type is 'all'
  const selectAllKeys = (type: string = 'all') => {
    const keysToSelect = type === 'all'
      ? availableKeys.map(k => k.key)
      : availableKeys.filter(k => k.type === type).map(k => k.key);

    // Filter out already inherited keys
    const availableKeysToSelect = keysToSelect.filter(key => !existingInheritedKeys.includes(key));

    form.setValue('selectedKeys', availableKeysToSelect);
  };

  // Deselect all keys of a specific type or all keys if type is 'all'
  const deselectAllKeys = (type: string = 'all') => {
    const currentKeys = form.getValues('selectedKeys') || [];

    if (type === 'all') {
      form.setValue('selectedKeys', []);
    } else {
      const keysToRemove = availableKeys
        .filter(k => k.type === type)
        .map(k => k.key);

      const newKeys = currentKeys.filter((key: any) => !keysToRemove.includes(key));
      form.setValue('selectedKeys', newKeys);
    }
  };

  // Filter keys based on search term and selected type
  const getFilteredKeys = () => {
    if (!searchTerm && selectedType === 'all') {
      return availableKeys;
    }

    return availableKeys.filter(key => {
      const matchesSearch = searchTerm
        ? key.key.toLowerCase?.().includes(searchTerm.toLowerCase()) ||
        (key.description && key.description.toLowerCase?.().includes(searchTerm.toLowerCase()))
        : true;

      const matchesType = selectedType === 'all' || key.type === selectedType;

      return matchesSearch && matchesType;
    });
  };

  const footer = (
    <div className="flex justify-end space-x-2">
      <Button variant="ghost" onClick={handleCancel}>Cancel</Button>
      <Button
        type="submit"
        form="add-inheritance-form"
        disabled={
          !form.watch('sourceAppId') ||
          !form.watch('sourceAppSecret') ||
          !isSecretVerified ||
          (form.watch('selectedKeys')?.length || 0) === 0
        }
      >
        Inherit Selected
      </Button>
    </div>
  );

  // Reset state when dialog is opened or closed
  useEffect(() => {
    if (open) {
      // Dialog is being opened
      form.reset();
      setAvailableKeys([]);
      setSearchTerm('');
      setSelectedType('all');
      setIsSecretVerified(false);
      setVerificationError(null);
      setExistingInheritedKeys([]);
    }
  }, [open, form]);

  return (
    <Dialog
      title="Add Inheritance"
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          // Reset state when dialog is closed
          form.reset();
          setAvailableKeys([]);
          setSearchTerm('');
          setSelectedType('all');
          setIsSecretVerified(false);
          setVerificationError(null);
          setExistingInheritedKeys([]);
        }
        onOpenChange(isOpen);
      }}
      footer={footer}
    >
      <form id="add-inheritance-form" onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="sourceAppId" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Source App <span className="text-red-500">*</span>
          </label>
          <Controller
            control={form.control}
            name="sourceAppId"
            rules={{required: 'Source app is required'}}
            render={({field, fieldState}) => (
              <>
                <Select
                  id="sourceAppId"
                  {...field}
                  options={availableApps.map(app => ({label: app.name, value: app.id}))}
                  placeholder="Select source app"
                  error={fieldState.error?.message}
                  onChange={(value) => {
                    field.onChange(value);
                    if (value) {
                      // Call handleSourceAppChange when the source app changes
                      handleSourceAppChange(value as string);
                    }
                  }}
                />
              </>
            )}
          />
        </div>

        <div className="space-y-2">
          <label htmlFor="sourceAppSecret" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Source App Secret <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <Controller
              control={form.control}
              name="sourceAppSecret"
              rules={{required: 'Source app secret is required'}}
              render={({field, fieldState}) => (
                <div className="flex-1">
                  <Input
                    id="sourceAppSecret"
                    {...field}
                    type="password"
                    placeholder="Enter source app secret"
                    error={fieldState.error?.message}
                    disabled={isSecretVerified || isVerifying}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    The source app secret is required to verify your access to the source app&#39;s ENVs.
                  </p>
                </div>
              )}
            />
            <Button
              type="button"
              onClick={verifySourceAppSecret}
              disabled={!form.watch('sourceAppId') || !form.watch('sourceAppSecret') || isVerifying || isSecretVerified}
              isLoading={isVerifying}
              className="self-start"
            >
              {isVerifying ? 'Verifying...' : isSecretVerified ? 'Verified' : 'Verify Secret'}
            </Button>
          </div>

          {verificationError && (
            <Alert severity="error" className="mt-2">
              {verificationError}
            </Alert>
          )}

          {isSecretVerified && (
            <Alert severity="success" className="mt-2">
              Source App Secret verified successfully. You can now select environment variables to inherit.
            </Alert>
          )}
        </div>

        {form.watch('sourceAppId') && isSecretVerified && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Select ENVs to Inherit <span className="text-red-500">*</span>
              </label>
              <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {form.watch('selectedKeys')?.length || 0} of {availableKeys.length - existingInheritedKeys.length} available selected
                  </span>
                  {existingInheritedKeys.length > 0 && (
                    <span className="text-xs text-orange-600 dark:text-orange-400">
                      ({existingInheritedKeys.length} already inherited)
                    </span>
                  )}
              </div>
            </div>

            {/* Search and filter controls */}
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="flex-1">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <svg
                      className="w-4 h-4 text-gray-500 dark:text-gray-400"
                      aria-hidden="true"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 20 20"
                    >
                      <path
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                      />
                    </svg>
                  </div>
                  <Input
                    type="search"
                    placeholder="Search environment variables..."
                    value={searchTerm}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
                    className="w-full pl-10"
                  />
                </div>
              </div>
              <div className="flex-none">
                <Select
                  value={selectedType}
                  onChange={(value) => setSelectedType(value as string)}
                  options={[
                    {label: 'All Types', value: 'all'},
                    ...Object.keys(groupedKeys).map(type => ({
                      label: `${type} (${groupedKeys[type].length})`,
                      value: type
                    }))
                  ]}
                />
              </div>
            </div>

            {/* Select/Deselect All controls */}
            <div className="flex justify-between items-center">
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => selectAllKeys(selectedType)}
                >
                  Select All {selectedType !== 'all' ? selectedType : ''}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => deselectAllKeys(selectedType)}
                >
                  Deselect All {selectedType !== 'all' ? selectedType : ''}
                </Button>
              </div>
            </div>

            {/* Loading state */}
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
              </div>
            ) : (
              <>
                {/* No results state */}
                {getFilteredKeys().length === 0 ? (
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
                    <p className="text-gray-500 dark:text-gray-400">
                      {searchTerm
                        ? `No environment variables found matching "${searchTerm}"`
                        : 'No environment variables available'
                      }
                    </p>
                  </div>
                ) : (
                  /* Environment variables list */
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                    <div className="max-h-80 overflow-y-auto">
                      {selectedType === 'all' ? (
                        // Group by type when showing all
                        Object.entries(groupedKeys).map(([type, keys]) => {
                          const filteredKeys = keys.filter(key =>
                            !searchTerm ||
                            key.key.toLowerCase?.().includes(searchTerm.toLowerCase()) ||
                            (key.description && key.description.toLowerCase?.().includes(searchTerm.toLowerCase()))
                          );

                          if (filteredKeys.length === 0) return null;

                          return (
                            <div key={type} className="mb-2">
                              <div
                                className="bg-gray-50 dark:bg-gray-800 px-4 py-2 font-medium text-gray-700 dark:text-gray-300 flex justify-between items-center">
                                <div className="flex items-center">
                                  <span>{type}</span>
                                  <Badge className="ml-2">{filteredKeys.length}</Badge>
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => selectAllKeys(type)}
                                  >
                                    Select All
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => deselectAllKeys(type)}
                                  >
                                    Deselect All
                                  </Button>
                                </div>
                              </div>
                              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                                {filteredKeys.map((key) => {
                                  const isAlreadyInherited = existingInheritedKeys.includes(key.key);
                                  return (
                                    <div key={key.key}
                                         className={`px-4 py-3 transition-colors ${
                                           isAlreadyInherited
                                             ? 'bg-gray-100 dark:bg-gray-800 opacity-60'
                                             : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                                         }`}>
                                      <div className="flex items-start">
                                        <div className="flex-none pt-1">
                                          <Checkbox
                                            checked={(form.watch('selectedKeys') || []).includes(key.key)}
                                            onChange={() => toggleKeySelection(key.key)}
                                            id={`key-${key.key}`}
                                            disabled={isAlreadyInherited}
                                          />
                                        </div>
                                        <div className="ml-3 flex-1">
                                          <label htmlFor={`key-${key.key}`} className={`flex flex-col ${isAlreadyInherited ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
                                            <div className="flex items-center">
                                              <span className={`font-medium ${isAlreadyInherited ? 'text-gray-500 dark:text-gray-400' : 'text-gray-900 dark:text-white'}`}>
                                                {key.key}
                                              </span>
                                              <Badge className="ml-2">{key.type}</Badge>
                                              {isAlreadyInherited && (
                                                <Badge variant="warning" className="ml-2">Already Inherited</Badge>
                                              )}
                                            </div>
                                            {key.description && (
                                              <span className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                  {key.description}
                                                </span>
                                            )}
                                            {key.lastUpdated && (
                                              <span className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                                  Last updated: {key.lastUpdated}
                                                </span>
                                            )}
                                            {isAlreadyInherited && (
                                              <span className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                                                This key is already inherited from this source app
                                              </span>
                                            )}
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            </div>
                          );
                        })
                      ) : (
                        // Show flat list when filtered by type
                        <div className="divide-y divide-gray-200 dark:divide-gray-700">
                          {getFilteredKeys().map((key) => {
                            const isAlreadyInherited = existingInheritedKeys.includes(key.key);
                            return (
                              <div key={key.key}
                                   className={`px-4 py-3 transition-colors ${
                                     isAlreadyInherited
                                       ? 'bg-gray-100 dark:bg-gray-800 opacity-60'
                                       : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
                                   }`}>
                                <div className="flex items-start">
                                  <div className="flex-none pt-1">
                                    <Checkbox
                                      checked={(form.watch('selectedKeys') || []).includes(key.key)}
                                      onChange={() => toggleKeySelection(key.key)}
                                      id={`key-${key.key}`}
                                      disabled={isAlreadyInherited}
                                    />
                                  </div>
                                  <div className="ml-3 flex-1">
                                    <label htmlFor={`key-${key.key}`} className={`flex flex-col ${isAlreadyInherited ? 'cursor-not-allowed' : 'cursor-pointer'}`}>
                                      <div className="flex items-center">
                                        <span className={`font-medium ${isAlreadyInherited ? 'text-gray-500 dark:text-gray-400' : 'text-gray-900 dark:text-white'}`}>
                                          {key.key}
                                        </span>
                                        <Badge className="ml-2">{key.type}</Badge>
                                        {isAlreadyInherited && (
                                          <Badge variant="warning" className="ml-2">Already Inherited</Badge>
                                        )}
                                      </div>
                                      {key.description && (
                                        <span className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            {key.description}
                                          </span>
                                      )}
                                      {key.lastUpdated && (
                                        <span className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                            Last updated: {key.lastUpdated}
                                          </span>
                                      )}
                                      {isAlreadyInherited && (
                                        <span className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                                          This key is already inherited from this source app
                                        </span>
                                      )}
                                    </label>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Validation message */}
            {(form.watch('selectedKeys')?.length || 0) === 0 && availableKeys.length > 0 && !isLoading && (
              <Alert severity="error" className="mt-2">
                At least one environment variable must be selected
              </Alert>
            )}

            {/* Help text */}
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              Selected environment variables will be inherited from the source app.
              You will not be able to see or modify their values, but you can use them in your application.
            </p>
          </div>
        )}
      </form>
    </Dialog>
  );
}

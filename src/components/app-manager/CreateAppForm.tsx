'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Steps } from '@/components/ui/Steps';
import { useToast } from '@/components/ui/Toast';
import { createApp, updateKeyCheck } from '@/server/actions/app-manager';
import { generateAppSecret, generateKeyCheck } from '@/utils/encryption';
import { generateAndEncryptMEK } from '@/utils/master-key-manager';
import { logger } from '@/utils/logger';

// Form validation schema
const appFormSchema = z.object({
  name: z.string().min(1, 'App name is required'),
  description: z.string().optional(),
  tags: z.string().optional(),
});

type AppFormData = z.infer<typeof appFormSchema>;

interface CreateAppFormProps {
  organizationId: string;
}

interface GeneratedCredentials {
  appId: string;
  apiKey: string;
  appSecret: string;
}

export default function CreateAppForm({ organizationId }: CreateAppFormProps) {
  const router = useRouter();
  const { success, error } = useToast();
  const [activeStep, setActiveStep] = useState(0);
  const [generatedData, setGeneratedData] = useState<GeneratedCredentials | null>(null);
  const [secretVisible, setSecretVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form with React Hook Form and Zod validation
  const {
    control,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<AppFormData>({
    defaultValues: {
      name: '',
      description: '',
      tags: '',
    },
    resolver: zodResolver(appFormSchema),
    mode: 'onChange'
  });

  const steps = [
    { label: 'App Details' },
    { label: 'Credentials Generation' },
  ];

  const handleSubmitStep1 = async (data: AppFormData) => {
    try {
      setIsSubmitting(true);

      // Parse tags from comma-separated string to array
      const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];

      // Create the app (without App Secret)
      const response = await createApp({
        name: data.name,
        description: data.description || '',
        tags,
        organizationId
      });

      if (response.success && response.data) {
        // Generate App Secret on the client side
        const appSecret = generateAppSecret();

        setGeneratedData({
          appId: response.data.id,
          apiKey: response.data.apiKey,
          appSecret: appSecret,
        });
        success('Success', 'Application created successfully');
        setActiveStep(1);
      } else {
        error('Error', response.error || 'Failed to create app');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const copyToClipboard = (text: string, message: string) => {
    navigator.clipboard.writeText(text);
    success('Copied', message);
  };

  const copyAllCredentials = () => {
    if (!generatedData) return;

    const formattedCredentials = [
      `NEW_INSTANCE_APP_ID="${generatedData.appId}"`,
      `NEW_INSTANCE_API_KEY="${generatedData.apiKey}"`,
      `NEW_INSTANCE_APP_SECRET="${generatedData.appSecret}"`
    ].join('\n');

    navigator.clipboard.writeText(formattedCredentials);
    success('Copied', 'All credentials copied to clipboard');
  };

  const finishSetup = async () => {
    if (!generatedData) return;

    try {
      setIsSubmitting(true);

      // Generate keyCheck value by encrypting the app ID with the App Secret
      const keyCheck = await generateKeyCheck(generatedData.appId, generatedData.appSecret);
      // FRONTEND-ONLY: Generate and encrypt MEK with App Secret
      const mekData = await generateAndEncryptMEK(generatedData.appSecret, generatedData.appId);

      // Update the keyCheck value and store frontend-generated MEK on the server
      const response = await updateKeyCheck({
        appId: generatedData.appId,
        keyCheck,
        organizationId,
        // Frontend-generated MEK fields (Three-Key Model)
        encryptedMasterKey: mekData.encryptedMasterKey,
        masterKeyIV: mekData.masterKeyIV,
        masterKeyTag: mekData.masterKeyTag
      });

      if (response.success) {
        success('Setup Complete', 'App Setup Completed');
        // Redirect to the app dashboard
        router.push(`/dashboard/organization/${organizationId}/app-manager/apps/${generatedData.appId}`);
      } else {
        error('Error', response.error || 'Failed to complete app setup');
      }
    } catch (err: any) {
      error('Error', err.message || 'An unexpected error occurred during setup');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <Steps
          model={steps}
          activeIndex={activeStep}
          readOnly
        />

        {activeStep === 0 && (
          <form onSubmit={handleSubmit(handleSubmitStep1)} className="space-y-6 mt-6">
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                App Name <span className="text-red-500">*</span>
              </label>
              <Controller
                control={control}
                name="name"
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter app name"
                    aria-invalid={errors.name ? 'true' : 'false'}
                  />
                )}
              />
              {errors.name && (
                <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description (optional)
              </label>
              <Controller
                control={control}
                name="description"
                render={({ field }) => (
                  <Textarea
                    {...field}
                    placeholder="Enter app description"
                    rows={3}
                  />
                )}
              />
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tags (optional)
              </label>
              <Controller
                control={control}
                name="tags"
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Enter comma-separated tags (e.g., api, backend, production)"
                  />
                )}
              />
              <p className="text-xs text-gray-500">
                Separate tags with commas (e.g., api, backend, production)
              </p>
            </div>

            <div className="pt-4">
              <Button
                type="submit"
                disabled={isSubmitting || !isValid}
                className="w-full sm:w-auto"
              >
                {isSubmitting ? (
                  <>
                    <span className="animate-spin mr-2">
                      <i className="pi pi-spinner"></i>
                    </span>
                    Creating...
                  </>
                ) : (
                  'Next'
                )}
              </Button>
            </div>
          </form>
        )}

        {activeStep === 1 && generatedData && (
          <div className="space-y-6 mt-6">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
              <p className="text-yellow-800 dark:text-yellow-300 text-sm font-medium">
                <i className="pi pi-exclamation-triangle mr-2"></i>
                These credentials will only be shown once. Please save them securely.
              </p>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4 flex justify-between items-center">
                <p className="text-blue-800 dark:text-blue-300 text-sm">
                  <i className="pi pi-info-circle mr-2"></i>
                  Need all credentials for your .env file?
                </p>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={copyAllCredentials}
                  className="ml-4"
                >
                  <i className="pi pi-copy mr-2"></i>
                  Copy All
                </Button>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  App ID
                </label>
                <div className="flex">
                  <Input
                    value={generatedData.appId}
                    readOnly
                    className="font-mono"
                  />
                  <Button
                    variant="outline"
                    className="ml-2"
                    onClick={() => copyToClipboard(generatedData.appId, 'App ID copied to clipboard')}
                  >
                    <i className="pi pi-copy"></i>
                  </Button>
                </div>
                <p className="text-xs text-gray-500">Your unique application identifier</p>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  API Key
                </label>
                <div className="flex">
                  <Input
                    value={generatedData.apiKey}
                    readOnly
                    className="font-mono"
                  />
                  <Button
                    variant="outline"
                    className="ml-2"
                    onClick={() => copyToClipboard(generatedData.apiKey, 'API Key copied to clipboard')}
                  >
                    <i className="pi pi-copy"></i>
                  </Button>
                </div>
                <p className="text-xs text-gray-500">Used for API authentication</p>
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  App Secret
                </label>
                <div className="flex">
                  <Input
                    type={secretVisible ? 'text' : 'password'}
                    value={generatedData.appSecret}
                    readOnly
                    className="font-mono"
                  />
                  <Button
                    variant="outline"
                    className="ml-2"
                    onClick={() => setSecretVisible(!secretVisible)}
                  >
                    <i className={`pi ${secretVisible ? 'pi-eye-slash' : 'pi-eye'}`}></i>
                  </Button>
                  <Button
                    variant="outline"
                    className="ml-2"
                    onClick={() => copyToClipboard(generatedData.appSecret, 'App Secret copied to clipboard')}
                  >
                    <i className="pi pi-copy"></i>
                  </Button>
                </div>
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mt-2">
                  <p className="text-red-800 dark:text-red-300 text-xs">
                    <strong>Important:</strong> Generated now; never stored. Copy & save securely! Without this key, you cannot encrypt/decrypt ENVs!
                  </p>
                </div>
              </div>
            </div>

            <div className="pt-6 flex justify-between">
              <Button variant="outline" onClick={() => setActiveStep(0)} disabled={isSubmitting}>Back</Button>
              <Button onClick={finishSetup} disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <span className="animate-spin mr-2">
                      <i className="pi pi-spinner"></i>
                    </span>
                    Finalizing...
                  </>
                ) : (
                  'Finish & Go to App Dashboard'
                )}
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}

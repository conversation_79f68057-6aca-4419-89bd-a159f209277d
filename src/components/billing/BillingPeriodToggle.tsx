import React from 'react';

interface BillingPeriodToggleProps {
  isYearly: boolean;
  yearlyDiscount: number;
  onChange: (yearly: boolean) => void;
}

export default function BillingPeriodToggle({ isYearly, yearlyDiscount, onChange }: BillingPeriodToggleProps) {
  // Helper to generate the discount label
  const getYearlyDiscountLabel = () => {
    if (yearlyDiscount > 0) {
      return <span className="text-[#BE9544] text-xs ml-1">(Save {yearlyDiscount}%)</span>;
    }
    return null;
  };

  return (
    <div className="flex justify-center items-center bg-[#252444]/30 rounded-full py-2 px-6 shadow-sm">
      <span
        className={`text-sm font-medium mr-3 ${
          !isYearly ? 'text-[#8178E8] font-semibold' : 'text-[#C6C6C6]'
        } cursor-pointer transition-colors`}
        onClick={() => onChange(false)}
      >
        Monthly
      </span>
      <div
        className={`w-14 h-7 bg-[#1e1e28] rounded-full p-1 flex items-center cursor-pointer shadow-inner`}
        onClick={() => onChange(!isYearly)}
      >
        <div
          className={`w-5 h-5 rounded-full bg-gradient-to-br from-[#8178E8] to-[#6964D3] shadow-md transform duration-300 ease-in-out ${
            isYearly ? 'translate-x-7' : 'translate-x-0'
          }`}
        ></div>
      </div>
      <span
        className={`text-sm font-medium ml-3 ${
          isYearly ? 'text-[#8178E8] font-semibold' : 'text-[#C6C6C6]'
        } cursor-pointer transition-colors`}
        onClick={() => onChange(true)}
      >
        Annual {getYearlyDiscountLabel()}
      </span>
    </div>
  );
} 
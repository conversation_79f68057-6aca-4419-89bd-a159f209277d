import React from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Currency } from '@/constants/pricing';
import { PaymentProvider } from '@/constants/transactions';
import { formatCurrency } from '@/utils/format-utils';
import { usePaystackPayment } from 'react-paystack';
import { useNetAppsPay } from '@netappsng/react-netappspaysdk';

interface Step4PaymentProps {
  transactionResult: any;
  selectedPlanDetails: any;
  hasActiveSubscription: boolean;
  isDowngrade: boolean;
  isUpgradeWithProration: boolean;
  proratedAmountData: any;
  userCount: number;
  selectedCurrency: Currency;
  selectedGateway: PaymentProvider;
  handlePaymentSuccess: (response: any) => void;
  handlePaymentClose: () => void;
  processingPayment: boolean;
  handlePreviousStep: () => void;
}

// Inner NetAppsPay component
const NetAppsPayHookComponent = ({
  reference,
  email,
  amount,
  currency,
  publicKey,
  metadata,
  onSuccess,
  onClose
}: {
  reference: string;
  email: string;
  amount: number;
  currency: Currency;
  publicKey: string;
  metadata: any;
  onSuccess: (response: any) => void;
  onClose: () => void;
}) => {
  const {isReady, initPayment} = useNetAppsPay({
    publicKey,
    isDev: process.env.NODE_ENV !== 'production',
    onSuccess,
    onError: onClose
  });

  return (
    <button
      className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
      disabled={!isReady}
      onClick={() => {
        if (isReady) {
          initPayment({
            email,
            amount,
            currency,
            ref: reference,
            metadata
          });
        }
      }}
    >
      Pay with NetAppsPay
    </button>
  );
};

export default function Step4Payment({
  transactionResult,
  selectedPlanDetails,
  hasActiveSubscription,
  isDowngrade,
  isUpgradeWithProration,
  proratedAmountData,
  userCount,
  selectedCurrency,
  selectedGateway,
  handlePaymentSuccess,
  handlePaymentClose,
  processingPayment,
  handlePreviousStep,
}: Step4PaymentProps) {
  if (!transactionResult || !selectedPlanDetails) return null;

  // Configure the Paystack payment
  const paystackConfig = {
    reference: transactionResult.transactionId,
    email: transactionResult.email || transactionResult.customerEmail,
    amount: Math.round(transactionResult.amount * 100), // Convert to kobo/cents
    publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || '',
    currency: selectedCurrency,
    metadata: transactionResult.metadata
  };

  // Initialize the Paystack payment
  const initializePaystack = usePaystackPayment(paystackConfig);

  return (
    <div className="flex flex-col gap-6">
      <Card className="bg-[#20212f] border border-[#2c2d3d] shadow-lg rounded-xl overflow-hidden">
        <div className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">
            Complete Payment
          </h3>

          <div className="flex flex-col gap-8">
            <div className="flex flex-col gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                Payment Summary
              </h4>

              {isUpgradeWithProration ? (
                <>
                  <div className="flex justify-between text-sm">
                    <p className="text-gray-500 dark:text-gray-400">
                      New plan: {selectedPlanDetails.name} ({userCount} {userCount === 1 ? 'user' : 'users'})
                    </p>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(proratedAmountData.newPlanProrated, selectedCurrency)}
                    </span>
                  </div>

                  <div className="flex justify-between text-sm">
                    <p className="text-gray-500 dark:text-gray-400">
                      Prorated amount
                    </p>
                    <span className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(proratedAmountData.netAmountDue, selectedCurrency)}
                    </span>
                  </div>
                </>
              ) : (
                <div className="flex justify-between text-sm">
                  <p className="text-gray-500 dark:text-gray-400">
                    {selectedPlanDetails.name} for {userCount} {userCount === 1 ? 'user' : 'users'}
                  </p>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {formatCurrency(transactionResult.baseAmount, selectedCurrency)}
                  </span>
                </div>
              )}

              {transactionResult.vatPercentage > 0 && (
                <div className="flex justify-between text-sm">
                  <p className="text-gray-500 dark:text-gray-400">
                    VAT ({transactionResult.vatPercentage}%)
                  </p>
                  <span className="font-medium text-gray-900 dark:text-white">
                    + {formatCurrency(transactionResult.vatAmount, selectedCurrency)}
                  </span>
                </div>
              )}

              {transactionResult.taxPercentage > 0 && (
                <div className="flex justify-between text-sm">
                  <p className="text-gray-500 dark:text-gray-400">
                    Tax ({transactionResult.taxPercentage}%)
                  </p>
                  <span className="font-medium text-gray-900 dark:text-white">
                    + {formatCurrency(transactionResult.taxAmount, selectedCurrency)}
                  </span>
                </div>
              )}

              {transactionResult.transactionFee > 0 && (
                <div className="flex justify-between text-sm">
                  <p className="text-gray-500 dark:text-gray-400">
                    Transaction fee
                  </p>
                  <span className="font-medium text-gray-900 dark:text-white">
                    + {formatCurrency(transactionResult.transactionFee, selectedCurrency)}
                  </span>
                </div>
              )}

              <hr className="border-gray-200 dark:border-gray-700"/>

              <div className="flex justify-between">
                <p className="font-semibold text-gray-900 dark:text-white">
                  Total
                </p>
                <div className="text-xl font-bold text-gray-900 dark:text-white">
                  {formatCurrency(transactionResult.amount, selectedCurrency)}
                </div>
              </div>

              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 pt-2">
                <p>Transaction ID: {transactionResult.transactionId}</p>
                <p>Payment Provider: {selectedGateway}</p>
              </div>
            </div>

            <div className="flex flex-col items-center gap-4">
              <p className="text-center text-gray-600 dark:text-gray-400">
                Click the button below to complete your payment
              </p>

              {selectedGateway === PaymentProvider.PAYSTACK && (
                <button
                  className="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors"
                  onClick={() => {
                    initializePaystack({
                      onSuccess: handlePaymentSuccess,
                      onClose: handlePaymentClose
                    });
                  }}
                >
                  Pay with Paystack
                </button>
              )}

              {selectedGateway === PaymentProvider.NETAPPSPAY && (
                <div className="w-full">
                  <NetAppsPayHookComponent
                    reference={transactionResult.transactionId}
                    email={transactionResult.email || transactionResult.customerEmail}
                    amount={Math.round(transactionResult.amount * 100)}
                    currency={selectedCurrency}
                    publicKey={process.env.NEXT_PUBLIC_NETAPPSPAY_PUBLIC_KEY || ''}
                    metadata={transactionResult.metadata}
                    onSuccess={handlePaymentSuccess}
                    onClose={handlePaymentClose}
                  />
                </div>
              )}
            </div>

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={handlePreviousStep}
                disabled={processingPayment}
              >
                Back
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
} 
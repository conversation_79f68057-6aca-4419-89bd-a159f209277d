import React from 'react';
import { Button } from '@/components/ui/Button';
import { Skeleton } from '@/components/ui/Skeleton';
import { Currency } from '@/constants/pricing';
import { formatCurrency } from '@/utils/format-utils';
import BillingPeriodToggle from '../BillingPeriodToggle';
import CurrentSubscriptionBanner from '../CurrentSubscriptionBanner';
import { useRouter } from 'next/navigation';

interface Step1PlanSelectionProps {
  loading: boolean;
  error: string | null;
  pricingPlans: any[];
  selectedPlanId: string;
  selectedCurrency: Currency;
  isYearly: boolean;
  yearlyDiscount: number;
  hasActiveSubscription: boolean;
  subscription: any;
  organizationId: string;
  handlePlanSelect: (planId: string) => void;
  handlePeriodChange: (yearly: boolean) => void;
  handleCurrencyChange: (currency: Currency) => void;
  handleNextStep: () => void;
}

export default function Step1PlanSelection({
  loading,
  error,
  pricingPlans,
  selectedPlanId,
  selectedCurrency,
  isYearly,
  yearlyDiscount,
  hasActiveSubscription,
  subscription,
  organizationId,
  handlePlanSelect,
  handlePeriodChange,
  handleCurrencyChange,
  handleNextStep
}: Step1PlanSelectionProps) {
  const router = useRouter();

  if (loading) {
    return (
      <div className="grid md:grid-cols-3 gap-8">
        {[1, 2, 3].map(i => (
          <div key={i} className="animate-pulse bg-white dark:bg-[#1e1e28] rounded-2xl h-96"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-red-500 dark:text-red-400 p-8">
        {error}
      </div>
    );
  }

  // Sort plans by product category and price
  const sortPlans = (plans: any[]): any[] => {
    return [...plans].sort((a, b) => {
      // Get the main products for comparison
      const productA = a.products && a.products.length > 0 ? a.products[0] : undefined;
      const productB = b.products && b.products.length > 0 ? b.products[0] : undefined;

      // First sort by product category if available
      if (productA?.category && productB?.category) {
        if (productA.category !== productB.category) {
          // core first, then addon, then premium
          const order: Record<string, number> = {core: 1, addon: 2, premium: 3};
          return order[productA.category] - order[productB.category];
        }
      }

      // Then sort by price based on selected currency
      if (selectedCurrency === Currency.USD) {
        return (a.amountUSD || 0) - (b.amountUSD || 0);
      } else {
        return (a.amountNGN || 0) - (b.amountNGN || 0);
      }
    });
  };

  const sortedPlans = sortPlans(pricingPlans);
  const totalPlans = pricingPlans.length;

  // Return UI based on subscription status
  return (
    <div>
      {hasActiveSubscription && subscription && <CurrentSubscriptionBanner subscription={subscription} />}

      <h2 className="text-xl font-semibold mb-6 text-center">Select a Subscription Plan</h2>

      <div className="flex justify-center mb-10 space-x-4">
        {/* Billing period toggle */}
        <BillingPeriodToggle
          isYearly={isYearly}
          yearlyDiscount={yearlyDiscount}
          onChange={handlePeriodChange}
        />

        {/* Currency toggle */}
        <div className="flex justify-center items-center bg-[#252444]/30 rounded-full py-2 px-6 shadow-sm">
          <span
            className={`text-sm font-medium mr-3 ${
              selectedCurrency === Currency.USD ? 'text-[#8178E8] font-semibold' : 'text-[#C6C6C6]'
            } cursor-pointer transition-colors`}
            onClick={() => handleCurrencyChange(Currency.USD)}
          >
            USD
          </span>
          <div
            className={`w-14 h-7 bg-[#1e1e28] rounded-full p-1 flex items-center cursor-pointer shadow-inner`}
            onClick={() => handleCurrencyChange(selectedCurrency === Currency.USD ? Currency.NGN : Currency.USD)}
          >
            <div
              className={`w-5 h-5 rounded-full bg-gradient-to-br from-[#BE9544] to-[#D1AB66] shadow-md transform duration-300 ease-in-out ${
                selectedCurrency === Currency.NGN ? 'translate-x-7' : 'translate-x-0'
              }`}
            ></div>
          </div>
          <span
            className={`text-sm font-medium ml-3 ${
              selectedCurrency === Currency.NGN ? 'text-[#8178E8] font-semibold' : 'text-[#C6C6C6]'
            } cursor-pointer transition-colors`}
            onClick={() => handleCurrencyChange(Currency.NGN)}
          >
            NGN
          </span>
        </div>
      </div>

      {/* Display message if no higher-tier plans are available */}
      {pricingPlans.length === 0 && (
        <div className="text-center p-8 bg-[#20212f] rounded-xl shadow-lg border border-[#2c2d3d] mb-8">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4 text-[#8178E8]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-xl font-bold text-white mb-2">You&#39;re Already on the Highest Plan!</h3>
          <p className="text-[#C6C6C6] mb-4">
            You are currently subscribed to our highest tier plan. No upgrades are available at this time.
          </p>
          <Button
            onClick={() => router.push(`/dashboard/organization/${organizationId}/billing`)}
          >
            Return to Billing
          </Button>
        </div>
      )}

      {/* Pricing Plans Grid */}
      {pricingPlans.length > 0 && (
        <div className="flex flex-wrap justify-center">
          {sortedPlans.map((plan, index) => {
            // Design logic: Feature the 2nd plan when there are 3+ plans
            const featuredPlanIndex = 1;
            const isProfessional = index === featuredPlanIndex && totalPlans >= 3;

            // Get the main product for category styling
            const mainProduct = plan.products && plan.products.length > 0 ? plan.products[0] : undefined;
            const category = mainProduct?.category || 'core';

            // Determine if this is the premium tier for styling
            const isPremium = category === 'premium' || (totalPlans >= 3 && index === 2);

            // Responsive width classes based on number of plans
            let widthClasses = "w-full sm:w-2/3 md:w-1/2 lg:w-1/3 px-4 mb-8";

            // For 3 plans, make them equal width and the middle one slightly larger
            if (totalPlans === 3) {
              widthClasses = "w-full md:w-1/3 px-4 mb-8";
            }
            // For 2 plans, make them equal width
            else if (totalPlans === 2) {
              widthClasses = "w-full sm:w-1/2 lg:w-1/3 px-4 mb-8";
            }

            // Get the correct price based on the selected currency
            const price = selectedCurrency === Currency.USD ? (plan.amountUSD || 0) : (plan.amountNGN || 0);

            // Add a special badge for current plan
            const isCurrentPlan = subscription?.pricingPlan?.id === plan._id.toString();

            return (
              <div key={plan._id} className={widthClasses}>
                <div
                  className={`relative h-full ${
                    isProfessional
                      ? 'bg-[#20212f] dark:bg-[#20212f] rounded-xl shadow-lg border-2 border-[#6964D3] dark:border-[#6964D3] md:scale-105 md:-my-4 z-10'
                      : 'bg-[#20212f] dark:bg-[#20212f] rounded-xl shadow-lg border border-[#2c2d3d] dark:border-[#2c2d3d]'
                  } overflow-hidden hover-card flex flex-col ${selectedPlanId === plan._id ? 'ring-2 ring-[#6964D3] dark:ring-[#8178E8]' : ''}`}
                  onClick={() => handlePlanSelect(plan._id)}
                >
                  {isProfessional && (
                    <div
                      className="absolute top-0 left-0 w-full bg-[#8178E8] text-white py-1.5 text-xs font-medium text-center">
                      MOST POPULAR
                    </div>
                  )}
                  {plan.discountActive && plan.discountPercentage && (
                    <div
                      className={`absolute top-0 right-0 ${
                        isProfessional ? 'mt-7' : 'mt-0'
                      } bg-[#BE9544] text-white px-2 py-1 text-xs font-medium rounded-bl-lg`}
                    >
                      {plan.discountPercentage}% OFF
                    </div>
                  )}
                  {isCurrentPlan && (
                    <div
                      className={`absolute top-0 ${isProfessional ? 'mt-7' : 'mt-0'} left-0 bg-green-600 text-white px-2 py-1 text-xs font-medium rounded-br-lg`}
                    >
                      CURRENT PLAN
                    </div>
                  )}
                  <div className={`p-8 ${isProfessional ? 'pt-12' : ''} ${isCurrentPlan ? 'pt-12' : ''} flex-grow`}>
                    <h3 className="text-xl font-semibold mb-2 text-white">
                      {plan.name}
                    </h3>
                    <p className="text-[#C6C6C6] text-sm mb-4">{plan.description}</p>
                    <div className="mb-6">
                      <span className="text-4xl font-bold text-white">{formatCurrency(price, selectedCurrency)}</span>
                      <span className="text-[#C6C6C6]">
                        {plan.isPerUser ? '/user' : ''}/{plan.timeUnit}
                      </span>
                    </div>
                    <div className="space-y-6 mt-6">
                      {/* Group features by product */}
                      {plan.productItems && plan.productItems.map((productItem: any, productIndex: number) => {
                        // Find the corresponding product
                        const product = plan.products?.find((p: any) =>
                          String(p._id) === String(productItem.productId)
                        );

                        if (!product) return null;

                        // Get features for this product
                        const productFeatures = plan.features?.filter((feature: any) =>
                          productItem.features.some((featureId: string) =>
                            String(feature._id) === String(featureId)
                          )
                        );

                        if (!productFeatures || productFeatures.length === 0) return null;

                        return (
                          <div key={productIndex} className="space-y-3">
                            {/* Product name as header */}
                            <h4 className={`text-base font-semibold ${
                              isPremium
                                ? 'text-[#BE9544] dark:text-[#D1AB66]'
                                : 'text-[#6964D3] dark:text-[#8178E8]'
                            } border-b border-dashed ${
                              isPremium
                                ? 'border-[#BE9544]/30 dark:border-[#D1AB66]/30'
                                : 'border-[#6964D3]/30 dark:border-[#8178E8]/30'
                            } pb-1 mb-2`}>
                              {product.name}
                            </h4>
                            <div className="pl-2">
                              {productFeatures.map((feature: any, featureIndex: number) => (
                                <div key={featureIndex} className="flex items-start mb-2.5">
                                  <div className={`w-5 h-5 rounded-full ${
                                    isPremium
                                      ? 'bg-[#F0DAB8]/50 dark:bg-[#BE9544]/20'
                                      : 'bg-[#E0D7FF] dark:bg-[#6964D3]/20'
                                  } flex items-center justify-center mt-0.5 mr-3 flex-shrink-0`}>
                                    <svg className={`w-3 h-3 ${
                                      isPremium ? 'text-[#BE9544]' : 'text-[#6964D3]'
                                    }`} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                      <path fillRule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clipRule="evenodd"></path>
                                    </svg>
                                  </div>
                                  <span className="text-sm text-[#4B4B4B] dark:text-[#C6C6C6]">{feature.name}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                  <div className="flex p-4 border-t border-[#E0D7FF]/50 dark:border-[#1e1e28] justify-center">
                    <Button
                      onClick={() => {
                        handlePlanSelect(plan._id)
                        handleNextStep()
                      }}
                      disabled={isCurrentPlan}
                    >
                      {isCurrentPlan ? 'Current Plan' : (selectedPlanId === plan._id ? 'Selected' : 'Select Plan')}
                    </Button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
      {/*{pricingPlans.length > 0 && selectedPlanId && (*/}
      {/*  <div className="mt-8 w-full flex justify-center">*/}
      {/*    <Button*/}
      {/*      className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white"*/}
      {/*      onClick={handleNextStep}*/}
      {/*    >*/}
      {/*      Continue*/}
      {/*    </Button>*/}
      {/*  </div>*/}
      {/*)}*/}
      {/* Pro-rate Notice */}
      {pricingPlans.length > 0 && (
        <div className="mt-10 text-center w-full">
          <div
            className="inline-block bg-[#F9F8FF] dark:bg-[#252444]/30 rounded-xl px-6 py-4 max-w-xl mx-auto shadow-sm border border-[#E0D7FF]/50 dark:border-[#6964D3]/30">
            <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"
                   className="mr-2 text-[#6964D3]">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
              All plans include pro-rated billing, allowing you to add users anytime during your billing cycle and
              only pay for what you use.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}

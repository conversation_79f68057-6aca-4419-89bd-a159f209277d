import React from 'react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Currency } from '@/constants/pricing';
import { PaymentProvider } from '@/constants/transactions';
import CurrencySelector from '../CurrencySelector';
import ProrationCalculation from '../ProrationCalculation';
import PaymentGatewaySelector from '@/components/billing/PaymentGatewaySelector';
import OrderSummary from '../OrderSummary';

interface Step3PaymentMethodProps {
  selectedPlanDetails: any;
  hasActiveSubscription: boolean;
  subscription: any;
  proratedAmountData: any;
  calculatingProration: boolean;
  isDowngrade: boolean;
  selectedCurrency: Currency;
  originalCurrency: Currency | null;
  currencyError: string | null;
  handleCurrencyChange: (currency: Currency) => void;
  baseAmount: number;
  transactionFee: number;
  totalAmount: number;
  adjustedTotalAmount: number;
  userCount: number;
  selectedGateway: PaymentProvider;
  processingPayment: boolean;
  handleGatewayChange: (gateway: PaymentProvider) => void;
  handlePreviousStep: () => void;
  handleCheckout: () => void;
  vatPercentage: number;
  vatAmount: number;
  taxPercentage: number;
  taxAmount: number;
}

export default function Step3PaymentMethod({
  selectedPlanDetails,
  hasActiveSubscription,
  subscription,
  proratedAmountData,
  calculatingProration,
  isDowngrade,
  selectedCurrency,
  originalCurrency,
  currencyError,
  handleCurrencyChange,
  baseAmount,
  transactionFee,
  totalAmount,
  adjustedTotalAmount,
  userCount,
  selectedGateway,
  processingPayment,
  handleGatewayChange,
  handlePreviousStep,
  handleCheckout,
  vatPercentage,
  vatAmount,
  taxPercentage,
  taxAmount,
}: Step3PaymentMethodProps) {
  if (!selectedPlanDetails) return null;

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6 text-center">Payment Information</h2>

      {/* Currency selector */}
      <CurrencySelector
        selectedCurrency={selectedCurrency}
        originalCurrency={originalCurrency}
        onCurrencyChange={handleCurrencyChange}
        currencyError={currencyError}
      />

      {/* Display proration calculation when applicable */}
      {hasActiveSubscription && (
        <ProrationCalculation
          proratedAmountData={proratedAmountData}
          hasActiveSubscription={hasActiveSubscription}
          subscription={subscription}
          calculatingProration={calculatingProration}
          isDowngrade={isDowngrade}
          selectedCurrency={selectedCurrency}
        />
      )}

      <Card className="bg-[#20212f] border border-[#2c2d3d] shadow-lg rounded-xl overflow-hidden">
        <div className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">
            Payment Details
          </h3>

          <div className="flex flex-col gap-8">
            {/* Order summary with tax and VAT */}
            {!hasActiveSubscription &&<OrderSummary
              baseAmount={baseAmount}
              transactionFee={transactionFee}
              vatPercentage={vatPercentage}
              vatAmount={vatAmount}
              taxPercentage={taxPercentage}
              taxAmount={taxAmount}
              totalAmount={totalAmount}
              currency={selectedCurrency}
              planName={selectedPlanDetails.name}
              userCount={userCount}
              isDowngrade={isDowngrade}
              adjustedTotalAmount={adjustedTotalAmount}
              hasActiveSubscription={hasActiveSubscription}
            />}

            {!isDowngrade && (
              <PaymentGatewaySelector
                value={selectedGateway}
                onChange={handleGatewayChange}
                disabled={processingPayment}
              />
            )}

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={handlePreviousStep}
                disabled={processingPayment}
              >
                Back
              </Button>
              {!isDowngrade && (
                <Button
                  onClick={handleCheckout}
                  disabled={processingPayment}
                  loading={processingPayment}>
                  {processingPayment ? 'Processing...' : 'Continue to Payment'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}

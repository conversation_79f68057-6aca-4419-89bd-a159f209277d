import React from 'react';
import {Button} from '@/components/ui/Button';
import {Card} from '@/components/ui/Card';
import UserCountSelector from '@/components/ui/UserCountSelector';
import {Currency} from '@/constants/pricing';

interface Step2UserConfigProps {
  selectedPlanDetails: any;
  userCount: number;
  selectedCurrency: Currency;
  hasActiveSubscription: boolean;
  processingPayment: boolean;
  handleUserCountChange: (count: number) => void;
  handlePreviousStep: () => void;
  handleNextStep: () => void;
}

export default function Step2UserConfig({
                                          selectedPlanDetails,
                                          userCount,
                                          selectedCurrency,
                                          hasActiveSubscription,
                                          processingPayment,
                                          handleUserCountChange,
                                          handlePreviousStep,
                                          handleNextStep
                                        }: Step2UserConfigProps) {
  if (!selectedPlanDetails) return null;

  return (
    <div className="flex flex-col gap-6">
      <Card className="bg-[#20212f] border border-[#2c2d3d] shadow-lg rounded-xl overflow-hidden">
        <div className="p-6">
          <h3 className="text-xl font-bold text-white mb-4">
            Configure Your Plan
          </h3>

          <div className="flex flex-col gap-8">
            <div className="flex justify-between items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  Selected Plan
                </h4>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedPlanDetails.name} - {selectedPlanDetails.description}
                </p>
              </div>
              <Button
                variant="link"
                onClick={handlePreviousStep}
              >
                Change
              </Button>
            </div>

            <UserCountSelector
              value={userCount}
              onChange={handleUserCountChange}
              pricePerUser={selectedCurrency === Currency.USD ?
                (selectedPlanDetails.amountUSD || 0) : (selectedPlanDetails.amountNGN || 0)}
              currency={selectedCurrency}
              minUsers={1}
              hasActiveSubscription={hasActiveSubscription}
              disabled={processingPayment}
            />

            <div className="flex justify-between mt-4">
              <Button
                variant="outline"
                onClick={handlePreviousStep}
                disabled={processingPayment}
              >
                Back
              </Button>
              <Button
                variant="primary"
                isLoading={processingPayment}
                onClick={handleNextStep}
                disabled={processingPayment}
                // className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white"
              >
                Continue
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}

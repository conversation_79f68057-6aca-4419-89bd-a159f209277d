import React from 'react';
import { SubscriptionStatus } from '@/constants/pricing';

interface CurrentSubscriptionBannerProps {
  subscription: any;
}

export default function CurrentSubscriptionBanner({ subscription }: CurrentSubscriptionBannerProps) {
  if (!subscription) return null;

  return (
    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6 mb-8 border border-blue-100 dark:border-blue-800">
      <h3 className="text-lg font-semibold text-blue-700 dark:text-blue-400 mb-2">
        Your Current Subscription
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div className="bg-white dark:bg-slate-800 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">Plan</div>
          <div className="font-medium">{subscription.pricingPlan?.name}</div>
        </div>
        <div className="bg-white dark:bg-slate-800 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">Status</div>
          <div className="font-medium capitalize">{subscription.status}</div>
        </div>
        <div className="bg-white dark:bg-slate-800 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">Renewal Date</div>
          <div className="font-medium">{new Date(subscription.endDate).toLocaleDateString()}</div>
        </div>
      </div>

      {subscription.status === SubscriptionStatus.CANCELED ? (
        <div className="text-amber-600 dark:text-amber-400 text-sm bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
          Your subscription is set to expire on {new Date(subscription.endDate).toLocaleDateString()}.
          The new plan you select will become active after your current subscription ends.
        </div>
      ) : subscription.status === SubscriptionStatus.ACTIVE ? (
        <div className="text-blue-600 dark:text-blue-300 text-sm">
          When you select a new plan, we&#39;ll calculate the prorated cost based on your remaining days.
          If you select a higher-tier plan, you&#39;ll be charged the difference immediately.
          If you select a lower-tier plan, the change will take effect after your current billing cycle ends.
        </div>
      ) : null}
    </div>
  );
} 
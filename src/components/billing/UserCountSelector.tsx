'use client';

import React from 'react';
import { InputNumber } from 'primereact/inputnumber';
import { formatCurrency } from '@/utils/format-utils';
import { Currency } from '@/constants/pricing';

export interface UserCountSelectorProps {
  value: number;
  onChange: (value: number) => void;
  pricePerUser: number;
  currency: Currency;
  minUsers?: number;
  maxUsers?: number;
  disabled?: boolean;
}

export const UserCountSelector: React.FC<UserCountSelectorProps> = ({
  value,
  onChange,
  pricePerUser,
  currency,
  minUsers = 1,
  maxUsers = 1000,
  disabled = false
}) => {
  const totalPrice = value * pricePerUser;
  
  return (
    <div className="flex flex-col gap-3">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Number of Users
        </h3>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {formatCurrency(pricePerUser, currency)} per user
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center">
          <InputNumber
            value={value}
            onValueChange={(e) => onChange(e.value ?? minUsers)}
            min={minUsers}
            max={maxUsers}
            disabled={disabled}
            showButtons
            buttonLayout="horizontal"
            decrementButtonClassName="p-button-outlined p-button-secondary"
            incrementButtonClassName="p-button-outlined p-button-secondary"
            incrementButtonIcon="pi pi-plus"
            decrementButtonIcon="pi pi-minus"
            className="w-full"
            pt={{
              root: { className: 'w-full' },
              input: { root: { className: 'text-center' } }
            }}
          />
        </div>
        
        <div className="flex flex-col justify-center p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total for {value} {value === 1 ? 'user' : 'users'}
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(totalPrice, currency)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Billed {currency === Currency.USD ? 'monthly' : 'monthly'}
          </div>
        </div>
      </div>
      
      <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
        Select the number of users you want to subscribe for. You can add more users later.
      </div>
    </div>
  );
};

export default UserCountSelector; 
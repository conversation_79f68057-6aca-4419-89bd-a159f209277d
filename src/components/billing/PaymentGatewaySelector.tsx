'use client';

import React, { useState } from 'react';
import { RadioButton } from 'primereact/radiobutton';
import { PaymentProvider } from '@/constants/transactions';

export interface PaymentGatewaySelectorProps {
  value: PaymentProvider;
  onChange: (value: PaymentProvider) => void;
  disabled?: boolean;
}

export const PaymentGatewaySelector: React.FC<PaymentGatewaySelectorProps> = ({
  value,
  onChange,
  disabled = false
}) => {
  const [selectedGateway, setSelectedGateway] = useState<PaymentProvider>(value);

  const handleGatewayChange = (gateway: PaymentProvider) => {
    setSelectedGateway(gateway);
    onChange(gateway);
  };

  return (
    <div className="flex flex-col gap-4 w-full">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
        Select Payment Method
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div 
          className={`
            flex items-start p-4 gap-3 rounded-lg border 
            ${selectedGateway === PaymentProvider.PAYSTACK 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-200 dark:border-gray-700'}
            ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          `}
          onClick={() => !disabled && handleGatewayChange(PaymentProvider.PAYSTACK)}
        >
          <RadioButton
            inputId="paystack"
            name="paymentGateway"
            value={PaymentProvider.PAYSTACK}
            onChange={() => handleGatewayChange(PaymentProvider.PAYSTACK)}
            checked={selectedGateway === PaymentProvider.PAYSTACK}
            disabled={disabled}
            className="mt-0.5"
          />
          <div>
            <label 
              htmlFor="paystack" 
              className="text-base font-medium text-gray-900 dark:text-white cursor-pointer flex items-center gap-2"
            >
              <span>Paystack</span>
              <span className="text-xs px-2 py-0.5 bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 rounded-full">
                Recommended
              </span>
            </label>
            <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Pay securely using Paystack. Supports card payments, bank transfers, and USSD.
            </div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-10 h-6 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center">
                <span className="text-xs">VISA</span>
              </div>
              <div className="w-10 h-6 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center">
                <span className="text-xs">MC</span>
              </div>
            </div>
          </div>
        </div>
        
        <div 
          className={`
            flex items-start p-4 gap-3 rounded-lg border 
            ${selectedGateway === PaymentProvider.NETAPPSPAY 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : 'border-gray-200 dark:border-gray-700'}
            ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          `}
          onClick={() => !disabled && handleGatewayChange(PaymentProvider.NETAPPSPAY)}
        >
          <RadioButton
            inputId="netappspay"
            name="paymentGateway"
            value={PaymentProvider.NETAPPSPAY}
            onChange={() => handleGatewayChange(PaymentProvider.NETAPPSPAY)}
            checked={selectedGateway === PaymentProvider.NETAPPSPAY}
            disabled={disabled}
            className="mt-0.5"
          />
          <div>
            <label 
              htmlFor="netappspay" 
              className="text-base font-medium text-gray-900 dark:text-white cursor-pointer"
            >
              NetAppsPay
            </label>
            <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Pay using NetAppsPay secure payment platform. Multiple payment options available.
            </div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-10 h-6 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center">
                <span className="text-xs">VISA</span>
              </div>
              <div className="w-10 h-6 bg-gray-100 dark:bg-gray-800 rounded flex items-center justify-center">
                <span className="text-xs">MC</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PaymentGatewaySelector; 
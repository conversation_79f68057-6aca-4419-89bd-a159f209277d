import React from 'react';
import { Currency } from '@/constants/pricing';
import { formatCurrency } from '@/utils/format-utils';

interface OrderSummaryProps {
  baseAmount: number;
  transactionFee: number;
  vatPercentage: number;
  vatAmount: number;
  taxPercentage: number;
  taxAmount: number;
  totalAmount: number;
  currency: Currency;
  planName: string;
  userCount: number;
  isDowngrade: boolean;
  adjustedTotalAmount?: number;
  hasActiveSubscription: boolean;
}

export default function OrderSummary({
  baseAmount,
  transactionFee,
  vatPercentage,
  vatAmount,
  taxPercentage,
  taxAmount,
  totalAmount,
  currency,
  planName,
  userCount,
  isDowngrade,
  adjustedTotalAmount,
  hasActiveSubscription
}: OrderSummaryProps) {
  // Determine the final amount to display based on conditions
  const finalAmount = hasActiveSubscription && !isDowngrade && adjustedTotalAmount
    ? adjustedTotalAmount
    : totalAmount;
  return (
    <div className="flex flex-col gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
      <h4 className="font-medium text-gray-900 dark:text-white">
        Order Summary
      </h4>

      <div className="flex justify-between text-sm">
        <p className="text-gray-500 dark:text-gray-400">
          {planName} for {userCount} {userCount === 1 ? 'user' : 'users'}
        </p>
        <span className="font-medium text-gray-900 dark:text-white">
          {formatCurrency(baseAmount, currency)}
        </span>
      </div>

      {vatPercentage > 0 && (
        <div className="flex justify-between text-sm">
          <p className="text-gray-500 dark:text-gray-400">
            VAT ({vatPercentage}%)
          </p>
          <span className="font-medium text-gray-900 dark:text-white">
            + {formatCurrency(vatAmount, currency)}
          </span>
        </div>
      )}

      {taxPercentage > 0 && (
        <div className="flex justify-between text-sm">
          <p className="text-gray-500 dark:text-gray-400">
            Tax ({taxPercentage}%)
          </p>
          <span className="font-medium text-gray-900 dark:text-white">
            + {formatCurrency(taxAmount, currency)}
          </span>
        </div>
      )}

      {transactionFee > 0 && (
        <div className="flex justify-between text-sm">
          <p className="text-gray-500 dark:text-gray-400">
            Transaction fee
          </p>
          <span className="font-medium text-gray-900 dark:text-white">
            + {formatCurrency(transactionFee, currency)}
          </span>
        </div>
      )}

      <hr className="border-gray-200 dark:border-gray-700"/>

      <div className="flex justify-between">
        <p className="font-semibold text-gray-900 dark:text-white">
          Total
        </p>
        <div className="text-xl font-bold text-gray-900 dark:text-white">
          {formatCurrency(finalAmount, currency)}
        </div>
      </div>

      {isDowngrade && (
        <div className="text-amber-600 dark:text-amber-400 text-sm mt-2">
          Downgrade will take effect at the end of your current billing cycle. No immediate payment is needed.
        </div>
      )}
    </div>
  );
}

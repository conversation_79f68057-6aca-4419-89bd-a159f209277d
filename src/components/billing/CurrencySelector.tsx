import React from 'react';
import { Currency } from '@/constants/pricing';

interface CurrencySelectorProps {
  selectedCurrency: Currency;
  originalCurrency: Currency | null;
  onCurrencyChange: (currency: Currency) => void;
  currencyError: string | null;
}

export default function CurrencySelector({
  selectedCurrency,
  originalCurrency,
  onCurrencyChange,
  currencyError
}: CurrencySelectorProps) {
  return (
    <div className="mb-6">
      <label className="block text-sm font-medium mb-2">Currency</label>
      <div className="flex space-x-3">
        <button
          type="button"
          onClick={() => onCurrencyChange(Currency.NGN)}
          disabled={originalCurrency !== null && originalCurrency !== Currency.NGN}
          className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
            selectedCurrency === Currency.NGN
              ? 'bg-primary text-white shadow-md'
              : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
          } ${originalCurrency !== null && originalCurrency !== Currency.NGN ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          NGN (₦)
        </button>
        <button
          type="button"
          onClick={() => onCurrencyChange(Currency.USD)}
          disabled={originalCurrency !== null && originalCurrency !== Currency.USD}
          className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
            selectedCurrency === Currency.USD
              ? 'bg-primary text-white shadow-md'
              : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
          } ${originalCurrency !== null && originalCurrency !== Currency.USD ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          USD ($)
        </button>
      </div>
      {currencyError && (
        <div className="mt-2 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
          {currencyError}
        </div>
      )}
      {originalCurrency && (
        <div className="mt-2 text-xs text-gray-500">
          Your subscription is currently in {originalCurrency}. To change currencies, you must cancel your current
          subscription first.
        </div>
      )}
    </div>
  );
} 
'use client';

import React from 'react';
import { Currency } from '@/constants/pricing';
import { formatCurrency } from '@/utils/format-utils';

interface ProrationCalculationProps {
  proratedAmountData: any;
  hasActiveSubscription: boolean;
  subscription: any;
  calculatingProration: boolean;
  isDowngrade: boolean;
  selectedCurrency: Currency;
}

export default function ProrationCalculation({
  proratedAmountData,
  hasActiveSubscription,
  subscription,
  calculatingProration,
  isDowngrade,
  selectedCurrency
}: ProrationCalculationProps) {
  if (!proratedAmountData || !hasActiveSubscription || !subscription || calculatingProration) {
    return null;
  }

  return (
    <div className="mb-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
      <h4 className="font-medium text-blue-700 dark:text-blue-400 mb-2">Prorated Calculation</h4>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">Days remaining in billing cycle:</span>
          <span className="font-medium">
            {proratedAmountData.daysRemaining} of {proratedAmountData.totalDaysInCycle} days
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">Current plan daily rate:</span>
          <span className="font-medium">
            {formatCurrency(proratedAmountData.currentPlanPerDayRate, selectedCurrency)}/day
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">New plan daily rate:</span>
          <span className="font-medium">
            {formatCurrency(proratedAmountData.newPlanPerDayRate, selectedCurrency)}/day
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">VAT ({proratedAmountData.vatPercentage}%):</span>
          <span className="font-medium">
            {formatCurrency(proratedAmountData.vatAmount, selectedCurrency)}
          </span>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-600 dark:text-gray-400">TAX ({proratedAmountData.taxPercentage}%):</span>
          <span className="font-medium">
            {formatCurrency(proratedAmountData.taxAmount, selectedCurrency)}
          </span>
        </div>

        <div className="flex justify-between border-t border-blue-200 dark:border-blue-700 pt-2 mt-2 font-semibold">
          <span className="text-gray-700 dark:text-gray-300">
            {isDowngrade ? 'Credit at end of billing cycle:' : 'Net amount due now without taxes and VAT:'}
          </span>
          <span className={isDowngrade ? 'text-green-600 dark:text-green-400' : 'text-gray-900 dark:text-white'}>
            {formatCurrency(proratedAmountData.netAmountDue, selectedCurrency)}
          </span>
        </div>
      </div>

      {isDowngrade && (
        <div className="mt-4 text-amber-600 dark:text-amber-400 text-xs bg-amber-50 dark:bg-amber-900/20 p-2 rounded">
          This is a downgrade and will take effect at the end of your current billing cycle
          on {new Date(subscription.endDate).toLocaleDateString()}.
          No immediate payment is required.
        </div>
      )}
    </div>
  );
}

import React from 'react';

interface ProgressIndicatorProps {
  currentStep: number;
  steps: Array<{
    id: number;
    label: string;
  }>;
}

export default function ProgressIndicator({ currentStep, steps }: ProgressIndicatorProps) {
  return (
    <div className="mb-12">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <React.Fragment key={step.id}>
            <div className={`flex items-center ${currentStep >= step.id ? 'text-[#8178E8]' : 'text-gray-400'}`}>
              <div className={`w-10 h-10 rounded-full flex items-center justify-center mr-2 
                ${currentStep >= step.id ? 'bg-[#8178E8] text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-500'}`}>
                {step.id}
              </div>
              <span className="hidden sm:inline">{step.label}</span>
            </div>
            
            {/* Don't show line after the last step */}
            {index < steps.length - 1 && (
              <div
                className={`flex-1 mx-4 h-1 ${currentStep > step.id ? 'bg-[#8178E8]' : 'bg-gray-200 dark:bg-gray-700'}`}>
              </div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
} 
'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';

interface AccordionItemProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  className?: string;
  titleClassName?: string;
  contentClassName?: string;
  icon?: React.ReactNode;
}

interface AccordionProps {
  children: React.ReactNode;
  className?: string;
  allowMultiple?: boolean;
}

/**
 * Individual accordion item component
 */
export const AccordionItem: React.FC<AccordionItemProps> = ({
  title,
  children,
  defaultOpen = false,
  className = '',
  titleClassName = '',
  contentClassName = '',
  icon
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <button
        onClick={toggleOpen}
        className={`w-full flex items-center justify-between p-4 text-left bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${titleClassName}`}
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-2">
          {icon && <span className="flex-shrink-0">{icon}</span>}
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {title}
          </span>
        </div>
        <div className="flex-shrink-0 ml-2">
          {isOpen ? (
            <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-400" />
          )}
        </div>
      </button>

      {/* Content */}
      {isOpen && (
        <div className={`p-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 ${contentClassName}`}>
          {children}
        </div>
      )}
    </div>
  );
};

/**
 * Accordion container component
 */
export const Accordion: React.FC<AccordionProps> = ({
  children,
  className = '',
  allowMultiple = true
}) => {
  return (
    <div className={`space-y-2 ${className}`}>
      {children}
    </div>
  );
};

/**
 * Styled accordion item with glass morphism design
 */
export const GlassAccordionItem: React.FC<AccordionItemProps> = ({
  title,
  children,
  defaultOpen = false,
  className = '',
  titleClassName = '',
  contentClassName = '',
  icon
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className={`backdrop-blur-sm bg-white/70 dark:bg-gray-800/70 border border-gray-200/50 dark:border-gray-700/50 rounded-lg overflow-hidden shadow-sm ${className}`}>
      {/* Header */}
      <button
        onClick={toggleOpen}
        className={`w-full flex items-center justify-between p-4 text-left hover:bg-white/80 dark:hover:bg-gray-700/80 transition-all duration-200 ${titleClassName}`}
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-2">
          {icon && <span className="flex-shrink-0">{icon}</span>}
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {title}
          </span>
        </div>
        <div className="flex-shrink-0 ml-2">
          {isOpen ? (
            <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200" />
          )}
        </div>
      </button>

      {/* Content with smooth animation */}
      <div className={`transition-all duration-200 ease-in-out ${isOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className={`p-4 bg-gradient-to-br from-gray-50/80 to-gray-100/80 dark:from-gray-900/80 dark:to-gray-800/80 border-t border-gray-200/50 dark:border-gray-700/50 ${contentClassName}`}>
          {children}
        </div>
      </div>
    </div>
  );
};

/**
 * Colored accordion item variants
 */
interface ColoredAccordionItemProps extends AccordionItemProps {
  variant?: 'blue' | 'green' | 'purple' | 'red' | 'yellow';
}

export const ColoredAccordionItem: React.FC<ColoredAccordionItemProps> = ({
  title,
  children,
  defaultOpen = false,
  className = '',
  titleClassName = '',
  contentClassName = '',
  icon,
  variant = 'blue'
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  const toggleOpen = () => {
    setIsOpen(!isOpen);
  };

  const variantClasses = {
    blue: {
      container: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
      header: 'text-blue-900 dark:text-blue-100',
      content: 'bg-blue-50/50 dark:bg-blue-900/10 border-blue-200/50 dark:border-blue-800/50',
      text: 'text-blue-800 dark:text-blue-200'
    },
    green: {
      container: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
      header: 'text-green-900 dark:text-green-100',
      content: 'bg-green-50/50 dark:bg-green-900/10 border-green-200/50 dark:border-green-800/50',
      text: 'text-green-800 dark:text-green-200'
    },
    purple: {
      container: 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800',
      header: 'text-purple-900 dark:text-purple-100',
      content: 'bg-purple-50/50 dark:bg-purple-900/10 border-purple-200/50 dark:border-purple-800/50',
      text: 'text-purple-800 dark:text-purple-200'
    },
    red: {
      container: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
      header: 'text-red-900 dark:text-red-100',
      content: 'bg-red-50/50 dark:bg-red-900/10 border-red-200/50 dark:border-red-800/50',
      text: 'text-red-800 dark:text-red-200'
    },
    yellow: {
      container: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
      header: 'text-yellow-900 dark:text-yellow-100',
      content: 'bg-yellow-50/50 dark:bg-yellow-900/10 border-yellow-200/50 dark:border-yellow-800/50',
      text: 'text-yellow-800 dark:text-yellow-200'
    }
  };

  const colors = variantClasses[variant];

  return (
    <div className={`${colors.container} border rounded-lg overflow-hidden ${className}`}>
      {/* Header */}
      <button
        onClick={toggleOpen}
        className={`w-full flex items-center justify-between p-3 text-left hover:opacity-80 transition-opacity ${titleClassName}`}
        aria-expanded={isOpen}
      >
        <div className="flex items-center space-x-2">
          {icon && <span className="flex-shrink-0">{icon}</span>}
          <span className={`text-sm font-medium ${colors.header}`}>
            {title}
          </span>
        </div>
        <div className="flex-shrink-0 ml-2">
          {isOpen ? (
            <ChevronDown className={`w-4 h-4 ${colors.header} transition-transform duration-200`} />
          ) : (
            <ChevronRight className={`w-4 h-4 ${colors.header} transition-transform duration-200`} />
          )}
        </div>
      </button>

      {/* Content */}
      {isOpen && (
        <div className={`p-3 ${colors.content} border-t ${contentClassName}`}>
          <div className={`text-xs ${colors.text} ${contentClassName}`}>
            {children}
          </div>
        </div>
      )}
    </div>
  );
};

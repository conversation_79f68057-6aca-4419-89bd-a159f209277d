/* React Colorful Custom Styles for Modern 2025 Design */

.react-colorful-wrapper {
  width: 200px;
  height: auto;
}

.react-colorful {
  width: 200px !important;
  height: 150px !important;
}

.react-colorful__saturation {
  border-radius: 8px !important;
  border: 1px solid rgba(229, 231, 235, 0.5) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.react-colorful__hue {
  border-radius: 8px !important;
  border: 1px solid rgba(229, 231, 235, 0.5) !important;
  height: 16px !important;
  margin-top: 12px !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

.react-colorful__pointer {
  width: 16px !important;
  height: 16px !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05) !important;
}


.react-colorful__hue .react-colorful__pointer {
  width: 16px !important;
  height: 20px !important;
  border-radius: 4px !important;
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .react-colorful__saturation {
    border: 1px solid rgba(75, 85, 99, 0.5) !important;
  }
  
  .react-colorful__hue {
    border: 1px solid rgba(75, 85, 99, 0.5) !important;
  }
}

/* Custom dark mode class support */
.dark .react-colorful__saturation {
  border: 1px solid rgba(75, 85, 99, 0.5) !important;
}

.dark .react-colorful__hue {
  border: 1px solid rgba(75, 85, 99, 0.5) !important;
}

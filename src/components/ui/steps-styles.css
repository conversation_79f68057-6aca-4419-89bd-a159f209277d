/* Custom styles for Steps component */
.p-steps .p-steps-item .p-menuitem-link {
  background: transparent;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  border-radius: 0.375rem;
}

.p-steps .p-steps-item.p-highlight .p-steps-number {
  background: rgb(79, 70, 229) !important;
  color: white !important;
}

.p-steps .p-steps-item .p-steps-number {
  color: rgb(107, 114, 128);
  border: 1px solid rgb(209, 213, 219);
  background: white;
}

.dark .p-steps .p-steps-item .p-steps-number {
  color: rgb(156, 163, 175);
  border: 1px solid rgb(75, 85, 99);
  background: rgb(31, 41, 55);
}

.dark .p-steps .p-steps-item.p-highlight .p-steps-number {
  background: rgb(79, 70, 229) !important;
  color: white !important;
}

.p-steps .p-steps-item .p-menuitem-link .p-steps-title {
  color: rgb(107, 114, 128);
}

.dark .p-steps .p-steps-item .p-menuitem-link .p-steps-title {
  color: rgb(156, 163, 175);
}

.p-steps .p-steps-item.p-highlight .p-menuitem-link .p-steps-title {
  color: rgb(79, 70, 229);
}

.dark .p-steps .p-steps-item.p-highlight .p-menuitem-link .p-steps-title {
  color: rgb(129, 140, 248);
}

/* Remove connector line between steps */
.p-steps .p-steps-item:before {
  border-top: 1px solid rgb(209, 213, 219);
}

.dark .p-steps .p-steps-item:before {
  border-top: 1px solid rgb(75, 85, 99);
}

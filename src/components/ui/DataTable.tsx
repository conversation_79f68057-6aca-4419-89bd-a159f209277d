'use client';

import React, { forwardRef } from 'react';
import { DataTable as PrimeDataTable } from 'primereact/datatable';
import { classNames } from 'primereact/utils';

export interface DataTableProps {
  value: any[];
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  dataKey?: string;
  rows?: number;
  first?: number;
  totalRecords?: number;
  paginator?: boolean;
  sortField?: string;
  sortOrder?: number;
  multiSortMeta?: any[];
  onSort?: (event: any) => void;
  onPage?: (event: any) => void;
  loading?: boolean;
  emptyMessage?: React.ReactNode;
  rowsPerPageOptions?: number[];
  lazy?: boolean;
  responsive?: boolean;
  resizableColumns?: boolean;
  scrollable?: boolean;
  scrollHeight?: string;
  filterDisplay?: string;
  globalFilter?: any;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  tableClassName?: string;
  tableStyle?: React.CSSProperties;
  onRowClick?: (e: any) => void;
  selectionMode?: string;
  rowClassName?: (data: any) => string;
  [key: string]: any;
}

export const DataTable = forwardRef<any, DataTableProps>(({
  value,
  className,
  style,
  children,
  loading,
  emptyMessage,
  paginator = false,
  tableClassName,
  tableStyle,
  rows,
  totalRecords,
  first,
  onPage,
  rowsPerPageOptions,
  dataKey,
  lazy,
  sortField,
  sortOrder,
  onSort,
  ...props
}, ref) => {
  const baseTableClass = 'min-w-full divide-y divide-gray-200 dark:divide-gray-700';
  const finalTableClass = classNames(baseTableClass, tableClassName);

  return (
    <div className={classNames('bg-[#171721] rounded-xl border border-[#323074]/20 shadow-xl overflow-hidden', className)} style={style}>
      <PrimeDataTable
        ref={ref}
        value={value}
        loading={loading}
        emptyMessage={emptyMessage}
        paginator={paginator}
        rows={rows}
        totalRecords={totalRecords}
        first={first}
        onPage={onPage}
        rowsPerPageOptions={rowsPerPageOptions}
        dataKey={dataKey}
        lazy={lazy}
        sortField={sortField}
        sortOrder={sortOrder}
        onSort={onSort}
        pt={{
          root: { className: 'overflow-hidden' },
          table: { className: finalTableClass, style: tableStyle },
          thead: { className: 'bg-gray-50 dark:bg-gray-800' },
          tbody: { className: 'bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700' },
          tfoot: { className: 'bg-gray-50 dark:bg-gray-800' },
          loadingOverlay: { className: 'bg-black/20 backdrop-blur-sm' },
          loadingIcon: { className: 'text-indigo-500 w-10 h-10' },
          paginator: {
            root: { className: 'px-6 py-3 bg-[#1e1e2e] border-t border-[#323074]/20' },
            pageButton: { className: 'p-2 rounded-md hover:bg-[#323074]/20' },
            firstPageButton: { className: 'p-2 rounded-md hover:bg-[#323074]/20' },
            prevPageButton: { className: 'p-2 rounded-md hover:bg-[#323074]/20' },
            nextPageButton: { className: 'p-2 rounded-md hover:bg-[#323074]/20' },
            lastPageButton: { className: 'p-2 rounded-md hover:bg-[#323074]/20' },
            pages: { className: 'mx-2' }
          }
        }}
        {...props}
      >
        {children}
      </PrimeDataTable>
    </div>
  );
});

DataTable.displayName = 'DataTable';

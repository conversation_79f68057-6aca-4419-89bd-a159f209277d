'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';

/**
 * Test component for toast notifications
 * This component can be added to any page to test toast functionality
 */
export function ToastTest() {
  const { success, error, info, warn } = useToast();

  return (
    <div className="flex flex-wrap gap-2 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
      <h3 className="w-full text-lg font-medium mb-2">Toast Test</h3>
      <Button onClick={() => success('Success Toast', 'This is a success message')}>
        Show Success
      </Button>
      <Button onClick={() => error('Error Toast', 'This is an error message')}>
        Show Error
      </Button>
      <Button onClick={() => info('Info Toast', 'This is an info message')}>
        Show Info
      </Button>
      <Button onClick={() => warn('Warning Toast', 'This is a warning message')}>
        Show Warning
      </Button>
    </div>
  );
}

export default ToastTest;

'use client';

import React from 'react';

interface LoadingViewProps {
  message?: string;
  className?: string;
}

/**
 * A reusable component for displaying loading states with a spinner
 * 
 * @example
 * <LoadingView message="Loading dashboard..." />
 */
export function LoadingView({
  message = 'Loading...',
  className = '',
}: LoadingViewProps) {
  return (
    <div className={`min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex items-center justify-center ${className}`}>
      <div className="text-center">
        <div
          className="w-16 h-16 border-4 border-[#6964D3] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">{message}</p>
      </div>
    </div>
  );
}

/**
 * A variant of LoadingView that can be used inline within a layout (not full screen)
 */
export function InlineLoadingView({
  message = 'Loading...',
  className = '',
}: LoadingViewProps) {
  return (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <div className="text-center">
        <div
          className="w-10 h-10 border-3 border-[#6964D3] border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
        <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">{message}</p>
      </div>
    </div>
  );
}

export default LoadingView; 
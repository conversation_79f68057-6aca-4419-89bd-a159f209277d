'use client';

import React, {useState} from 'react';
import {Dialog as PrimeDialog} from 'primereact/dialog';
import {X} from 'lucide-react';

interface DialogProps {
  title?: React.ReactNode;
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  onClose?: () => void;
  children: React.ReactNode;
  draggable?: boolean;
  showHeader?: boolean;
  footer?: React.ReactNode;
  style?: React.CSSProperties;
  className?: string;
  size?: 'default' | 'large' | 'full';
}

export function Dialog({
                         title,
                         open,
                         onOpenChange,
                         children,
                         showHeader=true,
                         footer,
                         onClose,
                         draggable=false,
                         style,
                         className = '',
                         size = 'default'
                       }: DialogProps) {
  const onHide = () => {
    onOpenChange?.(false);
    onClose?.();
  }
  const [maximized, setMaximized] = useState<DialogProps["size"]>(size);
  const headerTemplate = (
    <div className="flex justify-between items-center w-full">
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">{title}</h2>
      <button
        onClick={onHide}
        className="text-[#5E5E5E] hover:text-[#6964D3] dark:text-[#C6C6C6] dark:hover:text-white bg-transparent border-0 p-2 rounded-full hover:bg-[#F3F3F3] dark:hover:bg-[#2a2a38] transition-colors"
      >
        <X size={20}/>
      </button>
    </div>
  );
  
  const getDialogStyle = () => {
    if (style) return style;
    
    switch (maximized) {
      case 'large':
        return {
          width: '95%',
          maxWidth: '900px',
          maxHeight: '90vh' // Ensure it doesn't exceed viewport height
        };
      case 'full':
        return {
          width: '100vw',
          height: '100vh',
          maxWidth: '100vw',
          margin: '0'
        };
      case 'default':
      default:
        return {
          width: '95%',
          maxWidth: '450px',
          maxHeight: '90vh' // Ensure it doesn't exceed viewport height
        };
    }
  };
  
  const onMaximize = () => {
    if (maximized === "full") {
      setMaximized(size);
    } else {
      setMaximized("full");
    }
  };
  
  return (
    <PrimeDialog
      onMaximize={onMaximize}
      header={headerTemplate}
      footer={footer}
      visible={open}
      showHeader={showHeader}
      onHide={onHide}
      draggable={draggable}
      maximizable={size !== 'full'}
      style={getDialogStyle()}
      className={`${className} ${maximized === 'full' ? 'p-dialog-maximized' : ''}`}
      pt={{
        root: {className: 'rounded-lg shadow-lg border-0'},
        header: {
          className: 'p-4 border-b border-[#E0D7FF]/50 dark:border-[#2c2d3d] bg-white dark:bg-[#1e1e28] rounded-t-lg'
        },
        content: {
          className: 'p-5 bg-white dark:bg-[#1e1e28]'
        },
        footer: {
          className: 'p-4 border-t border-[#E0D7FF]/50 dark:border-[#2c2d3d] bg-white dark:bg-[#1e1e28] rounded-b-lg'
        },
        closeButton: {className: 'hidden'}, // We're using our own close button
        mask: {className: 'bg-black/30 backdrop-blur-sm'}
      }}
    >
      {children}
    </PrimeDialog>
  );
}

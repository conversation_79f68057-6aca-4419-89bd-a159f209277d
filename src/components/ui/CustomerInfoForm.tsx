import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';

export interface CustomerInfo {
  name: string;
  email?: string;
}

export interface CustomerInfoFormProps {
  onSubmit: (customerInfo: CustomerInfo) => void;
  isLoading?: boolean;
  title?: string;
  subtitle?: string;
  nameLabel?: string;
  emailLabel?: string;
  submitButtonText?: string;
  className?: string;
  requireEmail?: boolean; // Whether email is required
}

export const CustomerInfoForm: React.FC<CustomerInfoFormProps> = ({
  onSubmit,
  isLoading = false,
  title = "Welcome! Let's get started",
  subtitle = "Please provide your information to begin the conversation",
  nameLabel = "Your Name",
  emailLabel = "Email Address (Optional)",
  submitButtonText = "Start Conversation",
  className = "",
  requireEmail = false
}) => {
  const { error } = useToast();
  const [formData, setFormData] = useState<CustomerInfo>({
    name: '',
    email: ''
  });
  const [errors, setErrors] = useState<Partial<CustomerInfo>>({});

  // Validate form data
  const validateForm = (): boolean => {
    const newErrors: Partial<CustomerInfo> = {};

    // Name is required
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    } else if (formData.name.trim().length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = 'Name must be less than 50 characters';
    }

    // Email validation based on requireEmail setting
    if (requireEmail) {
      // Email is required
      if (!formData.email || !formData.email.trim()) {
        newErrors.email = 'Email address is required';
      } else {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email.trim())) {
          newErrors.email = 'Please enter a valid email address';
        } else if (formData.email.trim().length > 255) {
          newErrors.email = 'Email must be less than 255 characters';
        }
      }
    } else {
      // Email is optional but must be valid if provided
      if (formData.email && formData.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email.trim())) {
          newErrors.email = 'Please enter a valid email address';
        } else if (formData.email.trim().length > 255) {
          newErrors.email = 'Email must be less than 255 characters';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      error('Please fix the errors below');
      return;
    }

    // Clean up the data
    const cleanedData: CustomerInfo = {
      name: formData.name.trim(),
      email: formData.email?.trim() || undefined
    };

    onSubmit(cleanedData);
  };

  // Handle input changes
  const handleInputChange = (field: keyof CustomerInfo, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  return (
    <div className={`max-w-md mx-auto ${className}`}>
      <div className="bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm rounded-xl border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 shadow-lg">
        {/* Header */}
        <div className="text-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {title}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {subtitle}
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name Field */}
          <div>
            <label
              htmlFor="customer-name"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              {nameLabel} <span className="text-red-500">*</span>
            </label>
            <Input
              id="customer-name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter your full name"
              disabled={isLoading}
              className={`w-full ${errors.name ? 'border-red-500 focus:border-red-500' : ''}`}
              maxLength={50}
              autoComplete="name"
              autoFocus
            />
            {errors.name && (
              <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                {errors.name}
              </p>
            )}
          </div>

          {/* Email Field */}
          <div>
            <label
              htmlFor="customer-email"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
            >
              {requireEmail ? emailLabel.replace('(Optional)', '').trim() : emailLabel}
              {requireEmail && <span className="text-red-500"> *</span>}
            </label>
            <Input
              id="customer-email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              disabled={isLoading}
              className={`w-full ${errors.email ? 'border-red-500 focus:border-red-500' : ''}`}
              maxLength={255}
              autoComplete="email"
            />
            {errors.email && (
              <p className="mt-1 text-xs text-red-600 dark:text-red-400">
                {errors.email}
              </p>
            )}
          </div>

          {/* Submit Button */}
          <Button
            className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:from-[#7169E7] hover:to-[#5854D2] text-white border-0 py-3 justify-center"
            type="submit"
            isLoading={isLoading}
            disabled={
              isLoading ||
              !formData.name.trim() ||
              (requireEmail && (!formData.email || !formData.email.trim()))
            }
          >
            {submitButtonText}
          </Button>
        </form>

        {/* Privacy Notice */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Your information is used only for this conversation and is handled according to our privacy policy.
          </p>
        </div>
      </div>
    </div>
  );
};

'use client';

import React from 'react';
import { Chip as PrimeChip } from 'primereact/chip';
import { X } from 'lucide-react';

interface ChipProps {
  children: React.ReactNode;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'gray';
  onDelete?: () => void;
  className?: string;
  icon?: string;
}

const colorVariants = {
  blue: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  green: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  red: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  yellow: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  gray: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
};

export function Chip({ children, color = 'gray', onDelete, className = '', icon }: ChipProps) {
  // Convert children to string if needed
  const label = typeof children === 'string' ? children : String(children);
  
  // Handle the onRemove event - PrimeReact's Chip component expects a function that returns boolean
  const handleRemove = onDelete ? () => {
    onDelete();
    return true;
  } : undefined;

  return (
    <PrimeChip
      label={label}
      removable={!!onDelete}
      onRemove={handleRemove}
      icon={icon}
      pt={{
        root: { className: `rounded-full px-2.5 py-1 text-xs font-medium ${colorVariants[color]} ${className}` },
        label: { className: 'px-0 py-0' },
        removeIcon: { className: 'ml-1.5 text-current hover:text-opacity-70' }
      }}
    />
  );
} 

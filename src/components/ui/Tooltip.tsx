'use client';

import React, { ReactNode } from 'react';
import { Tooltip as PrimeTooltip } from 'primereact/tooltip';

interface TooltipProps {
  children: ReactNode;
  content: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
  showDelay?: number;
  hideDelay?: number;
  autoHide?: boolean;
  id?: string;
}

export function Tooltip({
  children,
  content,
  position = 'top',
  className = '',
  showDelay = 150,
  hideDelay = 150,
  autoHide = true,
  id,
  ...props
}: TooltipProps) {
  // Generate a unique ID if none is provided
  const tooltipId = id || `tooltip-${Math.random().toString(36).substring(2, 9)}`;

  return (
    <>
      <PrimeTooltip
        target={`#${tooltipId}`}
        position={position}
        showDelay={showDelay}
        hideDelay={hideDelay}
        autoHide={autoHide}
        pt={{
          root: {
            className: `bg-gray-800 text-white dark:bg-gray-700 dark:text-gray-100 px-3 py-2 rounded-md text-sm shadow-md ${className}`
          },
          arrow: {
            className: 'bg-gray-800 dark:bg-gray-700'
          }
        }}
      >
        {content}
      </PrimeTooltip>
      <span id={tooltipId} {...props}>
        {children}
      </span>
    </>
  );
}

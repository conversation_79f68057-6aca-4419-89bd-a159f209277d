import React, { useState, ReactNode } from 'react';

interface TabsProps {
  children: ReactNode;
  activeIndex?: number;
  onTabChange?: (index: number) => void;
  className?: string;
}

interface TabListProps {
  children: ReactNode;
  className?: string;
}

interface TabProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  'data-state'?: 'active' | 'inactive';
}

interface TabPanelProps {
  children: ReactNode;
  className?: string;
}

// Create a type for the TabList element with proper props typing
type TabListElement = React.ReactElement<TabListProps, React.ComponentType<TabListProps>>;
// Create a type for the Tab element with proper props typing
type TabElement = React.ReactElement<TabProps, React.ComponentType<TabProps>>;

export const Tabs: React.FC<TabsProps> = ({ 
  children, 
  activeIndex = 0, 
  onTabChange,
  className = '' 
}) => {
  const [selectedIndex, setSelectedIndex] = useState(activeIndex);
  
  // Find TabList and TabPanel children
  const childrenArray = React.Children.toArray(children);
  
  // The first child should be TabList
  const tabList = childrenArray.find(
    child => React.isValidElement(child) && child.type === TabList
  );
  
  // All other children should be TabPanels
  const tabPanels = childrenArray.filter(
    child => React.isValidElement(child) && child.type === TabPanel
  );
  
  // Extract tabs from TabList with proper typing
  const tabListElement = tabList as TabListElement;
  const tabs = tabListElement 
    ? React.Children.toArray(tabListElement.props.children)
    : [];
  
  // Handle tab change
  const handleTabChange = (index: number) => {
    setSelectedIndex(index);
    if (onTabChange) {
      onTabChange(index);
    }
  };
  
  // Clone TabList with active tab info
  const clonedTabList = tabListElement
    ? React.cloneElement(tabListElement, {
        ...tabListElement.props,
        children: React.Children.map(tabListElement.props.children, (child, index) => {
          if (React.isValidElement<TabProps>(child)) {
            return React.cloneElement(child, {
              ...child.props,
              onClick: () => handleTabChange(index),
              'data-state': index === selectedIndex ? 'active' : 'inactive',
            });
          }
          return child;
        })
      })
    : null;
  
  // Show only the active TabPanel
  const activeTabPanel = tabPanels[selectedIndex] || null;
  
  return (
    <div className={`tabs ${className}`}>
      {clonedTabList}
      {activeTabPanel}
    </div>
  );
};

export const TabList: React.FC<TabListProps> = ({ children, className = '' }) => {
  return (
    <div className={`tab-list ${className}`}>
      {children}
    </div>
  );
};

export const Tab: React.FC<TabProps> = ({ children, className = '', ...props }) => {
  return (
    <div className={`tab ${className}`} role="tab" {...props}>
      {children}
    </div>
  );
};

export const TabPanel: React.FC<TabPanelProps> = ({ children, className = '' }) => {
  return (
    <div className={`tab-panel ${className}`} role="tabpanel">
      {children}
    </div>
  );
}; 
'use client';

import React, { InputHTMLAttributes } from 'react';

export interface SearchProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'> {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function Search({ 
  className = '', 
  size = 'md', 
  ...props 
}: SearchProps) {
  // Size-specific classes
  const sizeClasses = {
    sm: 'h-8 text-xs px-2 py-1',
    md: 'h-10 text-sm px-3 py-2',
    lg: 'h-12 text-base px-4 py-3',
  };

  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
        <svg 
          className="w-4 h-4 text-gray-500 dark:text-gray-400" 
          aria-hidden="true" 
          xmlns="http://www.w3.org/2000/svg" 
          fill="none" 
          viewBox="0 0 20 20"
        >
          <path 
            stroke="currentColor" 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth="2" 
            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
          />
        </svg>
      </div>
      <input
        type="search"
        className={`
          block w-full pl-10 
          ${sizeClasses[size]} 
          bg-white dark:bg-gray-800 
          border border-gray-300 dark:border-gray-700 
          rounded-md 
          text-gray-900 dark:text-white 
          focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600 
          focus:border-indigo-500 dark:focus:border-indigo-600 
          focus:outline-none
          placeholder-gray-400 dark:placeholder-gray-500
          transition-colors
        `}
        {...props}
      />
    </div>
  );
}

export default Search;

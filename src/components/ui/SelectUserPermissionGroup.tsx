'use client';

import React, {useEffect, useState} from 'react';
import {Select} from '@/components/ui/Select';
import {getUserPermissionGroups} from '@/server/actions/user-permission-groups';

interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  code: string;
  isDefault: boolean;
  permissions: string[];
}

interface SelectPermissionGroupProps {
  organizationId: string;
  value: string | number | null;
  onChange: (value: string | number | null) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  required?: boolean;
  name?: string;
  id?: string;
  filter?: boolean;
  onPermissionGroupsLoaded?: (groups: PermissionGroup[]) => void;
}

export const SelectUserPermissionGroup = React.forwardRef<HTMLSelectElement, SelectPermissionGroupProps>(
  ({
     organizationId,
     value,
     onChange,
     placeholder = 'Select a permission group',
     disabled = false,
     error,
     required,
     name,
     id,
     filter = true,
     onPermissionGroupsLoaded
   }, ref) => {
    const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadError, setLoadError] = useState<string | null>(null);

    useEffect(() => {
      const fetchPermissionGroups = async () => {
        try {
          setLoading(true);
          // Use the defined response type
          const response = await getUserPermissionGroups(organizationId);
          if (response.success) {
            // Convert the server response to our PermissionGroup type
            const groups = response?.data?.map(group => ({
              _id: String(group._id),
              name: String(group.name),
              description: String(group.description),
              code: String(group.code),
              isDefault: Boolean(group.isDefault),
              permissions: Array.isArray(group.permissions) ? group.permissions.map(String) : []
            })) || [];
            setPermissionGroups(groups);
            if (onPermissionGroupsLoaded) {
              onPermissionGroupsLoaded(groups);
            }
          } else {
            // Handle error case
            setLoadError('Failed to load permission groups: '+response.error);
          }
        } catch (err) {
          setLoadError('An error occurred while fetching permission groups');
        } finally {
          setLoading(false);
        }
      };

      if (organizationId) {
        fetchPermissionGroups();
      } else {
        setLoadError('Organization ID is required');
        setLoading(false);
      }
    }, [organizationId, onPermissionGroupsLoaded]);
    // Transform permission groups to dropdown options
    const options = permissionGroups?.map(group => ({
      label: `${group.name} ${group.permissions?.length > 0 ? `(${group.permissions?.length || 0} permissions)` : ''}${group.isDefault ? ' (Default)' : ''}`,
      value: group._id.toString(),
    })) || [];

    // Combine component error with loading error
    const displayError = error || (loadError && !loading ? loadError : undefined);
    return (
      <div className="w-full">
        <Select
          ref={ref}
          id={id}
          filter={filter}
          name={name}
          options={options}
          value={value}
          onChange={onChange}
          placeholder={loading ? 'Loading permission groups...' : placeholder}
          disabled={disabled || loading}
          required={required}
          error={displayError}
        />
      </div>
    );
  }
);

SelectUserPermissionGroup.displayName = 'SelectPermissionGroup';

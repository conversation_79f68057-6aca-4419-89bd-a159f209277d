'use client';

import React, { forwardRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Paginator, PaginatorPageChangeEvent } from 'primereact/paginator';
import "./custom-datatable.css";

export interface CustomDataTableProps {
  value: any[];
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  dataKey?: string;
  rows?: number;
  first?: number;
  totalRecords?: number;
  paginator?: boolean;
  sortField?: string;
  sortOrder?: number;
  multiSortMeta?: any[];
  onSort?: (event: any) => void;
  onPage?: (event: any) => void;
  loading?: boolean;
  emptyMessage?: React.ReactNode;
  rowsPerPageOptions?: number[];
  onRowsPerPageChange?: (rows: number) => void;
  lazy?: boolean;
  responsive?: boolean;
  resizableColumns?: boolean;
  scrollable?: boolean;
  scrollHeight?: string;
  filterDisplay?: string;
  globalFilter?: any;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  tableClassName?: string;
  tableStyle?: React.CSSProperties;
  onRowClick?: (e: any) => void;
  selectionMode?: string;
  rowClassName?: (data: any) => string;
  bgColor?: string; // Custom background color for light mode
  darkBgColor?: string; // Custom background color for dark mode
  hoverBgColor?: string; // Hover background color for light mode
  darkHoverBgColor?: string; // Hover background color for dark mode
  headerClassName?: string;
  totalRecordsMessage?: string;
  [key: string]: any;
}

export const CustomDataTable = forwardRef<any, CustomDataTableProps>(({
  value,
  className,
  style,
  children,
  loading,
  emptyMessage = 'No records found',
  paginator = true,
  rows = 10,
  totalRecords,
  first,
  onPage,
  rowsPerPageOptions,
  onRowsPerPageChange,
  dataKey,
  lazy,
  sortField,
  sortOrder,
  onSort,
  bgColor = '#FFFFFF', // Default light mode background
  darkBgColor = '#212130', // Default dark mode background
  hoverBgColor = '#E8E8E8', // Default light mode hover
  darkHoverBgColor = '#353545', // Default dark mode hover
  totalRecordsMessage,
  ...props
}, ref) => {
  // Set custom CSS variables for styling
  const tableStyles = {
    '--bg-color': bgColor,
    '--dark-bg-color': darkBgColor,
    '--hover-bg-color': hoverBgColor,
    '--dark-hover-bg-color': darkHoverBgColor,
  } as React.CSSProperties;



  const totalItems = totalRecords || (value as any)?.length || 0;
  // Handle page change event
  const handlePageChange = (e: PaginatorPageChangeEvent) => {
    if (onPage) {
      onPage(e);
    }

    // If rows changed and onRowsPerPageChange callback provided
    if (e.rows !== rows && onRowsPerPageChange) {
      onRowsPerPageChange(e.rows);
    }
  };

  return (
    <div className="w-full">
      {totalRecordsMessage && (
        <div className="flex justify-between mb-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {totalRecordsMessage}
          </div>
        </div>
      )}

      <DataTable
        ref={ref}
        value={value}
        loading={loading}
        emptyMessage={emptyMessage}
        paginator={false}
        rows={rows}
        totalRecords={totalRecords}
        first={first}
        onPage={onPage}
        rowsPerPageOptions={rowsPerPageOptions}
        dataKey={dataKey}
        lazy={lazy}
        sortField={sortField}
        sortOrder={sortOrder}
        onSort={onSort}
        className={`custom-datatable ${className}`}
        style={tableStyles}
        {...props}
      >
        {children}
      </DataTable>

      {paginator && totalItems > 0 && (
        <div className="mt-4">
          <Paginator
            first={first || 0}
            rows={rows}
            totalRecords={totalItems}
            onPageChange={handlePageChange}
            rowsPerPageOptions={rowsPerPageOptions}
            template="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink RowsPerPageDropdown"
            className="p-paginator-bottom border-t border-gray-200 dark:border-gray-700 pt-4"
          />
        </div>
      )}
    </div>
  );
});

CustomDataTable.displayName = 'CustomDataTable';

export default CustomDataTable;

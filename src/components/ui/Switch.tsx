'use client';

import React, { useCallback } from 'react';

interface SwitchProps {
  checked: boolean;
  onChange?: (e: { value: boolean }) => void;
  onCheckedChange?: (checked: boolean) => void;
  className?: string;
  disabled?: boolean;
  id?: string;
  name?: string;
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
}

export function Switch({
  checked,
  onChange,
  onCheckedChange,
  className = '',
  disabled = false,
  id,
  name,
  'aria-label': ariaLabel,
  'aria-labelledby': ariaLabelledBy,
  'aria-describedby': ariaDescribedBy,
}: SwitchProps) {
  const handleChange = useCallback((newChecked: boolean) => {
    if (disabled) return;

    // Support both callback patterns for backward compatibility
    if (onChange) {
      onChange({ value: newChecked });
    }
    if (onCheckedChange) {
      onCheckedChange(newChecked);
    }
  }, [onChange, onCheckedChange, disabled]);

  const handleClick = useCallback(() => {
    handleChange(!checked);
  }, [checked, handleChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      handleChange(!checked);
    }
  }, [checked, handleChange]);

  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      aria-label={ariaLabel}
      aria-labelledby={ariaLabelledBy}
      aria-describedby={ariaDescribedBy}
      id={id}
      name={name}
      disabled={disabled}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      className={`
        relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-white
        dark:focus:ring-offset-gray-900
        ${checked
          ? 'bg-indigo-600 hover:bg-indigo-700'
          : 'bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600'
        }
        ${disabled
          ? 'cursor-not-allowed opacity-50'
          : 'cursor-pointer'
        }
        ${className}
      `.trim()}
    >
      <span
        className={`
          inline-block h-4 w-4 transform rounded-full bg-white shadow-lg ring-0 transition-transform duration-200 ease-in-out
          ${checked ? 'translate-x-6' : 'translate-x-1'}
        `.trim()}
      />
    </button>
  );
}
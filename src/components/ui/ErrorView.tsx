'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';

interface ErrorViewProps {
  error: string;
  title?: string;
  buttonText?: string;
  onButtonClick?: () => void;
  className?: string;
}

/**
 * A reusable component for displaying error messages with a customizable action button
 *
 * @example
 * <ErrorView
 *   error="You don't have access to this organization"
 *   buttonText="Back to Dashboard"
 *   onButtonClick={() => router.push('/dashboard')}
 * />
 */
export function ErrorView({
  error,
  title = 'Error',
  buttonText = 'Back',
  onButtonClick,
  className = '',
}: ErrorViewProps) {
  return (
    <div className={`min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex items-center justify-center ${className}`}>
      <div className="bg-white dark:bg-[#1e1e28] shadow rounded-xl p-6 max-w-md w-full">
        <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-4">{title}</h2>
        <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6 capitalize">{error}</p>
        {onButtonClick && (
          <Button
            className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
            onClick={onButtonClick}
          >
            {buttonText}
          </Button>
        )}
      </div>
    </div>
  );
}


export default ErrorView;

'use client';

import React from 'react';
import {InputText} from 'primereact/inputtext';
import './input-autofill.css'; // Import CSS for autofill styling

interface InputProps extends Omit<React.ComponentProps<typeof InputText>, "value"> {
  className?: string;
  error?: string;
  containerClass?: string;
  value?: string | number;
}

export const Input = React.forwardRef<HTMLInputElement, InputProps>(({
  className = '',
  error,
  containerClass = "",
  ...props
}, ref) => {
  // Ensure value is always a string and handle null/undefined
  const safeValue = props.value != null ? String(props.value) : '';

  return (
    <div className={`w-full ${containerClass}`}>
      <InputText
        ref={ref}
        className={error ? 'p-invalid' : ''}
        {...props}
        value={safeValue}
        pt={{
          root: {
            className: `w-full h-10 rounded-md border ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} bg-transparent px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500 ${className}`
          }
        }}
      />
      {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}
    </div>
  );
});

Input.displayName = 'Input';

'use client';

import React from 'react';
import {Button as PrimeButton} from 'primereact/button';

export interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'subtle' | 'ghost' | 'link' | 'primary' | 'danger' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  isLoading?: boolean;
  className?: string;
  children?: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  disabled?: boolean | null;
  type?: 'button' | 'submit' | 'reset';
  icon?: string;
  iconPos?: 'left' | 'right' | 'top' | 'bottom';
  label?: string;
  id?: string;

  [x: string]: any; // Allow any other props
}

const variantClasses = {
  default: 'bg-slate-900 text-white hover:bg-slate-800 dark:bg-slate-200 dark:text-slate-900 dark:hover:bg-slate-100',
  destructive: 'bg-red-500 text-white hover:bg-red-600 dark:hover:bg-red-600',
  outline: 'border border-slate-200 hover:bg-slate-100 dark:border-slate-700 dark:text-slate-100 dark:hover:bg-slate-700',
  subtle: 'bg-slate-100 text-slate-900 hover:bg-slate-200 dark:bg-slate-700 dark:text-slate-100',
  ghost: 'hover:bg-slate-100 hover:text-slate-900 dark:hover:bg-slate-800 dark:hover:text-slate-50 data-[state=open]:bg-transparent dark:data-[state=open]:bg-transparent',
  link: 'underline-offset-4 hover:underline text-slate-900 dark:text-slate-100 hover:bg-transparent dark:hover:bg-transparent',
  primary: 'bg-indigo-600 text-white hover:bg-indigo-700',
};

const sizeClasses = {
  default: 'h-10 py-2 px-4',
  sm: 'h-9 px-2 rounded-md',
  lg: 'h-11 px-8 rounded-md',
  icon: 'h-10 w-10',
};

const Button: React.FC<ButtonProps> = ({
                                         className = '',
                                         variant = 'primary',
                                         size = 'default',
                                         isLoading,
                                         children,
                                         ...props
                                       }) => {
  const baseClasses = 'rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';
  const variantClass = variant in variantClasses ? variantClasses[variant as keyof typeof variantClasses] : variantClasses.default;
  const sizeClass = size in sizeClasses ? sizeClasses[size as keyof typeof sizeClasses] : sizeClasses.default;
  const ptClasses = `${baseClasses} ${variantClass} ${sizeClass} ${className}`;
  // Map our size values to PrimeReact's size values
  const primeSize = size === 'sm' ? 'small' : size === 'lg' ? 'large' : undefined;
  return (
    <PrimeButton
      {...props}
      loading={isLoading}
      className={className}
      disabled={isLoading || props.disabled as boolean}
      size={primeSize}
      pt={{
        root: {className: `${ptClasses}`}
      }}
    >
      {!props.label && (
        isLoading ? (
          <>
            {children}
          </>
        ) : (
          children
        )
      )}
    </PrimeButton>
  );
}

Button.displayName = 'Button';

export {Button};

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { HexColorPicker } from 'react-colorful';
import './colorpicker-styles.css';

interface ColorPickerProps {
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  placeholder?: string;
  className?: string;
  containerClass?: string;
  id?: string;
  name?: string;
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#000000',
  onChange,
  disabled = false,
  error,
  placeholder,
  className = '',
  containerClass = '',
  id,
  name,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const pickerRef = useRef<HTMLDivElement>(null);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleColorChange = (newColor: string) => {
    if (onChange && !disabled) {
      onChange(newColor);
    }
  };

  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange && !disabled) {
      let inputValue = e.target.value;
      // Ensure # prefix for hex colors
      if (!inputValue.startsWith('#') && inputValue.length > 0) {
        inputValue = `#${inputValue}`;
      }
      onChange(inputValue);
    }
  };

  const togglePicker = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  return (
    <div className={`relative w-full ${containerClass}`} ref={pickerRef}>
      <div className="flex items-center space-x-2">
        {/* Color preview button */}
        <button
          type="button"
          onClick={togglePicker}
          disabled={disabled}
          className={`w-10 h-10 rounded-lg border-2 border-white shadow-sm cursor-pointer transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent ${
            error
              ? 'ring-2 ring-red-500/50 border-red-300'
              : 'border-gray-200 dark:border-gray-600'
          } ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
          style={{ backgroundColor: value }}
          aria-label={`Select color, current color is ${value}`}
        />

        {/* Text input for manual hex entry */}
        <div className="flex-1">
          <input
            type="text"
            id={id}
            name={name}
            value={value}
            onChange={handleTextInputChange}
            placeholder={placeholder || '#000000'}
            disabled={disabled}
            className={`w-full px-3 py-2 text-sm bg-white/50 dark:bg-gray-800/50 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500/50 focus:border-transparent transition-all text-gray-900 dark:text-white ${
              error
                ? 'border-red-500/50 focus:ring-red-500/50'
                : 'border-gray-200/50 dark:border-gray-600/50'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            maxLength={7}
          />
        </div>
      </div>

      {/* Color picker panel */}
      {isOpen && !disabled && (
        <div className="absolute z-50 mt-2 bg-white/70 dark:bg-[#1e1e28]/70 backdrop-blur-sm border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl shadow-lg p-4">
          <div className="react-colorful-wrapper">
            <HexColorPicker
              color={value}
              onChange={handleColorChange}
            />
          </div>
        </div>
      )}

      {error && (
        <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>
      )}
    </div>
  );
};

ColorPicker.displayName = 'ColorPicker';

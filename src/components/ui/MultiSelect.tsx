'use client';
import React from 'react';
import { MultiSelect as PrimeMultiSelect } from 'primereact/multiselect';
import './select-styles.css';

interface MultiSelectOption {
  label: string;
  value: string | number;
}

interface MultiSelectProps {
  options: MultiSelectOption[];
  value: (string | number)[];
  onChange: (value: (string | number)[]) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  required?: boolean;
  name?: string;
  id?: string;
  filter?: boolean;
  loading?: boolean;
  display?: 'comma' | 'chip';
  pt?: any; // Support for PrimeReact PassThrough props
}

// Define context type for item rendering
interface ItemContext {
  selected: boolean;
  focused: boolean;
  disabled: boolean;
  filter?: boolean;
  [key: string]: any;
}

export const MultiSelect = React.forwardRef<HTMLDivElement, MultiSelectProps>(
  ({
    options,
    value,
    onChange,
    placeholder,
    disabled,
    error,
    required,
    name,
    id,
    filter = false,
    loading = false,
    display = 'chip',
    pt
  }, ref) => {
    const defaultPt = {
      // Root and input styling
      root: {
        className: `w-full min-h-10 rounded-md border ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} bg-transparent text-sm dark:text-gray-50`
      },
      labelContainer: {
        className: 'px-3 py-2 flex-1 overflow-hidden'
      },
      label: {
        className: 'text-gray-900 dark:text-white'
      },
      trigger: {
        className: 'text-gray-500 dark:text-gray-400 px-3'
      },
      token: {
        className: 'inline-flex items-center px-2 py-1 mr-1 mb-1 bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300 rounded text-xs font-medium'
      },
      tokenLabel: {
        className: 'mr-1'
      },
      removeTokenIcon: {
        className: 'text-indigo-500 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-200 cursor-pointer'
      },

      // Dropdown panel styling
      panel: {
        className: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-md mt-1 z-50'
      },
      wrapper: {
        className: 'max-h-[200px] overflow-auto'
      },
      list: {
        className: 'py-1'
      },

      // Header with select all and filter
      header: {
        className: 'p-2 border-b border-gray-200 dark:border-gray-700'
      },
      headerCheckbox: {
        className: 'mr-2'
      },

      // Item styling
      itemGroup: {
        className: 'font-medium text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-900/50 px-4 py-2'
      },
      item: ({ context }: { context: ItemContext }) => ({
        className: `px-4 py-2 cursor-pointer transition-colors text-sm flex items-center ${
          context.selected
            ? 'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300'
            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50'
        }`
      }),
      itemCheckbox: {
        className: 'mr-2'
      },

      // Search and empty state styling
      filterContainer: {
        className: 'p-2 border-b border-gray-200 dark:border-gray-700'
      },
      filterInput: {
        className: 'w-full rounded-md border border-gray-300 dark:border-gray-700 bg-transparent py-1 px-2 text-sm dark:text-gray-50'
      },
      emptyMessage: {
        className: 'p-4 text-gray-500 dark:text-gray-400 text-center text-sm'
      },

      // Loading styling
      loadingIcon: {
        className: 'text-indigo-500'
      }
    };

    // Merge custom PT with defaults
    const mergedPt = { ...defaultPt, ...pt };

    return (
      <div className="w-full">
        <PrimeMultiSelect
          id={id}
          name={name}
          options={options}
          value={value}
          onChange={(e) => onChange(e.value)}
          placeholder={placeholder}
          disabled={disabled || loading}
          required={required}
          filter={filter}
          display={display}
          loading={loading}
          className={error ? 'p-invalid' : ''}
          pt={mergedPt}
          optionLabel="label"
          optionValue="value"
          showSelectAll={true}
          selectAllLabel="Select All"
          showClear={true}
        />
        {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}
      </div>
    );
  }
);

MultiSelect.displayName = 'MultiSelect';

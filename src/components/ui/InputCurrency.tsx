'use client';

import React from 'react';
import { InputNumber } from 'primereact/inputnumber';

interface InputCurrencyProps {
  id?: string;
  value: number;
  onChange: (value: number) => void;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
  currency?: string;
  locale?: string;
  min?: number;
  max?: number;
  step?: number;
  required?: boolean;
}

export function InputCurrency({
  id,
  value,
  onChange,
  className = '',
  placeholder = '0.00',
  disabled = false,
  currency = 'USD',
  locale = 'en-US',
  min = 0,
  max,
  step = 0.01,
  required = false,
}: InputCurrencyProps) {
  return (
    <InputNumber
      id={id}
      value={value}
      onValueChange={(e) => onChange(e.value || 0)}
      mode="currency"
      currency={currency}
      locale={locale}
      minFractionDigits={2}
      maxFractionDigits={2}
      min={min}
      max={max}
      step={step}
      placeholder={placeholder}
      disabled={disabled}
      required={required}
      pt={{
        root: { className: 'w-full' },
        input: {
          root:{
            className: `w-full rounded-md border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 p-3 text-gray-900 dark:text-white ${className}`
          }
        }
      }}
    />
  );
}

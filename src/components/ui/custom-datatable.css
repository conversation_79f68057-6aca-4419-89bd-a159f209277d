.custom-datatable {
  background-color: var(--bg-color);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.custom-datatable .p-datatable-wrapper,
.custom-datatable .p-datatable-table,
.custom-datatable .p-datatable-tbody,
.custom-datatable .p-datatable-tbody > tr,
.custom-datatable .p-datatable-thead,
.custom-datatable .p-datatable-thead > tr,
.custom-datatable .p-datatable-thead > tr > th {
  background-color: var(--bg-color) !important;
}

.custom-datatable .p-datatable-tbody > tr {
  transition: background-color 0.15s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.custom-datatable .p-datatable-tbody > tr:last-child {
  border-bottom: none;
}

.custom-datatable .p-datatable-tbody > tr:hover {
  background-color: var(--hover-bg-color) !important;
}

/* Pagination styling */
.p-paginator {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.75rem 0;
  gap: 0.5rem;
  background: transparent !important;
  border: none !important;
}

.p-paginator button {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.375rem !important;
  margin: 0 0.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.03) !important;
  border: 1px solid rgba(0, 0, 0, 0.05) !important;
  color: rgba(0, 0, 0, 0.6) !important;
  transition: all 0.2s ease;
}

.p-paginator button:hover {
  background-color: rgba(0, 0, 0, 0.08) !important;
  color: rgba(0, 0, 0, 0.8) !important;
}

.p-paginator button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.p-paginator select {
  border-radius: 0.375rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.375rem 0.75rem;
  background-color: white;
  color: rgba(0, 0, 0, 0.7);
  font-size: 0.875rem;
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
  .custom-datatable {
    background-color: var(--dark-bg-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  .custom-datatable .p-datatable-wrapper,
  .custom-datatable .p-datatable-table,
  .custom-datatable .p-datatable-tbody,
  .custom-datatable .p-datatable-tbody > tr,
  .custom-datatable .p-datatable-thead,
  .custom-datatable .p-datatable-thead > tr,
  .custom-datatable .p-datatable-thead > tr > th {
    background-color: var(--dark-bg-color) !important;
  }
  
  .custom-datatable .p-datatable-tbody > tr {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }
  
  .custom-datatable .p-datatable-tbody > tr:hover {
    background-color: var(--dark-hover-bg-color) !important;
  }

  /* Dark mode pagination */
  .p-paginator button {
    background-color: rgba(255, 255, 255, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.7) !important;
  }

  .p-paginator button:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: rgba(255, 255, 255, 0.9) !important;
  }

  .p-paginator select {
    background-color: var(--dark-bg-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
  }
}

/* Ensure header cells have the right color */
.custom-datatable .p-datatable-thead .p-column-header-content {
  background-color: inherit !important;
}

/* Force the header colors to override any specificity issues */
.custom-datatable .p-datatable-thead,
.custom-datatable th.p-sortable-column {
  background-color: var(--bg-color) !important;
  border-bottom: 2px solid rgba(0, 0, 0, 0.08);
  font-weight: 600;
}

@media (prefers-color-scheme: dark) {
  .custom-datatable .p-datatable-thead,
  .custom-datatable th.p-sortable-column {
    background-color: var(--dark-bg-color) !important;
    border-bottom: 2px solid rgba(255, 255, 255, 0.08);
  }
} 
'use client';

import React from 'react';
import { ToastContainer, Theme } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

/**
 * ToastProvider component that should be used once at the root layout
 * This ensures toast notifications persist during client-side navigation
 */
export function ToastProvider() {
  return (
    <ToastContainer
      position="top-right"
      autoClose={5000}
      hideProgressBar={false}
      newestOnTop
      closeOnClick
      rtl={false}
      pauseOnFocusLoss
      draggable
      pauseOnHover
      theme={"dark" as Theme}
      className="toast-container"
    />
  );
}

export default ToastProvider;

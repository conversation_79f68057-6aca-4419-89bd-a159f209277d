'use client';

import React from 'react';
import { InputTextarea } from 'primereact/inputtextarea';

interface TextareaProps extends Omit<React.ComponentProps<typeof InputTextarea>, 'value'> {
  className?: string;
  error?: string;
  onKeyDown?: React.KeyboardEventHandler<HTMLTextAreaElement>;
  onKeyUp?: React.KeyboardEventHandler<HTMLTextAreaElement>;
  onBlur?: React.FocusEventHandler<HTMLTextAreaElement>;
  onFocus?: React.FocusEventHandler<HTMLTextAreaElement>;
  containerClass?: string;
  value?: string | number;
}

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(({
  className = '',
  error,
  containerClass = "",
  ...props
}, ref) => {
  // Ensure value is always a string and handle null/undefined
  const safeValue = props.value != null ? String(props.value) : '';

  return (
    <div className={`w-full ${containerClass}`}>
      <InputTextarea
        ref={ref}
        className={error ? 'p-invalid' : ''}
        {...props}
        value={safeValue}
        pt={{
          root: {
            className: `min-h-[80px] w-full rounded-md border ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} bg-transparent py-2 px-3 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500 ${className}`
          }
        }}
      />
      {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}
    </div>
  );
});

Textarea.displayName = 'Textarea';

'use client';

import { useState, useEffect } from 'react';
import { useToast } from './Toast';
import { Select } from './Select';
import { getActiveSubscriptionWithDetails } from '@/server/actions/billing';
import { getProducts } from '@/server/actions/product-actions';
import { logger } from '@/utils/logger';

export interface ProductOption {
  label: string;
  value: string;
}

interface ApiResponse {
  success: boolean;
  data?: any;
  error?: string;
}

interface ProductSelectProps {
  organizationId: string;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  error?: string;
}

export function ProductSelect({
  organizationId,
  value,
  onChange,
  disabled = false,
  placeholder = 'Select a product',
  className = '',
  error
}: ProductSelectProps) {
  const [loading, setLoading] = useState(true);
  const [productOptions, setProductOptions] = useState<ProductOption[]>([]);
  const { error: showError } = useToast();

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      try {
        // Get active subscription for this organization
        const subscriptionsResult = await getActiveSubscriptionWithDetails({
          organizationId,
        });
        
        if (subscriptionsResult.success && subscriptionsResult.data) {
          const activeSubscription = subscriptionsResult.data;

          // Get products directly using the subscription's productId
          const productsResult: ApiResponse = await getProducts({
            organizationId, 
            subscriptionId: activeSubscription.id
          });
          
          if (productsResult.success && productsResult.data) {
            const products: ProductOption[] = productsResult.data.map((product: any) => ({
              label: product.name,
              value: product.code
            }));
            
            setProductOptions(products);
            
            // Set default product if no value is selected and products are available
            if (!value && products.length > 0) {            
              onChange(products[0].value);
            }
          }
        }
      } catch (err) {
        logger.error('Error loading products:', err);
        showError('Error', 'Failed to load available products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [organizationId, value]);

  return (
    <div>
      <Select
        placeholder={placeholder}
        options={productOptions}
        value={value}
        onChange={(newValue) => newValue !== null && onChange(newValue as string)}

        disabled={disabled || loading || productOptions.length === 0}
      />
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
      {productOptions.length === 0 && !loading && !error && (
        <p className="mt-1 text-xs text-gray-500">No products available for selection</p>
      )}
    </div>
  );
} 
'use client';

import React from 'react';

interface SubDomainInputProps {
  /** Current value of the subdomain input */
  value?: string;
  /** Callback fired when the input value changes */
  onChange?: (value: string) => void;
  /** Placeholder text for the input */
  placeholder?: string;
  /** The domain suffix to display in the addon */
  domainSuffix?: string;
  /** Error message to display below the input (following Input.tsx pattern) */
  error?: string;
  /** Additional CSS classes for the input */
  className?: string;
  /** Additional CSS classes for the container */
  containerClass?: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Input ID for accessibility */
  id?: string;
  /** Input name attribute */
  name?: string;
  /** Whether the input is required */
  required?: boolean;
  /** ARIA label for accessibility */
  'aria-label'?: string;
  /** ARIA described by for accessibility */
  'aria-describedby'?: string;
}

export const SubDomainInput: React.FC<SubDomainInputProps> = ({
  value = '',
  onChange,
  placeholder = 'your-company',
  domainSuffix = '-support.newinstance.com',
  error,
  className = '',
  containerClass = '',
  disabled = false,
  id,
  name,
  required = false,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
  ...props
}) => {
  // Handle input change with validation
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // Basic subdomain validation: alphanumeric and hyphens only, no spaces
    const sanitizedValue = inputValue
      .toLowerCase?.()
      .replace(/[^a-z0-9-]/g, '') // Remove invalid characters
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
      .replace(/-{2,}/g, '-'); // Replace multiple consecutive hyphens with single hyphen

    if (onChange) {
      onChange(sanitizedValue);
    }
  };

  // Generate unique IDs for accessibility
  const inputId = id || `subdomain-input-${Math.random().toString(36).substring(2, 11)}`;

  return (
    <div className={`w-full ${containerClass}`}>
      {/* InputGroup Container - Following Input.tsx structure */}
      <div className="flex">
        {/* Subdomain Input - Using exact same styling as Input.tsx */}
        <input
          type="text"
          id={inputId}
          name={name}
          value={value}
          onChange={handleInputChange}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          aria-label={ariaLabel || 'Subdomain'}
          aria-describedby={ariaDescribedBy}
          className={`flex-1 h-10 rounded-l-md border ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} bg-transparent px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500 ${className}`}
          {...props}
        />

        {/* Domain Suffix Addon - Matching Input.tsx styling patterns */}
        <span
          className={`h-10 px-3 py-2 text-sm font-mono rounded-r-md border border-l-0 flex items-center ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} ${disabled ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-600' : 'bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400'}`}
        >
          {domainSuffix}
        </span>
      </div>

      {/* Error Message - Exact same pattern as Input.tsx */}
      {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}

      {/* Helper Text for URL Preview - Only show when no error */}
      {value && !error && (
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Portal URL: <span className="font-mono">
            {process.env.NODE_ENV === 'development'
              ? `http://localhost:3000/portal/${value}`
              : `https://${value}${domainSuffix}`
            }
          </span>
        </p>
      )}
    </div>
  );
};

'use client';

import React from 'react';
import { Checkbox as PrimeCheckbox } from 'primereact/checkbox';

interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: React.ReactNode;
  className?: string;
  disabled?: boolean;
  id?: string;
  name?: string;
  required?: boolean;
  error?: string;
}

export const Checkbox: React.FC<CheckboxProps> = ({
  checked,
  onChange,
  label,
  className = '',
  disabled,
  id,
  name,
  required,
  error
}) => {
  return (
    <div className={`${className}`}>
      <div className="flex items-center">
        <PrimeCheckbox
          id={id}
          name={name}
          checked={checked}
          onChange={(e) => onChange(!!e.checked)}
          disabled={disabled}
          required={required}
          className={error ? 'p-invalid' : ''}
          pt={{
            root: { className: 'mr-2' },
            box: { 
              className: `border ${
                error 
                  ? 'border-red-500 dark:border-red-400' 
                  : 'border-gray-300 dark:border-gray-600'
              } rounded  dark:bg-gray-700` 
            },
            input: { className: 'text-indigo-600 dark:text-indigo-400' }
          }}
        />
        {label && (
          <label 
            htmlFor={id} 
            className={`text-sm ${disabled ? 'text-gray-400 dark:text-gray-500' : 'text-gray-700 dark:text-gray-300'}`}
          >
            {label}
          </label>
        )}
      </div>
      {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}
    </div>
  );
};

Checkbox.displayName = 'Checkbox'; 

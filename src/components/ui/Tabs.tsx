'use client';

import React, { useState } from 'react';

interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
}

interface TabsProps {
  tabs: Tab[];
  defaultActiveTab?: string;
  className?: string;
  variant?: 'default' | 'minimal';
}

export function Tabs({ tabs, defaultActiveTab, className = '', variant = 'default' }: TabsProps) {
  // Find the index of the default active tab
  const defaultId = defaultActiveTab || (tabs.length > 0 ? tabs[0].id : '');
  const [activeTab, setActiveTab] = useState(defaultId);
  const [prevActiveTab, setPrevActiveTab] = useState(defaultId);
  const [isAnimating, setIsAnimating] = useState(false);

  // Handle tab click
  const handleTabClick = (tabId: string) => {
    if (tabId !== activeTab && !isAnimating) {
      setPrevActiveTab(activeTab);
      setActiveTab(tabId);
      setIsAnimating(true);

      // Reset animation state after animation completes
      setTimeout(() => {
        setIsAnimating(false);
      }, 300);
    }
  };

  // Get active tab content
  const activeContent = tabs.find(tab => tab.id === activeTab)?.content || null;

  // Determine border styles based on variant
  const borderStyles = variant === 'minimal'
    ? 'border-b border-gray-100/50 dark:border-gray-800/50'
    : 'border-b border-[#E0D7FF]/30 dark:border-gray-700/30';

  // Determine button styles based on variant
  const getButtonStyles = (isActive: boolean) => {
    const baseStyles = 'group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200';

    if (variant === 'minimal') {
      return `${baseStyles} ${
        isActive 
          ? 'border-[#8178E8] dark:border-[#6964D3] text-[#6964D3] dark:text-[#8178E8]' 
          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-200/50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-700/50'
      }`;
    }

    return `${baseStyles} ${
      isActive 
        ? 'border-[#8178E8] dark:border-[#6964D3] text-[#6964D3] dark:text-[#8178E8]' 
        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300/50 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:border-gray-600/50'
    }`;
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Tab Navigation */}
      <div className={borderStyles}>
        <nav className="flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => handleTabClick(tab.id)}
              className={getButtonStyles(activeTab === tab.id)}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.icon && (
                <span className={`mr-2 ${activeTab === tab.id ? 'text-[#8178E8] dark:text-[#6964D3]' : 'text-gray-400 group-hover:text-gray-500 dark:text-gray-500 dark:group-hover:text-gray-400'}`}>
                  {tab.icon}
                </span>
              )}
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content with Animation */}
      <div className="py-6 relative overflow-hidden">
        <div
          className={`transform transition-all duration-300 ease-in-out ${
            isAnimating ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0'
          }`}
        >
          {activeContent}
        </div>
      </div>
    </div>
  );
}


'use client';

import { useRef, memo, useCallback } from 'react';
import { InputText } from 'primereact/inputtext';
import { Button } from '@/components/ui/Button';
import { Select } from '@/components/ui/Select';
import { TicketStatus, TicketPriority } from '@/types/ticket-types';
import React from 'react';

const ticketStatusOptions = [
  { label: 'All', value: '' },
  { label: 'Open', value: TicketStatus.OPEN },
  { label: 'In Progress', value: TicketStatus.IN_PROGRESS },
  { label: 'Resolved', value: TicketStatus.RESOLVED },
  { label: 'Closed', value: TicketStatus.CLOSED },
];

const ticketPriorityOptions = [
  { label: 'All', value: '' },
  { label: 'Low', value: TicketPriority.LOW },
  { label: 'Medium', value: TicketPriority.MEDIUM },
  { label: 'High', value: TicketPriority.HIGH },
  { label: 'Urgent', value: TicketPriority.URGENT },
];

export interface FilterControlsProps {
  searchInput: string;
  setSearchInput: (value: string) => void;
  status: string;
  setStatus: (value: string) => void;
  priority: string;
  setPriority: (value: string) => void;
  applyFilters: () => void;
  goToNewTicket: () => void;
}

export const FilterControls = memo(({
  searchInput,
  setSearchInput,
  status,
  setStatus,
  priority,
  setPriority,
  applyFilters,
  goToNewTicket
}: FilterControlsProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  }, [setSearchInput]);

  const handleStatusChange = useCallback((value: string | number | null | Record<string, any>) => {
    if (value !== null) setStatus(typeof value === 'object' ? value.value : value.toString());
  }, [setStatus]);

  const handlePriorityChange = useCallback((value: string | number | null) => {
    if (value !== null) setPriority(value.toString());
  }, [setPriority]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      applyFilters();
    }
  }, [applyFilters]);

  return (
    <div className="mb-8 animate-fadeIn">
      <div className="bg-opacity-10 bg-gradient-to-br from-[#2A2A3C] to-[#1C1C28] rounded-xl p-5 border border-[#323074]/20 shadow-xl">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="w-full md:w-64 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="pi pi-search text-[#998EF8]/70" />
            </div>
            <InputText
              ref={searchInputRef}
              value={searchInput}
              onChange={handleSearchChange}
              placeholder="Search tickets..."
              pt={{
                root: { 
                  className: 'w-full pl-10 bg-[#1E1E2E] border border-[#323074]/30 focus:border-[#998EF8] rounded-lg outline-none shadow-none h-10 px-3 py-2 text-sm' 
                }
              }}
              onKeyDown={handleKeyDown}
            />
          </div>

          <div className="w-full md:w-40">
            <Select
              value={status}
              onChange={handleStatusChange}
              options={ticketStatusOptions}
              placeholder="Status"

            />
          </div>

          <div className="w-full md:w-40">
            <Select
              value={priority}
              onChange={handlePriorityChange}
              options={ticketPriorityOptions}
              placeholder="Priority"

            />
          </div>

          <div className="flex gap-3 w-full md:w-auto md:ml-auto">
            <Button
              label="Apply Filters"
              icon="pi pi-filter"
              onClick={applyFilters}
              className="w-full md:w-auto shadow-md"
              variant="primary"
            />

            <Button
              label="New Ticket"
              icon="pi pi-plus"
              onClick={goToNewTicket}
              className="w-full md:w-auto shadow-md"
              variant="primary"
            />
          </div>
        </div>
      </div>
    </div>
  );
});

FilterControls.displayName = 'FilterControls'; 
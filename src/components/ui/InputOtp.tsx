'use client';

import React from 'react';
import {InputOtp as PrimeInputOtp, InputOtpChangeEvent as InputOtpChangeEvents} from 'primereact/inputotp';

export type InputOtpChangeEvent = InputOtpChangeEvents & {}

interface InputOtpProps extends Omit<React.ComponentProps<typeof PrimeInputOtp>, 'ref'> {
  className?: string;
  error?: string;
  length?: number;
}

export const InputOtp = React.forwardRef<HTMLInputElement, InputOtpProps>(
  ({className = '', error, length = 6, ...props}, ref) => {
    return (
      <div className="w-full">
        <PrimeInputOtp
          ref={ref as any}
          length={length}
          inputMode="numeric"
          className={error ? 'p-invalid dark:bg-[#1e1e28]' : ''}
          {...props}
          pt={{
            root: {
              className: 'flex justify-center gap-2'
            },
            input: {
              className: `w-12 h-14 text-center text-xl dark:bg-[#1e1e28] rounded-xl border ${error ? 'border-red-500 dark:border-red-400' : 'border-transparent'} focus:border-[#8178E8] focus:outline-none ${className}`
            }
          }}
        />
        {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400 text-center">{error}</p>}
      </div>
    );
  }
);

InputOtp.displayName = 'InputOtp';

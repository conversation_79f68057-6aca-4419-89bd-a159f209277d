'use client';

import React from 'react';
import { Steps as PrimeSteps } from 'primereact/steps';
import { MenuItem } from 'primereact/menuitem';
import './steps-styles.css'; // Import custom CSS for styling

export interface StepItem {
  label: string;
  icon?: string;
  command?: (event: { originalEvent: React.MouseEvent<HTMLElement>; item: MenuItem }) => void;
  url?: string;
  className?: string;
  disabled?: boolean;
  visible?: boolean;
  target?: string;
}

export interface StepsProps {
  model: StepItem[];
  activeIndex: number;
  onSelect?: (e: { originalEvent: React.SyntheticEvent; index: number }) => void;
  readOnly?: boolean;
  className?: string;
  pt?: any; // Support for PrimeReact PassThrough props
}

/**
 * Steps component for multi-step processes
 * Wraps PrimeReact Steps component with consistent styling using Tailwind CSS
 */
export function Steps({
  model,
  activeIndex,
  onSelect,
  readOnly = false,
  className = '',
  pt
}: StepsProps) {
  // Define default PassThrough (PT) props for styling
  const defaultPt = {
    // Root styling
    root: {
      className: 'mb-6'
    },

    // Step item styling
    step: ({ context }: { context: { active: boolean, disabled: boolean } }) => ({
      className: `${
        context.active
          ? 'font-medium'
          : context.disabled
            ? 'opacity-60 cursor-not-allowed'
            : 'hover:text-indigo-600 dark:hover:text-indigo-400'
      }`
    }),

    // Step number styling
    number: ({ context }: { context: { active: boolean, disabled: boolean } }) => ({
      className: `${
        context.active
          ? 'bg-indigo-600 dark:bg-indigo-500 text-white border-indigo-600 dark:border-indigo-500'
          : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-300 dark:border-gray-700'
      } flex items-center justify-center w-8 h-8 rounded-full border transition-colors`
    }),

    // Step title styling
    title: ({ context }: { context: { active: boolean, disabled: boolean } }) => ({
      className: `${
        context.active
          ? 'text-indigo-600 dark:text-indigo-400'
          : 'text-gray-600 dark:text-gray-400'
      } mt-2 text-sm transition-colors`
    }),

    // Step connector styling
    connector: {
      className: 'border-t border-gray-300 dark:border-gray-700 flex-1 transition-colors'
    }
  };

  // Merge custom PT with defaults
  const mergedPt = { ...defaultPt, ...pt };

  return (
    <PrimeSteps
      model={model}
      activeIndex={activeIndex}
      onSelect={onSelect}
      readOnly={readOnly}
      className={className}
      pt={mergedPt}
    />
  );
}

export default Steps;

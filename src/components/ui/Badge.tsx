'use client';

import React from 'react';
import { Tag } from 'primereact/tag';

interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info' | 'secondary';
  size?: 'sm' | 'md';
  className?: string;
  onClick?: () => void;
}

// Define severity types accepted by PrimeReact Tag
type SeverityType = 'success' | 'info' | 'warning' | 'danger' | 'secondary' | undefined;

// Extract only the valid string literal variants (exclude `undefined`)
type ValidVariant = Required<BadgeProps>['variant'];

const variantToSeverity: Record<ValidVariant, SeverityType> = {
  default: undefined,
  success: 'success',
  warning: 'warning',
  danger: 'danger',
  info: 'info',
  secondary: 'secondary',
};

const variantStyles = {
  default: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
  success: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  danger: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  info: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  secondary: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
};

const sizeStyles = {
  sm: 'text-xs px-2 py-0.5',
  md: 'text-sm px-2.5 py-0.5',
};

export function Badge({
                        children,
                        variant = 'default',
                        size = 'md',
                        className = '',
                        onClick
                      }: BadgeProps) {
  // Safely get severity with fallback for `undefined` variant
  const severity = variant ? variantToSeverity[variant] : undefined;

  const customClass = `font-medium rounded-full ${variantStyles[variant]} ${sizeStyles[size]} ${onClick ? 'cursor-pointer' : ''} ${className}`;

  return (
    <Tag
      value={children}
      severity={severity}
      onClick={onClick}
      pt={{
        root: { className: customClass }
      }}
    />
  );
}

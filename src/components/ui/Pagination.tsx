'use client';

import { useState, useEffect } from 'react';
import { Button } from './Button';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface PaginationProps {
  currentPage: number;
  totalItems: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  siblingCount?: number;
}

export function Pagination({
  currentPage,
  totalItems,
  pageSize,
  onPageChange,
  siblingCount = 1,
}: PaginationProps) {
  const [pages, setPages] = useState<(number | string)[]>([]);
  
  // Calculate total pages
  const totalPages = Math.ceil(totalItems / pageSize);
  
  // Generate page numbers array with ellipsis
  useEffect(() => {
    const generatePagination = () => {
      // If there are 7 or fewer pages, show all pages without ellipsis
      if (totalPages <= 7) {
        return Array.from({ length: totalPages }, (_, i) => i + 1);
      }
      
      // Calculate range of pages to show
      const leftSiblingIndex = Math.max(currentPage - siblingCount, 1);
      const rightSiblingIndex = Math.min(currentPage + siblingCount, totalPages);
      
      // Should show ellipsis
      const shouldShowLeftDots = leftSiblingIndex > 2;
      const shouldShowRightDots = rightSiblingIndex < totalPages - 1;
      
      // Always show first and last page
      if (shouldShowLeftDots && shouldShowRightDots) {
        // Show ellipsis on both sides
        const middleRange = Array.from(
          { length: rightSiblingIndex - leftSiblingIndex + 1 },
          (_, i) => leftSiblingIndex + i
        );
        return [1, '...', ...middleRange, '...', totalPages];
      } else if (shouldShowLeftDots && !shouldShowRightDots) {
        // Show ellipsis only on left side
        const rightRange = Array.from(
          { length: totalPages - leftSiblingIndex + 1 },
          (_, i) => leftSiblingIndex + i
        );
        return [1, '...', ...rightRange];
      } else if (!shouldShowLeftDots && shouldShowRightDots) {
        // Show ellipsis only on right side
        const leftRange = Array.from(
          { length: rightSiblingIndex },
          (_, i) => i + 1
        );
        return [...leftRange, '...', totalPages];
      }
      
      // Fallback to showing all pages (shouldn't reach here)
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    };
    
    setPages(generatePagination());
  }, [currentPage, totalPages, siblingCount]);
  
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;
  
  return (
    <div className="flex items-center justify-between">
      <div className="flex-1 flex justify-between sm:hidden">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          Previous
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next
        </Button>
      </div>
      
      <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{Math.min((currentPage - 1) * pageSize + 1, totalItems)}</span> to{' '}
            <span className="font-medium">{Math.min(currentPage * pageSize, totalItems)}</span> of{' '}
            <span className="font-medium">{totalItems}</span> results
          </p>
        </div>
        
        <div>
          <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
            <Button
              variant="outline"
              size="sm"
              className="rounded-l-md"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <span className="sr-only">Previous</span>
              <ChevronLeftIcon className="h-5 w-5" aria-hidden="true" />
            </Button>
            
            {pages.map((page, index) => {
              if (page === '...') {
                return (
                  <span
                    key={`ellipsis-${index}`}
                    className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700"
                  >
                    ...
                  </span>
                );
              }
              
              return (
                <Button
                  key={`page-${page}`}
                  variant={currentPage === page ? 'primary' : 'outline'}
                  size="sm"
                  className="rounded-none"
                  onClick={() => onPageChange(page as number)}
                >
                  {page}
                </Button>
              );
            })}
            
            <Button
              variant="outline"
              size="sm"
              className="rounded-r-md"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <span className="sr-only">Next</span>
              <ChevronRightIcon className="h-5 w-5" aria-hidden="true" />
            </Button>
          </nav>
        </div>
      </div>
    </div>
  );
}

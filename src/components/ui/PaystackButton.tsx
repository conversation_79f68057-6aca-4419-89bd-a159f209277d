'use client';

import React from 'react';
import { Button } from './Button';
import { usePaystackPayment } from 'react-paystack';

interface PaystackButtonProps {
  config: {
    reference: string;
    email: string;
    amount: number | string;  // Accept both number and string for flexibility
    publicKey: string;
    currency: string;
    metadata?: any;
  };
  onSuccess: (reference: any) => void;
  onClose?: () => void;
  disabled?: boolean;
  processing?: boolean;
  children: React.ReactNode;
}

const PaystackButton = ({
  config,
  onSuccess,
  onClose = () => {},
  disabled = false,
  processing = false,
  children
}: PaystackButtonProps) => {
  // Initialize usePaystackPayment with the configuration
  const initializePayment = usePaystackPayment({
    reference: config.reference,
    email: config.email,
    amount: typeof config.amount === 'string' ? parseInt(config.amount) : config.amount,
    publicKey: config.publicKey,
    currency: config.currency,
    metadata: config.metadata || {},
  });

  const handlePaymentClick = () => {
    // Initialize the payment with success and close callbacks
    initializePayment({
      onSuccess,
      onClose
    });
  };

  return (
    <Button
      onClick={handlePaymentClick}
      disabled={disabled || processing}
      className="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
    >
      {children}
    </Button>
  );
};

export default PaystackButton;

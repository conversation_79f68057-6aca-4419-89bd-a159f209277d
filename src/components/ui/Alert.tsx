'use client';

import React from 'react';
import { Message } from 'primereact/message';

interface AlertProps {
  severity: 'success' | 'info' | 'warn' | 'error';
  title?: string;
  children: React.ReactNode;
  className?: string;
  icon?: string;
  closable?: boolean;
  onClose?: () => void;
}

export const Alert: React.FC<AlertProps> = ({
  severity,
  title,
  children,
  className = '',
  icon,
}) => {
  // Define colors based on severity
  const severityColors = {
    success: 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800',
    info: 'bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800',
    warn: 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800',
    error: 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800'
  };

  // Define icon colors
  const iconColors = {
    success: 'text-green-500 dark:text-green-400',
    info: 'text-blue-500 dark:text-blue-400',
    warn: 'text-yellow-500 dark:text-yellow-400',
    error: 'text-red-500 dark:text-red-400'
  };

  // Custom content to handle title and message
  const content = (
    <div>
      {title && <div className="font-medium mb-1">{title}</div>}
      <div className="text-sm">{children}</div>
    </div>
  );

  return (
    <Message
      severity={severity}
      icon={icon}
      content={content}
      className={className}
      pt={{
        root: {
          className: `w-full p-3 rounded-md border ${severityColors[severity]} mb-4`
        },
        icon: {
          className: `${iconColors[severity]} mr-3`
        },
        text: {
          className: 'leading-5'
        }
      }}
    />
  );
};

// Add additional convenience components for common alert types
export const SuccessAlert: React.FC<Omit<AlertProps, 'severity'>> = (props) => (
  <Alert severity="success" {...props} />
);

export const InfoAlert: React.FC<Omit<AlertProps, 'severity'>> = (props) => (
  <Alert severity="info" {...props} />
);

export const WarningAlert: React.FC<Omit<AlertProps, 'severity'>> = (props) => (
  <Alert severity="warn" {...props} />
);

export const ErrorAlert: React.FC<Omit<AlertProps, 'severity'>> = (props) => (
  <Alert severity="error" {...props} />
);

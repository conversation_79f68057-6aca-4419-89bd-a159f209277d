'use client';

import React from 'react';
import { toast } from 'react-toastify';

type ToastSeverity = 'success' | 'info' | 'warn' | 'error';
type ToastType = 'success' | 'info' | 'warning' | 'error' | 'default';

type ConfirmOptions = {
  message: React.ReactNode;
  header?: string;
  icon?: string;
  accept?: () => void;
  reject?: () => void;
  acceptLabel?: string;
  rejectLabel?: string;
};

// Map primereact severities to react-toastify types
const getSeverityType = (severity: ToastSeverity): ToastType => {
  switch (severity) {
    case 'success':
      return 'success';
    case 'info':
      return 'info';
    case 'warn':
      return 'warning';
    case 'error':
      return 'error';
    default:
      return 'default';
  }
};

export const useToast = () => {
  // Helper function to show a toast with specified severity
  const show = (severity: ToastSeverity, summary: string, detail?: string, life?: number) => {
    const options = {
      type: getSeverityType(severity),
      autoClose: life || 5000,
      className: "relative flex p-4 min-h-10 rounded-lg justify-between overflow-hidden cursor-pointer mb-3 bg-[#20212f] text-white shadow-lg",
    };

    // If there's a detail, show it under the summary
    const content = detail
      ? <div className="text-sm font-medium block p-3">
          <div className="font-bold">{summary}</div>
          <div className="text-sm opacity-90">{detail}</div>
        </div>
      : <div className="text-sm font-medium block p-3">{summary}</div>;

    toast(content, options);
  };

  const success = (summary: string, detail?: string, life?: number) => {
    show('success', summary, detail, life);
  };

  const info = (summary: string, detail?: string, life?: number) => {
    show('info', summary, detail, life);
  };

  const warn = (summary: string, detail?: string, life?: number) => {
    show('warn', summary, detail, life);
  };

  const error = (summary: string, detail?: string | null, life?: number) => {
    show('error', summary, detail || undefined, life);
  };

  // Confirm dialog using react-toastify
  const confirm = (options: ConfirmOptions) => {
    toast.info(
      <div className="confirm-dialog p-3">
        <div className="font-bold text-base mb-2">{options.header || 'Confirmation'}</div>
        <div className="text-sm mb-4">{options.message}</div>
        <div className="flex justify-end space-x-2">
          {options.reject && (
            <button
              className="px-3 py-1 bg-gray-600 hover:bg-gray-700 rounded-md transition-colors"
              onClick={() => {
                toast.dismiss();
                options.reject?.();
              }}
            >
              {options.rejectLabel || 'No'}
            </button>
          )}
          <button
            className="px-3 py-1 bg-[#6964D3] hover:bg-[#5d58bf] rounded-md transition-colors"
            onClick={() => {
              toast.dismiss();
              options.accept?.();
            }}
          >
            {options.acceptLabel || 'Yes'}
          </button>
        </div>
      </div>,
      {
        autoClose: false,
        closeButton: false,
        closeOnClick: false,
        draggable: false,
        className: "bg-[#20212f] text-white rounded-lg shadow-lg"
      }
    );
  };

  const clear = () => {
    toast.dismiss();
  };

  return {
    success,
    info,
    warn,
    error,
    show,
    clear,
    confirm
  };
};

// Export the hook as the default export
export default useToast;

'use client';

import React from 'react';
import { UseFormReturn, FieldValues, SubmitHandler } from 'react-hook-form';

// Form wrapper component that integrates with React Hook Form
interface FormProps<T extends FieldValues> {
  form?: UseFormReturn<T>;
  onSubmit?: SubmitHandler<T>;
  children: React.ReactNode;
  className?: string;
  id?: string;
}

export function Form<T extends FieldValues>({ 
  form, 
  onSubmit, 
  children, 
  className = '', 
  id 
}: FormProps<T>) {
  const handleSubmit = form && onSubmit ? form.handleSubmit(onSubmit) : undefined;

  return (
    <form 
      onSubmit={handleSubmit} 
      className={className}
      id={id}
      noValidate
    >
      {children}
    </form>
  );
}

// FormSection component for grouping related fields
interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function FormSection({ title, description, children, className = '' }: FormSectionProps) {
  return (
    <div className={`space-y-4 ${className}`}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {title}
            </h3>
          )}
          {description && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {description}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  );
}

// FormRow component for responsive grid layouts
interface FormRowProps {
  children: React.ReactNode;
  className?: string;
  columns?: number;
}

export function FormRow({ children, className = '', columns = 2 }: FormRowProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid gap-4 ${gridCols[columns as keyof typeof gridCols] || gridCols[2]} ${className}`}>
      {children}
    </div>
  );
}

// FormField component for wrapping inputs with labels and error handling
interface FormFieldProps {
  label?: string;
  htmlFor?: string;
  required?: boolean;
  error?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export function FormField({ 
  label, 
  htmlFor, 
  required, 
  error, 
  description, 
  children, 
  className = '' 
}: FormFieldProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label 
          htmlFor={htmlFor} 
          className="block text-sm font-medium text-gray-700 dark:text-gray-300"
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      {children}
      {description && !error && (
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {description}
        </p>
      )}
      {error && (
        <p className="text-sm text-red-500 dark:text-red-400">
          {error}
        </p>
      )}
    </div>
  );
}

// FormActions component for form buttons
interface FormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

export function FormActions({ children, className = '', align = 'right' }: FormActionsProps) {
  const alignmentClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  };

  return (
    <div className={`flex gap-3 ${alignmentClasses[align]} ${className}`}>
      {children}
    </div>
  );
}

// FormLabel component for consistent label styling
interface FormLabelProps {
  htmlFor?: string;
  required?: boolean;
  children: React.ReactNode;
  className?: string;
}

export function FormLabel({ htmlFor, required, children, className = '' }: FormLabelProps) {
  return (
    <label 
      htmlFor={htmlFor} 
      className={`block text-sm font-medium text-gray-700 dark:text-gray-300 ${className}`}
    >
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  );
}

// FormMessage component for error messages
interface FormMessageProps {
  children?: React.ReactNode;
  className?: string;
}

export function FormMessage({ children, className = '' }: FormMessageProps) {
  if (!children) return null;
  
  return (
    <p className={`text-sm text-red-500 dark:text-red-400 ${className}`}>
      {children}
    </p>
  );
}

// FormControl component for wrapping form inputs
interface FormControlProps {
  children: React.ReactNode;
  className?: string;
}

export function FormControl({ children, className = '' }: FormControlProps) {
  return (
    <div className={`relative ${className}`}>
      {children}
    </div>
  );
}

// FormItem component for complete form field structure
interface FormItemProps {
  children: React.ReactNode;
  className?: string;
}

export function FormItem({ children, className = '' }: FormItemProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      {children}
    </div>
  );
}

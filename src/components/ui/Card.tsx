'use client';

import React from 'react';
import { Card as PrimeCard } from 'primereact/card';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  title?: React.ReactNode;
  subTitle?: React.ReactNode;
  onClick?: () => void;
  isPadded?: boolean;
  footer?: React.ReactNode;
  header?: React.ReactNode;
  hasBorder?: boolean;
}

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}


const Card: React.FC<CardProps> = ({
  children,
  className = '',
  onClick,
  title,
  isPadded=false,
  subTitle,
  footer,
  header,
  hasBorder = true,
  ...props
}) => {
  // If using title, subTitle, footer, or header props, use PrimeCard
  if (title || subTitle || footer || header) {
    return (
      <PrimeCard
      onClick={onClick}
        title={title}
        subTitle={subTitle}
        footer={footer}
        header={header}
        pt={{
          root: { className: `rounded-lg ${hasBorder ? 'border border-gray-200 dark:border-gray-700' : ''} shadow-sm ${className}` },
          body: { className: isPadded? 'p-5':'p-0' },
          title: { className: 'text-lg font-semibold leading-none tracking-tight' },
          subTitle: { className: 'text-sm text-gray-500 dark:text-gray-400' },
          content: { className: 'p-0' },
          footer: { className: 'flex items-center p-6 pt-0' }
        }}
        {...props}
      >
        {children}
      </PrimeCard>
    );
  }

  // Otherwise use a simpler div-based approach for more flexibility
  return (
    <div
      className={`rounded-lg dark:bg-[#1e1e28] ${hasBorder ? 'border border-gray-200 dark:border-gray-700' : ''} shadow-sm ${className}`}
      {...props}
    >
      {children}
    </div>
  );
};


const CardContent: React.FC<CardContentProps> = ({ children, className = '', ...props }) => {
  return (
    <div className={`pt-0  ${className}`} {...props}>
      {children}
    </div>
  );
};

export { Card, CardContent };

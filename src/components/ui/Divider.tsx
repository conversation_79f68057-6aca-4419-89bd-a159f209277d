'use client';

import React from 'react';
import { Divider as PrimeDivider } from 'primereact/divider';

interface DividerProps {
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  align?: 'left' | 'center' | 'right' | 'top' | 'bottom';
  type?: 'solid' | 'dashed' | 'dotted';
  children?: React.ReactNode;
}

export const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  ({ 
    className = '', 
    orientation = 'horizontal',
    align = 'center',
    type = 'solid',
    children 
  }, ref) => {
    const getTypeClasses = () => {
      switch (type) {
        case 'dashed':
          return 'border-dashed';
        case 'dotted':
          return 'border-dotted';
        default:
          return 'border-solid';
      }
    };

    const getOrientationClasses = () => {
      if (orientation === 'vertical') {
        return 'border-l border-gray-200 dark:border-gray-700';
      }
      return 'border-t border-gray-200 dark:border-gray-700';
    };

    const getAlignClasses = () => {
      if (orientation === 'horizontal') {
        switch (align) {
          case 'left':
            return 'text-left';
          case 'right':
            return 'text-right';
          default:
            return 'text-center';
        }
      } else {
        switch (align) {
          case 'top':
            return 'items-start';
          case 'bottom':
            return 'items-end';
          default:
            return 'items-center';
        }
      }
    };

    return (
      <PrimeDivider
        layout={orientation}
        align={align}
        pt={{
          root: {
            className: `my-4 ${getTypeClasses()} ${getOrientationClasses()} ${getAlignClasses()} ${className}`
          },
          content: {
            className: 'px-3 text-sm text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-900'
          }
        }}
      >
        {children}
      </PrimeDivider>
    );
  }
);

Divider.displayName = 'Divider';

'use client';

import React from 'react';
import { ConfirmDialog as PrimeConfirmDialog, confirmDialog } from 'primereact/confirmdialog';

// Export the confirmDialog function from PrimeReact directly
export { confirmDialog };

/**
 * A styled wrapper around PrimeReact's ConfirmDialog component
 * Use the imported confirmDialog function to show confirmation dialogs
 *
 * Example usage:
 *
 * // Import what you need
 * import { ConfirmDialog, confirmDialog } from '@/components/ui/ConfirmDialog';
 *
 * // Add the component once in your page/layout
 * <ConfirmDialog />
 *
 * // Then use the service anywhere in your component
 * confirmDialog({
 *   message: 'Are you sure you want to proceed?',
 *   header: 'Confirmation',
 *   icon: 'pi pi-exclamation-triangle',
 *   accept: () => { // action on accept },
 *   reject: () => { // action on reject }
 * });
 */
export function ConfirmDialog(props: React.ComponentProps<typeof PrimeConfirmDialog>) {
  return (
    <PrimeConfirmDialog
      {...props}
      className={`p-confirm-dialog-customized ${props.className || ''}`}
      pt={{
        // Override PrimeReact styling with Tailwind classes
        root: {
          className: 'rounded-lg shadow-2xl bg-white dark:bg-[#1e1e28] border border-[#E0D7FF]/50 dark:border-[#2c2d3d] max-w-md w-full md:w-[30rem]'
        },
        content: {
          className: 'bg-white dark:bg-[#1e1e28] p-6 rounded-lg'
        },
        icon: {
          className: 'text-[#6964D3] mr-3'
        },
        message: {
          className: 'text-[#5E5E5E] dark:text-[#C6C6C6]'
        },
        header: {
          className: 'font-semibold text-gray-900 dark:text-white border-b border-[#E0D7FF]/50 dark:border-[#2c2d3d] bg-white dark:bg-[#1e1e28] rounded-t-lg p-4'
        },
        footer: {
          className: 'flex justify-end gap-3 pt-4 mt-4 border-t border-[#E0D7FF]/50 dark:border-[#2c2d3d]'
        },
        acceptButton: {
          root: {
            className: 'bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:opacity-90 text-white rounded-lg px-4 py-2 transition-colors'
          }
        },
        rejectButton: {
          root: {
            className: 'bg-[#F3F3F3] dark:bg-[#2a2a38] text-[#5E5E5E] dark:text-[#C6C6C6] hover:bg-[#E0D7FF] dark:hover:bg-[#3a3a48] rounded-lg px-4 py-2 transition-colors'
          }
        },
        ...(props.pt || {})
      }}
    />
  );
}

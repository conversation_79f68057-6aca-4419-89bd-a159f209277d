'use client';
import React from 'react';
import {Dropdown} from 'primereact/dropdown';
import './select-styles.css'; // Import a CSS file for direct styling

interface SelectProps {
  options: { label: string; value: string | number | boolean }[];
  value: string | number | null | boolean;
  onChange: (value: string | number | null) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  required?: boolean;
  name?: string;
  id?: string;
  pt?: any; // Support for PrimeReact PassThrough props
  filter?: boolean;
}

// Define context type for item rendering
interface ItemContext {
  selected: boolean;
  focused: boolean;
  disabled: boolean;
  filter?: boolean;

  [key: string]: any;
}

export const Select = React.forwardRef<HTMLSelectElement, SelectProps>(
  ({options, value, onChange, placeholder, disabled, error, required, name, id, pt, filter}, ref) => {
    const defaultPt = {
      // Root and input styling
      root: {
        className: `w-full h-10 rounded-md border ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} bg-transparent py-2 text-sm dark:text-gray-50`
      },
      input: {
        className: 'px-3 py-2 text-gray-900 dark:text-white'
      },
      trigger: {
        className: 'text-gray-500 dark:text-gray-400'
      },
      label: {
        style: {paddingTop: '0.27rem'}
      },
      labelContainer: {
        style: {paddingTop: '0.27rem'}
      },

      // Dropdown panel styling
      panel: {
        className: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-md mt-1'
      },
      wrapper: {
        className: 'max-h-[200px] overflow-auto'
      },
      list: {
        className: 'py-1'
      },

      // Item styling
      itemGroup: {
        className: 'font-medium text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-900/50 px-4 py-2'
      },
      item: ({context}: { context: ItemContext }) => ({
        className: `px-4 py-2 cursor-pointer transition-colors text-sm ${
          context.selected
            ? 'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300'
            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700/50'
        }`
      }),

      // Search and empty state styling
      filterInputWrapper: {
        className: 'p-2 border-b border-gray-200 dark:border-gray-700'
      },
      filterInput: {
        className: 'w-full rounded-md border border-gray-300 dark:border-gray-700 bg-transparent py-1 px-2 text-sm dark:text-gray-50'
      },
      emptyMessage: {
        className: 'p-4 text-gray-500 dark:text-gray-400 text-center text-sm'
      },

      // Loading styling
      loadingIcon: {
        className: 'text-indigo-500'
      },
      loadingOverlay: {
        className: 'bg-white/70 dark:bg-gray-800/70 absolute inset-0 flex items-center justify-center'
      }
    };

    // Merge custom PT with defaults
    const mergedPt = {...defaultPt, ...pt};

    return (
      <div className="w-full">
        <Dropdown
          id={id}
          filter={filter}
          name={name}
          options={options}
          value={value}
          onChange={(e) => onChange(e.value)}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={error ? 'p-invalid' : ''}
          pt={mergedPt}
        />
        {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}
      </div>
    );
  }
);

Select.displayName = 'Select';

/* Remove autofill background color and text color in Chrome, Safari, and Edge */
input:-webkit-autofill,
input:-webkit-autofill:hover, 
input:-webkit-autofill:focus,
input:-webkit-autofill:active,
.p-inputtext:-webkit-autofill,
.p-inputtext:-webkit-autofill:hover,
.p-inputtext:-webkit-autofill:focus,
.p-inputtext:-webkit-autofill:active,
.p-password-input:-webkit-autofill,
.p-password-input:-webkit-autofill:hover,
.p-password-input:-webkit-autofill:focus,
.p-password-input:-webkit-autofill:active {
    -webkit-text-fill-color: #fff !important;
    color: #f3f4f6 !important;
    transition: background-color 5000s ease-in-out 0s;
    background-color: transparent !important;
}

/* Firefox specific autofill removal */
@-moz-document url-prefix() {
    input:-moz-autofill,
    input:-moz-autofill:focus {
        transition: background-color 5000s ease-in-out 0s;
        background-color: transparent !important;
    }
}

/* Maintain dark mode text color with autofill */
.dark input:-webkit-autofill,
.dark input:-webkit-autofill:hover,
.dark input:-webkit-autofill:focus,
.dark input:-webkit-autofill:active,
.dark .p-inputtext:-webkit-autofill,
.dark .p-inputtext:-webkit-autofill:hover,
.dark .p-inputtext:-webkit-autofill:focus,
.dark .p-inputtext:-webkit-autofill:active,
.dark .p-password-input:-webkit-autofill,
.dark .p-password-input:-webkit-autofill:hover,
.dark .p-password-input:-webkit-autofill:focus,
.dark .p-password-input:-webkit-autofill:active {
    -webkit-text-fill-color: #f3f4f6 !important;
} 

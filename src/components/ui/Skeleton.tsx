'use client';

import React from 'react';
import { Skeleton as PrimeSkeleton } from 'primereact/skeleton';

interface SkeletonProps {
  variant?: 'text' | 'rectangular' | 'rounded' | 'circular';
  width?: string;
  height?: string;
  className?: string;
  animation?: 'wave' | 'pulse' | 'none';
  borderRadius?: string;
}

export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'rectangular',
  width,
  height,
  className = '',
  animation = 'pulse',
  borderRadius
}) => {
  // Determine classes based on variant
  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded';
      case 'rectangular':
        return 'rounded-md';
      case 'rounded':
        return 'rounded-lg';
      case 'circular':
        return 'rounded-full';
      default:
        return 'rounded-md';
    }
  };

  // Determine animation classes
  const getAnimationClasses = () => {
    switch (animation) {
      case 'wave':
        return 'animate-pulse'; // We'll use pulse for now as wave is custom
      case 'pulse':
        return 'animate-pulse';
      case 'none':
        return '';
      default:
        return 'animate-pulse';
    }
  };

  const variantClasses = getVariantClasses();
  const animationClasses = getAnimationClasses();
  
  const customStyles = {
    width: width,
    height: height,
    borderRadius: borderRadius
  };

  return (
    <PrimeSkeleton
      pt={{
        root: { 
          className: `bg-gray-200 dark:bg-gray-700 ${variantClasses} ${animationClasses} ${className}` 
        }
      }}
      style={customStyles}
    />
  );
};

// Commonly used skeleton components
export const TextSkeleton: React.FC<Omit<SkeletonProps, 'variant'>> = (props) => (
  <Skeleton variant="text" height="1rem" {...props} />
);

export const CircleSkeleton: React.FC<Omit<SkeletonProps, 'variant'>> = (props) => (
  <Skeleton variant="circular" {...props} />
);

export const CardSkeleton: React.FC<Omit<SkeletonProps, 'variant'>> = (props) => (
  <Skeleton variant="rounded" height="12rem" {...props} />
);

// Skeleton layout placeholders
export const TableRowSkeleton: React.FC<{ columns?: number }> = ({ columns = 4 }) => (
  <div className="flex space-x-4 p-4 items-center">
    <CircleSkeleton width="40px" height="40px" />
    <div className="flex-1 space-y-2">
      {Array(columns).fill(0).map((_, i) => (
        <TextSkeleton key={i} className={i === columns - 1 ? 'w-3/4' : ''} />
      ))}
    </div>
  </div>
);

export const FormFieldSkeleton: React.FC = () => (
  <div className="space-y-2">
    <TextSkeleton width="30%" />
    <Skeleton variant="rectangular" height="2.5rem" />
  </div>
);

export const AvatarWithTextSkeleton: React.FC = () => (
  <div className="flex items-center space-x-3">
    <CircleSkeleton width="40px" height="40px" />
    <div className="space-y-1">
      <TextSkeleton width="120px" />
      <TextSkeleton width="80px" />
    </div>
  </div>
); 
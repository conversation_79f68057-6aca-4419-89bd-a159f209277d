'use client';

import React from 'react';
import { Column as PrimeColumn } from 'primereact/column';
import { classNames } from 'primereact/utils';

export interface CustomColumnProps {
  field: string;
  header: string;
  sortable?: boolean;
  body?: (data: any, options: any) => React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  headerClassName?: string;
  headerStyle?: React.CSSProperties;
  [key: string]: any;
}

export const CustomColumn: React.FC<CustomColumnProps> = ({ 
  field, 
  header, 
  sortable = false, 
  body, 
  className, 
  style,
  headerClassName,
  headerStyle,
  ...props 
}) => {
  const customHeaderStyle = {
    ...headerStyle,
    backgroundColor: 'var(--bg-color)'
  };
  
  return (
    <PrimeColumn 
      field={field} 
      header={header} 
      sortable={sortable} 
      body={body}
      className={className}
      style={style}
      headerClassName={headerClassName}
      headerStyle={customHeaderStyle}
      pt={{
        root: { className: classNames('bg-transparent', className) },
        headerCell: { 
          className: classNames('text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider px-6 py-3', headerClassName),
          style: customHeaderStyle
        },
        bodyCell: { 
          className: classNames('px-6 py-4 whitespace-nowrap text-sm', className),
        }
      }}
      {...props} 
    />
  );
}; 
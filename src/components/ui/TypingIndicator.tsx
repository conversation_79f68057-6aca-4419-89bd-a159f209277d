import React from 'react';

export interface TypingUser {
  userId: string;
  userType: 'agent' | 'customer';
  userName: string;
  timestamp: Date;
}

export interface TypingIndicatorProps {
  typingUsers: TypingUser[];
  className?: string;
  showAnimation?: boolean;
  maxDisplayUsers?: number;
}

/**
 * Typing indicator component for live chat
 * Shows animated dots and user names for active typers
 */
export function TypingIndicator({
  typingUsers,
  className,
  showAnimation = true,
  maxDisplayUsers = 3
}: TypingIndicatorProps) {
  if (typingUsers.length === 0) {
    return null;
  }

  // Limit displayed users and sort by timestamp (most recent first)
  const displayUsers = typingUsers
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, maxDisplayUsers);

  const remainingCount = typingUsers.length - displayUsers.length;

  // Generate typing message
  const getTypingMessage = () => {
    if (displayUsers.length === 1) {
      return `${displayUsers[0].userName} is typing`;
    } else if (displayUsers.length === 2) {
      return `${displayUsers[0].userName} and ${displayUsers[1].userName} are typing`;
    } else if (displayUsers.length === 3 && remainingCount === 0) {
      return `${displayUsers[0].userName}, ${displayUsers[1].userName}, and ${displayUsers[2].userName} are typing`;
    } else {
      const names = displayUsers.slice(0, 2).map(user => user.userName).join(', ');
      const additionalCount = remainingCount + (displayUsers.length - 2);
      return `${names} and ${additionalCount} other${additionalCount > 1 ? 's' : ''} are typing`;
    }
  };

  return (
    <div
      className={`flex items-center gap-2 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg border border-gray-200/70 dark:border-gray-700/70 shadow-md animate-in fade-in-0 slide-in-from-bottom-2 duration-200 ${className || ''}`}
    >
      <div className="flex items-center gap-1">
        <span className="text-xs font-medium">
          {getTypingMessage()}
        </span>
        {showAnimation && <TypingDots />}
      </div>
    </div>
  );
}

/**
 * Animated typing dots component
 */
function TypingDots() {
  return (
    <div className="flex items-center gap-1 ml-1">
      <div className="flex space-x-1">
        <div className="w-1 h-1 bg-current rounded-full animate-bounce [animation-delay:-0.3s]" />
        <div className="w-1 h-1 bg-current rounded-full animate-bounce [animation-delay:-0.15s]" />
        <div className="w-1 h-1 bg-current rounded-full animate-bounce" />
      </div>
    </div>
  );
}

/**
 * Compact typing indicator for use in message lists
 */
export function CompactTypingIndicator({
  typingUsers,
  className
}: {
  typingUsers: TypingUser[];
  className?: string;
}) {
  if (typingUsers.length === 0) {
    return null;
  }

  const agentTypers = typingUsers.filter(user => user.userType === 'agent');
  const customerTypers = typingUsers.filter(user => user.userType === 'customer');

  return (
    <div
      className={`flex items-center gap-2 px-2 py-1 text-xs text-gray-600 dark:text-gray-400 animate-in fade-in-0 slide-in-from-bottom-1 duration-150 ${className || ''}`}
    >
      {agentTypers.length > 0 && (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full" />
          <span>Agent typing</span>
          <TypingDots />
        </div>
      )}
      
      {customerTypers.length > 0 && (
        <div className="flex items-center gap-1">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          <span>Customer typing</span>
          <TypingDots />
        </div>
      )}
    </div>
  );
}

/**
 * Typing indicator for specific user types
 */
export function UserTypeTypingIndicator({
  userType,
  isTyping,
  userName,
  className
}: {
  userType: 'agent' | 'customer';
  isTyping: boolean;
  userName?: string;
  className?: string;
}) {
  if (!isTyping) {
    return null;
  }

  const displayName = userName || (userType === 'agent' ? 'Agent' : 'Customer');
  const colorClass = userType === 'agent' ? 'text-blue-600' : 'text-green-600';

  return (
    <div
      className={`flex items-center gap-2 px-2 py-1 text-xs animate-in fade-in-0 slide-in-from-bottom-1 duration-150 ${colorClass} ${className || ''}`}
    >
      <span className="font-medium">{displayName} is typing</span>
      <TypingDots />
    </div>
  );
}

/**
 * Typing indicator with avatar for richer UI
 */
export function AvatarTypingIndicator({
  typingUsers,
  className
}: {
  typingUsers: TypingUser[];
  className?: string;
}) {
  if (typingUsers.length === 0) {
    return null;
  }

  return (
    <div
      className={`flex items-center gap-2 px-3 py-2 animate-in fade-in-0 slide-in-from-bottom-2 duration-200 ${className || ''}`}
    >
      <div className="flex -space-x-2">
        {typingUsers.slice(0, 3).map((user, index) => (
          <div
            key={user.userId}
            className={`w-6 h-6 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-medium ${user.userType === 'agent' ? 'bg-blue-500 text-white' : 'bg-green-500 text-white'}`}
            style={{ zIndex: typingUsers.length - index }}
          >
            {user.userName.charAt(0).toUpperCase()}
          </div>
        ))}
      </div>
      
      <div className="flex items-center gap-1 text-sm text-muted-foreground">
        <span>
          {typingUsers.length === 1 
            ? `${typingUsers[0].userName} is typing`
            : `${typingUsers.length} people are typing`
          }
        </span>
        <TypingDots />
      </div>
    </div>
  );
}

export default TypingIndicator;

'use client';

import React from 'react';
import { InputNumber } from 'primereact/inputnumber';
import { formatCurrency } from '@/utils/format-utils';
import { Currency } from '@/constants/pricing';

export interface UserCountSelectorProps {
  value: number;
  onChange: (value: number) => void;
  pricePerUser: number;
  currency: Currency;
  minUsers?: number;
  hasActiveSubscription?: boolean;
  disabled?: boolean;
}

export const UserCountSelector: React.FC<UserCountSelectorProps> = ({
  value,
  onChange,
  pricePerUser,
  currency,
  minUsers = 1,
  hasActiveSubscription = false,
  disabled = false
}) => {
  const totalPrice = value * pricePerUser;

  return (
    <div className="flex flex-col gap-3">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Number of Users
        </h3>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {formatCurrency(pricePerUser, currency)} per user
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center">
          <InputNumber
            value={value}
            onValueChange={(e) => onChange(e.value ?? minUsers)}
            min={minUsers}
            disabled={disabled || hasActiveSubscription}
            showButtons
            buttonLayout="horizontal"
            className="w-full"
            pt={{
              // Override PrimeReact styling with Tailwind classes
              root: { className: 'w-full border-0' },
              input: {
                root: {
                  className: 'text-center bg-transparent w-full h-12 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-[#6964D3] focus:border-[#6964D3] dark:focus:ring-[#8178E8] dark:focus:border-[#8178E8]'
                }
              },
              buttonGroup: { className: 'flex h-12' },
              incrementButton: {
                root: {
                  className: 'flex items-center justify-center h-12 p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-r-md transition-colors'
                },
                icon: { className: 'pi pi-plus text-sm' }
              },
              decrementButton: {
                root: {
                  className: 'flex items-center justify-center h-12 p-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-l-md transition-colors'
                },
                icon: { className: 'pi pi-minus text-sm' }
              }
            }}
          />
        </div>

        <div className="flex flex-col justify-center p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total for {value} {value === 1 ? 'user' : 'users'}
          </div>
          <div className="text-xl font-bold text-gray-900 dark:text-white">
            {formatCurrency(totalPrice, currency)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Billed {currency === Currency.USD ? 'monthly' : 'monthly'}
          </div>
        </div>
      </div>
      {hasActiveSubscription
        ? <div className="mt-2 text-sm text-red-500 dark:text-red-300">
          Cannot change the number of users for an active subscription here
        </div>
        : <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          Select the number of users you want to subscribe for. You can add more users later.
        </div>
      }
    </div>
  );
};

UserCountSelector.displayName = 'UserCountSelector';

export default UserCountSelector;

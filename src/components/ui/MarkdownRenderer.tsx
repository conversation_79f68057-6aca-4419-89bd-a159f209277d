'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

/**
 * Props for the MarkdownRenderer component
 */
interface MarkdownRendererProps {
  children: string;
  className?: string;
}

/**
 * Standardized Markdown renderer with consistent styling across the application
 * 
 * Features:
 * - GitHub Flavored Markdown support
 * - Consistent typography and spacing
 * - Dark mode support
 * - Security-focused link handling
 * - Professional code block styling
 * - Proper whitespace handling
 */
export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ 
  children, 
  className = "prose prose-sm max-w-none dark:prose-invert" 
}) => {
  return (
    <div className={className}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Paragraph with proper whitespace handling
          p: ({ children }) => (
            <p className="mb-2 last:mb-0 whitespace-pre-wrap">{children}</p>
          ),
          
          // Text formatting
          strong: ({ children }) => (
            <strong className="font-semibold">{children}</strong>
          ),
          em: ({ children }) => (
            <em className="italic">{children}</em>
          ),
          
          // Lists with consistent spacing
          ul: ({ children }) => (
            <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>
          ),
          li: ({ children }) => (
            <li className="text-sm">{children}</li>
          ),
          
          // Links with security attributes
          a: ({ href, children }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              {children}
            </a>
          ),
          
          // Code blocks with professional styling
          code: ({ children }) => (
            <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono">
              {children}
            </code>
          ),
          pre: ({ children }) => (
            <pre className="bg-gray-100 dark:bg-gray-700 p-2 rounded text-sm font-mono overflow-x-auto mb-2">
              {children}
            </pre>
          ),
          
          // Blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic mb-2">
              {children}
            </blockquote>
          ),
          
          // Headings with consistent hierarchy
          h1: ({ children }) => (
            <h1 className="text-lg font-bold mb-2">{children}</h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-base font-bold mb-2">{children}</h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-sm font-bold mb-1">{children}</h3>
          ),
          
          // Horizontal rules
          hr: () => (
            <hr className="border-gray-300 dark:border-gray-600 my-2" />
          )
        }}
      >
        {children}
      </ReactMarkdown>
    </div>
  );
};

/**
 * Lightweight Markdown renderer for inline content without wrapper div
 */
export const InlineMarkdownRenderer: React.FC<{ children: string }> = ({ children }) => {
  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      components={{
        // Inline paragraph without margin
        p: ({ children }) => (
          <span className="whitespace-pre-wrap">{children}</span>
        ),
        
        // Text formatting
        strong: ({ children }) => (
          <strong className="font-semibold">{children}</strong>
        ),
        em: ({ children }) => (
          <em className="italic">{children}</em>
        ),
        
        // Inline code
        code: ({ children }) => (
          <code className="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono">
            {children}
          </code>
        ),
        
        // Links with security attributes
        a: ({ href, children }) => (
          <a
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            {children}
          </a>
        )
      }}
    >
      {children}
    </ReactMarkdown>
  );
};

'use client';

import React from 'react';
import { Password as PrimePassword } from 'primereact/password';
import './input-autofill.css';

interface PasswordProps extends Omit<React.ComponentProps<typeof PrimePassword>, 'ref'> {
  className?: string;
  error?: string;
}

export const Password = React.forwardRef<HTMLInputElement, PasswordProps>(
  ({ className = '', error, feedback = false, ...props }, ref) => {
    return (
      <div className="w-full">
        <PrimePassword
          ref={ref as any}
          feedback={feedback}
          className={error ? 'p-invalid' : ''}
          toggleMask
          {...props}
          pt={{
            root: { 
              className: 'w-full'
            },
            input: { 
              className: `w-full rounded-md border ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-gray-700'} bg-transparent px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:cursor-not-allowed disabled:opacity-50 dark:text-gray-50 dark:focus:ring-indigo-500 ${className}`
            },
            panel: {
              className: 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-md'
            },
            meter: { 
              className: 'mt-2 bg-gray-200 dark:bg-gray-700 rounded-full h-2'
            },
            meterLabel: ({ props }: any) => ({
              className: `h-full rounded-full ${
                props.strength === 'weak' ? 'bg-red-500' : 
                props.strength === 'medium' ? 'bg-yellow-500' : 
                'bg-green-500'
              }`
            }),
            showIcon: {
              className: 'text-gray-500 dark:text-gray-400 cursor-pointer absolute right-3 top-1/2 transform -translate-y-1/2'
            },
            hideIcon: {
              className: 'text-gray-500 dark:text-gray-400 cursor-pointer absolute right-3 top-1/2 transform -translate-y-1/2'
            },
            info: {
              className: 'text-xs text-gray-500 dark:text-gray-400 mt-1'
            }
          }}
        />
        {error && <p className="mt-1 text-sm text-red-500 dark:text-red-400">{error}</p>}
      </div>
    );
  }
);

Password.displayName = 'Password'; 
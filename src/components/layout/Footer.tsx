import Link from "next/link";
import Image from "next/image";

export default function Footer() {
  return (
    <footer className="bg-[#0f0f18] text-white py-16">
      <div className="max-w-7xl mx-auto px-6">
        <div className="grid md:grid-cols-5 gap-12">
          <div className="md:col-span-2">
            <div className="flex items-center mb-6">
              <Image 
                src="/assets/logos/white-cloud-black-bg.svg" 
                alt="New Instance Logo" 
                width={40} 
                height={40}
                className="mr-3"
              />
              <span className="text-xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
            </div>
            <p className="text-[#C6C6C6] mb-6 max-w-sm">
              A multi-service platform providing integrated business solutions for the modern digital enterprise.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 flex items-center justify-center rounded-full bg-[#1e1e28] hover:bg-[#6964D3] transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                  <rect width="4" height="12" x="2" y="9"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </a>
              <a href="#" className="w-10 h-10 flex items-center justify-center rounded-full bg-[#1e1e28] hover:bg-[#6964D3] transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                </svg>
              </a>
              <a href="#" className="w-10 h-10 flex items-center justify-center rounded-full bg-[#1e1e28] hover:bg-[#6964D3] transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                </svg>
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4 font-[family-name:var(--font-jakarta)]">Services</h4>
            <ul className="space-y-3">
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Customer Support</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Error Logging</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Admin Dashboard</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Analytics</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4 font-[family-name:var(--font-jakarta)]">Company</h4>
            <ul className="space-y-3">
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">About Us</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Careers</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Blog</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-lg font-semibold mb-4 font-[family-name:var(--font-jakarta)]">Legal</h4>
            <ul className="space-y-3">
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Terms of Service</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">Cookie Policy</Link></li>
              <li><Link href="#" className="text-[#C6C6C6] hover:text-white transition-colors">GDPR</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-[#1e1e28] mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-[#868686] text-sm">
            &copy; {new Date().getFullYear()} New Instance. All rights reserved.
          </p>
          <div className="flex gap-6 mt-4 md:mt-0">
            <Link href="#" className="text-[#868686] hover:text-white text-sm transition-colors">
              Status
            </Link>
            <Link href="#" className="text-[#868686] hover:text-white text-sm transition-colors">
              Sitemap
            </Link>
            <Link href="#" className="text-[#868686] hover:text-white text-sm transition-colors">
              Accessibility
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
} 
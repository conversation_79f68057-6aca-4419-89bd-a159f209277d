'use client';

import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getUserData } from "@/server/actions/user-actions";
import { logger } from '@/utils/logger';

interface User {
  id: string;
  name: string;
  email: string;
}

export default function Navbar() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await getUserData();
        if (response.success && response.data) {
          setUser(response.data);
        }
      } catch (error) {
        logger.error("Authentication check failed:", error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  return (
    <>
      <nav className="sticky top-0 z-50 bg-[#2d2d39] py-3 px-6 shadow-md">
        <div className="max-w-7xl mx-auto flex justify-between items-center">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image 
                src="/assets/logos/purple-cloud-yellow-dots.svg" 
                alt="New Instance Logo" 
                width={30} 
                height={30}
                className="mr-3"
              />
              <span className="text-lg font-bold text-white">New Instance</span>
            </Link>
          </div>
          
          {!loading && (
            <>
              {user ? (
                /* Authenticated navigation - Desktop */
                <div className="hidden md:flex items-center">
                  <div className="flex items-center space-x-8">
                    <Link 
                      href="/dashboard" 
                      className="text-white hover:text-[#8178E8] transition-colors"
                    >
                      Dashboard
                    </Link>
                    <div className="flex items-center ml-8">
                      <span className="text-white mr-3">{user.name}</span>
                      <div 
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push('/dashboard/profile');
                        }}
                        className="h-8 w-8 rounded-full bg-white flex items-center justify-center cursor-pointer overflow-hidden"
                      >
                        {/* If user has an avatar, show it, otherwise show first letter of name */}
                        {user.name ? (
                          <span className="text-[#2d2d39] font-medium">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        ) : (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-[#2d2d39]" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                /* Unauthenticated navigation - Desktop */
                <div className="hidden md:flex items-center space-x-8">
                  <Link href="#features" className="text-white hover:text-[#8178E8] transition-colors">Features</Link>
                  <Link href="#services" className="text-white hover:text-[#8178E8] transition-colors">Services</Link>
                  <Link href="#pricing" className="text-white hover:text-[#8178E8] transition-colors">Pricing</Link>
                  <Link href="/auth" className="text-white hover:text-[#8178E8] transition-colors">Login</Link>
                  <Link 
                    href="/auth?mode=signup" 
                    className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all"
                  >
                    Get Started
                  </Link>
                </div>
              )}

              {/* Mobile Menu Button */}
              <div className="md:hidden">
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleMobileMenu();
                  }}
                  className="text-white p-2"
                >
                  {mobileMenuOpen ? (
                    // X icon when menu is open
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  ) : (
                    // Hamburger icon when menu is closed
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16m-7 6h7" />
                    </svg>
                  )}
                </button>
              </div>
            </>
          )}
        </div>
      </nav>

      {/* Mobile Menu Drawer */}
      <div 
        className={`fixed inset-0 z-40 md:hidden transition-opacity duration-300 ease-in-out ${
          mobileMenuOpen ? "opacity-100 pointer-events-auto" : "opacity-0 pointer-events-none"
        }`}
      >
        {/* Dark overlay with animation */}
        <div 
          className={`fixed inset-0 bg-black transition-opacity duration-300 ease-in-out ${
            mobileMenuOpen ? "opacity-50" : "opacity-0"
          }`} 
          onClick={() => setMobileMenuOpen(false)}
        ></div>
        
        <div 
          className={`fixed inset-y-0 right-0 max-w-[70%] w-full bg-[#2d2d39] shadow-xl transform transition-transform duration-300 ease-in-out z-50 ${
            mobileMenuOpen ? "translate-x-0" : "translate-x-full"
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="flex flex-col h-full p-6">
            {user ? (
              /* Authenticated mobile menu */
              <>
                <div className="flex items-center justify-between mb-8">
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-white flex items-center justify-center mr-3">
                      <span className="text-[#2d2d39] font-medium">
                        {user.name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <span className="text-white text-sm font-medium">{user.name}</span>
                  </div>
                </div>

                <div className="flex flex-col space-y-6">
                  <Link 
                    href="/dashboard" 
                    className="text-white hover:text-[#8178E8] transition-colors text-lg relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Dashboard
                  </Link>
                  <Link 
                    href="/dashboard/profile" 
                    className="text-white hover:text-[#8178E8] transition-colors text-lg relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Profile
                  </Link>
                </div>
              </>
            ) : (
              /* Unauthenticated mobile menu */
              <>
                <div className="flex flex-col space-y-6 mb-10">
                  <Link 
                    href="#features" 
                    className="text-white hover:text-[#8178E8] transition-colors text-lg relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Features
                  </Link>
                  <Link 
                    href="#services" 
                    className="text-white hover:text-[#8178E8] transition-colors text-lg relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Services
                  </Link>
                  <Link 
                    href="#pricing" 
                    className="text-white hover:text-[#8178E8] transition-colors text-lg relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Pricing
                  </Link>
                </div>

                <div className="flex flex-col space-y-4">
                  <Link 
                    href="/auth" 
                    className="text-white text-center border border-[#8178E8] py-2 rounded-full hover:bg-[#8178E8]/10 transition-colors relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Login
                  </Link>
                  <Link 
                    href="/auth?mode=signup" 
                    className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white py-2 rounded-full text-center shadow-lg relative z-10"
                    onClick={(e) => {
                      e.stopPropagation();
                      setMobileMenuOpen(false);
                    }}
                  >
                    Get Started
                  </Link>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </>
  );
} 
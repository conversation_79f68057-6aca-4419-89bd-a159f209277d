'use client';

import { ReactNode, useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import LogoutButton from '@/components/auth/LogoutButton';

interface DashboardHeaderProps {
  userName?: string;
  rightContent?: ReactNode;
  showProfileLink?: boolean;
}

export default function DashboardHeader({ userName, rightContent, showProfileLink = true }: DashboardHeaderProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const headerRef = useRef<HTMLDivElement>(null);

  // <PERSON>le click outside to close the mobile menu
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (headerRef.current && !headerRef.current.contains(event.target as Node) && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    }

    // Add event listener when the mobile menu is open
    if (mobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [mobileMenuOpen]);

  // Close mobile menu when navigating to a new page
  useEffect(() => {
    const handleRouteChange = () => {
      setMobileMenuOpen(false);
    };

    window.addEventListener('popstate', handleRouteChange);

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
    };
  }, []);

  // Handle keyboard accessibility - close menu with Escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [mobileMenuOpen]);

  return (
    <header ref={headerRef} className="bg-white dark:bg-[#1e1e28] shadow sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 md:px-5 lg:px-8 py-3 sm:py-4 flex items-center justify-between">
        {/* Logo - always visible */}
        <div className="flex items-center">
          <Link href="/dashboard" className="flex items-center">
            <Image
              src="/assets/logos/purple-cloud-yellow-dots.svg"
              alt="New Instance Logo"
              width={40}
              height={40}
              className="w-8 h-8 sm:w-10 sm:h-10 mr-2 sm:mr-3"
            />
            <span className="hidden xs:inline text-lg sm:text-xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
          </Link>
        </div>

        {/* Desktop Navigation - hidden on mobile */}
        <div className="hidden sm:flex items-center gap-2 md:gap-4">
          {userName && (
            <div className="flex items-center gap-2">
              <span className="hidden lg:inline text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
                Welcome, {userName}
              </span>

              {showProfileLink && (
                <Link href="/dashboard/profile">
                  <div className="h-8 w-8 rounded-full bg-[#E0D7FF] dark:bg-[#2a2a38] flex items-center justify-center cursor-pointer hover:bg-[#D0C7FF] dark:hover:bg-[#3a3a48] transition-colors">
                    <span className="text-[#6964D3] font-medium text-sm">
                      {userName?.charAt(0)?.toUpperCase() || 'U'}
                    </span>
                  </div>
                </Link>
              )}
            </div>
          )}
          <div className="flex-shrink-0">
            {rightContent}
          </div>
          <LogoutButton variant="icon" className="ml-2" />
        </div>

        {/* Mobile Menu Button - only visible on mobile */}
        <div className="sm:hidden flex items-center">
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-[#2a2a38] focus:outline-none focus:ring-2 focus:ring-[#6964D3]"
            aria-label="Toggle menu"
            aria-expanded={mobileMenuOpen}
            aria-controls="mobile-menu"
          >
            {mobileMenuOpen ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu - only visible when open */}
      <div
        id="mobile-menu"
        className={`sm:hidden bg-white dark:bg-[#1e1e28] border-t border-gray-200 dark:border-gray-800 shadow-lg overflow-hidden transition-all duration-300 ease-in-out ${
          mobileMenuOpen ? 'max-h-[calc(100vh-60px)] py-3 opacity-100' : 'max-h-0 py-0 opacity-0 pointer-events-none'
        }`}
        aria-hidden={!mobileMenuOpen}>
        <div className="px-4 pt-1 pb-3">
          <div className="flex flex-col space-y-4">
            {userName && (
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
                    Welcome, {userName}
                  </span>
                </div>
                {showProfileLink && (
                  <Link href="/dashboard/profile" className="flex items-center gap-2 text-[#6964D3] hover:text-[#8178E8] transition-colors">
                    <div className="h-8 w-8 rounded-full bg-[#E0D7FF] dark:bg-[#2a2a38] flex items-center justify-center">
                      <span className="text-[#6964D3] font-medium text-sm">
                        {userName?.charAt(0)?.toUpperCase() || 'U'}
                      </span>
                    </div>
                    <span className="text-sm">Profile</span>
                  </Link>
                )}
              </div>
            )}

            {/* Mobile version of rightContent */}
            {rightContent && (
              <div className="py-2 border-t border-gray-200 dark:border-gray-800">
                <div className="pt-2">
                  {rightContent}
                </div>
              </div>
            )}

            {/* Logout button for mobile */}
            <div className="py-2 border-t border-gray-200 dark:border-gray-800">
              <LogoutButton variant="default" className="w-full mt-2" />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}

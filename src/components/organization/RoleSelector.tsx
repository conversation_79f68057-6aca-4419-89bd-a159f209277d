'use client';

import { useState, useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { Skeleton } from 'primereact/skeleton';
import { getRoles } from '@/utils/role-utils';
import { connectToDatabase } from '@/utils/db';
import UserPermissionGroup from '@/models/UserPermissionGroup';
import { logger } from '@/utils/logger';

type Option = {
  label: string;
  value: string;
  description?: string;
  type: 'role' | 'permissionGroup';
};

type RoleSelectorProps = {
  value: string;
  onChange: (value: string, type: 'role' | 'permissionGroup') => void;
  showPermissionGroups?: boolean;
  className?: string;
  label?: string;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
};

export default function RoleSelector({
  value,
  onChange,
  showPermissionGroups = false,
  className = '',
  label = 'Role',
  placeholder = 'Select a role',
  disabled = false,
  error
}: RoleSelectorProps) {
  const [options, setOptions] = useState<Option[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOption, setSelectedOption] = useState<Option | null>(null);

  useEffect(() => {
    const loadOptions = async () => {
      setLoading(true);
      try {
        // Get standard roles
        const roles = getRoles().map(role => ({
          label: role.name,
          value: role.id,
          type: 'role' as const
        }));

        let allOptions: Option[] = [...roles];

        // If permission groups are enabled, fetch them
        if (showPermissionGroups) {
          try {
            // This would be replaced with an API call in a real implementation
            const response = await fetch('/api/permission-groups');
            if (response.ok) {
              const permissionGroups = await response.json();
              const groupOptions = permissionGroups.map((group: any) => ({
                label: group.name,
                value: group._id,
                description: group.description,
                type: 'permissionGroup' as const
              }));
              allOptions = [...roles, ...groupOptions];
            }
          } catch (error) {
            logger.error('Failed to load permission groups:', error);
          }
        }

        setOptions(allOptions);

        // Set the selected option based on the value prop
        const selected = allOptions.find(option => option.value === value) || null;
        setSelectedOption(selected);
      } finally {
        setLoading(false);
      }
    };

    loadOptions();
  }, [value, showPermissionGroups]);

  const handleChange = (option: Option | null) => {
    if (option) {
      setSelectedOption(option);
      onChange(option.value, option.type);
    }
  };

  const optionTemplate = (option: Option) => {
    return (
      <div className="flex flex-col">
        <span className="font-medium">{option.label}</span>
        {option.description && (
          <small className="text-gray-500">{option.description}</small>
        )}
        <small className="text-xs text-gray-400 mt-1">
          {option.type === 'permissionGroup' ? 'Permission Group' : 'Role'}
        </small>
      </div>
    );
  };

  const selectedItemTemplate = (option: Option, props: any) => {
    if (option) {
      return (
        <div className="flex items-center">
          <span>{option.label}</span>
          {option.type === 'permissionGroup' && (
            <span className="ml-2 text-xs bg-purple-100 text-purple-800 rounded-full px-2 py-0.5">
              Group
            </span>
          )}
        </div>
      );
    }
    return <span>{props.placeholder}</span>;
  };

  return (
    <div className={className}>
      {label && <label htmlFor="role-selector">{label}</label>}

      {loading ? (
        <Skeleton height="2.5rem" className="mb-2" />
      ) : (
        <Dropdown
          id="role-selector"
          value={selectedOption}
          onChange={(e) => handleChange(e.value)}
          options={options}
          optionLabel="label"
          placeholder={placeholder}
          className={`w-full ${error ? 'p-invalid' : ''}`}
          itemTemplate={optionTemplate}
          valueTemplate={selectedItemTemplate}
          disabled={disabled}
          filter
        />
      )}

      {error && <small className="p-error">{error}</small>}
    </div>
  );
}

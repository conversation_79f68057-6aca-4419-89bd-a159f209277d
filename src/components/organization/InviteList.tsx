'use client';

import { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from '@/components/ui/Button';
import { format } from 'date-fns';
import { Card } from '@/components/ui/Card';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { createInvite, cancelInvite } from '@/server/actions/invite-actions';
import { useToast } from '@/components/ui/Toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import RoleSelector from './RoleSelector';

// Define invite type
type Invite = {
  _id: string;
  email: string;
  role: string;
  status: 'pending' | 'accepted' | 'expired';
  expiresAt: string;
  createdAt: string;
  invitedBy: {
    _id: string;
    name: string;
    email: string;
  };
};

// Form schema for creating invites
const inviteFormSchema = z.object({
  email: z.string().email('Please enter a valid email'),
  role: z.string().min(1, 'Please select a role'),
  permissionGroupId: z.string().optional(),
});

type InviteFormData = z.infer<typeof inviteFormSchema>;

type InviteListProps = {
  organizationId: string;
  invites: Invite[];
  roles: { label: string; value: string }[];
  onInviteCreated?: () => void;
  onInviteCancelled?: () => void;
};

export default function InviteList({
  organizationId,
  invites,
  roles,
  onInviteCreated,
  onInviteCancelled,
}: InviteListProps) {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const { success, error: showError, info } = useToast();
  const [selectedRoleType, setSelectedRoleType] = useState<'role' | 'permissionGroup'>('role');

  const {
    control,
    handleSubmit,
    reset,
    setValue,
     formState: { errors },
  } = useForm<InviteFormData>({
    resolver: zodResolver(inviteFormSchema),
    defaultValues: {
      email: '',
      role: '',
      permissionGroupId: undefined,
    },
  });

  const handleCreateInvite = async (data: InviteFormData) => {
    setIsProcessing(true);
    try {
      // Prepare data based on role type
      const inviteData = {
        email: data.email,
        organizationId,
        role: selectedRoleType === 'role' ? data.role : 'member', // Default role for permission groups
        permissionGroupId: selectedRoleType === 'permissionGroup' ? data.role : undefined
      };

      const response = await createInvite(inviteData);

      if (response.success) {
        success('Success', 'Invitation sent successfully');
        setIsCreateModalOpen(false);
        reset();
        if (onInviteCreated) {
          onInviteCreated();
        }
      } else {
        showError('Error', response.message || 'Failed to send invitation');
      }
    } catch (error) {
      showError('Error', 'An unexpected error occurred');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelInvite = async (inviteId: string) => {
    confirmDialog({
      message: 'Are you sure you want to cancel this invitation?',
      header: 'Cancel Invitation',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      accept: async () => {
        setIsProcessing(true);
        try {
          const response = await cancelInvite(inviteId);
          if (response.success) {
            success('Success', 'Invitation cancelled successfully');
            if (onInviteCancelled) {
              onInviteCancelled();
            }
          } else {
            showError('Error', response.message || 'Failed to cancel invitation');
          }
        } catch (error) {
          showError('Error', 'An unexpected error occurred');
        } finally {
          setIsProcessing(false);
        }
      },
    });
  };

  // Handler for role/permission group selection
  const handleRoleChange = (value: string, type: 'role' | 'permissionGroup') => {
    setValue('role', value);
    setSelectedRoleType(type);

    // If switching from permission group to role, clear permissionGroupId
    if (type === 'role') {
      setValue('permissionGroupId', undefined);
    }
    // If switching to permission group, store the ID
    else {
      setValue('permissionGroupId', value);
    }
  };

  const emailBodyTemplate = (rowData: Invite) => {
    return <span className="font-medium">{rowData.email}</span>;
  };

  const roleBodyTemplate = (rowData: Invite) => {
    const roleObj = roles.find((r) => r.value === rowData.role);
    return <span>{roleObj?.label || rowData.role}</span>;
  };

  const dateBodyTemplate = (rowData: Invite) => {
    return <span>{format(new Date(rowData.createdAt), 'MMM d, yyyy')}</span>;
  };

  const expiryBodyTemplate = (rowData: Invite) => {
    return <span>{format(new Date(rowData.expiresAt), 'MMM d, yyyy')}</span>;
  };

  const invitedByBodyTemplate = (rowData: Invite) => {
    return (
      <span>
        {rowData.invitedBy
          ? `${rowData.invitedBy.name}`
          : 'Unknown'}
      </span>
    );
  };

  const actionBodyTemplate = (rowData: Invite) => {
    return (
      <Button
        icon="pi pi-times"
        severity="danger"
        variant="ghost"
        size="sm"
        onClick={() => handleCancelInvite(rowData._id)}
        disabled={isProcessing}
      />
    );
  };

  return (
    <div className="space-y-4">
      <ConfirmDialog />
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Pending Invitations</h3>
        <Button
          label="Invite User"
          icon="pi pi-plus"
          onClick={() => setIsCreateModalOpen(true)}
          disabled={isProcessing}
        />
      </div>

      <Card className="mt-4">
        <DataTable
          value={invites}
          emptyMessage="No pending invitations"
          paginator
          rows={5}
          rowsPerPageOptions={[5, 10, 25]}
          tableStyle={{ minWidth: '50rem' }}
          stripedRows
        >
          <Column field="email" header="Email" body={emailBodyTemplate} sortable />
          <Column field="role" header="Role" body={roleBodyTemplate} sortable />
          <Column field="createdAt" header="Sent Date" body={dateBodyTemplate} sortable />
          <Column field="expiresAt" header="Expires On" body={expiryBodyTemplate} sortable />
          <Column field="invitedBy" header="Invited By" body={invitedByBodyTemplate} sortable />
          <Column body={actionBodyTemplate} exportable={false} style={{ width: '8rem' }} />
        </DataTable>
      </Card>

      <Dialog
        header="Invite User"
        visible={isCreateModalOpen}
        style={{ width: '500px' }}
        onHide={() => {
          setIsCreateModalOpen(false);
          reset();
          setSelectedRoleType('role');
        }}
        draggable={false}
        resizable={false}
      >
        <form onSubmit={handleSubmit(handleCreateInvite)} className="space-y-4 mt-4">
          <div className="field">
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <InputText
                  id={field.name}
                  {...field}
                  className={`w-full ${errors.email ? 'p-invalid' : ''}`}
                  placeholder="<EMAIL>"
                />
              )}
            />
            {errors.email && <small className="p-error">{errors.email.message}</small>}
          </div>

          <div className="field">
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <RoleSelector
                  value={field.value}
                  onChange={handleRoleChange}
                  showPermissionGroups={true}
                  error={errors.role?.message}
                />
              )}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              label="Cancel"
              variant="outline"
              onClick={() => {
                setIsCreateModalOpen(false);
                reset();
                setSelectedRoleType('role');
              }}
              disabled={isProcessing}
            />
            <Button
              label="Send Invitation"
              type="submit"
              loading={isProcessing}
              disabled={isProcessing}
            />
          </div>
        </form>
      </Dialog>
    </div>
  );
}

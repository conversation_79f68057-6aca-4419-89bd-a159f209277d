import React from 'react';
import { usePaystackPayment } from 'react-paystack';
import { Button } from '@/components/ui/Button';
import { Currency } from '@/constants/pricing';
import { env } from '@/utils/env';

interface PaystackButtonProps {
  amount: number;
  email: string;
  reference?: string;
  currency?: Currency;
  metadata?: Record<string, any>;
  onSuccess: (reference: string) => void;
  onClose: () => void;
  className?: string;
  disabled?: boolean;
  text?: string;
  variant?: 'primary' | 'outline' | 'destructive';
  fullWidth?: boolean;
}

const PaystackButton: React.FC<PaystackButtonProps> = ({
  amount,
  email,
  reference,
  currency = Currency.NGN,
  metadata = {},
  onSuccess,
  onClose,
  className = '',
  disabled = false,
  text = 'Pay Now',
  variant = 'primary',
  fullWidth = false,
}) => {
  // Paystack needs amount in kobo (smallest currency unit)
  const amountInKobo = Math.round(amount * 100);
  
  // Configuration for Paystack
  const config:any = {
    reference: reference || `ref_${Date.now()}`,
    email,
    amount: amountInKobo,
    publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY || '',
    currency,
    metadata: {
      custom_fields: [
        {
          display_name: 'Organization',
          variable_name: 'organization',
          value: metadata.organizationName || '',
        },
        ...Object.entries(metadata).map(([key, value]) => ({
          display_name: key,
          variable_name: key,
          value: typeof value === 'string' ? value : JSON.stringify(value),
        })),
      ],
    },
    onSuccess: (reference:any) => onSuccess(reference),
    onClose,
  };
  
  // Initialize Paystack payment
  const initializePayment = usePaystackPayment(config);
  
  // Handle button click
  const handlePayment = () => {
    // @ts-ignore (the type definitions for usePaystackPayment are not perfect)
    initializePayment();
  };
  
  // Generate button classes based on variant and fullWidth
  const getButtonClasses = () => {
    let classes = className;
    
    if (variant === 'primary') {
      classes += ' bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white';
    } else if (variant === 'outline') {
      classes += ' border border-[#6964D3] text-[#6964D3]';
    } else if (variant === 'destructive') {
      classes += ' bg-red-500 text-white';
    }
    
    if (fullWidth) {
      classes += ' w-full';
    }
    
    return classes;
  };
  
  return (
    <Button
      className={getButtonClasses()}
      disabled={disabled || !process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY}
      onClick={handlePayment}
    >
      {text}
    </Button>
  );
};

export default PaystackButton; 
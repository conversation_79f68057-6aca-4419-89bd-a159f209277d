'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { hasActiveSubscription } from '@/utils/subscription-checks';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';

// Loading indicator component
const SubscriptionCheckLoading = () => (
  <div className="flex items-center justify-center min-h-screen bg-gray-900">
    <div className="flex flex-col items-center space-y-4">
      <div className="w-16 h-16 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
      <p className="text-lg text-white">Verifying subscription...</p>
    </div>
  </div>
);

// Error component shown when subscription is inactive
const SubscriptionInactive = () => (
  <div className="flex items-center justify-center min-h-screen bg-gray-900">
    <div className="max-w-md p-8 bg-gray-800 rounded-lg shadow-lg text-center">
      <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-red-500 mx-auto mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      </svg>
      <h2 className="text-2xl font-bold text-white mb-4">Subscription Required</h2>
      <p className="text-gray-300 mb-6">
        Your organization's subscription is inactive or has expired. Please contact your administrator to update the subscription.
      </p>
      <a href="/dashboard" className="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
        Return to Dashboard
      </a>
    </div>
  </div>
);

/**
 * HOC that checks if an organization has an active subscription
 * @param Component - The component to wrap
 * @returns A new component that redirects if subscription check fails
 */
export default function WithSubscriptionCheck<P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> {
  return function WrappedComponent(props: P) {
    const router = useRouter();
    const params = useParams();
    const { error } = useToast();
    
    const [isLoading, setIsLoading] = useState(true);
    const [hasSubscription, setHasSubscription] = useState(false);
    
    useEffect(() => {
      async function checkSubscription() {
        try {
          // Extract the organization ID from URL params
          const organizationId = params.id as string || params.organizationId as string;
          
          if (!organizationId) {
            error('Error', 'Organization ID not found in URL');
            router.push('/dashboard');
            return;
          }
          
          // Check if the organization has an active subscription
          const isActive = await hasActiveSubscription(organizationId);
          setHasSubscription(isActive);
          setIsLoading(false);
        } catch (e) {
          logger.error('Error checking subscription:', e);
          error('Error', 'Failed to verify subscription status');
          setIsLoading(false);
        }
      }
      
      checkSubscription();
    }, [params, router, error]);
    
    if (isLoading) {
      return <SubscriptionCheckLoading />;
    }
    
    if (!hasSubscription) {
      return <SubscriptionInactive />;
    }
    
    return <Component {...props} />;
  };
} 
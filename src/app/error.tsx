'use client';

import Link from 'next/link';
import Image from 'next/image';
import { useEffect } from 'react';
import { logger } from '@/utils/logger';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    logger.error(error);
  }, [error]);

  return (
    <section className="py-12 md:py-20 bg-white dark:bg-[#0a0a14] relative overflow-hidden min-h-screen flex items-center animate-fadeIn">
      {/* Background elements with animation */}
      <div className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl animate-float"></div>

      <div className="max-w-3xl mx-auto px-6 relative z-10 w-full text-center">
        <div className="mb-8 flex items-center justify-center">
          <Image 
            src="/assets/logos/purple-cloud-yellow-dots.svg" 
            alt="New Instance Logo" 
            width={60} 
            height={60}
            className="mr-3 animate-pulse-subtle"
          />
          <span className="text-2xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
        </div>

        <div className="mb-8">
          <h1 className="text-9xl font-bold tracking-tighter animate-slideInUp mb-4 bg-gradient-to-r from-[#BE9544] to-[#D1AB66] bg-clip-text text-transparent animate-gradient">
            500
          </h1>
          <p className="text-2xl md:text-3xl font-bold font-[family-name:var(--font-jakarta)] mb-4 animate-slideInUp animation-delay-100">
            Server Error
          </p>
          <p className="text-lg text-[#4B4B4B] dark:text-[#C6C6C6] animate-fadeIn animation-delay-200">
            Sorry, something went wrong on our servers. We're already working on fixing it.
          </p>
        </div>

        <div className="glass-card dark:glass-dark p-8 rounded-2xl shadow-lg border border-[#F0DAB8] dark:border-[#BE9544]/30 mb-10 max-w-md mx-auto animate-slideInUp animation-delay-300">
          <div className="flex items-center justify-center mb-6 space-x-2">
            <div className="w-10 h-10 bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-[#BE9544]">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                <line x1="12" y1="9" x2="12" y2="13"></line>
                <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
            </div>
            <h2 className="text-xl font-semibold">Error Information</h2>
          </div>
          
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
            Our team has been notified and is working on a fix. The error reference is:
          </p>
          
          <div className="bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-lg px-4 py-3 font-mono text-sm mb-6 overflow-hidden text-[#4B4B4B] dark:text-[#C6C6C6]">
            {error.digest || 'Unknown error'}
          </div>
          
          <div className="flex flex-col space-y-3">
            <button
              onClick={reset}
              className="w-full bg-gradient-to-r from-[#BE9544] to-[#D1AB66] text-white py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all btn-hover-fx"
            >
              Try Again
            </button>
            
            <Link 
              href="/"
              className="flex items-center justify-center gap-2 bg-[#F3F3F3] dark:bg-[#1e1e28] hover:bg-[#E9E9E9] dark:hover:bg-[#262631] py-3 rounded-xl transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              <span>Back to Home</span>
            </Link>
          </div>
        </div>

        <div className="text-center animate-fadeIn animation-delay-400">
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-3">
            Need immediate assistance?
          </p>
          <Link 
            href="/contact"
            className="text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white transition-colors font-medium"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </section>
  );
} 
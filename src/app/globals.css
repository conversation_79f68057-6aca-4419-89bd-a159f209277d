@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}


/* PrimeReact base styles */
.p-inputotp-input.p-inputtext.p-component {
  min-height: 40px;
}
.p-icon-field.p-icon-field-right {
  width: 100%;
}
.p-tabview-nav-link {
  padding: 0;
}
.p-inputotp-input.p-inputtext.p-component {
  background-color: #1e1e28 !important;
  border: 1px solid rgb(60, 68, 80) !important;
  border-radius: 10px !important;
}
.p-inline-message {
 align-items: normal;
  justify-content: normal;
}
/*.p-dialog {*/
/*  background: #212130;*/
/*}*/
/*.p-dialog__content {*/
/*  background: #212130;*/
/*}*/
/* Custom tab styling for permissions page */
.permissions-tabs .p-tabview-nav {
  border-bottom: 1px solid rgba(107, 114, 128, 0.2) !important;
  background: transparent !important;
}

.permissions-tabs .p-tabview-nav li .p-tabview-nav-link {
  background: transparent !important;
  border: none !important;
  color: inherit !important;
  transition: all 0.2s ease;
}

.permissions-tabs .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  border-bottom: none !important;
  color: #6964D3 !important;
  font-weight: 500;
}

.dark .permissions-tabs .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  color: #8178E8 !important;
}

.permissions-tabs .p-tabview-panels {
  padding: 1.5rem !important;
  border: none !important;
}

.permissions-tabs .p-tabview-panel {
  padding: 0 !important;
}

:root {
  --background: #ffffff;
  --foreground: #0f0f18;
  --primary: #8178E8;
  --primary-dark: #6964D3;
  --secondary: #D1AB66;
  --secondary-dark: #BE9544;
  --gray-light: #F3F3F3;
  --gray-medium: #C6C6C6;
  --gray-dark: #4B4B4B;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-jakarta);
  --font-body: var(--font-inter);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a14;
    --foreground: #f8f8ff;
    --gray-light: #1e1e28;
    --gray-medium: #44444e;
  }
}

@layer base {
  body {
    background: var(--background);
    color: var(--foreground);
    font-family: system-ui, sans-serif;
  }
}

@layer components {
  .glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
  }

  .glass-dark {
    background: rgba(15, 15, 24, 0.75);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.08);
  }

  .blob-shape {
    border-radius: 40% 60% 70% 30% / 40% 50% 60% 50%;
  }

  /* Modern scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: var(--gray-light);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 10px;
  }

  /* Modern button hover effect */
  .btn-hover-fx {
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
  }

  .btn-hover-fx::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.4s ease-in-out;
  }

  .btn-hover-fx:hover::before {
    width: 100%;
  }
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pulse-subtle {
  0% {
    box-shadow: 0 0 0 0 rgba(129, 120, 232, 0.2);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(129, 120, 232, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(129, 120, 232, 0);
  }
}

@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes bounce-once {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes shakeX {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

@keyframes bounceIn {
  0%, 20%, 40%, 60%, 80%, 100% {
    transition-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
  }
  0% {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }
  20% {
    transform: scale3d(1.1, 1.1, 1.1);
  }
  40% {
    transform: scale3d(.9, .9, .9);
  }
  60% {
    opacity: 1;
    transform: scale3d(1.03, 1.03, 1.03);
  }
  80% {
    transform: scale3d(.97, .97, .97);
  }
  100% {
    opacity: 1;
    transform: scale3d(1, 1, 1);
  }
}

@layer utilities {
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-fadeIn {
    animation: fadeIn 0.8s ease forwards;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.8s ease forwards;
  }

  .animate-slideInRight {
    animation: slideInRight 0.8s ease forwards;
  }

  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }

  .animate-pulse-subtle {
    animation: pulse-subtle 2s infinite;
  }

  .animate-spin-slow {
    animation: spin-slow 6s linear infinite;
  }

  .animate-bounce-once {
    animation: bounce-once 1s ease-in-out;
  }

  .animate-gradient {
    animation: gradient 8s ease infinite;
    background-size: 200% 200%;
  }

  .animate-shakeX {
    animation: shakeX 0.5s ease-in-out;
  }

  .animate-bounceIn {
    animation: bounceIn 0.7s ease forwards;
  }

  .animation-delay-300 {
    animation-delay: 0.3s;
  }

  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  .animation-delay-500 {
    animation-delay: 0.5s;
  }

  .animation-delay-0 {
    animation-delay: 0s;
  }

  .animation-delay-1 {
    animation-delay: 0.1s;
  }

  .animation-delay-2 {
    animation-delay: 0.2s;
  }

  .animation-delay-3 {
    animation-delay: 0.3s;
  }

  .animation-delay-4 {
    animation-delay: 0.4s;
  }

  .animation-delay-5 {
    animation-delay: 0.5s;
  }

  .delay-200 {
    transition-delay: 200ms;
  }

  .delay-400 {
    transition-delay: 400ms;
  }

  .gradient-text {
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .hover-card {
    transition: all 0.3s ease;
  }

  .hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, var(--gray-light), var(--gray-medium), var(--gray-light));
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;
}

.height-transition {
  transition: height 0.3s ease-in-out;
}

/* Custom InputNumber Styling */
.p-inputnumber-custom .p-inputnumber-input {
  width: 100%;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.dark .p-inputnumber-custom .p-inputnumber-input {
  background-color: #374151;
  color: white;
  border-color: #4b5563;
}

.p-inputnumber-custom .p-inputnumber-button {
  border-color: #d1d5db;
  color: #374151;
}

.dark .p-inputnumber-custom .p-inputnumber-button {
  background-color: #374151;
  border-color: #4b5563;
  color: #d1d5db;
}

/* Custom Dialog Styling */
.p-dialog {
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: none;
}

.p-dialog .p-dialog-header {
  padding: 1rem;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.dark .p-dialog .p-dialog-header {
  background-color: #1f2937;
  border-color: #374151;
}

.p-dialog .p-dialog-content {
  padding: 1rem;
}

.dark .p-dialog .p-dialog-content {
  background-color: #1f2937;
  color: #e5e7eb;
}

.p-dialog .p-dialog-footer {
  padding: 1rem;
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}

.dark .p-dialog .p-dialog-footer {
  background-color: #1f2937;
  border-color: #374151;
}

/* Custom button styling */
.gradient-primary {
  background: linear-gradient(to right, #8178E8, #6964D3);
  color: white;
}

.gradient-danger {
  background: linear-gradient(to right, #ef4444, #dc2626);
  color: white;
}

/* Custom TabView styling */
.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background-color: #1f2937;
  color: #2563eb;
  border-bottom: 2px solid #2563eb;
}

.dark .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background-color: #1f2937;
  color: #60a5fa;
  border-bottom: 2px solid #60a5fa;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link {
  background-color: transparent;
  color: #6b7280;
  transition: all 0.2s;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus {
  outline: none;
  box-shadow: none;
}

.dark .p-tabview .p-tabview-nav li .p-tabview-nav-link {
  color: #9ca3af;
}

.p-tabview .p-tabview-nav {
  background: transparent;
  border: none;
  border-bottom: 1px solid #e5e7eb;
}

.dark .p-tabview .p-tabview-nav {
  border-bottom: 1px solid #374151;
}

.p-tabview .p-tabview-panels {
  padding: 1rem 0;
}

/* Tag styling */
.p-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Chip styling */
.p-chip {
  background: transparent;
  border: none;
}

.p-chip .p-chip-text {
  line-height: 1rem;
}

.p-chip .p-chip-remove-icon {
  margin-left: 0.25rem;
}

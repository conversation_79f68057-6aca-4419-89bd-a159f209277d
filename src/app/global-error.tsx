'use client';

import Link from 'next/link';
import { useEffect } from 'react';
import { logger } from '@/utils/logger';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    logger.error(error);
  }, [error]);

  return (
    <html lang="en">
      <body>
        <div className="min-h-screen bg-white dark:bg-[#0a0a14] flex flex-col items-center justify-center p-4">
          <div className="w-full max-w-md text-center">
            <h1 className="text-6xl font-bold text-red-500 mb-6">Critical Error</h1>
            
            <p className="text-xl mb-8 text-[#4B4B4B] dark:text-[#C6C6C6]">
              A fatal error has occurred. We apologize for the inconvenience.
            </p>

            <div className="bg-[#F3F3F3] dark:bg-[#1e1e28] rounded-lg px-4 py-3 font-mono text-sm mb-6 overflow-hidden text-[#4B4B4B] dark:text-[#C6C6C6]">
              {error.digest || 'Unknown error'}
            </div>

            <div className="flex flex-col space-y-4">
              <button
                onClick={reset}
                className="w-full bg-gradient-to-r from-[#BE9544] to-[#D1AB66] text-white py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
              >
                Try Again
              </button>
              
              <a
                href="/"
                className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all text-center"
              >
                Back to Home
              </a>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
} 
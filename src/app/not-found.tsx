'use client';

import Link from 'next/link';
import Image from 'next/image';

export default function NotFound() {
  return (
    <section className="py-12 md:py-20 bg-white dark:bg-[#0a0a14] relative overflow-hidden min-h-screen flex items-center animate-fadeIn">
      {/* Background elements with animation */}
      <div className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl animate-float"></div>

      <div className="max-w-3xl mx-auto px-6 relative z-10 w-full text-center">
        <div className="mb-8 flex items-center justify-center">
          <Image 
            src="/assets/logos/purple-cloud-yellow-dots.svg" 
            alt="New Instance Logo" 
            width={60} 
            height={60}
            className="mr-3 animate-pulse-subtle"
          />
          <span className="text-2xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
        </div>

        <div className="mb-8">
          <h1 className="text-9xl font-bold tracking-tighter animate-slideInUp mb-4 bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent animate-gradient">
            404
          </h1>
          <p className="text-2xl md:text-3xl font-bold font-[family-name:var(--font-jakarta)] mb-4 animate-slideInUp animation-delay-100">
            Page Not Found
          </p>
          <p className="text-lg text-[#4B4B4B] dark:text-[#C6C6C6] animate-fadeIn animation-delay-200">
            Sorry, we couldn't find the page you're looking for. It might have been moved or deleted.
          </p>
        </div>

        <div className="glass-card dark:glass-dark p-8 rounded-2xl shadow-lg border border-[#E0D7FF] dark:border-[#1e1e28] mb-8 max-w-md mx-auto animate-slideInUp animation-delay-300">
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
            You might want to check out these links instead:
          </p>
          <div className="flex flex-col gap-3">
            <Link 
              href="/"
              className="flex items-center gap-2 text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white transition-all hover:translate-x-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              <span>Back to Home</span>
            </Link>
            <Link 
              href="/auth?mode=login"
              className="flex items-center gap-2 text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white transition-all hover:translate-x-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                <polyline points="10 17 15 12 10 7"></polyline>
                <line x1="15" y1="12" x2="3" y2="12"></line>
              </svg>
              <span>Sign In</span>
            </Link>
            <Link 
              href="/contact"
              className="flex items-center gap-2 text-[#6964D3] hover:text-[#424098] dark:text-[#B2A5FF] dark:hover:text-white transition-all hover:translate-x-1"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>
              </svg>
              <span>Contact Support</span>
            </Link>
          </div>
        </div>

        <button 
          onClick={() => window.history.back()}
          className="inline-flex items-center gap-2 text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-[#424098] dark:hover:text-white transition-colors animate-fadeIn animation-delay-400"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          <span>Go Back</span>
        </button>
      </div>
    </section>
  );
} 
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { InputOtp } from '@/components/ui/InputOtp';
import { InputOtpChangeEvent } from 'primereact/inputotp';
import { requestPasswordReset, resetPassword, sendOtpForToken, verifyOtp } from '@/server/actions/user-auth';
import { FormField, FormActions } from '@/components/ui/Form';
import { Alert } from '@/components/ui/Alert';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';

// Define the type for action responses
interface ServerActionResponse {
  success: boolean;
  error?: string;
  verificationToken?: string;
}

// Function to validate email format
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

type ResetStep = 'request' | 'verify' | 'reset';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [step, setStep] = useState<ResetStep>('request');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(0);
  const [resetToken, setResetToken] = useState('');

  // Handle countdown for resend button
  useEffect(() => {
    if (resendCountdown > 0) {
      const timer = setTimeout(() => {
        setResendCountdown(resendCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (resendCountdown === 0 && resendDisabled) {
      setResendDisabled(false);
    }
  }, [resendCountdown, resendDisabled]);

  // Extract token from URL if present
  useEffect(() => {
    const url = new URL(window.location.href);
    const token = url.searchParams.get('token');
    const emailParam = url.searchParams.get('email');

    if (token && emailParam) {
      setResetToken(token);
      setEmail(emailParam);
      setStep('reset');
    }
  }, []);

  const handleRequestReset = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValidEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await requestPasswordReset(email) as ServerActionResponse;
      if (result.success) {
        setSuccessMessage(`Password reset link sent to ${email}. Please check your email.`);
        // No need to move to verification step as we're using email link
      } else {
        setError(result.error || 'Failed to send password reset email. Please try again.');
      }
    } catch (err) {
      console.error('Error requesting password reset:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    if (otp.length !== 6) {
      setError('Please enter a valid 6-digit verification code');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await verifyOtp(resetToken, otp) as ServerActionResponse;
      if (result.success) {
        setSuccessMessage('Verification successful! Set your new password.');
        setStep('reset');
      } else {
        setError(result.error || 'Invalid verification code. Please try again.');
      }
    } catch (err) {
      console.error('Error verifying OTP:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (resendDisabled || !resetToken) return;

    setIsLoading(true);
    setError('');
    setResendDisabled(true);

    try {
      const result = await sendOtpForToken(resetToken) as ServerActionResponse;
      if (result.success) {
        setSuccessMessage(`Verification code resent`);
        setResendCountdown(60); // 60 seconds cooldown
      } else {
        setError(result.error || 'Failed to resend code. Please try again.');
        setResendDisabled(false);
      }
    } catch (err) {
      console.error('Error resending OTP:', err);
      setError('An error occurred. Please try again.');
      setResendDisabled(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters long');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const result = await resetPassword({
        token: resetToken,
        email,
        password: newPassword,
        confirmPassword
      }) as ServerActionResponse;

      if (result.success) {
        setSuccessMessage('Password reset successful! Redirecting to login...');
        setTimeout(() => {
          router.push('/auth?mode=login');
        }, 2000);
      } else {
        setError(result.error || 'Failed to reset password. Please try again.');
      }
    } catch (err) {
      console.error('Error resetting password:', err);
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <section className="py-12 md:py-20 bg-white dark:bg-[#0a0a14] relative overflow-hidden min-h-screen flex items-center animate-fadeIn">
      {/* Background elements with animation */}
      <div className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl animate-float"></div>

      <div className="max-w-lg mx-auto px-6 relative z-10 w-full">
        <div className="flex flex-col items-center">
          <div className="mb-8 flex items-center">
            <Image
              src="/assets/logos/purple-cloud-yellow-dots.svg"
              alt="New Instance Logo"
              width={50}
              height={50}
              className="mr-3 animate-pulse-subtle"
            />
            <span className="text-2xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
          </div>

          <div className="w-full glass-card dark:glass-dark p-8 md:p-10 rounded-2xl shadow-xl border border-[#E0D7FF] dark:border-[#1e1e28] animate-pulse-subtle">
            <h1 className="text-2xl md:text-3xl font-bold font-[family-name:var(--font-jakarta)] leading-tight mb-6 bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent animate-gradient text-center">
              {step === 'request'
                ? 'Reset your password'
                : step === 'verify'
                ? 'Verify your email'
                : 'Create new password'}
            </h1>

            <p className="text-md mb-8 text-[#4B4B4B] dark:text-[#C6C6C6] animate-fadeIn text-center">
              {step === 'request'
                ? 'Enter your email and we\'ll send you a password reset link'
                : step === 'verify'
                ? `We've sent a verification code to ${email}`
                : 'Enter and confirm your new password'}
            </p>

            {/* Error and Success Messages */}
            {error && (
              <Alert severity="error" className="mb-6 animate-shakeX">
                {error}
              </Alert>
            )}

            {successMessage && (
              <Alert severity="success" className="mb-6 animate-bounceIn">
                {successMessage}
              </Alert>
            )}

            {/* Request Reset Form */}
            {step === 'request' && (
              <form onSubmit={handleRequestReset} className="animate-slideInUp" noValidate>
                <FormField
                  label="Email Address"
                  htmlFor="email"
                  className="mb-6 animate-fadeIn"
                >
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={isLoading}
                    required
                  />
                </FormField>

                <FormActions className="border-none p-0">
                  <Button
                    type="submit"
                    isLoading={isLoading}
                    disabled={isLoading}
                    className="w-full py-3.5 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
                  >
                    {isLoading ? 'Sending...' : 'Send Reset Link'}
                  </Button>
                </FormActions>

                <div className="text-center mt-6">
                  <Link href="/auth?mode=login" className="text-[#6964D3] hover:text-[#424098] text-sm">
                    Return to Login
                  </Link>
                </div>
              </form>
            )}

            {/* Verify OTP Form */}
            {step === 'verify' && (
              <form onSubmit={handleVerifyOtp} className="animate-slideInUp" noValidate>
                <div className="mb-6 animate-fadeIn">
                  <FormField
                    label="Verification Code"
                    htmlFor="otp"
                    description="Enter the 6-digit code we sent to your email"
                    className="text-center"
                  >
                    <div className="flex justify-center gap-2 mb-6">
                      <InputOtp
                        value={otp}
                        onChange={(e: InputOtpChangeEvent) => setOtp(e.value as string || '')}
                        length={6}
                        error={error ? '' : undefined}
                        className="custom-otp"
                        disabled={isLoading}
                      />
                    </div>
                  </FormField>
                </div>

                <FormActions className="border-none p-0">
                  <Button
                    type="submit"
                    isLoading={isLoading}
                    disabled={isLoading}
                    className="w-full py-3.5 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
                  >
                    {isLoading ? 'Verifying...' : 'Verify Code'}
                  </Button>
                </FormActions>

                <div className="text-center mt-6">
                  <button
                    type="button"
                    onClick={handleResendOtp}
                    className={`text-[#6964D3] hover:text-[#424098] text-sm ${resendDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={resendDisabled}
                  >
                    {resendCountdown > 0
                      ? `Resend code in ${resendCountdown}s`
                      : 'Resend code'}
                  </button>
                </div>
              </form>
            )}

            {/* Reset Password Form */}
            {step === 'reset' && (
              <form onSubmit={handleResetPassword} className="animate-slideInUp" noValidate>
                <FormField
                  label="New Password"
                  htmlFor="newPassword"
                  className="mb-6 animate-fadeIn"
                >
                  <Input
                    id="newPassword"
                    type="password"
                    placeholder="Min. 8 characters"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    disabled={isLoading}
                    required
                    minLength={8}
                  />
                </FormField>

                <FormField
                  label="Confirm Password"
                  htmlFor="confirmPassword"
                  className="mb-6 animate-fadeIn animation-delay-100"
                >
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm your password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={isLoading}
                    required
                  />
                </FormField>

                <FormActions className="border-none p-0">
                  <Button
                    type="submit"
                    isLoading={isLoading}
                    disabled={isLoading}
                    className="w-full py-3.5 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
                  >
                    {isLoading ? 'Updating Password...' : 'Reset Password'}
                  </Button>
                </FormActions>
              </form>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
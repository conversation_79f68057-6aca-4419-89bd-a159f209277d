'use client';

import { useState, useEffect } from 'react';
import { getAdminProfile, updateAdminProfile, changeAdminPassword, generate2FASetup, verify2FASetup, disable2FA } from '@/server/actions/admin-profile';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Alert, SuccessAlert } from '@/components/ui/Alert';
import { Form, FormField, FormSection, FormActions } from '@/components/ui/Form';
import { Tabs } from '@/components/ui/Tabs';
import { Skeleton } from '@/components/ui/Skeleton';
import { Dialog } from '@/components/ui/Dialog';
import { AdminRole } from '@/constants/roles';
import { logger } from '@/utils/logger';

// Icon imports
import { KeyIcon, ShieldCheckIcon, UserIcon } from '@heroicons/react/24/outline';

interface AdminProfile {
  _id: string;
  name: string;
  email: string;
  role: AdminRole;
  twoFactorEnabled: boolean;
  active: boolean;
  lastLogin?: string;
  permissionGroups: {
    _id: string;
    name: string;
    description: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

export default function ProfilePage() {
  // State for profile data
  const [profile, setProfile] = useState<AdminProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for profile update
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [updating, setUpdating] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);

  // State for password change
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState({
    label: '',
    severity: ''
  });

  // State for 2FA setup
  const [setupStep, setSetupStep] = useState(0);
  const [setupSecret, setSetupSecret] = useState('');
  const [setupQrCode, setSetupQrCode] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [setupLoading, setSetupLoading] = useState(false);
  const [setupError, setSetupError] = useState<string | null>(null);
  const [setupSuccess, setSetupSuccess] = useState(false);

  // State for 2FA disable
  const [showDisableDialog, setShowDisableDialog] = useState(false);
  const [disablePassword, setDisablePassword] = useState('');
  const [disabling, setDisabling] = useState(false);
  const [disableError, setDisableError] = useState<string | null>(null);

  // Fetch admin profile data
  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      setError(null);
      try {
        const result = await getAdminProfile();
        if (result.success && result.data) {
          setProfile(result.data);
          setName(result.data.name);
          setEmail(result.data.email);
        } else {
          setError(result.error || 'Failed to load profile data');
        }
      } catch (err) {
        logger.error('Error fetching profile:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // Password strength check
  useEffect(() => {
    if (!newPassword) {
      setPasswordStrength({ label: '', severity: '' });
      return;
    }

    const getPasswordStrength = (password: string) => {
      if (password.length < 8) return { label: 'Weak', severity: 'danger' };

      const hasLowercase = /[a-z]/.test(password);
      const hasUppercase = /[A-Z]/.test(password);
      const hasDigit = /\d/.test(password);
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

      const conditions = [hasLowercase, hasUppercase, hasDigit, hasSpecial];
      const metCount = conditions.filter(Boolean).length;

      if (metCount <= 2) return { label: 'Medium', severity: 'warning' };
      if (metCount === 3) return { label: 'Strong', severity: 'info' };
      return { label: 'Very Strong', severity: 'success' };
    };

    setPasswordStrength(getPasswordStrength(newPassword));
  }, [newPassword]);

  // Handle profile update submission
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setUpdating(true);
    setUpdateError(null);
    setUpdateSuccess(false);

    try {
      // Only include changed fields
      const updateData: any = {};
      if (name !== profile?.name) updateData.name = name;
      if (email !== profile?.email) updateData.email = email;

      // Only proceed if there are changes
      if (Object.keys(updateData).length === 0) {
        setUpdateError('No changes detected');
        setUpdating(false);
        return;
      }

      const result = await updateAdminProfile(updateData);

      if (result.success) {
        setUpdateSuccess(true);

        // Update profile data
        if (result.data) {
          setProfile(prevProfile => {
            if (!prevProfile) return null;
            return {
              ...prevProfile,
              ...result.data
            };
          });
        }
      } else {
        setUpdateError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      logger.error('Error updating profile:', err);
      setUpdateError('An unexpected error occurred');
    } finally {
      setUpdating(false);
    }
  };

  // Handle password change submission
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setChangingPassword(true);
    setPasswordError(null);
    setPasswordSuccess(false);

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match');
      setChangingPassword(false);
      return;
    }

    try {
      const result = await changeAdminPassword({
        currentPassword,
        newPassword,
        confirmPassword
      });

      if (result.success) {
        setPasswordSuccess(true);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      } else {
        setPasswordError(result.error || 'Failed to change password');
      }
    } catch (err) {
      logger.error('Error changing password:', err);
      setPasswordError('An unexpected error occurred');
    } finally {
      setChangingPassword(false);
    }
  };

  // Handle 2FA setup initialization
  const handleStart2FASetup = async () => {
    setSetupLoading(true);
    setSetupError(null);
    setSetupSuccess(false);
    setSetupStep(1);

    try {
      const result = await generate2FASetup();

      if (result.success && result.data) {
        setSetupSecret(result.data.secret);
        setSetupQrCode(result.data.qrCode);
        setSetupStep(2);
      } else {
        setSetupError(result.error || 'Failed to generate 2FA setup');
        setSetupStep(0);
      }
    } catch (err) {
      logger.error('Error generating 2FA setup:', err);
      setSetupError('An unexpected error occurred');
      setSetupStep(0);
    } finally {
      setSetupLoading(false);
    }
  };

  // Handle 2FA verification
  const handleVerify2FA = async (e: React.FormEvent) => {
    e.preventDefault();
    setSetupLoading(true);
    setSetupError(null);
    setSetupSuccess(false);

    try {
      const result = await verify2FASetup({
        secret: setupSecret,
        token: verificationCode
      });

      if (result.success) {
        setSetupSuccess(true);
        setSetupStep(3);

        // Update profile data to show 2FA as enabled
        setProfile(prevProfile => {
          if (!prevProfile) return null;
          return {
            ...prevProfile,
            twoFactorEnabled: true
          };
        });

        // Reset setup state
        setTimeout(() => {
          setSetupStep(0);
          setSetupSecret('');
          setSetupQrCode('');
          setVerificationCode('');
        }, 3000);
      } else {
        setSetupError(result.error || 'Failed to verify 2FA setup');
      }
    } catch (err) {
      logger.error('Error verifying 2FA setup:', err);
      setSetupError('An unexpected error occurred');
    } finally {
      setSetupLoading(false);
    }
  };

  // Handle opening disable 2FA dialog
  const openDisableDialog = () => {
    setDisablePassword('');
    setDisableError(null);
    setShowDisableDialog(true);
  };

  // Handle 2FA disablement
  const handleDisable2FA = async () => {
    setDisabling(true);
    setDisableError(null);

    try {
      const result = await disable2FA({
        password: disablePassword
      });

      if (result.success) {
        // Update profile data to show 2FA as disabled
        setProfile(prevProfile => {
          if (!prevProfile) return null;
          return {
            ...prevProfile,
            twoFactorEnabled: false
          };
        });

        setShowDisableDialog(false);
      } else {
        setDisableError(result.error || 'Failed to disable 2FA');
      }
    } catch (err) {
      logger.error('Error disabling 2FA:', err);
      setDisableError('An unexpected error occurred');
    } finally {
      setDisabling(false);
    }
  };

  if (loading) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-semibold mb-6">My Profile</h1>
        <div className="space-y-4">
          <Skeleton className="h-40 w-full" variant="rounded" />
          <Skeleton className="h-40 w-full" variant="rounded" />
          <Skeleton className="h-40 w-full" variant="rounded" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <h1 className="text-2xl font-semibold mb-6">My Profile</h1>
        <Alert severity="error">{error}</Alert>
      </div>
    );
  }

  const tabItems = [
    {
      id: 'profile',
      label: 'Profile Details',
      icon: <UserIcon className="h-5 w-5 mr-2" />,
      content: (
        <Card hasBorder={false}>
          <CardContent>
            <div className="py-4">
              <Form onSubmit={handleProfileUpdate} className="space-y-6">
                <FormSection title="Basic Information">
                  <FormField label="Name" htmlFor="name" required>
                    <Input
                      id="name"
                      value={name}
                      onChange={e => setName(e.target.value)}
                      placeholder="Your name"
                    />
                  </FormField>

                  <FormField label="Email" htmlFor="email" required>
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={e => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </FormField>

                  <FormField label="Role" htmlFor="role">
                    <Input
                      id="role"
                      value={profile?.role || ''}
                      disabled
                    />
                  </FormField>
                </FormSection>

                {updateError && (
                  <Alert severity="error">{updateError}</Alert>
                )}

                {updateSuccess && (
                  <SuccessAlert>Profile updated successfully</SuccessAlert>
                )}

                <FormActions>
                  <Button
                    type="submit"
                    variant="primary"
                    isLoading={updating}
                    disabled={updating}
                  >
                    Save Changes
                  </Button>
                </FormActions>
              </Form>
            </div>
          </CardContent>
        </Card>
      )
    },
    {
      id: 'password',
      label: 'Change Password',
      icon: <KeyIcon className="h-5 w-5 mr-2" />,
      content: (
        <Card hasBorder={false}>
          <CardContent>
            <div className="py-4">
              <Form onSubmit={handlePasswordChange} className="space-y-6">
                <FormSection title="Change Password">
                  <FormField label="Current Password" htmlFor="currentPassword" required>
                    <Input
                      id="currentPassword"
                      type="password"
                      value={currentPassword}
                      onChange={e => setCurrentPassword(e.target.value)}
                      placeholder="Your current password"
                    />
                  </FormField>

                  <FormField
                    label="New Password"
                    htmlFor="newPassword"
                    required
                    description={
                      passwordStrength.label ?
                        `Password strength: ${passwordStrength.label}` :
                        'Password must be at least 8 characters'
                    }
                  >
                    <Input
                      id="newPassword"
                      type="password"
                      value={newPassword}
                      onChange={e => setNewPassword(e.target.value)}
                      placeholder="New password"
                    />
                  </FormField>

                  <FormField label="Confirm New Password" htmlFor="confirmPassword" required>
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={e => setConfirmPassword(e.target.value)}
                      placeholder="Confirm new password"
                    />
                  </FormField>
                </FormSection>

                {passwordError && (
                  <Alert severity="error">{passwordError}</Alert>
                )}

                {passwordSuccess && (
                  <SuccessAlert>Password changed successfully</SuccessAlert>
                )}

                <FormActions>
                  <Button
                    type="submit"
                    variant="primary"
                    isLoading={changingPassword}
                    disabled={changingPassword || !currentPassword || !newPassword || !confirmPassword}
                  >
                    Change Password
                  </Button>
                </FormActions>
              </Form>
            </div>
          </CardContent>
        </Card>
      )
    },
    {
      id: 'security',
      label: 'Two-Factor Authentication',
      icon: <ShieldCheckIcon className="h-5 w-5 mr-2" />,
      content: (
        <Card hasBorder={false}>
          <CardContent>
            <div className="py-4">
              <FormSection
                title="Two-Factor Authentication (2FA)"
                description="Enhance your account security by enabling two-factor authentication."
              >
                <div className="mb-4">
                  <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <ShieldCheckIcon className={`h-8 w-8 mr-3 ${profile?.twoFactorEnabled ? 'text-green-500' : 'text-gray-400'}`} />
                        <div>
                          <h3 className="text-md font-medium">Two-Factor Authentication</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {profile?.twoFactorEnabled ?
                              'Enabled - Your account is protected with 2FA.' :
                              'Disabled - Your account is not using 2FA.'}
                          </p>
                        </div>
                      </div>

                      {profile?.twoFactorEnabled ? (
                        <Button
                          variant="danger"
                          onClick={openDisableDialog}
                        >
                          Disable 2FA
                        </Button>
                      ) : (
                        <Button
                          variant="primary"
                          onClick={handleStart2FASetup}
                          isLoading={setupLoading && setupStep === 1}
                          disabled={setupLoading}
                        >
                          Enable 2FA
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* 2FA Setup Flow */}
                  {setupStep > 1 && (
                    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mt-4">
                      <h3 className="text-md font-medium mb-2">Setup Two-Factor Authentication</h3>

                      {setupStep === 2 && (
                        <div className="space-y-4">
                          <ol className="list-decimal pl-5 space-y-2">
                            <li>Install an authenticator app (Google Authenticator, Authy, etc.)</li>
                            <li>Scan the QR code with your authenticator app</li>
                            <li>Enter the verification code from your app</li>
                          </ol>

                          <div className="flex justify-center my-4">
                            {setupQrCode && (
                              <div className="border border-gray-200 dark:border-gray-700 p-2 bg-white rounded-lg">
                                <img src={setupQrCode} alt="QR Code for 2FA setup" width={200} height={200} />
                              </div>
                            )}
                          </div>

                          <div className="text-center text-sm text-gray-500 mt-2 mb-4">
                            <p>If you can't scan the QR code, enter this code manually:</p>
                            <code className="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono">
                              {setupSecret}
                            </code>
                          </div>

                          <Form onSubmit={handleVerify2FA}>
                            <FormField label="Verification Code" htmlFor="verificationCode" required>
                              <Input
                                id="verificationCode"
                                value={verificationCode}
                                onChange={e => setVerificationCode(e.target.value)}
                                placeholder="6-digit code"
                                maxLength={6}
                              />
                            </FormField>

                            {setupError && (
                              <Alert severity="error" className="mt-4">{setupError}</Alert>
                            )}

                            <div className="flex justify-end mt-4">
                              <Button
                                variant="secondary"
                                onClick={() => setSetupStep(0)}
                                className="mr-2"
                              >
                                Cancel
                              </Button>
                              <Button
                                type="submit"
                                variant="primary"
                                isLoading={setupLoading}
                                disabled={setupLoading || !verificationCode || verificationCode.length !== 6}
                              >
                                Verify
                              </Button>
                            </div>
                          </Form>
                        </div>
                      )}

                      {setupStep === 3 && setupSuccess && (
                        <div className="text-center p-4">
                          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 dark:bg-green-900 mb-4">
                            <ShieldCheckIcon className="h-6 w-6 text-green-600 dark:text-green-300" />
                          </div>
                          <h3 className="text-lg font-medium text-gray-900 dark:text-white">2FA Enabled Successfully</h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                            Your account is now protected with two-factor authentication.
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </FormSection>
            </div>
          </CardContent>
        </Card>
      )
    }
  ];

  return (
    <div className="p-4">
      <h1 className="text-2xl font-semibold mb-6">My Profile</h1>

      <Tabs tabs={tabItems} />

      {/* Disable 2FA Confirmation Dialog */}
      <Dialog
        title="Disable Two-Factor Authentication"
        open={showDisableDialog}
        onOpenChange={setShowDisableDialog}
        footer={
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setShowDisableDialog(false)}
              disabled={disabling}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDisable2FA}
              isLoading={disabling}
              disabled={disabling || !disablePassword}
            >
              Disable 2FA
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p>Are you sure you want to disable two-factor authentication? This will make your account less secure.</p>

          <FormField label="Confirm your password" htmlFor="disablePassword" required>
            <Input
              id="disablePassword"
              type="password"
              value={disablePassword}
              onChange={e => setDisablePassword(e.target.value)}
              placeholder="Your current password"
            />
          </FormField>

          {disableError && (
            <Alert severity="error">{disableError}</Alert>
          )}
        </div>
      </Dialog>
    </div>
  );
}

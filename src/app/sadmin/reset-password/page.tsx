'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { resetPassword } from '@server/actions/admin-auth';
import { Button } from 'primereact/button';
import { Password } from 'primereact/password';
import Image from 'next/image';
import { Form, FormField, FormActions } from '@/components/ui/Form';
import { ErrorAlert, SuccessAlert, InfoAlert } from '@/components/ui/Alert';
import { logger } from '@/utils/logger';

// Client component that uses useSearchParams
function ResetPasswordContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  // Form state
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [invalidToken, setInvalidToken] = useState(false);

  // Validate token on mount
  useEffect(() => {
    if (!token) {
      setInvalidToken(true);
      setError('Invalid or missing reset token. Please request a new password reset link.');
    }
  }, [token]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setError('Invalid or missing reset token. Please request a new password reset link.');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    if (newPassword.length < 8) {
      setError('Password must be at least 8 characters');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess(false);
    
    try {
      const result = await resetPassword({
        token,
        newPassword,
        confirmPassword
      });

      if (result.success) {
        setSuccess(true);
        setNewPassword('');
        setConfirmPassword('');
        
        // Redirect to login after a short delay
        setTimeout(() => {
          router.push('/sadmin/login');
        }, 3000);
      } else {
        setError(result.error || 'Failed to reset password');
      }
    } catch (err) {
      logger.error('Error resetting password:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Password strength feedback
  const getPasswordStrength = (password: string) => {
    if (!password) return { label: 'Empty', severity: 'danger' };

    if (password.length < 8) return { label: 'Weak', severity: 'danger' };

    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const conditions = [hasLowercase, hasUppercase, hasDigit, hasSpecial];
    const metCount = conditions.filter(Boolean).length;

    if (metCount <= 2) return { label: 'Medium', severity: 'warning' };
    if (metCount === 3) return { label: 'Strong', severity: 'info' };
    return { label: 'Very Strong', severity: 'success' };
  };

  const passwordStrength = getPasswordStrength(newPassword);

  // Password footer
  const passwordFooter = (
    <div className="flex flex-wrap justify-between items-center">
      <span className={`text-${passwordStrength.severity} text-xs`}>
        {passwordStrength.label}
      </span>
      <ul className="text-xs p-0 mt-2 list-none">
        <li>At least 8 characters</li>
        <li>At least 1 lowercase letter</li>
        <li>At least 1 uppercase letter</li>
        <li>At least 1 digit</li>
        <li>At least 1 special character</li>
      </ul>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8 flex flex-col items-center justify-center">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <Image
              src="/assets/logos/purple-cloud-yellow-dots.svg"
              alt="New Instance Logo"
              width={60}
              height={60}
              className="h-12 w-auto"
            />
          </div>
          <h1 className="text-3xl font-extrabold text-gray-900 dark:text-white">
            Set Your Password
          </h1>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Create a secure password for your admin account
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
          {error && (
            <div className="px-6 pt-6">
              <ErrorAlert>
                {error}
              </ErrorAlert>
            </div>
          )}

          {success && (
            <div className="px-6 pt-6">
              <SuccessAlert>
                Password has been reset successfully! Redirecting to login page...
              </SuccessAlert>
            </div>
          )}

          {!invalidToken && !success && (
            <Form onSubmit={handleSubmit} className="p-6">
              <div className="space-y-6">
                <FormField 
                  label="New Password" 
                  htmlFor="newPassword"
                  required
                >
                  <Password
                    id="newPassword"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    required
                    toggleMask
                    className="w-full"
                    footer={passwordFooter}
                    promptLabel="Enter a password"
                    weakLabel="Weak"
                    mediumLabel="Medium"
                    strongLabel="Strong"
                    pt={{
                      root: { className: 'w-full' },
                      input: { 
                        className: 'w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white' 
                      },
                      panel: {
                        className: 'bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-200 dark:border-gray-700 mt-2'
                      }
                    }}
                  />
                </FormField>

                <FormField 
                  label="Confirm Password" 
                  htmlFor="confirmPassword"
                  required
                >
                  <Password
                    id="confirmPassword"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    required
                    feedback={false}
                    toggleMask
                    className="w-full"
                    pt={{
                      root: { className: 'w-full' },
                      input: { 
                        className: 'w-full p-3 border border-gray-300 rounded-lg shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white' 
                      }
                    }}
                  />
                </FormField>

                <FormActions className="border-none p-0 mt-8">
                  <Button
                    label={loading ? 'Setting Password...' : 'Set Password'}
                    type="submit"
                    className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] px-6 py-3 rounded-lg text-white shadow-md font-medium hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#8178E8] focus:ring-offset-2"
                    loading={loading}
                    loadingIcon="pi pi-spin pi-spinner"
                    disabled={loading || invalidToken}
                  />
                </FormActions>
              </div>
            </Form>
          )}

          {invalidToken && (
            <div className="p-6 text-center">
              <div className="mb-4 flex justify-center">
                <span className="inline-flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-red-600 dark:text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </span>
              </div>
              <InfoAlert className="mb-6">
                <h3 className="text-lg font-medium mb-2">
                  Invalid Reset Link
                </h3>
                <p className="text-sm">
                  This password reset link is invalid or has expired. Please request a new one.
                </p>
              </InfoAlert>
              <Button
                label="Go to Login"
                onClick={() => router.push('/sadmin/login')}
                className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] px-6 py-2 rounded-lg text-white shadow-md font-medium hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#8178E8] focus:ring-offset-2"
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Main page component
export default function ResetPasswordPage() {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading reset password page...</div>}>
      <ResetPasswordContent />
    </Suspense>
  );
} 
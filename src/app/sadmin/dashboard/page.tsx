'use client';

import { useState, useEffect } from 'react';
import { getCurrentAdmin } from '@/server/actions/admin-auth';
import { getDashboardStats, getRecentActivity, getSystemHealth, getUserAnalytics, getSecurityAnalytics } from '@/server/actions/dashboard';
import { Alert } from '@/components/ui/Alert';
import { useToast } from '@/components/ui/Toast';
import StatsOverview from './components/StatsOverview';
import RecentActivity from './components/RecentActivity';
import SystemHealth from './components/SystemHealth';
import QuickActions from './components/QuickActions';
import UserActivityChart from './components/UserActivityChart';
import AnalyticsOverview from './components/AnalyticsOverview';

import { BarChart3 } from 'lucide-react';
import { logger } from '@/utils/logger';

export default function DashboardPage() {
  const { success, error: showError } = useToast();

  // State management
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Dashboard data state
  const [dashboardStats, setDashboardStats] = useState<any>(null);
  const [recentActivity, setRecentActivity] = useState<any[]>([]);
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const [userAnalytics, setUserAnalytics] = useState<any>(null);
  const [securityAnalytics, setSecurityAnalytics] = useState<any>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [activityLoading, setActivityLoading] = useState(true);
  const [healthLoading, setHealthLoading] = useState(true);
  const [analyticsLoading, setAnalyticsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [errors, setErrors] = useState<{
    stats?: string;
    activity?: string;
    health?: string;
    analytics?: string;
  }>({});

  // Load all dashboard data
  const loadDashboardData = async () => {
    try {
      // Load user data
      const userResult = await getCurrentAdmin();
      if (userResult.success) {
        setUser(userResult.data);
      } else {
        setError(userResult.error || 'Failed to load user data');
        return;
      }

      // Load dashboard stats
      setStatsLoading(true);
      const statsResult = await getDashboardStats();
      if (statsResult.success) {
        setDashboardStats(statsResult.data);
        setErrors(prev => ({ ...prev, stats: undefined }));
      } else {
        logger.error('Failed to load dashboard stats:', statsResult.error);
        setErrors(prev => ({ ...prev, stats: statsResult.error }));
      }
      setStatsLoading(false);

      // Load recent activity
      setActivityLoading(true);
      const activityResult = await getRecentActivity(10);
      if (activityResult.success) {
        setRecentActivity(activityResult.data || []);
        setErrors(prev => ({ ...prev, activity: undefined }));
      } else {
        logger.error('Failed to load recent activity:', activityResult.error);
        setErrors(prev => ({ ...prev, activity: activityResult.error }));
      }
      setActivityLoading(false);

      // Load system health
      setHealthLoading(true);
      const healthResult = await getSystemHealth();
      if (healthResult.success) {
        setSystemHealth(healthResult.data);
        setErrors(prev => ({ ...prev, health: undefined }));
      } else {
        logger.error('Failed to load system health:', healthResult.error);
        setErrors(prev => ({ ...prev, health: healthResult.error }));
      }
      setHealthLoading(false);

      // Load analytics data
      setAnalyticsLoading(true);
      const [userAnalyticsResult, securityAnalyticsResult] = await Promise.all([
        getUserAnalytics(timeRange),
        getSecurityAnalytics(timeRange)
      ]);

      if (userAnalyticsResult.success) {
        setUserAnalytics(userAnalyticsResult.data);
      } else {
        logger.error('Failed to load user analytics:', userAnalyticsResult.error);
        setErrors(prev => ({ ...prev, analytics: userAnalyticsResult.error }));
      }

      if (securityAnalyticsResult.success) {
        setSecurityAnalytics(securityAnalyticsResult.data);
      } else {
        logger.error('Failed to load security analytics:', securityAnalyticsResult.error);
        setErrors(prev => ({ ...prev, analytics: securityAnalyticsResult.error }));
      }
      setAnalyticsLoading(false);

    } catch (error) {
      logger.error('Failed to load dashboard data:', error);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  // Reload analytics data when time range changes
  useEffect(() => {
    const loadAnalyticsData = async () => {
      setAnalyticsLoading(true);
      const [userAnalyticsResult, securityAnalyticsResult] = await Promise.all([
        getUserAnalytics(timeRange),
        getSecurityAnalytics(timeRange)
      ]);

      if (userAnalyticsResult.success) {
        setUserAnalytics(userAnalyticsResult.data);
        setErrors(prev => ({ ...prev, analytics: undefined }));
      } else {
        logger.error('Failed to load user analytics:', userAnalyticsResult.error);
        setErrors(prev => ({ ...prev, analytics: userAnalyticsResult.error }));
      }

      if (securityAnalyticsResult.success) {
        setSecurityAnalytics(securityAnalyticsResult.data);
      } else {
        logger.error('Failed to load security analytics:', securityAnalyticsResult.error);
        setErrors(prev => ({ ...prev, analytics: securityAnalyticsResult.error }));
      }
      setAnalyticsLoading(false);
    };

    loadAnalyticsData();
  }, [timeRange]);

  // Action handlers
  const handleRefreshHealth = async () => {
    setHealthLoading(true);
    const healthResult = await getSystemHealth();
    if (healthResult.success) {
      setSystemHealth(healthResult.data);
      success('System health refreshed');
    } else {
      showError('Failed to refresh system health');
    }
    setHealthLoading(false);
  };

  const handleViewAllActivity = () => {
    // Navigate to activity logs page
    window.location.href = '/sadmin/logs';
  };

  const handleCreateUser = () => {
    window.location.href = '/sadmin/users/new';
  };

  const handleCreatePermissionGroup = () => {
    window.location.href = '/sadmin/permissions';
  };

  const handleExportData = () => {
    success('Export functionality coming soon');
  };

  const handleImportData = () => {
    success('Import functionality coming soon');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="px-4 sm:px-6 lg:px-8 py-8">
          <StatsOverview loading={true} />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
        <div className="px-4 sm:px-6 lg:px-8 py-8">
          <Alert severity="error">
            {error}
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-xl">
            <BarChart3 className="w-6 h-6 text-[#6964D3]" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent">
              Admin Dashboard
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Welcome back, {user?.name}! Here's what's happening with your system.
            </p>
          </div>
        </div>
      </div>



      {/* Stats Overview */}
      <StatsOverview
        data={dashboardStats}
        loading={statsLoading}
        error={statsLoading ? undefined : errors.stats}
      />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Charts and Activity */}
        <div className="lg:col-span-2 space-y-6">
          {/* User Activity Chart */}
          <UserActivityChart
            data={userAnalytics?.activityTrend}
            loading={analyticsLoading}
            error={analyticsLoading ? undefined : errors.analytics}
            timeRange={timeRange}
            onTimeRangeChange={setTimeRange}
          />

          {/* Recent Activity */}
          <RecentActivity
            activities={recentActivity}
            loading={activityLoading}
            error={activityLoading ? undefined : errors.activity}
            onViewAll={handleViewAllActivity}
          />
        </div>

        {/* Right Column - System Health */}
        <div className="space-y-6">
          <SystemHealth
            data={systemHealth}
            loading={healthLoading}
            error={healthLoading ? undefined : errors.health}
            onRefresh={handleRefreshHealth}
          />
        </div>
      </div>

      {/* Analytics Overview */}
      <AnalyticsOverview
        userAnalytics={userAnalytics}
        securityAnalytics={securityAnalytics}
        loading={analyticsLoading}
        timeRange={timeRange}
        onTimeRangeChange={setTimeRange}
      />

      {/* Quick Actions */}
      <QuickActions
        onCreateUser={handleCreateUser}
        onCreatePermissionGroup={handleCreatePermissionGroup}
        onViewLogs={handleViewAllActivity}
        onExportData={handleExportData}
        onImportData={handleImportData}
      />
    </div>
  );
}

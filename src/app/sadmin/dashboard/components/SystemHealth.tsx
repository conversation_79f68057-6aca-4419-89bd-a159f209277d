'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  Server,
  Database,
  Shield,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw
} from 'lucide-react';

interface SystemMetric {
  name: string;
  status: 'healthy' | 'warning' | 'critical';
  value?: string;
  description: string;
  lastChecked: string;
}

interface SystemHealthData {
  overall: 'healthy' | 'warning' | 'critical';
  metrics: SystemMetric[];
  uptime: string;
  lastUpdate: string;
}

interface SystemHealthProps {
  data?: SystemHealthData;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
}

export default function SystemHealth({ data, loading, error, onRefresh }: SystemHealthProps) {
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (onRefresh) {
      setRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setRefreshing(false);
      }
    }
  };

  const getStatusIcon = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy':
        return CheckCircle;
      case 'warning':
        return AlertTriangle;
      case 'critical':
        return XCircle;
    }
  };

  const getStatusColor = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy':
        return 'text-green-500';
      case 'warning':
        return 'text-yellow-500';
      case 'critical':
        return 'text-red-500';
    }
  };

  const getStatusBadgeVariant = (status: 'healthy' | 'warning' | 'critical') => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'danger';
    }
  };

  const getMetricIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'database':
      case 'database connection':
        return Database;
      case 'api server':
        return Server;
      case 'environment variables':
        return Shield;
      case 'system activity':
        return Cpu;
      case 'network':
        return Wifi;
      case 'disk space':
        return HardDrive;
      case 'memory usage':
        return MemoryStick;
      default:
        return Server;
    }
  };

  if (loading) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-20" />
          </div>
          <div className="space-y-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Skeleton className="h-8 w-8 rounded-lg" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
                <Skeleton className="h-5 w-16" />
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-red-200 dark:border-red-800 rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <XCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 dark:text-red-400">Failed to load system health</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="mt-3"
            >
              Try Again
            </Button>
          )}
        </div>
      </Card>
    );
  }

  // Only use real data - no fallback to mock data
  if (!data) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-yellow-200 dark:border-yellow-800 rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <AlertTriangle className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
          <p className="text-yellow-600 dark:text-yellow-400">System health data unavailable</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Unable to retrieve system health metrics from the server
          </p>
          {onRefresh && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="mt-3"
            >
              <RefreshCw className={`w-3 h-3 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Retry
            </Button>
          )}
        </div>
      </Card>
    );
  }

  const metrics = data.metrics;
  const overallStatus = data.overall;
  const uptime = data.uptime;

  return (
    <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg">
              <Server className="w-4 h-4 text-[#6964D3]" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                System Health
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                System Uptime: {uptime}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge
              variant={getStatusBadgeVariant(overallStatus)}
              className="text-xs"
            >
              {overallStatus}
            </Badge>
            {onRefresh && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="p-2"
              >
                <RefreshCw className={`w-3 h-3 ${refreshing ? 'animate-spin' : ''}`} />
              </Button>
            )}
          </div>
        </div>

        {/* Metrics */}
        <div className="space-y-4">
          {metrics.map((metric, index) => {
            const Icon = getMetricIcon(metric.name);
            const StatusIcon = getStatusIcon(metric.status);
            const statusColor = getStatusColor(metric.status);

            return (
              <div key={index} className="flex items-center justify-between group">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg group-hover:bg-gray-200 dark:group-hover:bg-gray-700 transition-colors">
                    <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {metric.name}
                      </span>
                      {metric.value && (
                        <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                          {metric.value}
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {metric.description}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      Last checked: {new Date(metric.lastChecked).toLocaleTimeString()}
                    </p>
                  </div>
                </div>

                <StatusIcon className={`w-5 h-5 ${statusColor}`} />
              </div>
            );
          })}
        </div>

        {/* Last Update */}
        {data?.lastUpdate && (
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              Last updated: {new Date(data.lastUpdate).toLocaleString()}
            </p>
          </div>
        )}
      </div>
    </Card>
  );
}

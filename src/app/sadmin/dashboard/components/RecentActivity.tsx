'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import {
  User,
  Shield,
  Key,
  LogIn,
  LogOut,
  Settings,
  AlertTriangle,
  Clock,
  ExternalLink
} from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'user_login' | 'user_logout' | 'user_created' | 'permission_changed' | 'secret_accessed' | 'settings_updated' | 'security_alert';
  description: string;
  user: {
    name: string;
    email: string;
  };
  timestamp: string;
  metadata?: {
    ipAddress?: string;
    userAgent?: string;
    resource?: string;
  };
}

interface RecentActivityProps {
  activities?: ActivityItem[];
  loading?: boolean;
  error?: string;
  onViewAll?: () => void;
}

export default function RecentActivity({ activities = [], loading, error, onViewAll }: RecentActivityProps) {
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_login':
        return LogIn;
      case 'user_logout':
        return LogOut;
      case 'user_created':
        return User;
      case 'permission_changed':
        return Shield;
      case 'secret_accessed':
        return Key;
      case 'settings_updated':
        return Settings;
      case 'security_alert':
        return AlertTriangle;
      default:
        return Clock;
    }
  };

  const getActivityColor = (type: ActivityItem['type']) => {
    switch (type) {
      case 'user_login':
        return 'from-green-500/20 to-green-600/20';
      case 'user_logout':
        return 'from-gray-500/20 to-gray-600/20';
      case 'user_created':
        return 'from-blue-500/20 to-blue-600/20';
      case 'permission_changed':
        return 'from-[#8178E8]/20 to-[#6964D3]/20';
      case 'secret_accessed':
        return 'from-purple-500/20 to-purple-600/20';
      case 'settings_updated':
        return 'from-orange-500/20 to-orange-600/20';
      case 'security_alert':
        return 'from-red-500/20 to-red-600/20';
      default:
        return 'from-gray-500/20 to-gray-600/20';
    }
  };

  const getBadgeVariant = (type: ActivityItem['type']) => {
    switch (type) {
      case 'security_alert':
        return 'danger';
      case 'user_login':
        return 'success';
      case 'permission_changed':
        return 'warning';
      default:
        return 'default';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  if (loading) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-20" />
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="flex items-start gap-4">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-red-200 dark:border-red-800 rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 dark:text-red-400">Failed to load recent activity</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg">
              <Clock className="w-4 h-4 text-[#6964D3]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Recent Activity
            </h3>
          </div>
          {onViewAll && (
            <Button
              variant="outline"
              size="sm"
              onClick={onViewAll}
              className="flex items-center gap-2"
            >
              View All
              <ExternalLink className="w-3 h-3" />
            </Button>
          )}
        </div>

        {/* Activity List */}
        {activities.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500 dark:text-gray-400">No recent activity</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activities.slice(0, 8).map((activity) => {
              const Icon = getActivityIcon(activity.type);
              const colorClass = getActivityColor(activity.type);

              return (
                <div key={activity.id} className="flex items-start gap-4 group">
                  <div className={`p-2 bg-gradient-to-r ${colorClass} rounded-lg group-hover:scale-110 transition-transform`}>
                    <Icon className="w-4 h-4 text-[#6964D3]" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <div className="flex-1">
                        <p className="text-sm text-gray-900 dark:text-white font-medium">
                          {activity.description}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            by {activity.user.name}
                          </span>
                          <Badge
                            variant={getBadgeVariant(activity.type)}
                            className="text-xs"
                          >
                            {activity.type.replace('_', ' ')}
                          </Badge>
                        </div>
                        {activity.metadata?.ipAddress && (
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            from {activity.metadata.ipAddress}
                          </p>
                        )}
                      </div>

                      <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                        {formatTimeAgo(activity.timestamp)}
                      </span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </Card>
  );
}

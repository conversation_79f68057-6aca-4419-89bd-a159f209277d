'use client';

import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  UserPlus,
  Shield,
  Settings,
  FileText,
  Users,
  Key,
  BarChart3,
  Download,
  Upload,
  Zap
} from 'lucide-react';
import Link from 'next/link';

interface QuickAction {
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  href?: string;
  onClick?: () => void;
  color: string;
  bgColor: string;
  disabled?: boolean;
}

interface QuickActionsProps {
  onCreateUser?: () => void;
  onCreatePermissionGroup?: () => void;
  onViewLogs?: () => void;
  onExportData?: () => void;
  onImportData?: () => void;
}

export default function QuickActions({
  onCreateUser,
  onCreatePermissionGroup,
  onViewLogs,
  onExportData,
  onImportData
}: QuickActionsProps) {
  const actions: QuickAction[] = [
    {
      title: 'Create User',
      description: 'Add a new user to the system',
      icon: UserPlus,
      href: '/sadmin/users/new',
      onClick: onCreateUser,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-500/20 to-blue-600/20'
    },
    {
      title: 'Manage Permissions',
      description: 'Create or edit permission groups',
      icon: Shield,
      href: '/sadmin/permissions',
      onClick: onCreatePermissionGroup,
      color: 'from-[#8178E8] to-[#6964D3]',
      bgColor: 'from-[#8178E8]/20 to-[#6964D3]/20'
    },
    {
      title: 'View All Users',
      description: 'Browse and manage user accounts',
      icon: Users,
      href: '/sadmin/users',
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-500/20 to-green-600/20'
    },
    {
      title: 'System Settings',
      description: 'Configure application settings',
      icon: Settings,
      href: '/sadmin/settings',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'from-orange-500/20 to-orange-600/20'
    },
    {
      title: 'Activity Logs',
      description: 'Review system activity and audit logs',
      icon: FileText,
      onClick: onViewLogs,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-500/20 to-purple-600/20'
    },
    {
      title: 'Analytics',
      description: 'View detailed system analytics',
      icon: BarChart3,
      href: '/sadmin/analytics',
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'from-indigo-500/20 to-indigo-600/20',
      disabled: true
    },
    {
      title: 'Export Data',
      description: 'Export system data and reports',
      icon: Download,
      onClick: onExportData,
      color: 'from-teal-500 to-teal-600',
      bgColor: 'from-teal-500/20 to-teal-600/20'
    },
    {
      title: 'Import Data',
      description: 'Import users or configuration data',
      icon: Upload,
      onClick: onImportData,
      color: 'from-pink-500 to-pink-600',
      bgColor: 'from-pink-500/20 to-pink-600/20'
    }
  ];

  const ActionCard = ({ action }: { action: QuickAction }) => {
    const Icon = action.icon;

    const cardContent = (
      <div className={`p-6 h-full transition-all duration-300 group ${
        action.disabled
          ? 'opacity-50 cursor-not-allowed'
          : 'hover:shadow-lg hover:shadow-[#8178E8]/10 hover:-translate-y-1 cursor-pointer'
      }`}>
        <div className="flex items-start gap-4">
          <div className={`p-3 bg-gradient-to-r ${action.bgColor} rounded-xl group-hover:scale-110 transition-transform`}>
            <Icon className="w-5 h-5 text-[#6964D3]" />
          </div>
          <div className="flex-1">
            <h3 className="text-base font-semibold text-gray-900 dark:text-white mb-1">
              {action.title}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
              {action.description}
            </p>
          </div>
        </div>

        {action.disabled && (
          <div className="mt-3">
            <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
              Coming Soon
            </span>
          </div>
        )}
      </div>
    );

    if (action.disabled) {
      return (
        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
          {cardContent}
        </Card>
      );
    }

    if (action.href) {
      return (
        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
          <Link href={action.href} className="block h-full">
            {cardContent}
          </Link>
        </Card>
      );
    }

    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
        <button
          onClick={action.onClick}
          className="w-full h-full text-left"
        >
          {cardContent}
        </button>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg">
          <Zap className="w-4 h-4 text-[#6964D3]" />
        </div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Quick Actions
        </h2>
      </div>

      {/* Actions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {actions.map((action, index) => (
          <ActionCard key={index} action={action} />
        ))}
      </div>

      {/* Additional Actions */}
      <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          onClick={() => window.location.reload()}
        >
          <Zap className="w-3 h-3" />
          Refresh Dashboard
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          asChild
        >
          <Link href="/sadmin/settings">
            <Settings className="w-3 h-3" />
            System Settings
          </Link>
        </Button>

        <Button
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
          asChild
        >
          <Link href="/sadmin/users">
            <Users className="w-3 h-3" />
            Manage Users
          </Link>
        </Button>
      </div>
    </div>
  );
}

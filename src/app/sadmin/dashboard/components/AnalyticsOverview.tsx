'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Shield, 
  AlertTriangle,
  PieChart,
  Activity,
  Calendar
} from 'lucide-react';

interface UserAnalytics {
  registrationTrend: Array<{
    date: string;
    count: number;
    cumulative: number;
  }>;
  activityTrend: Array<{
    date: string;
    logins: number;
    activeUsers: number;
  }>;
  usersByStatus: {
    active: number;
    inactive: number;
    emailVerified: number;
    emailUnverified: number;
  };
  topCompanies: Array<{
    company: string;
    userCount: number;
  }>;
}

interface SecurityAnalytics {
  failedLoginAttempts: Array<{
    date: string;
    count: number;
    uniqueIPs: number;
  }>;
  suspiciousActivities: Array<{
    type: string;
    count: number;
    lastOccurrence: string;
  }>;
  twoFactorAdoption: {
    enabled: number;
    disabled: number;
    percentage: number;
  };
}

interface AnalyticsOverviewProps {
  userAnalytics?: UserAnalytics;
  securityAnalytics?: SecurityAnalytics;
  loading?: boolean;
  error?: string;
  timeRange?: '7d' | '30d' | '90d';
  onTimeRangeChange?: (range: '7d' | '30d' | '90d') => void;
}

export default function AnalyticsOverview({ 
  userAnalytics, 
  securityAnalytics, 
  loading, 
  error, 
  timeRange = '30d',
  onTimeRangeChange 
}: AnalyticsOverviewProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-8 w-24" />
              </div>
              <Skeleton className="h-48 w-full" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-red-200 dark:border-red-800 rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 dark:text-red-400">Failed to load analytics</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
        </div>
      </Card>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const calculateTrend = (data: number[]) => {
    if (data.length < 2) return 0;
    const recent = data.slice(-3).reduce((sum, val) => sum + val, 0) / 3;
    const previous = data.slice(-6, -3).reduce((sum, val) => sum + val, 0) / 3;
    if (previous === 0) return recent > 0 ? 100 : 0;
    return ((recent - previous) / previous) * 100;
  };

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg">
            <BarChart3 className="w-4 h-4 text-[#6964D3]" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Analytics Overview
          </h2>
        </div>
        
        {onTimeRangeChange && (
          <div className="flex gap-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {(['7d', '30d', '90d'] as const).map((range) => (
              <Button
                key={range}
                variant={timeRange === range ? 'default' : 'ghost'}
                size="sm"
                onClick={() => onTimeRangeChange(range)}
                className="text-xs px-3 py-1"
              >
                {range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : '90 Days'}
              </Button>
            ))}
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Registration Trend */}
        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-500/20 to-blue-600/20 rounded-lg">
                  <Users className="w-4 h-4 text-[#6964D3]" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  User Registrations
                </h3>
              </div>
              {userAnalytics && (
                <Badge variant="default" className="text-xs">
                  {userAnalytics.registrationTrend.reduce((sum, day) => sum + day.count, 0)} total
                </Badge>
              )}
            </div>
            
            {userAnalytics ? (
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userAnalytics.usersByStatus.active}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Active Users</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {userAnalytics.usersByStatus.emailVerified}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Verified</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  {userAnalytics.registrationTrend.slice(-7).map((day, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>{formatDate(day.date)}</span>
                        <span>{day.count} registrations</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ 
                            width: `${Math.max(5, (day.count / Math.max(...userAnalytics.registrationTrend.map(d => d.count))) * 100)}%` 
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No registration data available
              </div>
            )}
          </div>
        </Card>

        {/* Security Overview */}
        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-red-500/20 to-red-600/20 rounded-lg">
                  <Shield className="w-4 h-4 text-[#6964D3]" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Security Events
                </h3>
              </div>
              {securityAnalytics && (
                <Badge 
                  variant={
                    securityAnalytics.failedLoginAttempts.reduce((sum, day) => sum + day.count, 0) > 100 
                      ? 'danger' : 'success'
                  } 
                  className="text-xs"
                >
                  {securityAnalytics.failedLoginAttempts.reduce((sum, day) => sum + day.count, 0)} failed logins
                </Badge>
              )}
            </div>
            
            {securityAnalytics ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {securityAnalytics.twoFactorAdoption.percentage}%
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">2FA Adoption</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">
                      {securityAnalytics.suspiciousActivities.length}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">Alert Types</div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Recent Security Events</h4>
                  {securityAnalytics.suspiciousActivities.slice(0, 5).map((activity, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400 truncate">
                        {activity.type}
                      </span>
                      <Badge variant="warning" className="text-xs">
                        {activity.count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                No security data available
              </div>
            )}
          </div>
        </Card>

        {/* Top Companies */}
        {userAnalytics?.topCompanies && userAnalytics.topCompanies.length > 0 && (
          <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-gradient-to-r from-purple-500/20 to-purple-600/20 rounded-lg">
                  <PieChart className="w-4 h-4 text-[#6964D3]" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Top Companies
                </h3>
              </div>
              
              <div className="space-y-3">
                {userAnalytics.topCompanies.slice(0, 8).map((company, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                      {company.company}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-purple-500 to-purple-600 h-2 rounded-full"
                          style={{ 
                            width: `${(company.userCount / Math.max(...userAnalytics.topCompanies.map(c => c.userCount))) * 100}%` 
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white w-8 text-right">
                        {company.userCount}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        )}

        {/* Activity Trend */}
        {userAnalytics?.activityTrend && (
          <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
            <div className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg">
                  <Activity className="w-4 h-4 text-[#6964D3]" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Login Activity
                </h3>
              </div>
              
              <div className="space-y-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {userAnalytics.activityTrend.reduce((sum, day) => sum + day.logins, 0)}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">Total Logins</div>
                </div>
                
                <div className="space-y-2">
                  {userAnalytics.activityTrend.slice(-7).map((day, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>{formatDate(day.date)}</span>
                        <span>{day.logins} logins</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
                          style={{ 
                            width: `${Math.max(5, (day.logins / Math.max(...userAnalytics.activityTrend.map(d => d.logins))) * 100)}%` 
                          }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}

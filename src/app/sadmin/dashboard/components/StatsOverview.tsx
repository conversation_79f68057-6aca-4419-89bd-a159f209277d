'use client';


import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { Badge } from '@/components/ui/Badge';
import {
  Users,
  Shield,
  Activity,
  Key,
  TrendingUp,
  TrendingDown,
  Database,
  Server
} from 'lucide-react';

interface StatsData {
  totalUsers: number;
  activeUsers: number;
  permissionGroups: number;
  environmentVariables: number;
  activeSessions: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  userGrowth: number;
  sessionGrowth: number;
  totalAdmins: number;
  failedLogins: number;
  apiCalls: number;
  avgResponseTime: number;
}

interface StatsOverviewProps {
  data?: StatsData;
  loading?: boolean;
  error?: string;
}

export default function StatsOverview({ data, loading, error }: StatsOverviewProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <Skeleton className="h-5 w-16" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-8 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-red-200 dark:border-red-800 rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <p className="text-red-600 dark:text-red-400">{error}</p>
        </div>
      </Card>
    );
  }

  const stats = [
    {
      title: 'Total Users',
      value: data?.totalUsers || 0,
      subtitle: `${data?.activeUsers || 0} active (last 30 days)`,
      icon: Users,
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-500/20 to-blue-600/20',
      change: data?.userGrowth || 0,
      changeLabel: 'vs previous month'
    },
    {
      title: 'Permission Groups',
      value: data?.permissionGroups || 0,
      subtitle: `${data?.totalAdmins || 0} total admins`,
      icon: Shield,
      color: 'from-[#8178E8] to-[#6964D3]',
      bgColor: 'from-[#8178E8]/20 to-[#6964D3]/20',
      change: 0,
      changeLabel: 'access control groups'
    },
    {
      title: 'Active Sessions',
      value: data?.activeSessions || 0,
      subtitle: 'Currently logged in',
      icon: Activity,
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-500/20 to-green-600/20',
      change: data?.sessionGrowth || 0,
      changeLabel: 'vs previous day'
    },
    {
      title: 'Environment Variables',
      value: data?.environmentVariables || 0,
      subtitle: 'Encrypted secrets stored',
      icon: Key,
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-500/20 to-purple-600/20',
      change: 0,
      changeLabel: 'total secrets'
    },
    {
      title: 'System Activity',
      value: data?.apiCalls || 0,
      subtitle: 'API calls (last 24h)',
      icon: Server,
      color: 'from-orange-500 to-orange-600',
      bgColor: 'from-orange-500/20 to-orange-600/20',
      change: 0,
      changeLabel: 'audit log entries'
    },
    {
      title: 'Security Events',
      value: data?.failedLogins || 0,
      subtitle: `Failed logins (last 24h)`,
      icon: Database,
      color: 'from-red-500 to-red-600',
      bgColor: 'from-red-500/20 to-red-600/20',
      change: 0,
      changeLabel: `${data?.avgResponseTime || 0}ms avg response`
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        const isPositive = stat.change >= 0;

        return (
          <Card
            key={index}
            className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300 hover:-translate-y-1 group"
          >
            <div className="p-6 space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className={`p-3 bg-gradient-to-r ${stat.bgColor} rounded-xl group-hover:scale-110 transition-transform`}>
                  <Icon className="w-5 h-5 text-[#6964D3]" />
                </div>
                {stat.title === 'Active Sessions' && data?.systemHealth && (
                  <Badge
                    variant={
                      data.systemHealth === 'healthy' ? 'success' :
                      data.systemHealth === 'warning' ? 'warning' : 'danger'
                    }
                    className="text-xs"
                  >
                    {data.systemHealth}
                  </Badge>
                )}
              </div>

              {/* Value */}
              <div className="space-y-1">
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {stat.value.toLocaleString()}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {stat.subtitle}
                </div>
              </div>

              {/* Change indicator */}
              {stat.change !== 0 && (
                <div className="flex items-center gap-2">
                  {isPositive ? (
                    <TrendingUp className="w-4 h-4 text-green-500" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-red-500" />
                  )}
                  <span className={`text-sm font-medium ${
                    isPositive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                  }`}>
                    {isPositive ? '+' : ''}{stat.change}%
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {stat.changeLabel}
                  </span>
                </div>
              )}
            </div>
          </Card>
        );
      })}
    </div>
  );
}

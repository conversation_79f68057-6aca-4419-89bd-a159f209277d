'use client';

import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { Button } from '@/components/ui/Button';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
} from 'lucide-react';

interface ActivityDataPoint {
  date: string;
  logins: number;
  activeUsers: number;
  newUsers: number;
}

interface UserActivityChartProps {
  data?: ActivityDataPoint[];
  loading?: boolean;
  error?: string;
  timeRange?: '7d' | '30d' | '90d';
  onTimeRangeChange?: (range: '7d' | '30d' | '90d') => void;
}

export default function UserActivityChart({
  data = [],
  loading,
  error,
  timeRange = '7d',
  onTimeRangeChange
}: UserActivityChartProps) {
  // Use real data or show empty state
  const chartData = data && data.length > 0 ? data : [];

  const calculateTrend = (data: ActivityDataPoint[], field: keyof ActivityDataPoint) => {
    if (data.length < 2) return 0;

    const recent = data.slice(-3).reduce((sum, item) => sum + (item[field] as number), 0) / 3;
    const previous = data.slice(-6, -3).reduce((sum, item) => sum + (item[field] as number), 0) / 3;

    if (previous === 0) return 0;
    return ((recent - previous) / previous) * 100;
  };

  const loginTrend = calculateTrend(chartData, 'logins');
  const activeUsersTrend = calculateTrend(chartData, 'activeUsers');
  const newUsersTrend = calculateTrend(chartData, 'newUsers');

  const maxValue = Math.max(...chartData.map(d => Math.max(d.logins, d.activeUsers, d.newUsers)));

  if (loading) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-8 w-32" />
          </div>
          <div className="space-y-4 mb-6">
            <div className="flex gap-4">
              <Skeleton className="h-16 w-24" />
              <Skeleton className="h-16 w-24" />
              <Skeleton className="h-16 w-24" />
            </div>
          </div>
          <Skeleton className="h-48 w-full" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-red-200 dark:border-red-800 rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <BarChart3 className="w-8 h-8 text-red-500 mx-auto mb-2" />
          <p className="text-red-600 dark:text-red-400">Failed to load activity chart</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
        </div>
      </Card>
    );
  }

  if (chartData.length === 0) {
    return (
      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
        <div className="p-6 text-center">
          <BarChart3 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-600 dark:text-gray-400">No activity data available</p>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            Activity data will appear here once users start logging in
          </p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg">
              <BarChart3 className="w-4 h-4 text-[#6964D3]" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              User Activity
            </h3>
          </div>

          {onTimeRangeChange && (
            <div className="flex gap-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              {(['7d', '30d', '90d'] as const).map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => onTimeRangeChange(range)}
                  className="text-xs px-3 py-1"
                >
                  {range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : '90 Days'}
                </Button>
              ))}
            </div>
          )}
        </div>

        {/* Metrics Summary */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {chartData.reduce((sum, d) => sum + d.logins, 0)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Total Logins</div>
            <div className="flex items-center justify-center gap-1 mt-1">
              {loginTrend >= 0 ? (
                <TrendingUp className="w-3 h-3 text-green-500" />
              ) : (
                <TrendingDown className="w-3 h-3 text-red-500" />
              )}
              <span className={`text-xs ${loginTrend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(loginTrend).toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {Math.round(chartData.reduce((sum, d) => sum + d.activeUsers, 0) / chartData.length)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Avg Active Users</div>
            <div className="flex items-center justify-center gap-1 mt-1">
              {activeUsersTrend >= 0 ? (
                <TrendingUp className="w-3 h-3 text-green-500" />
              ) : (
                <TrendingDown className="w-3 h-3 text-red-500" />
              )}
              <span className={`text-xs ${activeUsersTrend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(activeUsersTrend).toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {chartData.reduce((sum, d) => sum + d.newUsers, 0)}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">New Users</div>
            <div className="flex items-center justify-center gap-1 mt-1">
              {newUsersTrend >= 0 ? (
                <TrendingUp className="w-3 h-3 text-green-500" />
              ) : (
                <TrendingDown className="w-3 h-3 text-red-500" />
              )}
              <span className={`text-xs ${newUsersTrend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {Math.abs(newUsersTrend).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>

        {/* Simple Bar Chart */}
        <div className="space-y-3">
          <div className="flex items-center gap-4 text-xs">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">Logins</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">Active Users</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded"></div>
              <span className="text-gray-600 dark:text-gray-400">New Users</span>
            </div>
          </div>

          <div className="space-y-2">
            {chartData.slice(-7).map((dataPoint, index) => (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{new Date(dataPoint.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}</span>
                  <span>{dataPoint.logins} logins</span>
                </div>
                <div className="flex gap-1 h-6">
                  <div
                    className="bg-blue-500 rounded-sm transition-all duration-300"
                    style={{ width: `${(dataPoint.logins / maxValue) * 100}%` }}
                  />
                  <div
                    className="bg-green-500 rounded-sm transition-all duration-300"
                    style={{ width: `${(dataPoint.activeUsers / maxValue) * 100}%` }}
                  />
                  <div
                    className="bg-purple-500 rounded-sm transition-all duration-300"
                    style={{ width: `${(dataPoint.newUsers / maxValue) * 100}%` }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}

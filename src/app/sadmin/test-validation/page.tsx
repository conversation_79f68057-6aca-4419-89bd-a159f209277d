'use client';

import { useState } from 'react';
import { changePasswordSchema } from '@/utils/profile-validators';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Form, FormField } from '@/components/ui/Form';
import { Alert, SuccessAlert } from '@/components/ui/Alert';
import { Card, CardContent } from '@/components/ui/Card';
import { logger } from '@/utils/logger';

export default function TestValidationPage() {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [validationResult, setValidationResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleValidation = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    try {
      // Test with direct validation (standard approach)
      
      const result = changePasswordSchema.safeParse({
        currentPassword,
        newPassword,
        confirmPassword
      });
      
      // Log the raw result for debugging
      
      
      setValidationResult(result);
      
      // Test the refine logic explicitly
      if (newPassword !== confirmPassword) {
        
      } else {
        
      }
      
    } catch (error) {
      logger.error('Validation error:', error);
      setError('An unexpected error occurred during validation');
      setValidationResult({ success: false, error: 'Validation schema error occurred' });
    }
  };

  const reset = () => {
    setCurrentPassword('');
    setNewPassword('');
    setConfirmPassword('');
    setValidationResult(null);
    setError(null);
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-semibold mb-4">Test Password Validation</h1>
      
      <Card>
        <CardContent>
          <Form onSubmit={handleValidation} className="space-y-4">
            <FormField label="Current Password" htmlFor="currentPassword">
              <Input
                id="currentPassword"
                type="password"
                value={currentPassword}
                onChange={e => setCurrentPassword(e.target.value)}
                placeholder="Enter current password"
              />
            </FormField>
            
            <FormField label="New Password" htmlFor="newPassword">
              <Input
                id="newPassword"
                type="password"
                value={newPassword}
                onChange={e => setNewPassword(e.target.value)}
                placeholder="Enter new password (min 8 characters)"
              />
            </FormField>
            
            <FormField label="Confirm New Password" htmlFor="confirmPassword">
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                placeholder="Confirm your new password"
              />
            </FormField>
            
            <div className="flex space-x-2">
              <Button type="submit" variant="primary">Validate</Button>
              <Button type="button" variant="secondary" onClick={reset}>Reset</Button>
            </div>
          </Form>
          
          {error && (
            <div className="mt-6">
              <Alert severity="error">{error}</Alert>
            </div>
          )}
          
          {validationResult && (
            <div className="mt-6">
              <h2 className="text-lg font-medium mb-2">Validation Result:</h2>
              
              {validationResult.success ? (
                <SuccessAlert>Validation passed!</SuccessAlert>
              ) : (
                <div className="space-y-2">
                  <Alert severity="error">Validation failed</Alert>
                  <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto">
                    <h3 className="text-sm font-medium mb-2">Details:</h3>
                    <pre className="text-sm whitespace-pre-wrap">
                      {JSON.stringify(validationResult.error?.format?.(), null, 2)}
                    </pre>
                  </div>
                </div>
              )}
            </div>
          )}
          
          <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
            <h3 className="font-medium">Validation Rules:</h3>
            <ul className="list-disc pl-5 mt-2">
              <li>Current password is required</li>
              <li>New password must be at least 8 characters</li>
              <li>New password and confirmation must match</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 

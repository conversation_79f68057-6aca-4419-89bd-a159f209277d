'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Dialog } from '@/components/ui/Dialog';
import { getAllFaqs, createFaq, updateFaq, deleteFaq } from '@/server/actions/faq-actions';
import { useToast } from '@/components/ui/Toast';
import { ConfirmDialog, confirmDialog } from '@/components/ui/ConfirmDialog';
import { Select } from "@components/ui/Select";
import { Input } from "@components/ui/Input";
import { Textarea } from "@components/ui/Textarea";
import { Checkbox } from "@components/ui/Checkbox";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

type GetAllFaqsType = Awaited<ReturnType<typeof getAllFaqs>>
type FaqItem = NonNullable<GetAllFaqsType['data']>[number];

// Form validation schema
const faqFormSchema = z.object({
  question: z.string()
    .min(5, { message: "Question must be at least 5 characters" })
    .max(200, { message: "Question must be less than 200 characters" }),
  answer: z.string()
    .min(10, { message: "Answer must be at least 10 characters" })
    .max(2000, { message: "Answer must be less than 2000 characters" }),
  type: z.enum(["billing", "product"], {
    required_error: "Please select a type"
  }),
  active: z.boolean().default(true),
  order: z.number().int().nonnegative({ message: "Order must be a positive number" }).default(0)
});

type FaqFormValues = z.infer<typeof faqFormSchema>;

export default function FaqsPage() {
  const [faqs, setFaqs] = useState<GetAllFaqsType["data"]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [currentFaq, setCurrentFaq] = useState<FaqItem | null>(null);
  const [selectedType, setSelectedType] = useState<'billing' | 'product' | 'all'>('billing');
  const { success, error: showError } = useToast();

  // Add form
  const addForm = useForm<FaqFormValues>({
    resolver: zodResolver(faqFormSchema),
    defaultValues: {
      question: '',
      answer: '',
      type: 'billing',
      active: true,
      order: 0
    }
  });

  // Edit form
  const editForm = useForm<FaqFormValues>({
    resolver: zodResolver(faqFormSchema),
    defaultValues: {
      question: '',
      answer: '',
      type: 'billing',
      active: true,
      order: 0
    }
  });

  const typeOptions = [
    { label: 'Billing', value: 'billing' },
    { label: 'Product', value: 'product' }
  ];

  const filterOptions = [
    { label: 'Billing FAQs', value: 'billing' },
    { label: 'Product FAQs', value: 'product' },
    { label: 'All FAQs', value: 'all' }
  ];

  useEffect(() => {
    fetchFaqs().catch(()=>{});
  }, [selectedType]);

  const fetchFaqs = async () => {
    setIsLoading(true);
    try {
      // If 'all' is selected, pass undefined to get all FAQs
      const typeFilter = selectedType === 'all' ? undefined : selectedType;
      const result = await getAllFaqs(typeFilter);
      if (result.success && result.data) {
        setFaqs(result.data);
      } else {
        showError('Error', result.error || 'Failed to fetch FAQs');
      }
    } catch (err: any) {
      showError('Error', err.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddFaq = async (data: FaqFormValues) => {
    try {
      const result = await createFaq({
        question: data.question,
        answer: data.answer,
        type: data.type,
        active: data.active,
        order: data.order
      });

      if (result.success) {
        success('Success', 'FAQ added successfully');
        setShowAddDialog(false);
        addForm.reset();
        fetchFaqs().finally(()=>{});
      } else {
        showError('Error', result.error || 'Failed to add FAQ');
      }
    } catch (err: any) {
      showError('Error', err.message || 'An unexpected error occurred');
    }
  };

  const handleEditFaq = async (data: FaqFormValues) => {
    if (!currentFaq) return;

    try {
      const result = await updateFaq({
        id: currentFaq._id.toString(),
        question: data.question,
        answer: data.answer,
        type: data.type,
        active: data.active,
        order: data.order
      });

      if (result.success) {
        success('Success', 'FAQ updated successfully');
        setShowEditDialog(false);
        editForm.reset();
        fetchFaqs();
      } else {
        showError('Error', result.error || 'Failed to update FAQ');
      }
    } catch (err: any) {
      showError('Error', err.message || 'An unexpected error occurred');
    }
  };

  const handleDeleteFaq = (faq: FaqItem) => {
    confirmDialog({
      message: 'Are you sure you want to delete this FAQ?',
      header: 'Delete Confirmation',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'bg-red-500 hover:bg-red-600',
      accept: async () => {
        try {
          const result = await deleteFaq(faq._id.toString());
          if (result.success) {
            success('Success', 'FAQ deleted successfully');
            fetchFaqs();
          } else {
            showError('Error', result.error || 'Failed to delete FAQ');
          }
        } catch (err: any) {
          showError('Error', err.message || 'An unexpected error occurred');
        }
      }
    });
  };

  const openEditDialog = (faq: FaqItem) => {
    if (!faq) return;
    setCurrentFaq(faq);

    // Reset form with the FAQ data
    editForm.reset({
      question: faq.question,
      answer: faq.answer,
      type: faq.type as 'billing' | 'product',
      active: faq.active,
      order: faq.order
    });

    setShowEditDialog(true);
  };

  const renderDialogFooter = (isEdit: boolean) => {
    return (
      <div className="flex justify-end gap-2">
        <Button
          variant="outline"
          onClick={() => {
            if (isEdit) {
              setShowEditDialog(false);
            } else {
              setShowAddDialog(false);
            }
          }}
        >
          Cancel
        </Button>
        <Button
          className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
          onClick={isEdit
            ? editForm.handleSubmit(handleEditFaq)
            : addForm.handleSubmit(handleAddFaq)
          }
          disabled={isEdit ? editForm.formState.isSubmitting : addForm.formState.isSubmitting}
        >
          {isEdit ? 'Update' : 'Add'} FAQ
        </Button>
      </div>
    );
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <h1 className="text-2xl font-bold mr-4">Manage FAQs</h1>
          <div className="w-64">
            <Select
              value={selectedType}
              options={filterOptions}
              onChange={(value) => setSelectedType(value as 'billing' | 'product' | 'all')}
              placeholder="Filter by type"
            />
          </div>
        </div>
        <Button
          className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
          onClick={() => {
            // Reset form with default values
            addForm.reset({
              question: '',
              answer: '',
              type: selectedType !== 'all' ? selectedType : 'billing',
              active: true,
              order: 0
            });
            setShowAddDialog(true);
          }}
        >
          Add New FAQ
        </Button>
      </div>

      <Card className="bg-white dark:bg-[#1e1e28] shadow-sm rounded-xl overflow-hidden mb-8">
        <div className="p-6">
          <div className="mb-4">
            <h2 className="text-lg font-semibold">
              {selectedType === 'billing' ? 'Billing FAQs' :
               selectedType === 'product' ? 'Product FAQs' : 'All FAQs'}
            </h2>
          </div>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#6964D3]"></div>
            </div>
          ) : faqs && faqs.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-[#F8F9FD] dark:bg-[#15151e]">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Question
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Order
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {faqs.map((faq) => (
                    <tr key={faq._id.toString()} className="hover:bg-[#F8F9FD] dark:hover:bg-[#15151e] transition-colors">
                      <td className="px-6 py-4 text-sm">
                        <div className="font-medium">{faq.question}</div>
                        <div className="text-gray-500 dark:text-gray-400 text-xs mt-1 line-clamp-2">
                          {faq.answer}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm capitalize">
                        {faq.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <Badge variant={faq.active ? 'success' : 'danger'}>
                          {faq.active ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {faq.order}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-[#6964D3] text-[#6964D3]"
                            onClick={() => openEditDialog(faq)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-red-500 text-red-500"
                            onClick={() => handleDeleteFaq(faq)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="rounded-full bg-[#F3F3F3] dark:bg-[#15151e] p-4 inline-block mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                     stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="16" x2="12" y2="16.01"></line>
                  <path d="M12 13a1.5 1.5 0 0 0 1.5-1.5v-2a1.5 1.5 0 0 0-3 0v2a1.5 1.5 0 0 0 1.5 1.5z"></path>
                </svg>
              </div>
              <p className="text-gray-500 dark:text-gray-400">
                {selectedType === 'all'
                  ? 'No FAQs available'
                  : `No ${selectedType} FAQs available`}
              </p>
              <Button
                className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] mt-4"
                onClick={() => {
                  // Reset form with default values
                  addForm.reset({
                    question: '',
                    answer: '',
                    type: selectedType !== 'all' ? selectedType : 'billing',
                    active: true,
                    order: 0
                  });
                  setShowAddDialog(true);
                }}
              >
                Add Your First FAQ
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* Add FAQ Dialog */}
      <Dialog
        title="Add New FAQ"
        open={showAddDialog}
        onOpenChange={setShowAddDialog}
        footer={renderDialogFooter(false)}
      >
        <form className="space-y-4">
          <div>
            <label htmlFor="question" className="block text-sm font-medium mb-1">Question</label>
            <Controller
              name="question"
              control={addForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Input
                    id="question"
                    {...field}
                    className={`w-full ${fieldState.error ? 'border-red-500' : ''}`}
                    placeholder="Enter FAQ question"
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div>
            <label htmlFor="answer" className="block text-sm font-medium mb-1">Answer</label>
            <Controller
              name="answer"
              control={addForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Textarea
                    id="answer"
                    {...field}
                    rows={5}
                    className={`w-full ${fieldState.error ? 'border-red-500' : ''}`}
                    placeholder="Enter FAQ answer"
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div>
            <label htmlFor="type" className="block text-sm font-medium mb-1">Type</label>
            <Controller
              name="type"
              control={addForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Select
                    id="type"
                    value={field.value}
                    options={typeOptions}
                    onChange={(value) => field.onChange(value)}
                    placeholder="Select FAQ type"
                    error={fieldState.error?.message}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div>
            <label htmlFor="order" className="block text-sm font-medium mb-1">Display Order</label>
            <Controller
              name="order"
              control={addForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Input
                    id="order"
                    type="number"
                    {...field}
                    value={field.value.toString()}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    className={`w-full ${fieldState.error ? 'border-red-500' : ''}`}
                    placeholder="Enter display order (lower numbers appear first)"
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div className="flex items-center">
            <Controller
              name="active"
              control={addForm.control}
              render={({ field }) => (
                <Checkbox
                  checked={field.value}
                  onChange={(value) => field.onChange(value)}
                />
              )}
            />
            <label htmlFor="active" className="ml-2">Active</label>
          </div>
        </form>
      </Dialog>

      {/* Edit FAQ Dialog */}
      <Dialog
        title="Edit FAQ"
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        footer={renderDialogFooter(true)}
      >
        <form className="space-y-4">
          <div>
            <label htmlFor="edit-question" className="block text-sm font-medium mb-1">Question</label>
            <Controller
              name="question"
              control={editForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Input
                    id="edit-question"
                    {...field}
                    className={`w-full ${fieldState.error ? 'border-red-500' : ''}`}
                    placeholder="Enter FAQ question"
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div>
            <label htmlFor="edit-answer" className="block text-sm font-medium mb-1">Answer</label>
            <Controller
              name="answer"
              control={editForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Textarea
                    id="edit-answer"
                    {...field}
                    rows={5}
                    className={`w-full ${fieldState.error ? 'border-red-500' : ''}`}
                    placeholder="Enter FAQ answer"
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div>
            <label htmlFor="edit-type" className="block text-sm font-medium mb-1">Type</label>
            <Controller
              name="type"
              control={editForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Select
                    id="edit-type"
                    value={field.value}
                    options={typeOptions}
                    onChange={(value) => field.onChange(value)}
                    placeholder="Select FAQ type"
                    error={fieldState.error?.message}
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div>
            <label htmlFor="edit-order" className="block text-sm font-medium mb-1">Display Order</label>
            <Controller
              name="order"
              control={editForm.control}
              render={({ field, fieldState }) => (
                <div>
                  <Input
                    id="edit-order"
                    type="number"
                    {...field}
                    value={field.value.toString()}
                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    className={`w-full ${fieldState.error ? 'border-red-500' : ''}`}
                    placeholder="Enter display order (lower numbers appear first)"
                  />
                  {fieldState.error && (
                    <p className="text-red-500 text-xs mt-1">{fieldState.error.message}</p>
                  )}
                </div>
              )}
            />
          </div>
          <div className="flex items-center">
            <Controller
              name="active"
              control={editForm.control}
              render={({ field }) => (
                <Checkbox
                  checked={field.value}
                  onChange={(value) => field.onChange(value)}
                />
              )}
            />
            <label htmlFor="edit-active" className="ml-2">Active</label>
          </div>
        </form>
      </Dialog>

      <ConfirmDialog />
    </div>
  );
}

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { getOrganizations, updateOrganization } from '@/server/actions/pricing';
import { Badge } from '@/components/ui/Badge';
import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';

interface Organization {
  _id: string;
  name: string;
  description: string;
  email: string;
  domain: string;
  isActive: boolean;
  createdAt: string;
}

export default function OrganizationList() {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [updatingOrgId, setUpdatingOrgId] = useState<string | null>(null);

  const { success, error: showError } = useToast();

  // Fetch data on component mount
  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    setLoading(true);
    try {
      const result = await getOrganizations();
      if (result.success && result.data) {
        setOrganizations(result.data);
      } else {
        showError('Error', result.error || 'Failed to load organizations');
      }
    } catch (error) {
      logger.error('Error fetching organizations:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Toggle organization active status
  const toggleOrganizationStatus = async (org: Organization) => {
    if (updatingOrgId) return; // Prevent multiple clicks

    setUpdatingOrgId(org._id);
    try {
      const result = await updateOrganization({
        id: org._id,
        isActive: !org.isActive
      });

      if (result.success) {
        // Update local state
        const updatedOrgs = organizations.map(o =>
          o._id === org._id ? { ...o, isActive: !o.isActive } : o
        );
        setOrganizations(updatedOrgs);

        success(
          'Status Updated',
          `Organization "${org.name}" has been ${!org.isActive ? 'activated' : 'deactivated'}`
        );
      } else {
        showError('Error', result.error || 'Failed to update organization status');
      }
    } catch (error) {
      logger.error('Error updating organization:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setUpdatingOrgId(null);
    }
  };

  // Format creation date
  const createdAtTemplate = (rowData: any) => {
    const date = new Date(rowData.createdAt);
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-8 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Organizations</h2>
      </div>

      {organizations.length === 0 ? (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md text-center text-gray-500 dark:text-gray-400">
          <p>No organizations found. Organizations are created through the user registration process or API.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {organizations.map((org, index) => (
            <Card
              key={org._id}
              className="p-4 hover:shadow-md transition-shadow"
              hasBorder={true}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-lg dark:text-white">{org.name}</h3>
                <Badge
                  variant={org.isActive ? 'success' : 'danger'}
                  onClick={() => toggleOrganizationStatus(org)}
                >
                  {updatingOrgId === org._id ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Processing
                    </span>
                  ) : (
                    org.isActive ? 'Active' : 'Inactive'
                  )}
                </Badge>
              </div>
              <div className="text-gray-500 dark:text-gray-400 text-sm mb-1">Email: {org.email}</div>
              <div className="text-gray-500 dark:text-gray-400 text-sm mb-3">Domain: {org.domain || 'N/A'}</div>
              <p className="text-gray-700 dark:text-gray-300 mb-4">{org.description}</p>
              <div className="text-xs text-gray-500 dark:text-gray-400">Created: {createdAtTemplate(org)}</div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

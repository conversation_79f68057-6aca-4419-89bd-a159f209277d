'use client';

import React, { useState, useEffect } from 'react';
import { getSubscriptions } from '@/server/actions/pricing';
import { CircleDollarSign, Filter, Calendar, Building, Package, Tag } from 'lucide-react';
import {Button} from "@components/ui/Button";
import {Select} from "@components/ui/Select";

interface Subscription {
  _id: string;
  userId: string;
  organizationId: string;
  productId: string;
  pricingId: string;
  status: string;
  startDate: string;
  endDate: string;
  createdAt: string;
  organization: {
    name: string;
    industry: string;
  };
  product: {
    name: string;
    code: string;
  };
  pricing: {
    name: string;
    amount: number;
    currency: string;
    timeUnit: string;
    duration: number;
  };
}

export default function SubscriptionList() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    status: 'all',
    organizationId: '',
    productId: ''
  });

  // Load subscriptions on component mount
  useEffect(() => {
    const fetchSubscriptions = async () => {
      setLoading(true);
      try {
        const queryFilters: any = {};
        if (filters.status !== 'all') {
          queryFilters.status = filters.status;
        }
        if (filters.organizationId) {
          queryFilters.organizationId = filters.organizationId;
        }
        if (filters.productId) {
          queryFilters.productId = filters.productId;
        }

        const result = await getSubscriptions(queryFilters);
        if (result.success && result.data) {
          setSubscriptions(result.data);
        } else {
          setError(result.error || 'Failed to load subscriptions');
        }
      } catch (error) {
        console.error('Error fetching subscriptions:', error);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchSubscriptions();
  }, [filters]);

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'trial':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format subscription period
  const formatPeriod = (unit: string, duration: number) => {
    const unitDisplay = duration === 1 ? unit.slice(0, -1) : unit;
    return `${duration} ${unitDisplay}`;
  };

  // Loading state
  if (loading) {
    return (
      <div className="p-4 flex justify-center">
        <div className="w-8 h-8 border-4 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold">Subscriptions</h2>
        <div className="flex space-x-2">
          <Select
            options={[
              { label: 'All Statuses', value: 'all' },
              { label: 'Active', value: 'active' },
              { label: 'Trial', value: 'trial' },
              { label: 'Expired', value: 'expired' },
              { label: 'Cancelled', value: 'cancelled' },
            ]}
            value={filters.status}
            onChange={(status) => setFilters({...filters, status: status as string})}
          />
        </div>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-200 text-red-700 rounded">
          {error}
        </div>
      )}

      {subscriptions.length === 0 ? (
        <div className="bg-gray-50 dark:bg-gray-800 p-6 text-center rounded-lg border border-gray-200 dark:border-gray-700">
          <p className="text-gray-500 dark:text-gray-400">No subscriptions found with the current filters.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {subscriptions.map((subscription) => (
            <div
              key={subscription._id}
              className="bg-white dark:bg-gray-800 p-5 rounded-lg shadow hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="text-lg font-semibold">{subscription.organization.name}</h3>
                  <p className="text-sm text-gray-500">{subscription.organization.industry}</p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(subscription?.status)}`}>
                  {subscription?.status?.charAt(0).toUpperCase() + subscription?.status?.slice(1)}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="flex items-center">
                  <Package className="w-4 h-4 text-indigo-500 mr-2" />
                  <div>
                    <div className="text-xs text-gray-500">Product</div>
                    <div className="font-medium">{subscription.product.name}</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Tag className="w-4 h-4 text-indigo-500 mr-2" />
                  <div>
                    <div className="text-xs text-gray-500">Plan</div>
                    <div className="font-medium">{subscription.pricing.name}</div>
                  </div>
                </div>
                <div className="flex items-center">
                  <CircleDollarSign className="w-4 h-4 text-indigo-500 mr-2" />
                  <div>
                    <div className="text-xs text-gray-500">Price</div>
                    <div className="font-medium">
                      {formatCurrency(subscription.pricing.amount, subscription.pricing.currency)}
                      <span className="text-xs text-gray-500 ml-1">
                        / {formatPeriod(subscription.pricing.timeUnit, subscription.pricing.duration)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-indigo-500 mr-2" />
                  <div>
                    <div className="text-xs text-gray-500">Period</div>
                    <div className="font-medium text-sm">
                      {formatDate(subscription.startDate)} - {formatDate(subscription.endDate)}
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700 flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  Created: {formatDate(subscription.createdAt)}
                </span>
                <Button className="text-xs text-indigo-600 hover:text-indigo-800 font-medium">
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

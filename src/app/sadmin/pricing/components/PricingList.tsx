'use client';

import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
  getOrganizations,
  getProducts,
  getPricingPlans,
  createPricingPlan,
  getProductFeatures,
  updatePricingPlan
} from '@/server/actions/pricing';
import {Badge} from '@/components/ui/Badge';
import {Form, FormField} from '@/components/ui/Form';
import {Select} from '@/components/ui/Select';
import {Checkbox} from '@/components/ui/Checkbox';
import {useToast} from '@/components/ui/Toast';
import {ErrorAlert} from '@/components/ui/Alert';
import {Dialog} from '@/components/ui/Dialog';
import {Card} from '@/components/ui/Card';
import {Input} from '@/components/ui/Input';
import {Textarea} from '@/components/ui/Textarea';
import {Button} from '@/components/ui/Button';
import {Skeleton, CardSkeleton} from '@/components/ui/Skeleton';
import {InputCurrency} from '@/components/ui/InputCurrency';
import {Currency, TimeUnit} from '@/constants/pricing';
import {CheckIcon} from '@/components/icons/CheckIcon';
import { logger } from '@/utils/logger';

// Utility function to convert a string to a slug
const slugify = (text: string) => {
  return text
    .toLowerCase?.()
    .replace(/\s+/g, '_')        // Replace spaces with underscores
    .replace(/[^\w-]+/g, '')     // Remove all non-word chars
    .replace(/--+/g, '_')        // Replace multiple dashes with single underscore
    .replace(/^-+/, '')          // Trim dash from start
    .replace(/-+$/, '');         // Trim dash from end
};

interface Feature {
  _id: string;
  name: string;
  description?: string;
  active: boolean;
}

interface Product {
  _id: string;
  name: string;
  code: string;
  description: string;
  featureIds: string[];
  category: string;
  active: boolean;
}

interface ProductItem {
  productId: string;
  features: string[];
}

interface PricingPlan {
  _id: string;
  name: string;
  code: string;
  description: string;
  amountUSD: number;
  amountNGN: number;
  level: number;
  currency: Currency;
  timeUnit: TimeUnit;
  duration: number;
  isPerUser: boolean;
  active: boolean;
  productItems: ProductItem[];
  discountPercentage: number;
  discountActive: boolean;
  products?: Product[];
  features?: Feature[];
}

interface TimeUnitConversion {
  sourceUnit: TimeUnit;
  targetUnit: TimeUnit;
  conversionFactor: number;
  label: string;
}

interface PricingFormState {
  name: string;
  code: string;
  description: string;
  level: number;
  productItems: ProductItem[];
  amountUSD: number;
  amountNGN: number;
  currency: Currency;
  timeUnit: TimeUnit;
  duration: number;
  multiplier: number;
  targetTimeUnit: TimeUnit | null;
  isPerUser: boolean;
  active: boolean;
  discountPercentage: number;
  discountActive: boolean;
}

// Calculate final price with discount
function calculateFinalPrice(amount: number, discountPercentage: number, discountActive: boolean): number {
  if (!discountActive || !discountPercentage) return amount;
  return amount * (1 - (discountPercentage / 100));
}

// Get appropriate amount based on currency
function getAmountForCurrency(plan: PricingPlan): number {
  return plan.currency === Currency.NGN ? plan.amountNGN : plan.amountUSD;
}

// Utility function to format time unit
function formatTimeUnit(timeUnit: TimeUnit): string {
  switch (timeUnit) {
    case TimeUnit.DAY:
      return 'day';
    case TimeUnit.WEEK:
      return 'week';
    case TimeUnit.MONTH:
      return 'mo';
    case TimeUnit.YEAR:
      return 'yr';
    default:
      return 'month'; // Default fallback
  }
}

// Utility function to format currency
function formatCurrency(amount: number, currency: Currency): string {
  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
  });

  return formatter.format(amount);
}

export default function PricingList() {
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [createDialogVisible, setCreateDialogVisible] = useState(false);
  const [editDialogVisible, setEditDialogVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [currentProductId, setCurrentProductId] = useState<string | null>(null);
  const [productFeatures, setProductFeatures] = useState<Feature[]>([]);
  const [loadingFeatures, setLoadingFeatures] = useState(false);
  const [timeUnitFilter, setTimeUnitFilter] = useState<TimeUnit | null>(null);

  const emptyPricingPlan: PricingFormState = {
    name: '',
    code: '',
    description: '',
    productItems: [],
    level:1,
    amountUSD: 0,
    amountNGN: 0,
    currency: Currency.USD,
    timeUnit: TimeUnit.MONTH,
    duration: 1,
    multiplier: 1,
    targetTimeUnit: null,
    isPerUser: true,
    active: true,
    discountPercentage: 0,
    discountActive: false,
  };

  const [newPricingPlan, setNewPricingPlan] = useState<PricingFormState>(emptyPricingPlan);
  const [submitting, setSubmitting] = useState(false);
  const [editPricingPlan, setEditPricingPlan] = useState<PricingFormState & { id: string }>(
    {...emptyPricingPlan, id: ''}
  );

  // Generate the code from the name
  const generatedCode = useMemo(() =>
      slugify(newPricingPlan.name || ''),
    [newPricingPlan.name]
  );


  // Set the code whenever the name changes
  useEffect(() => {
    if (newPricingPlan.name) {
      setNewPricingPlan(prev => ({...prev, code: generatedCode}));
    }
  }, [generatedCode]);

  // Update the effect that loads product features to watch currentProductId
  useEffect(() => {
    // If we have a selected product ID, load its features
    if (currentProductId) {
      const product = products.find(p => p._id === currentProductId);
      if (product) {
        setSelectedProduct(product);
        // Load features for the selected product
        loadProductFeatures(product._id);
      } else {
        setSelectedProduct(null);
        setProductFeatures([]);
      }
    } else if (newPricingPlan.productItems.length > 0) {
      // Default to first product if none selected
      const firstProduct = newPricingPlan.productItems[0];
      const product = products.find(p => p._id === firstProduct.productId);
      if (product) {
        setSelectedProduct(product);
        setCurrentProductId(product._id);
        loadProductFeatures(product._id);
      }
    } else {
      setSelectedProduct(null);
      setProductFeatures([]);
    }
  }, [currentProductId, newPricingPlan.productItems, products]);


  const { success, error: showError} = useToast();

  // Load features for a selected product
  const loadProductFeatures = async (productId: string) => {
    setLoadingFeatures(true);
    try {
      const result = await getProductFeatures(productId);
      if (result.success && result.data) {
        setProductFeatures(result.data);
      } else {
        showError('Error', result.error || 'Failed to load product features');
      }
    } catch (error) {
      logger.error('Error loading product features:', error);
      showError('Error', 'An error occurred while loading product features');
    } finally {
      setLoadingFeatures(false);
    }
  };

  // Create a memoized fetchData function
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      // Fetch organizations
      const orgResult = await getOrganizations();
      if (orgResult.success && orgResult.data) {
        setOrganizations(orgResult.data);
      } else if (orgResult.error) {
        setError(orgResult.error);
      }

      // Fetch products
      const productResult = await getProducts();
      if (productResult.success && productResult.data) {
        setProducts(productResult.data);
      } else if (productResult.error) {
        setError(productResult.error);
      }

      // Fetch pricing plans
      const pricingResult = await getPricingPlans();
      if (pricingResult.success && pricingResult.data) {
        setPricingPlans(pricingResult.data);
      } else if (pricingResult.error) {
        setError(pricingResult.error || 'Failed to load pricing plans');
      }
    } catch (error) {
      logger.error('Error fetching data:', error);
      setError('An unexpected error occurred while fetching data');
    } finally {
      setLoading(false);
    }
  }, []);

  // Use effect to fetch data once on mount
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Update form field
  const updateFormField = (field: string, value: any) => {
    setNewPricingPlan(prev => {
      const updated = {...prev, [field]: value};

      // Generate the code from the name
      if (field === 'name') {
        updated.code = slugify(value);
      }

      return updated;
    });
  };

  // Add function to switch the currently selected product
  const handleProductChange = (productId: string) => {
    setCurrentProductId(productId);
  };

  // Modify the updateProductItems function to set currentProductId when adding first product
  const updateProductItems = (productId: string, selected: boolean) => {
    setNewPricingPlan(prev => {
      let updatedProductItems = [...prev.productItems];

      if (selected) {
        // Add product if not already in the list
        if (!updatedProductItems.some(item => item.productId === productId)) {
          updatedProductItems.push({productId, features: []});

          // Auto-select this product if it's the first one
          if (updatedProductItems.length === 1) {
            setCurrentProductId(productId);
          }
        }
      } else {
        // Remove product from the list
        updatedProductItems = updatedProductItems.filter(item => item.productId !== productId);

        // If we removed the current product, select the first remaining one (if any)
        if (productId === currentProductId) {
          const nextProduct = updatedProductItems[0];
          setCurrentProductId(nextProduct ? nextProduct.productId : null);
        }
      }

      return {
        ...prev,
        productItems: updatedProductItems
      };
    });
  };

  // Handler for toggling features for the selected product
  const handleToggleFeature = (featureId: string) => {
    setNewPricingPlan(prev => {
      // If no product is selected, do nothing
      if (!selectedProduct || prev.productItems.length === 0) return prev;

      // Find the selected product item
      const productItemIndex = prev.productItems.findIndex(
        item => item.productId === selectedProduct._id
      );

      // If product not found, do nothing
      if (productItemIndex === -1) return prev;

      // Get the current product item
      const productItem = prev.productItems[productItemIndex];

      // Check if feature is already selected
      const isSelected = productItem.features.includes(featureId);

      // Create updated features list
      let updatedFeatures;
      if (isSelected) {
        updatedFeatures = productItem.features.filter(id => id !== featureId);
      } else {
        updatedFeatures = [...productItem.features, featureId];
      }

      // Create updated product items list
      const updatedProductItems = [...prev.productItems];
      updatedProductItems[productItemIndex] = {
        ...productItem,
        features: updatedFeatures
      };

      return {
        ...prev,
        productItems: updatedProductItems
      };
    });
  };

  // Now update the openCloneDialog function to set currentProductId
  const openCloneDialog = (plan: PricingPlan) => {
    // Add debug logging

    // Clone productItems array
    let productItems: ProductItem[] = [];

    if (plan.productItems && Array.isArray(plan.productItems)) {
      productItems = plan.productItems.map(item => ({
        productId: typeof item.productId === 'string'
          ? item.productId
          : String(item.productId),
        features: Array.isArray(item.features)
          ? item.features.map(featureId =>
            typeof featureId === 'string'
              ? featureId
              : String(featureId))
          : []
      }));
    }


    // Set the new pricing plan with data from the selected plan
    setNewPricingPlan({
      name: `Copy of ${plan.name}`,
      code: slugify(`Copy of ${plan.name}`),
      description: plan.description,
      productItems: productItems,
      amountUSD: plan.amountUSD,
      level: plan.level,
      amountNGN: plan.amountNGN,
      currency: plan.currency,
      timeUnit: plan.timeUnit,
      duration: plan.duration,
      multiplier: 1, // Default multiplier for the new plan
      targetTimeUnit: null,
      isPerUser: plan.isPerUser,
      active: plan.active,
      discountPercentage: plan.discountPercentage,
      discountActive: plan.discountActive,
    });

    // Set the current product ID to the first product if available
    if (productItems.length > 0) {
      const primaryProductId = productItems[0].productId;
      setCurrentProductId(primaryProductId);

      // Find the product in the products list
      const product = products.find(p => p._id === primaryProductId);
      if (product) {
        setSelectedProduct(product);
        // Load the features for this product
        loadProductFeatures(primaryProductId);
      }
    } else {
      setCurrentProductId(null);
      setSelectedProduct(null);
      setProductFeatures([]);
    }

    setCreateDialogVisible(true);
  };

  // Get available multiplier options based on the time unit
  const getMultiplierOptions = (timeUnit: TimeUnit) => {
    switch (timeUnit) {
      case TimeUnit.DAY:
        return [
          {value: 1, label: '1 day'},
          {value: 7, label: '7 days'},
          {value: 14, label: '14 days'},
          {value: 30, label: '30 days'}
        ];
      case TimeUnit.WEEK:
        return [
          {value: 1, label: '1 week'},
          {value: 2, label: '2 weeks'},
          {value: 4, label: '4 weeks'},
          {value: 12, label: '12 weeks'}
        ];
      case TimeUnit.MONTH:
        return [
          {value: 1, label: '1 month'},
          {value: 3, label: '3 months'},
          {value: 6, label: '6 months'},
          {value: 12, label: '12 months'}
        ];
      case TimeUnit.YEAR:
        return [
          {value: 1, label: '1 year'},
          {value: 2, label: '2 years'},
          {value: 3, label: '3 years'},
          {value: 5, label: '5 years'}
        ];
      default:
        return [
          {value: 1, label: '1x'},
          {value: 2, label: '2x'},
          {value: 3, label: '3x'}
        ];
    }
  };

  // Calculate final price with multiplier and discount
  const calculateFinalPrice = (amount: number, discountPercentage: number, discountActive: boolean, multiplier: number = 1): number => {
    // Apply multiplier first
    const baseAmount = amount * multiplier;

    // Then apply discount if active
    if (discountActive && discountPercentage) {
      return baseAmount * (1 - (discountPercentage / 100));
    }

    return baseAmount;
  };

  // Function to get available time unit conversions based on the selected time unit
  const getTimeUnitConversions = (sourceUnit: TimeUnit): TimeUnitConversion[] => {
    switch (sourceUnit) {
      case TimeUnit.YEAR:
        return [
          {sourceUnit, targetUnit: TimeUnit.MONTH, conversionFactor: 12, label: 'Monthly (12x)'},
          {sourceUnit, targetUnit: TimeUnit.WEEK, conversionFactor: 52, label: 'Weekly (52x)'},
          {sourceUnit, targetUnit: TimeUnit.DAY, conversionFactor: 365, label: 'Daily (365x)'},
        ];
      case TimeUnit.MONTH:
        return [
          {sourceUnit, targetUnit: TimeUnit.YEAR, conversionFactor: 1 / 12, label: 'Yearly (1/12x)'},
          {sourceUnit, targetUnit: TimeUnit.WEEK, conversionFactor: 4, label: 'Weekly (4x)'},
          {sourceUnit, targetUnit: TimeUnit.DAY, conversionFactor: 30, label: 'Daily (30x)'},
        ];
      case TimeUnit.WEEK:
        return [
          {sourceUnit, targetUnit: TimeUnit.YEAR, conversionFactor: 1 / 52, label: 'Yearly (1/52x)'},
          {sourceUnit, targetUnit: TimeUnit.MONTH, conversionFactor: 1 / 4, label: 'Monthly (1/4x)'},
          {sourceUnit, targetUnit: TimeUnit.DAY, conversionFactor: 7, label: 'Daily (7x)'},
        ];
      case TimeUnit.DAY:
        return [
          {sourceUnit, targetUnit: TimeUnit.YEAR, conversionFactor: 1 / 365, label: 'Yearly (1/365x)'},
          {sourceUnit, targetUnit: TimeUnit.MONTH, conversionFactor: 1 / 30, label: 'Monthly (1/30x)'},
          {sourceUnit, targetUnit: TimeUnit.WEEK, conversionFactor: 1 / 7, label: 'Weekly (1/7x)'},
        ];
      default:
        return [];
    }
  };

  // Add a new function to calculate the final price with time unit conversion
  const calculateFinalPriceWithTimeConversion = (
    amount: number,
    duration: number,
    sourceUnit: TimeUnit,
    targetUnit: TimeUnit | null,
    discountPercentage: number = 0,
    discountActive: boolean = false
  ): number => {
    // If no target unit, just use regular calculation
    if (!targetUnit) {
      return calculateFinalPrice(amount * duration, discountPercentage, discountActive);
    }

    // Find the conversion
    const conversion = getTimeUnitConversions(sourceUnit).find(c => c.targetUnit === targetUnit);

    if (!conversion) {
      // If no conversion found, return the original amount
      return calculateFinalPrice(amount * duration, discountPercentage, discountActive);
    }

    // Apply the conversion factor
    const convertedAmount = amount * duration * conversion.conversionFactor;

    // Then apply discount if active
    return calculateFinalPrice(convertedAmount, discountPercentage, discountActive);
  };

  // Function to get formatted time unit label
  const getTimeUnitLabel = (timeUnit: TimeUnit): string => {
    switch (timeUnit) {
      case TimeUnit.DAY:
        return 'Day';
      case TimeUnit.WEEK:
        return 'Week';
      case TimeUnit.MONTH:
        return 'Month';
      case TimeUnit.YEAR:
        return 'Year';
      default:
        return 'Time Unit';
    }
  };

  // Create new pricing plan
  const handleCreatePricingPlan = async () => {
    setSubmitting(true);

    // Form validation
    if (!newPricingPlan.name) {
      setValidationMessage('Name is required');
      setSubmitting(false);
      return;
    }
    if (newPricingPlan.productItems.length === 0) {
      setValidationMessage('At least one product is required');
      setSubmitting(false);
      return;
    }

    if (newPricingPlan.amountUSD <= 0 && newPricingPlan.amountNGN <= 0) {
      setValidationMessage('Amount must be greater than 0');
      setSubmitting(false);
      return;
    }

    try {
      // Calculate the final amounts with time unit conversion
      const finalAmountUSD = calculateFinalPriceWithTimeConversion(
        newPricingPlan.amountUSD,
        newPricingPlan.duration,
        newPricingPlan.timeUnit,
        newPricingPlan.targetTimeUnit,
        0, // No discount at this stage
        false
      );

      const finalAmountNGN = calculateFinalPriceWithTimeConversion(
        newPricingPlan.amountNGN,
        newPricingPlan.duration,
        newPricingPlan.timeUnit,
        newPricingPlan.targetTimeUnit,
        0, // No discount at this stage
        false
      );

      // Use the base time unit that the user selected
      const finalTimeUnit = newPricingPlan.timeUnit;

      // Keep the original duration from the form
      const finalDuration = newPricingPlan.duration;

      // Map our UI form data to the API's expected format
      const planToCreate = {
        name: newPricingPlan.name,
        description: newPricingPlan.description,
        productItems: newPricingPlan.productItems,
        active: newPricingPlan.active,
        isPerUser: newPricingPlan.isPerUser,
        amountUSD: finalAmountUSD,
        amountNGN: finalAmountNGN,
        level: newPricingPlan.level,
        currency: newPricingPlan.currency,
        timeUnit: finalTimeUnit,
        duration: finalDuration,
        discountPercentage: newPricingPlan.discountPercentage,
        discountActive: newPricingPlan.discountActive,
      };

      const result = await createPricingPlan(planToCreate);
      if (result.success) {
        success('Success', 'Pricing plan created successfully');
        setCreateDialogVisible(false);
        setNewPricingPlan(emptyPricingPlan);
        setValidationMessage(null);
        setSelectedProduct(null);
        setProductFeatures([]);

        // Fetch updated list
        const updatedResult = await getPricingPlans();
        if (updatedResult.success && updatedResult.data) {
          setPricingPlans(updatedResult.data);
        }
      } else {
        showError('Error', result.error || 'Failed to create pricing plan');
      }
    } catch (error) {
      logger.error('Error creating pricing plan:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  // Open edit dialog with pricing plan data
  const openEditDialog = (plan: PricingPlan) => {
    // Convert productItems if needed for backward compatibility
    let productItems: ProductItem[] = [];

    if (plan.productItems && Array.isArray(plan.productItems)) {
      productItems = plan.productItems.map(item => ({
        productId: typeof item.productId === 'string'
          ? item.productId
          : String(item.productId),
        features: Array.isArray(item.features)
          ? item.features.map(featureId =>
            typeof featureId === 'string'
              ? featureId
              : String(featureId))
          : []
      }));
    }

    setEditPricingPlan({
      id: plan._id,
      name: plan.name,
      code: plan.code,
      level: plan.level,
      description: plan.description,
      productItems: productItems,
      amountUSD: plan.amountUSD,
      amountNGN: plan.amountNGN,
      currency: plan.currency,
      timeUnit: plan.timeUnit,
      duration: plan.duration,
      multiplier: 1, // Add default multiplier
      targetTimeUnit: null,
      isPerUser: plan.isPerUser,
      active: plan.active,
      discountPercentage: plan.discountPercentage,
      discountActive: plan.discountActive,
    });

    // Set the current product ID to the first product if available
    if (productItems.length > 0) {
      const primaryProductId = productItems[0].productId;
      setCurrentProductId(primaryProductId);
    } else {
      setCurrentProductId(null);
    }

    setEditDialogVisible(true);
  };

  // Update edit pricing plan field
  const updateEditFormField = (field: string, value: any) => {
    setEditPricingPlan(prev => {
      const updated = {...prev, [field]: value};

      // Generate the code from the name if it's a new pricing plan
      if (field === 'name') {
        updated.code = slugify(value);
      }

      return updated;
    });
  };

  // Same for edit mode
  const updateEditProductItems = (productId: string, selected: boolean) => {
    setEditPricingPlan(prev => {
      let updatedProductItems = [...prev.productItems];

      if (selected) {
        // Add product if not already in the list
        if (!updatedProductItems.some(item => item.productId === productId)) {
          updatedProductItems.push({productId, features: []});

          // Auto-select this product if it's the first one
          if (updatedProductItems.length === 1) {
            setCurrentProductId(productId);
          }
        }
      } else {
        // Remove product from the list
        updatedProductItems = updatedProductItems.filter(item => item.productId !== productId);

        // If we removed the current product, select the first remaining one (if any)
        if (productId === currentProductId) {
          const nextProduct = updatedProductItems[0];
          setCurrentProductId(nextProduct ? nextProduct.productId : null);
        }
      }

      return {
        ...prev,
        productItems: updatedProductItems
      };
    });
  };

  // Handler for toggling features in edit form
  const handleToggleEditFeature = (featureId: string) => {
    setEditPricingPlan(prev => {
      // If no product is selected, do nothing
      if (!selectedProduct || prev.productItems.length === 0) return prev;

      // Find the selected product item
      const productItemIndex = prev.productItems.findIndex(
        item => item.productId === selectedProduct._id
      );

      // If product not found, do nothing
      if (productItemIndex === -1) return prev;

      // Get the current product item
      const productItem = prev.productItems[productItemIndex];

      // Check if feature is already selected
      const isSelected = productItem.features.includes(featureId);

      // Create updated features list
      let updatedFeatures;
      if (isSelected) {
        updatedFeatures = productItem.features.filter(id => id !== featureId);
      } else {
        updatedFeatures = [...productItem.features, featureId];
      }

      // Create updated product items list
      const updatedProductItems = [...prev.productItems];
      updatedProductItems[productItemIndex] = {
        ...productItem,
        features: updatedFeatures
      };

      return {
        ...prev,
        productItems: updatedProductItems
      };
    });
  };

  // Update existing pricing plan
  const handleUpdatePricingPlan = async () => {
    setSubmitting(true);

    // Form validation
    if (!editPricingPlan.name) {
      setValidationMessage('Name is required');
      setSubmitting(false);
      return;
    }

    if (editPricingPlan.productItems.length === 0) {
      setValidationMessage('At least one product is required');
      setSubmitting(false);
      return;
    }

    if (editPricingPlan.amountUSD <= 0 && editPricingPlan.amountNGN <= 0) {
      setValidationMessage('Amount must be greater than 0');
      setSubmitting(false);
      return;
    }

    try {
      // Calculate the final amounts with time unit conversion
      const finalAmountUSD = calculateFinalPriceWithTimeConversion(
        editPricingPlan.amountUSD,
        editPricingPlan.duration,
        editPricingPlan.timeUnit,
        editPricingPlan.targetTimeUnit,
        0, // No discount at this stage
        false
      );

      const finalAmountNGN = calculateFinalPriceWithTimeConversion(
        editPricingPlan.amountNGN,
        editPricingPlan.duration,
        editPricingPlan.timeUnit,
        editPricingPlan.targetTimeUnit,
        0, // No discount at this stage
        false
      );

      // Use the base time unit that the user selected
      const finalTimeUnit = editPricingPlan.timeUnit;

      // Keep the original duration from the form
      const finalDuration = editPricingPlan.duration;

      // Map our UI form data to the API's expected format
      const planToUpdate = {
        id: editPricingPlan.id,
        name: editPricingPlan.name,
        description: editPricingPlan.description,
        level: editPricingPlan.level,
        productItems: editPricingPlan.productItems,
        active: editPricingPlan.active,
        isPerUser: editPricingPlan.isPerUser,
        amountUSD: finalAmountUSD,
        amountNGN: finalAmountNGN,
        currency: editPricingPlan.currency,
        timeUnit: finalTimeUnit,
        duration: finalDuration,
        discountPercentage: editPricingPlan.discountPercentage,
        discountActive: editPricingPlan.discountActive,
      };

      const result = await updatePricingPlan(planToUpdate);
      if (result.success) {
        success('Success', 'Pricing plan updated successfully');
        setEditDialogVisible(false);
        setEditPricingPlan({...emptyPricingPlan, id: ''});
        setValidationMessage(null);
        setSelectedProduct(null);
        setProductFeatures([]);

        // Fetch updated list
        const updatedResult = await getPricingPlans();
        if (updatedResult.success && updatedResult.data) {
          setPricingPlans(updatedResult.data);
        }
      } else {
        showError('Error', result.error || 'Failed to update pricing plan');
      }
    } catch (error) {
      logger.error('Error updating pricing plan:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  // Add a new togglePlanActive function
  const togglePlanActive = async (plan: PricingPlan) => {
    try {
      // Prepare the update with minimal data, just toggling the active status
      const planToUpdate = {
        id: plan._id,
        active: !plan.active // Toggle the active status
      };

      // Call the server action to update the plan
      const result = await updatePricingPlan(planToUpdate);

      if (result.success) {
        // Show success message
        success(
          'Success',
          `Pricing plan ${plan.active ? 'deactivated' : 'activated'} successfully`
        );

        // Refresh the pricing plans list
        const updatedResult = await getPricingPlans();
        if (updatedResult.success && updatedResult.data) {
          setPricingPlans(updatedResult.data);
        }
      } else {
        showError('Error', result.error || 'Failed to update pricing plan status');
      }
    } catch (error) {
      logger.error('Error toggling pricing plan status:', error);
      showError('Error', 'An unexpected error occurred');
    }
  };

  // Add a function to filter plans by time unit
  const getFilteredPlans = () => {
    if (!timeUnitFilter) {
      return pricingPlans; // Return all plans if no filter is selected
    }

    return pricingPlans.filter((plan) => plan.timeUnit === timeUnitFilter);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <Skeleton width="150px" height="28px"/>
          <Skeleton width="180px" height="40px"/>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[1, 2, 3, 4, 5, 6].map((item) => (
            <CardSkeleton key={item} className="h-[300px]"/>
          ))}
        </div>
      </div>
    );
  }

  if (error && !createDialogVisible) {
    return (
      <ErrorAlert title="Error Loading Data" onClose={() => setError(null)}>
        <p>{error}</p>
        <Button
          variant="danger"
          onClick={() => setError(null)}
          className="mt-2"
        >
          Dismiss
        </Button>
      </ErrorAlert>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold dark:text-white">Pricing Plans</h2>
        <Button
          variant="primary"
          onClick={() => setCreateDialogVisible(true)}
          className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
        >
          <i className="pi pi-plus mr-2"/>
          New Pricing Plan
        </Button>
      </div>

      {/* Add Time Unit Filter */}
      <div className="mb-4 flex items-center gap-2">
        <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Filter by time unit:</span>
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant={timeUnitFilter === null ? "primary" : "outline"}
            onClick={() => setTimeUnitFilter(null)}
            className={timeUnitFilter === null ? "bg-blue-500 text-white" : ""}
          >
            All
          </Button>
          <Button
            size="sm"
            variant={timeUnitFilter === TimeUnit.YEAR ? "primary" : "outline"}
            onClick={() => setTimeUnitFilter(TimeUnit.YEAR)}
            className={timeUnitFilter === TimeUnit.YEAR ? "bg-blue-500 text-white" : ""}
          >
            Yearly
          </Button>
          <Button
            size="sm"
            variant={timeUnitFilter === TimeUnit.MONTH ? "primary" : "outline"}
            onClick={() => setTimeUnitFilter(TimeUnit.MONTH)}
            className={timeUnitFilter === TimeUnit.MONTH ? "bg-blue-500 text-white" : ""}
          >
            Monthly
          </Button>
          <Button
            size="sm"
            variant={timeUnitFilter === TimeUnit.WEEK ? "primary" : "outline"}
            onClick={() => setTimeUnitFilter(TimeUnit.WEEK)}
            className={timeUnitFilter === TimeUnit.WEEK ? "bg-blue-500 text-white" : ""}
          >
            Weekly
          </Button>
          <Button
            size="sm"
            variant={timeUnitFilter === TimeUnit.DAY ? "primary" : "outline"}
            onClick={() => setTimeUnitFilter(TimeUnit.DAY)}
            className={timeUnitFilter === TimeUnit.DAY ? "bg-blue-500 text-white" : ""}
          >
            Daily
          </Button>
        </div>
      </div>

      {pricingPlans.length === 0 ? (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md text-center text-gray-500 dark:text-gray-400">
          <p>No pricing plans found. Create your first plan!</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {getFilteredPlans().map((plan) => (
            <Card key={plan._id} className="h-full">
              <div className="p-6">
                <div className="flex justify-between items-start mb-2">
                  <h3 className="font-semibold text-lg dark:text-white">{plan.name} {plan.level}</h3>
                  <Badge
                    variant={plan.active ? 'success' : 'danger'}
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={() => togglePlanActive(plan)}
                  >
                    {plan.active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                {/* Display all products included in this plan */}
                <div className="text-gray-500 dark:text-gray-400 text-sm mb-3">
                  {plan.productItems && plan.productItems.length > 0 ? (
                    <div>
                      <span className="font-medium">Products:</span>
                      <div className="mt-1 flex flex-wrap gap-1">
                        {plan.products?.map(product => (
                          <span
                            key={product._id}
                            className={`inline-flex items-center px-2 py-1 rounded-full text-xs 
                              ${product.category === 'core' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' :
                              product.category === 'premium' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'}`}
                          >
                            {product.name}
            </span>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div>No products</div>
                  )}
                </div>

                <div className="text-center mb-4">
                  <div className="flex justify-center items-baseline mb-2">
                    {plan.discountActive && plan.discountPercentage > 0 ? (
                      <>
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                          {formatCurrency(calculateFinalPrice(getAmountForCurrency(plan), plan.discountPercentage, plan.discountActive), plan.currency)}
                        </span>
                        <span className="text-gray-500 dark:text-gray-400 ml-1">
                          {plan.isPerUser ? '/user' : ''}/{formatTimeUnit(plan.timeUnit)}
                        </span>
                        <span className="ml-2 line-through text-gray-400 text-sm">
                          {formatCurrency(getAmountForCurrency(plan), plan.currency)}
                        </span>
                        <span className="ml-1 text-green-500 text-sm">
                          ({plan.discountPercentage}% off)
                        </span>
                      </>
                    ) : (
                      <>
                        <span className="text-2xl font-bold text-gray-900 dark:text-white">
                          {formatCurrency(getAmountForCurrency(plan), plan.currency)}
                        </span>
                        <span className="text-gray-500 dark:text-gray-400 ml-1">
                          {plan.isPerUser ? '/user' : ''}/{formatTimeUnit(plan.timeUnit)}
                        </span>
                      </>
                    )}
                  </div>

                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {plan.description}
                  </p>
                </div>

                {/* Feature list */}
                <div className="space-y-2 my-4">
                  {/* Organize features by product */}
                  {plan.productItems && plan.productItems.map((productItem, index) => {
                    // Find product by ID safely (string or object ID)
                    const productId = String(productItem.productId); // Convert any type to string

                    const product = plan.products?.find(p =>
                      p._id && String(p._id) === productId
                    );

                    if (!product) return null;

                    // Get features for this product
                    const productFeatures = plan.features?.filter(feature => {
                      if (!feature._id || !Array.isArray(productItem.features)) return false;

                      return productItem.features.some(featureId =>
                        String(feature._id) === String(featureId)
                      );
                    });

                    if (!productFeatures || productFeatures.length === 0) return null;

                    return (
                      <div key={index} className="mb-3">
                        {plan.productItems.length > 1 && (
                          <h4 className={`text-sm font-medium mb-2 
                            ${product.category === 'core' ? 'text-blue-600 dark:text-blue-400' :
                            product.category === 'premium' ? 'text-purple-600 dark:text-purple-400' :
                              'text-gray-600 dark:text-gray-400'}`}>
                            {product.name} Features:
                          </h4>
                        )}

                        {productFeatures.map((feature) => (
                          <div key={feature._id} className="flex items-center">
                            <CheckIcon className="h-4 w-4 text-green-500 mr-2"/>
                            <span className="text-sm">{feature.name}</span>
                          </div>
                        ))}
                      </div>
                    );
                  })}
                </div>

                <div className="mt-4 flex justify-end gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => openCloneDialog(plan)}
                  >
                    Clone
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700"
                    onClick={() => openEditDialog(plan)}
                  >
                    Edit
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Create Pricing Plan Dialog */}
      <Dialog
        open={createDialogVisible}
        onOpenChange={(open) => !open && setCreateDialogVisible(false)}
        title="Create Pricing Plan"
        size="large"
        footer={
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setCreateDialogVisible(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleCreatePricingPlan}
              disabled={submitting}
              loading={submitting}
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
            >
              Create Plan
            </Button>
          </div>
        }
      >
        <Form className="space-y-4">
          {error && createDialogVisible && (
            <ErrorAlert onClose={() => setError(null)}>
              {error}
            </ErrorAlert>
          )}

          {validationMessage && (
            <ErrorAlert onClose={() => setValidationMessage(null)}>
              {validationMessage}
            </ErrorAlert>
          )}

          <FormField label="Name" htmlFor="name" required>
            <Input
              id="name"
              value={newPricingPlan.name}
              onChange={(e) => updateFormField('name', e.target.value)}
              placeholder="Premium Plan"
              disabled={submitting}
            />
          </FormField>

          <FormField label="Code (Generated)" htmlFor="code">
            <Input
              id="code"
              value={newPricingPlan.code}
              disabled={true}
              className="opacity-80"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Automatically generated from the plan name
            </p>
          </FormField>

          <FormField label="Description" htmlFor="description">
            <Textarea
              id="description"
              value={newPricingPlan.description}
              onChange={(e) => updateFormField('description', e.target.value)}
              rows={3}
              placeholder="All premium features with 24/7 support"
              disabled={submitting}
            />
          </FormField>

          <div className="grid grid-cols-1 gap-4">
            <FormField label="Base Time Unit" htmlFor="timeUnit" required>
              <Select
                id="timeUnit"
                value={newPricingPlan.timeUnit}
                onChange={(value) => {
                  updateFormField('timeUnit', value);
                  // Reset target time unit when source changes
                  updateFormField('targetTimeUnit', null);
                }}
                options={[
                  {value: TimeUnit.DAY, label: 'Day'},
                  {value: TimeUnit.WEEK, label: 'Week'},
                  {value: TimeUnit.MONTH, label: 'Month'},
                  {value: TimeUnit.YEAR, label: 'Year'}
                ]}
                disabled={submitting}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Select the base time unit for pricing
              </p>
            </FormField>

            <FormField label="Duration" htmlFor="duration" required>
              <Input
                id="duration"
                type="number"
                min={1}
                value={newPricingPlan.duration.toString()}
                onChange={(e) => updateFormField('duration', parseInt(e.target.value, 10) || 1)}
                disabled={submitting}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Number of base time units (e.g., 1 month, 3 months, etc.)
              </p>
            </FormField>

            <FormField label="Time Unit Conversion" htmlFor="targetTimeUnit">
              <div className="relative">
                <select
                  id="targetTimeUnit"
                  className="block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  value={newPricingPlan.targetTimeUnit || ''}
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    updateFormField('targetTimeUnit', selectedValue ? selectedValue as TimeUnit : null);
                  }}
                  disabled={submitting}
                >
                  <option value="">No conversion (use base unit)</option>
                  {getTimeUnitConversions(newPricingPlan.timeUnit).map(conversion => (
                    <option key={conversion.targetUnit} value={conversion.targetUnit}>
                      {conversion.label}
                    </option>
                  ))}
                </select>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Convert base unit to different time units (optional)
              </p>
            </FormField>
            <FormField label="Level" htmlFor="level">
              <div className="relative">
                <select
                  id="level"
                  className="block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  value={newPricingPlan.level || ''}
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    updateFormField('level', selectedValue);
                  }}
                  disabled={submitting}
                >
                  <option value="">No Level Selected</option>
                  {[1,2,3,4,5,6,7,8,9].map(level => (
                    <option key={level} value={level}>
                      {level}
                    </option>
                  ))}
                </select>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                This indicates the tier of the plan showing which plan is the higher than the other.
              </p>
            </FormField>

            <div>
              <h3 className="text-lg font-semibold mb-3">Pricing</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="USD Amount" htmlFor="amountUSD" required>
                  <InputCurrency
                    id="amountUSD"
                    value={newPricingPlan.amountUSD}
                    onChange={(value) => updateFormField('amountUSD', value)}
                    disabled={submitting}
                    currency="USD"
                  />
                </FormField>

                <FormField label="NGN Amount" htmlFor="amountNGN" required>
                  <InputCurrency
                    id="amountNGN"
                    value={newPricingPlan.amountNGN}
                    onChange={(value) => updateFormField('amountNGN', value)}
                    disabled={submitting}
                    currency="NGN"
                  />
                </FormField>
              </div>
            </div>

            <FormField label="Default Currency" htmlFor="currency">
              <Select
                id="currency"
                value={newPricingPlan.currency}
                onChange={(value) => updateFormField('currency', value)}
                options={[
                  {value: Currency.USD, label: 'USD (Default for non-Nigerian customers)'},
                  {value: Currency.NGN, label: 'NGN (Default for Nigerian customers)'}
                ]}
                disabled={submitting}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                This will be used as the default display currency. Customers will be charged in the appropriate currency
                based on their location.
              </p>
            </FormField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField label="Discount %" htmlFor="discountPercentage">
                <Input
                  id="discountPercentage"
                  type="number"
                  min={0}
                  max={100}
                  value={newPricingPlan.discountPercentage.toString()}
                  onChange={(e) => {
                    const value = parseInt(e.target.value, 10);
                    updateFormField('discountPercentage', isNaN(value) ? 0 : Math.min(100, Math.max(0, value)));
                  }}
                  disabled={submitting || !newPricingPlan.discountActive}
                />
              </FormField>

              <FormField label="Enable Discount" htmlFor="discountActive">
                <div className="flex items-center">
                  <Checkbox
                    id="discountActive"
                    checked={newPricingPlan.discountActive}
                    onChange={(checked) => updateFormField('discountActive', checked)}
                    disabled={submitting}
                  />
                  <label htmlFor="discountActive" className="ml-2 text-sm">
                    Apply discount to this plan
                  </label>
                </div>
              </FormField>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md">
              <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Price Summary</h4>
              <div className="space-y-2">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <span className="font-medium">Base Price: </span>
                  {formatCurrency(newPricingPlan.amountUSD, Currency.USD)} USD /
                  {formatCurrency(newPricingPlan.amountNGN, Currency.NGN)} NGN
                  per {getTimeUnitLabel(newPricingPlan.timeUnit).toLowerCase?.()}
                </p>

                {newPricingPlan.duration > 1 && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <span className="font-medium">Duration: </span>
                    {newPricingPlan.duration} {getTimeUnitLabel(newPricingPlan.timeUnit).toLowerCase?.()}
                    {newPricingPlan.duration > 1 ? 's' : ''}
                  </p>
                )}

                {newPricingPlan.targetTimeUnit && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <span className="font-medium">Conversion: </span>
                    {newPricingPlan.duration} {getTimeUnitLabel(newPricingPlan.timeUnit).toLowerCase?.()}
                    {newPricingPlan.duration > 1 ? 's' : ''} →
                    {getTimeUnitLabel(newPricingPlan.targetTimeUnit).toLowerCase?.()}
                  </p>
                )}

                {newPricingPlan.discountActive && newPricingPlan.discountPercentage > 0 && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <span className="font-medium">Discount: </span>
                    {newPricingPlan.discountPercentage}% off
                  </p>
                )}

                <p className="text-sm text-blue-700 dark:text-blue-300 font-bold">
                  <span className="font-medium">Final Price: </span>
                  {formatCurrency(
                    calculateFinalPriceWithTimeConversion(
                      newPricingPlan.amountUSD,
                      newPricingPlan.duration,
                      newPricingPlan.timeUnit,
                      newPricingPlan.targetTimeUnit,
                      newPricingPlan.discountPercentage,
                      newPricingPlan.discountActive
                    ),
                    Currency.USD
                  )} USD /
                  {formatCurrency(
                    calculateFinalPriceWithTimeConversion(
                      newPricingPlan.amountNGN,
                      newPricingPlan.duration,
                      newPricingPlan.timeUnit,
                      newPricingPlan.targetTimeUnit,
                      newPricingPlan.discountPercentage,
                      newPricingPlan.discountActive
                    ),
                    Currency.NGN
                  )} NGN
                  {newPricingPlan.isPerUser ? ' per user' : ''}
                  {newPricingPlan.targetTimeUnit
                    ? ` per ${getTimeUnitLabel(newPricingPlan.targetTimeUnit).toLowerCase?.()}`
                    : ` per ${newPricingPlan.duration} ${getTimeUnitLabel(newPricingPlan.timeUnit).toLowerCase?.()}${newPricingPlan.duration > 1 ? 's' : ''}`
                  }

                  {newPricingPlan.discountActive && newPricingPlan.discountPercentage > 0 && (
                    <span className="ml-1 text-green-500 text-sm">
                      ({newPricingPlan.discountPercentage}% off)
            </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          <FormField label="Products" required>
            <div className="space-y-2 border border-gray-200 dark:border-gray-700 rounded-md p-3">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Select one or more products for this pricing plan. The first product will be used to determine features.
              </p>

              {products.map(product => (
                <div key={product._id}
                     className="flex items-center p-2 border-b last:border-b-0 border-gray-100 dark:border-gray-800">
                  <Checkbox
                    id={`product-${product._id}`}
                    checked={newPricingPlan.productItems.some(item => item.productId === product._id)}
                    onChange={(checked) => updateProductItems(product._id, checked)}
                    disabled={submitting}
                  />
                  <label htmlFor={`product-${product._id}`} className="ml-2 flex-grow">
                    <span className="font-medium">{product.name}</span>
                    <span className="ml-2 text-sm text-gray-500">({product.category})</span>
                  </label>
                </div>
              ))}

              {products.length === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                  No products available. Please create a product first.
                </p>
              )}
            </div>
          </FormField>

          <FormField>
            <div className="flex items-center mb-4">
              <Checkbox
                id="isPerUser"
                checked={newPricingPlan.isPerUser}
                onChange={(checked) => updateFormField('isPerUser', checked)}
                disabled={submitting}
              />
              <label htmlFor="isPerUser" className="ml-2 text-sm">
                Per-user pricing
              </label>
            </div>
          </FormField>

          <FormField label="Active" htmlFor="active">
            <div className="flex items-center">
              <Checkbox
                id="active"
                checked={newPricingPlan.active}
                onChange={(checked) => updateFormField('active', checked)}
              />
              <label htmlFor="active" className="ml-2 text-sm">
                Pricing plan is active
              </label>
            </div>
          </FormField>

          <FormField label="Features" required>
            {newPricingPlan.productItems.length > 0 ? (
              loadingFeatures ? (
                <div
                  className="border border-gray-200 dark:border-gray-700 rounded-md p-3 h-64 flex items-center justify-center">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Loading features...
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Product selector */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Select product to configure features
                    </label>
                    <select
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md
                                bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      value={currentProductId || ''}
                      onChange={(e) => handleProductChange(e.target.value)}
                      disabled={submitting}
                    >
                      <option value="" disabled>Select a product</option>
                      {newPricingPlan.productItems.map(item => {
                        const product = products.find(p => p._id === item.productId);
                        return product ? (
                          <option key={product._id} value={product._id}>
                            {product.name} - {product.category}
                          </option>
                        ) : null;
                      })}
                    </select>
                  </div>

                  {selectedProduct ? (
                    <div
                      className="border border-gray-200 dark:border-gray-700 rounded-md p-3 max-h-64 overflow-y-auto space-y-2">
                      <p className="text-sm font-medium mb-2">
                        Select features for: {selectedProduct.name}
                      </p>

                      {productFeatures?.length > 0 ? (
                        productFeatures.map((feature) => {
                          // Find the selected product item
                          const productItem = newPricingPlan.productItems.find(
                            item => item.productId === selectedProduct._id
                          );

                          // Check if this feature is selected for the current product
                          const isSelected = productItem?.features?.includes(feature._id) || false;

                          return (
                            <div key={feature._id} className="flex items-center">
                              <Checkbox
                                id={`feature-${feature._id}`}
                                checked={isSelected}
                                onChange={() => handleToggleFeature(feature._id)}
                                disabled={submitting}
                              />
                              <label htmlFor={`feature-${feature._id}`} className="ml-2 text-sm cursor-pointer">
                                {feature.name}
                                {feature.description && (
                                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    {feature.description}
                                  </p>
                                )}
                              </label>
                            </div>
                          );
                        })
                      ) : (
                        <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                          This product has no features defined.
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                      Select a product to view its available features.
                    </p>
                  )}
                </div>
              )
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                Please select at least one product to configure features.
              </p>
            )}
          </FormField>

        </Form>
      </Dialog>

      {/* Edit Pricing Plan Dialog */}
      <Dialog
        open={editDialogVisible}
        onOpenChange={(open) => !open && setEditDialogVisible(false)}
        title="Edit Pricing Plan"
        size="large"
        footer={
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setEditDialogVisible(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleUpdatePricingPlan}
              disabled={submitting}
              loading={submitting}
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
            >
              Update Plan
            </Button>
          </div>
        }
      >
        <Form className="space-y-4">
          {error && editDialogVisible && (
            <ErrorAlert onClose={() => setError(null)}>
              {error}
            </ErrorAlert>
          )}

          {validationMessage && (
            <ErrorAlert onClose={() => setValidationMessage(null)}>
              {validationMessage}
            </ErrorAlert>
          )}

          <FormField label="Name" htmlFor="edit-name" required>
            <Input
              id="edit-name"
              value={editPricingPlan.name}
              onChange={(e) => updateEditFormField('name', e.target.value)}
              placeholder="Premium Plan"
              disabled={submitting}
            />
          </FormField>
          <FormField label="Level" htmlFor="level">
            <div className="relative">
              <select
                id="level"
                className="block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={editPricingPlan.level || ''}
                onChange={(e) => {
                  const selectedValue = e.target.value;
                  updateEditFormField('level', selectedValue);
                }}
                disabled={submitting}
              >
                <option value="">No Level Selected</option>
                {[1,2,3,4,5,6,7,8,9].map(level => (
                  <option key={level} value={level}>
                    {level}
                  </option>
                ))}
              </select>
            </div>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Convert base unit to different time units (optional)
            </p>
          </FormField>
          <FormField label="Code (Generated)" htmlFor="edit-code">
            <Input
              id="edit-code"
              value={editPricingPlan.code}
              disabled={true}
              className="opacity-80"
            />
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Automatically generated from the plan name
            </p>
          </FormField>

          <FormField label="Description" htmlFor="edit-description">
            <Textarea
              id="edit-description"
              value={editPricingPlan.description}
              onChange={(e) => updateEditFormField('description', e.target.value)}
              rows={3}
              placeholder="All premium features with 24/7 support"
              disabled={submitting}
            />
          </FormField>

          <div className="grid grid-cols-1 gap-4">
            <FormField label="Base Time Unit" htmlFor="timeUnit" required>
              <Select
                id="timeUnit"
                value={editPricingPlan.timeUnit}
                onChange={(value) => updateEditFormField('timeUnit', value)}
                options={[
                  {value: TimeUnit.DAY, label: 'Day'},
                  {value: TimeUnit.WEEK, label: 'Week'},
                  {value: TimeUnit.MONTH, label: 'Month'},
                  {value: TimeUnit.YEAR, label: 'Year'}
                ]}
                disabled={submitting}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Select the base time unit for pricing
              </p>
            </FormField>

            <FormField label="Duration" htmlFor="duration" required>
              <Input
                id="duration"
                type="number"
                min={1}
                value={editPricingPlan.duration.toString()}
                onChange={(e) => updateEditFormField('duration', parseInt(e.target.value, 10) || 1)}
                disabled={submitting}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Number of base time units (e.g., 1 month, 3 months, etc.)
              </p>
            </FormField>

            <FormField label="Time Unit Conversion" htmlFor="targetTimeUnit">
              <div className="relative">
                <select
                  id="targetTimeUnit"
                  className="block w-full rounded-md border border-gray-300 dark:border-gray-700 px-3 py-2 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  value={editPricingPlan.targetTimeUnit || ''}
                  onChange={(e) => {
                    const selectedValue = e.target.value;
                    updateEditFormField('targetTimeUnit', selectedValue ? selectedValue as TimeUnit : null);
                  }}
                  disabled={submitting}
                >
                  <option value="">No conversion (use base unit)</option>
                  {getTimeUnitConversions(editPricingPlan.timeUnit).map(conversion => (
                    <option key={conversion.targetUnit} value={conversion.targetUnit}>
                      {conversion.label}
                    </option>
                  ))}
                </select>
              </div>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Convert base unit to different time units (optional)
              </p>
            </FormField>

            <div>
              <h3 className="text-lg font-semibold mb-3">Pricing</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField label="USD Amount" htmlFor="edit-amountUSD" required>
                  <InputCurrency
                    id="edit-amountUSD"
                    value={editPricingPlan.amountUSD}
                    onChange={(value) => updateEditFormField('amountUSD', value)}
                    disabled={submitting}
                    currency="USD"
                  />
                </FormField>

                <FormField label="NGN Amount" htmlFor="edit-amountNGN" required>
                  <InputCurrency
                    id="edit-amountNGN"
                    value={editPricingPlan.amountNGN}
                    onChange={(value) => updateEditFormField('amountNGN', value)}
                    disabled={submitting}
                    currency="NGN"
                  />
                </FormField>
              </div>
            </div>

            <FormField label="Default Currency" htmlFor="edit-currency">
              <Select
                id="edit-currency"
                value={editPricingPlan.currency}
                onChange={(value) => updateEditFormField('currency', value)}
                options={[
                  {value: Currency.USD, label: 'USD (Default for non-Nigerian customers)'},
                  {value: Currency.NGN, label: 'NGN (Default for Nigerian customers)'}
                ]}
                disabled={submitting}
              />
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                This will be used as the default display currency. Customers will be charged in the appropriate currency
                based on their location.
              </p>
            </FormField>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField label="Discount %" htmlFor="edit-discountPercentage">
                <Input
                  id="edit-discountPercentage"
                  type="number"
                  min={0}
                  max={100}
                  value={editPricingPlan.discountPercentage.toString()}
                  onChange={(e) => {
                    const value = parseInt(e.target.value, 10);
                    updateEditFormField('discountPercentage', isNaN(value) ? 0 : Math.min(100, Math.max(0, value)));
                  }}
                  disabled={submitting || !editPricingPlan.discountActive}
                />
              </FormField>

              <FormField label="Enable Discount" htmlFor="edit-discountActive">
                <div className="flex items-center">
                  <Checkbox
                    id="edit-discountActive"
                    checked={editPricingPlan.discountActive}
                    onChange={(checked) => updateEditFormField('discountActive', checked)}
                    disabled={submitting}
                  />
                  <label htmlFor="edit-discountActive" className="ml-2 text-sm">
                    Apply discount to this plan
                  </label>
                </div>
              </FormField>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md">
              <h4 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Price Summary</h4>
              <div className="space-y-2">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  <span className="font-medium">Base Price: </span>
                  {formatCurrency(editPricingPlan.amountUSD, Currency.USD)} USD /
                  {formatCurrency(editPricingPlan.amountNGN, Currency.NGN)} NGN
                  per {getTimeUnitLabel(editPricingPlan.timeUnit).toLowerCase?.()}
                </p>

                {editPricingPlan.duration > 1 && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <span className="font-medium">Duration: </span>
                    {editPricingPlan.duration} {getTimeUnitLabel(editPricingPlan.timeUnit).toLowerCase?.()}
                    {editPricingPlan.duration > 1 ? 's' : ''}
                  </p>
                )}

                {editPricingPlan.targetTimeUnit && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <span className="font-medium">Conversion: </span>
                    {editPricingPlan.duration} {getTimeUnitLabel(editPricingPlan.timeUnit).toLowerCase?.()}
                    {editPricingPlan.duration > 1 ? 's' : ''} →
                    {getTimeUnitLabel(editPricingPlan.targetTimeUnit).toLowerCase?.()}
                  </p>
                )}

                {editPricingPlan.discountActive && editPricingPlan.discountPercentage > 0 && (
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <span className="font-medium">Discount: </span>
                    {editPricingPlan.discountPercentage}% off
                  </p>
                )}

                <p className="text-sm text-blue-700 dark:text-blue-300 font-bold">
                  <span className="font-medium">Final Price: </span>
                  {formatCurrency(
                    calculateFinalPriceWithTimeConversion(
                      editPricingPlan.amountUSD,
                      editPricingPlan.duration,
                      editPricingPlan.timeUnit,
                      editPricingPlan.targetTimeUnit,
                      editPricingPlan.discountPercentage,
                      editPricingPlan.discountActive
                    ),
                    Currency.USD
                  )} USD /
                  {formatCurrency(
                    calculateFinalPriceWithTimeConversion(
                      editPricingPlan.amountNGN,
                      editPricingPlan.duration,
                      editPricingPlan.timeUnit,
                      editPricingPlan.targetTimeUnit,
                      editPricingPlan.discountPercentage,
                      editPricingPlan.discountActive
                    ),
                    Currency.NGN
                  )} NGN
                  {editPricingPlan.isPerUser ? ' per user' : ''}
                  {editPricingPlan.targetTimeUnit
                    ? ` per ${getTimeUnitLabel(editPricingPlan.targetTimeUnit).toLowerCase?.()}`
                    : ` per ${editPricingPlan.duration} ${getTimeUnitLabel(editPricingPlan.timeUnit).toLowerCase?.()}${editPricingPlan.duration > 1 ? 's' : ''}`
                  }

                  {editPricingPlan.discountActive && editPricingPlan.discountPercentage > 0 && (
                    <span className="ml-1 text-green-500 text-sm">
                      ({editPricingPlan.discountPercentage}% off)
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>

          <FormField label="Products" required>
            <div className="space-y-2 border border-gray-200 dark:border-gray-700 rounded-md p-3">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Select one or more products for this pricing plan. The first product will be used to determine features.
              </p>

              {products.map(product => (
                <div key={product._id}
                     className="flex items-center p-2 border-b last:border-b-0 border-gray-100 dark:border-gray-800">
                  <Checkbox
                    id={`edit-product-${product._id}`}
                    checked={editPricingPlan.productItems.some(item => item.productId === product._id)}
                    onChange={(checked) => updateEditProductItems(product._id, checked)}
                    disabled={submitting}
                  />
                  <label htmlFor={`edit-product-${product._id}`} className="ml-2 flex-grow">
                    <span className="font-medium">{product.name}</span>
                    <span className="ml-2 text-sm text-gray-500">({product.category})</span>
                  </label>
                </div>
              ))}

              {products.length === 0 && (
                <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                  No products available. Please create a product first.
                </p>
              )}
            </div>
          </FormField>

          <FormField>
            <div className="flex items-center mb-4">
              <Checkbox
                id="edit-isPerUser"
                checked={editPricingPlan.isPerUser}
                onChange={(checked) => updateEditFormField('isPerUser', checked)}
                disabled={submitting}
              />
              <label htmlFor="edit-isPerUser" className="ml-2 text-sm">
                Per-user pricing
              </label>
            </div>
          </FormField>

          <FormField label="Active" htmlFor="edit-active">
            <div className="flex items-center">
              <Checkbox
                id="edit-active"
                checked={editPricingPlan.active}
                onChange={(checked) => updateEditFormField('active', checked)}
              />
              <label htmlFor="edit-active" className="ml-2 text-sm">
                Pricing plan is active
              </label>
            </div>
          </FormField>

          <FormField label="Features" required>
            {editPricingPlan.productItems.length > 0 ? (
              loadingFeatures ? (
                <div
                  className="border border-gray-200 dark:border-gray-700 rounded-md p-3 h-64 flex items-center justify-center">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Loading features...
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Product selector */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Select product to configure features
                    </label>
                    <select
                      className="w-full p-2 border border-gray-300 dark:border-gray-700 rounded-md
                                bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      value={currentProductId || ''}
                      onChange={(e) => handleProductChange(e.target.value)}
                      disabled={submitting}
                    >
                      <option value="" disabled>Select a product</option>
                      {editPricingPlan.productItems.map(item => {
                        const product = products.find(p => p._id === item.productId);
                        return product ? (
                          <option key={product._id} value={product._id}>
                            {product.name} - {product.category}
                          </option>
                        ) : null;
                      })}
                    </select>
                  </div>

                  {selectedProduct ? (
                    <div
                      className="border border-gray-200 dark:border-gray-700 rounded-md p-3 max-h-64 overflow-y-auto space-y-2">
                      <p className="text-sm font-medium mb-2">
                        Select features for: {selectedProduct.name}
                      </p>

                      {productFeatures?.length > 0 ? (
                        productFeatures.map((feature) => {
                          // Find the selected product item
                          const productItem = editPricingPlan.productItems.find(
                            item => item.productId === selectedProduct._id
                          );

                          // Check if this feature is selected for the current product
                          const isSelected = productItem?.features?.includes(feature._id) || false;

                          return (
                            <div key={feature._id} className="flex items-center">
                              <Checkbox
                                id={`edit-feature-${feature._id}`}
                                checked={isSelected}
                                onChange={() => handleToggleEditFeature(feature._id)}
                                disabled={submitting}
                              />
                              <label htmlFor={`edit-feature-${feature._id}`} className="ml-2 text-sm cursor-pointer">
                                {feature.name}
                                {feature.description && (
                                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                    {feature.description}
                                  </p>
                                )}
                              </label>
                            </div>
                          );
                        })
                      ) : (
                        <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                          This product has no features defined.
                        </p>
                      )}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                      Select a product to view its available features.
                    </p>
                  )}
                </div>
              )
            ) : (
              <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                Please select at least one product to configure features.
              </p>
            )}
          </FormField>
        </Form>
      </Dialog>
    </div>
  );
}



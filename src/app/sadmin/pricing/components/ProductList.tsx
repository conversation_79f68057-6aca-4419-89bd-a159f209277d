'use client';

import React, { useState, useEffect, useRef } from 'react';
import { getProducts, updateProduct } from '@/server/actions/pricing';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';
import { Dialog } from '@/components/ui/Dialog';
import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { Checkbox } from '@/components/ui/Checkbox';
import { Form, FormField } from '@/components/ui/Form';
import { Skeleton } from '@/components/ui/Skeleton';

interface Product {
  _id: string;
  name: string;
  code: string;
  description: string;
  features: string[];
  category: string;
  active: boolean;
  createdAt: string;
}

export default function ProductList() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialogVisible, setEditDialogVisible] = useState(false);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [editFormData, setEditFormData] = useState({
    id: '',
    name: '',
    description: '',
    features: [] as string[],
    active: true
  });
  const [featureInput, setFeatureInput] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const { success, error: showError } = useToast();

  // Fetch products on component mount
  useEffect(() => {
    fetchProducts();
  }, []);

  // Fetch products
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const result = await getProducts();
      if (result.success && result.data) {
        setProducts(result.data);
      } else {
        showError('Error', result.error || 'Failed to load products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Open edit dialog
  const openEditDialog = (product: Product) => {
    setCurrentProduct(product);
    setEditFormData({
      id: product._id,
      name: product.name,
      description: product.description,
      features: product.features || [],
      active: product.active
    });
    setEditDialogVisible(true);
  };

  // Add feature
  const addFeature = () => {
    if (featureInput.trim()) {
      setEditFormData({
        ...editFormData,
        features: [...editFormData.features, featureInput.trim()]
      });
      setFeatureInput('');
    }
  };

  // Remove feature
  const removeFeature = (index: number) => {
    const updatedFeatures = [...editFormData.features];
    updatedFeatures.splice(index, 1);
    setEditFormData({
      ...editFormData,
      features: updatedFeatures
    });
  };

  // Toggle product active status
  const toggleProductStatus = async (product: Product) => {
    setSubmitting(true);
    try {
      const result = await updateProduct({
        id: product._id,
        active: !product.active
      });

      if (result.success) {
        success('Success', `Product ${!product.active ? 'activated' : 'deactivated'} successfully`);
        // Update product in list
        setProducts(products.map(p =>
          p._id === product._id ? { ...p, active: !p.active } : p
        ));
      } else {
        showError('Error', result.error || 'Failed to update product status');
      }
    } catch (error) {
      console.error('Error updating product status:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle update product
  const handleUpdateProduct = async () => {
    if (!editFormData.name || !editFormData.description) {
      setError('Please fill in all required fields');
      return;
    }

    setSubmitting(true);
    try {
      const result = await updateProduct(editFormData);

      if (result.success) {
        success('Success', 'Product updated successfully');
        setEditDialogVisible(false);

        // Update product in list
        setProducts(products.map(p =>
          p._id === editFormData.id ? { ...p, ...editFormData } : p
        ));
      } else {
        showError('Error', result.error || 'Failed to update product');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center mb-4">
          <Skeleton className="h-8 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold">Products</h2>
      </div>

      {products.length === 0 ? (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-md text-center text-gray-500 dark:text-gray-400">
          <p>No products found. Products are created through the admin initialization process.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {products.map((product) => (
            <Card key={product._id} className="p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-lg dark:text-white">{product.name}</h3>
                <Badge
                  variant={product.active ? 'success' : 'danger'}
                >
                  <span className="cursor-pointer" onClick={() => toggleProductStatus(product)}>
                    {product.active ? 'Active' : 'Inactive'}
                  </span>
                </Badge>
              </div>
              <div className="text-gray-500 dark:text-gray-400 text-sm mb-1">Code: {product.code}</div>
              {product.category === 'premium' && (
                <Badge variant="warning" className="mb-2">Premium</Badge>
              )}
              {product.category === 'core' && (
                <Badge variant="info" className="mb-2">Core</Badge>
              )}
              <p className="text-gray-700 dark:text-gray-300 mb-4">{product.description}</p>

              {product.features && product.features.length > 0 && (
                <div className="space-y-1 mb-4">
                  <h4 className="font-medium text-sm mb-1 dark:text-gray-300">Features:</h4>
                  <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400">
                    {product.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="flex justify-end space-x-2 mt-2">
                <Button
                  variant="secondary"
                  onClick={() => openEditDialog(product)}
                >
                  Edit
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Product Dialog */}
      <Dialog
        open={editDialogVisible}
        onOpenChange={(open) => !open && setEditDialogVisible(false)}
        title="Edit Product"
        footer={
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={() => setEditDialogVisible(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleUpdateProduct}
              disabled={submitting}
              loading={submitting}
            >
              Update Product
            </Button>
          </div>
        }
      >
        {error && (
          <div className="mb-4 p-2 bg-red-50 text-red-700 rounded-md border border-red-200 text-sm">
            {error}
          </div>
        )}

        <Form className="space-y-4">
          <FormField label="Name" required>
            <Input
              value={editFormData.name}
              onChange={(e) => setEditFormData({...editFormData, name: e.target.value})}
            />
          </FormField>

          <FormField label="Code (Read-only)">
            <Input
              value={currentProduct?.code || ''}
              readOnly
              disabled
              className="opacity-70"
            />
          </FormField>

          <FormField label="Description" required>
            <Textarea
              value={editFormData.description}
              onChange={(e) => setEditFormData({...editFormData, description: e.target.value})}
              rows={3}
            />
          </FormField>

          <FormField label="Features">
            <div className="space-y-2">
              <div className="flex">
                <Input
                  value={featureInput}
                  onChange={(e) => setFeatureInput(e.target.value)}
                  placeholder="Add a feature"
                  className="flex-1"
                />
                <Button
                  variant="secondary"
                  onClick={addFeature}
                  className="ml-2"
                >
                  Add
                </Button>
              </div>

              {editFormData.features.length > 0 && (
                <div className="mt-2">
                  <ul className="space-y-1">
                    {editFormData.features.map((feature, index) => (
                      <li key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-800 p-2 rounded">
                        <span className="text-sm">{feature}</span>
                        <Button
                          variant="ghost"
                          onClick={() => removeFeature(index)}
                          size="sm"
                          className="text-red-600 hover:text-red-800"
                        >
                          Remove
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </FormField>

          <FormField>
            <div className="flex items-center">
              <Checkbox
                checked={editFormData.active}
                onChange={(checked) => setEditFormData({...editFormData, active: checked})}
                id="isActive"
              />
              <label htmlFor="isActive" className="ml-2 text-sm">
                Active
              </label>
            </div>
          </FormField>
        </Form>
      </Dialog>
    </div>
  );
}

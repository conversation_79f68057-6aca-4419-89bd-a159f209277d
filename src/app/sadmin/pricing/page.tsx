'use client';

import React from 'react';
import { Tabs } from '@/components/ui/Tabs';
import { Card, CardContent } from '@/components/ui/Card';
import { useToast } from '@/components/ui/Toast';
import OrganizationList from './components/OrganizationList';
import ProductList from './components/ProductList';
import PricingList from './components/PricingList';
import SubscriptionList from './components/SubscriptionList';

export default function PricingPage() {

  const tabs = [
    {
      id: 'organizations',
      label: 'Organizations',
      content: <OrganizationList />
    },
    {
      id: 'products',
      label: 'Products',
      content: <ProductList />
    },
    {
      id: 'pricing',
      label: 'Pricing Plans',
      content: <PricingList />
    },
    {
      id: 'subscriptions',
      label: 'Subscriptions',
      content: <SubscriptionList />
    }
  ];

  return (
    <div className="p-6 max-w-7xl mx-auto pricing-admin-wrapper">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">Pricing Management</h1>
        <p className="text-gray-500 dark:text-gray-400">Configure products, pricing plans, and subscriptions for your organizations</p>
      </div>

      <Card hasBorder={false}>
        <CardContent>
          <Tabs tabs={tabs} />
        </CardContent>
      </Card>
    </div>
  );
}

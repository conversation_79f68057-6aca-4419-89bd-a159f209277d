'use client';

import { Tabs } from '@/components/ui/Tabs';
import TaxSettings from './components/TaxSettings';
import GeneralSettings from './components/GeneralSettings';
import SecuritySettings from './components/SecuritySettings';

export default function SettingsPage() {
  const tabs = [
    {
      id: 'general',
      label: 'General',
      content: <GeneralSettings />
    },
    {
      id: 'security',
      label: 'Security',
      content: <SecuritySettings />
    },
    {
      id: 'tax',
      label: 'Tax',
      content: <TaxSettings />
    }
  ];

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Platform Settings</h1>
      <Tabs
        tabs={tabs}
        defaultActiveTab="general"
      />
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Form, FormField, FormSection } from '@/components/ui/Form';
import { <PERSON><PERSON><PERSON>t, Error<PERSON>lert, InfoAlert } from '@/components/ui/Alert';
import { Chip } from '@/components/ui/Chip';
import { Card } from '@/components/ui/Card';

import {
  getSettings,
  updateSettings,
  addEmailDomain,
  removeEmailDomain
} from '@/server/actions/admin-settings';

type GeneralSettings = {
  maxUploadSize: number;
  acceptedEmailDomains?: string[];
};

export default function GeneralSettings() {
  // State for form values
  const [settings, setSettings] = useState<GeneralSettings>({
    maxUploadSize: 10,
    acceptedEmailDomains: [],
  });

  const [loading, setLoading] = useState(false);
  const [fetching, setFetching] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newDomain, setNewDomain] = useState('');

  // Fetch settings from the server
  useEffect(() => {
    const fetchSettings = async () => {
      setFetching(true);
      try {
        const response = await getSettings();
        if (response.success && response.data) {
          setSettings({
            maxUploadSize: response.data.maxUploadSize || 10,
            acceptedEmailDomains: response.data.acceptedEmailDomains || [],
          });
        } else {
          setError(response.error || 'Failed to fetch settings');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch settings');
      } finally {
        setFetching(false);
      }
    };

    fetchSettings();
  }, []);

  // Handle input changes
  const handleChange = (field: keyof GeneralSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear messages when form is edited
    setSuccess(false);
    setError(null);
  };

  // Add a domain to the accepted list
  const handleAddDomain = async () => {
    if (!newDomain) return;

    setLoading(true);
    setSuccess(false);
    setError(null);

    try {
      const response = await addEmailDomain(newDomain);

      if (response.success && response.data) {
        setSettings(prev => ({
          ...prev,
          acceptedEmailDomains: response.data.acceptedEmailDomains || [],
        }));
        setNewDomain('');
        setSuccess(true);
      } else {
        setError(response.error || 'Failed to add domain');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add domain');
    } finally {
      setLoading(false);
    }
  };

  // Remove a domain from the accepted list
  const handleRemoveDomain = async (domain: string) => {
    setLoading(true);
    setSuccess(false);
    setError(null);

    try {
      const response = await removeEmailDomain(domain);

      if (response.success && response.data) {
        setSettings(prev => ({
          ...prev,
          acceptedEmailDomains: response.data.acceptedEmailDomains || [],
        }));
        setSuccess(true);
      } else {
        setError(response.error || 'Failed to remove domain');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove domain');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(false);
    setError(null);

    try {
      // Update settings via server action
      const response = await updateSettings({
        maxUploadSize: settings.maxUploadSize,
        key: 'general'
      });

      if (response.success) {
        setSuccess(true);
      } else {
        setError(response.error || 'Failed to save settings');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  // Handle keypress in domain input
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddDomain();
    }
  };

  if (fetching) {
    return (
      <div className="py-8 text-center">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">General Settings</h2>

      <div className="mb-6 p-4 bg-blue-50/50 dark:bg-blue-900/10 text-blue-800 dark:text-blue-300 rounded-xl border border-blue-100/50 dark:border-blue-800/30 backdrop-blur-sm">
        <p className="text-sm">
          <strong>Note:</strong> Basic platform information like Platform Name and Support Email are managed through environment variables for consistency across services.
        </p>
      </div>

      {success && (
        <SuccessAlert className="mb-4">
          Settings saved successfully
        </SuccessAlert>
      )}

      {error && (
        <ErrorAlert className="mb-4">
          {error}
        </ErrorAlert>
      )}

      <Form onSubmit={handleSubmit}>
        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 rounded-xl backdrop-blur-sm mb-6">
          <FormSection
            title="Accepted Email Domains"
            description="Only email addresses from these domains will be allowed for admin accounts"
          >
            <FormField
              label="Add Domain"
              htmlFor="newDomain"
              description="Enter domain names without the @ symbol (e.g., company.com)"
            >
              <div className="flex gap-2">
                <Input
                  id="newDomain"
                  value={newDomain}
                  onChange={(e) => setNewDomain(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="yourdomain.com"
                  className="flex-grow"
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleAddDomain}
                  loading={loading}
                  disabled={loading}
                  className="bg-gradient-to-r from-[#8178E8]/90 to-[#6964D3]/90 hover:opacity-90 text-white border-0"
                >
                  Add
                </Button>
              </div>
            </FormField>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Accepted Domains
              </label>
              {settings.acceptedEmailDomains?.length === 0 && (
                <InfoAlert className="mb-4 bg-blue-50/50 dark:bg-blue-900/10 border-blue-100/50 dark:border-blue-800/30">
                  <div className="flex flex-col gap-1">
                    <strong>Security Notice:</strong>
                    <p>No domain restrictions are currently in place. This means anyone with any email domain can be added as an admin.</p>
                    <p>For better security, consider adding domain restrictions to limit admin accounts to specific company domains.</p>
                  </div>
                </InfoAlert>
              )}
              <div className="flex flex-wrap gap-2 p-4 border border-gray-200/50 dark:border-gray-700/30 rounded-md min-h-[20px] bg-gray-50/50 dark:bg-gray-800/20">
                {settings.acceptedEmailDomains?.length ? (
                  settings.acceptedEmailDomains.map(domain => (
                    <Chip
                      key={domain}
                      color="blue"
                      onDelete={() => !loading && handleRemoveDomain(domain)}
                      className="h-[45px] flex items-center text-base bg-[#8178E8]/10 text-[#6964D3] dark:text-[#8178E8] border-[#8178E8]/20"
                    >
                      {domain}
                    </Chip>
                  ))
                ) : (
                  <div className="text-gray-500 dark:text-gray-400 text-sm self-center w-full text-center">
                    No domains added. All email domains will be accepted.
                  </div>
                )}
              </div>
            </div>
          </FormSection>
        </Card>

        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 rounded-xl backdrop-blur-sm">
          <FormSection
            title="File Upload Settings"
            description="Configure file upload restrictions"
          >
            <FormField label="Maximum Upload Size (MB)" htmlFor="maxUploadSize" required>
              <Input
                id="maxUploadSize"
                type="number"
                min="1"
                value={settings.maxUploadSize.toString()}
                onChange={(e) => handleChange('maxUploadSize', parseInt(e.target.value) || 1)}
                disabled={loading}
              />
            </FormField>
          </FormSection>

          <div className="mt-6 flex justify-end">
            <Button
              type="submit"
              variant="primary"
              loading={loading}
              disabled={loading}
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:opacity-90 text-white border-0"
            >
              Save Settings
            </Button>
          </div>
        </Card>
      </Form>
    </div>
  );
}

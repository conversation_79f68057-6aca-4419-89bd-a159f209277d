'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Form, FormField, FormSection } from '@/components/ui/Form';
import { SuccessA<PERSON>t, ErrorAlert } from '@/components/ui/Alert';
import { InputSwitch } from 'primereact/inputswitch';
import { z } from 'zod';

// Validation schema
const emailSettingsSchema = z.object({
  fromEmail: z.string().email('Invalid email address'),
  fromName: z.string().min(2, 'From name must be at least 2 characters'),
  emailVerificationRequired: z.boolean(),
});

type EmailSettings = z.infer<typeof emailSettingsSchema>;

export default function EmailSettings() {
  // State for form values
  const [settings, setSettings] = useState<EmailSettings>({
    fromEmail: '<EMAIL>',
    fromName: 'New Instance',
    emailVerificationRequired: true,
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testEmailSent, setTestEmailSent] = useState(false);

  // Handle input changes
  const handleChange = (field: keyof EmailSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear messages when form is edited
    setSuccess(false);
    setError(null);
    setTestEmailSent(false);
  };

  // Send test email
  const handleTestEmail = async () => {
    setLoading(true);
    setTestEmailSent(false);
    setError(null);

    try {
      // Here would be the API call to send a test email
      // For now, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTestEmailSent(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send test email');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(false);
    setError(null);

    try {
      // Here would be the API call to save settings
      // For now, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Validate using schema
      const validationResult = emailSettingsSchema.safeParse(settings);
      if (!validationResult.success) {
        throw new Error(validationResult.error.errors[0].message);
      }

      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Email Settings</h2>

      {success && (
        <SuccessAlert className="mb-4">
          Email settings saved successfully
        </SuccessAlert>
      )}

      {testEmailSent && (
        <SuccessAlert className="mb-4">
          Test email sent successfully
        </SuccessAlert>
      )}

      {error && (
        <ErrorAlert className="mb-4">
          {error}
        </ErrorAlert>
      )}

      <Form onSubmit={handleSubmit}>
        <FormSection
          title="Email Information"
          description="Configure outgoing email settings"
        >
          <FormField label="From Email" htmlFor="fromEmail" required>
            <Input
              id="fromEmail"
              type="email"
              value={settings.fromEmail}
              onChange={(e) => handleChange('fromEmail', e.target.value)}
            />
          </FormField>

          <FormField label="From Name" htmlFor="fromName" required>
            <Input
              id="fromName"
              value={settings.fromName}
              onChange={(e) => handleChange('fromName', e.target.value)}
            />
          </FormField>
        </FormSection>

        <FormSection
          title="Email Verification"
          description="User email verification settings"
        >
          <FormField label="Require Email Verification" htmlFor="emailVerificationRequired">
            <div className="flex items-center">
              <InputSwitch
                id="emailVerificationRequired"
                checked={settings.emailVerificationRequired}
                onChange={(e) => handleChange('emailVerificationRequired', e.value)}
                className="mr-2"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {settings.emailVerificationRequired ? 'Required' : 'Optional'}
              </span>
            </div>
          </FormField>
        </FormSection>

        <div className="mt-6 flex justify-between">
          <Button
            type="button"
            variant="secondary"
            onClick={handleTestEmail}
            loading={loading && !success}
            disabled={loading}
          >
            Send Test Email
          </Button>

          <Button
            type="submit"
            variant="primary"
            loading={loading && !testEmailSent}
            disabled={loading}
          >
            Save Settings
          </Button>
        </div>
      </Form>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';
import {getSettings, updateSettings} from "@server/actions/admin-settings";
import { logger } from '@/utils/logger';

export default function TaxSettings() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [vatPercentage, setVatPercentage] = useState<number>(0);
  const [taxPercentage, setTaxPercentage] = useState<number>(0);
  const [initialVat, setInitialVat] = useState<number>(0);
  const [initialTax, setInitialTax] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const { success: showSuccess, error: showError } = useToast();

  // Load initial data
  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await getSettings("system");
        if (result.success && result.data) {
          const vat = result.data.vatPercentage ?? 0;
          const tax = result.data.taxPercentage ?? 0;
          setVatPercentage(vat);
          setTaxPercentage(tax);
          setInitialVat(vat);
          setInitialTax(tax);
        } else {
          setError(result.error || 'Failed to load system settings');
        }
      } catch (err: any) {
        logger.error('Error loading settings:', err);
        setError(err.message || 'An error occurred while loading settings');
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleSave = async () => {
    setSaving(true);
    setError(null);

    try {
      const result = await updateSettings({
        key: 'system',
        vatPercentage,
        taxPercentage
      });

      if (result.success) {
        showSuccess('Settings updated successfully');
        // Update initial values to match the new saved values
        setInitialVat(vatPercentage);
        setInitialTax(taxPercentage);
      } else {
        setError(result.error || 'Failed to update settings');
        showError(result.error || 'Failed to update settings');
      }
    } catch (err: any) {
      logger.error('Error saving settings:', err);
      setError(err.message || 'An error occurred while saving settings');
      showError(err.message || 'An error occurred while saving settings');
    } finally {
      setSaving(false);
    }
  };

  const hasChanges = vatPercentage !== initialVat || taxPercentage !== initialTax;

  // Handle number input changes
  const handleVatChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setVatPercentage(Math.min(100, Math.max(0, value)));
  };

  const handleTaxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) || 0;
    setTaxPercentage(Math.min(100, Math.max(0, value)));
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Tax Configuration</h2>

      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 rounded-xl backdrop-blur-sm">
        {error && (
          <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 rounded-lg">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              VAT Percentage (%)
            </label>
            <div className="relative">
              <Input
                type="number"
                value={vatPercentage.toString()}
                onChange={handleVatChange}
                min={0}
                max={100}
                step={0.01}
                disabled={loading || saving}
                className="pr-10"
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                %
              </span>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Value Added Tax applied to invoices. Range: 0-100%
            </p>
          </div>

          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              General Tax Percentage (%)
            </label>
            <div className="relative">
              <Input
                type="number"
                value={taxPercentage.toString()}
                onChange={handleTaxChange}
                min={0}
                max={100}
                step={0.01}
                disabled={loading || saving}
                className="pr-10"
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                %
              </span>
            </div>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              General tax rate applied to all transactions. Range: 0-100%
            </p>
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <Button
            onClick={handleSave}
            disabled={!hasChanges || loading || saving}
            loading={saving}
            className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:opacity-90 text-white"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </Card>

      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 rounded-xl backdrop-blur-sm">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Tax Settings Information</h3>
        <div className="bg-[#F8F9FD]/70 dark:bg-[#15151e]/70 rounded-lg p-4">
          <ul className="space-y-3 text-sm text-gray-600 dark:text-gray-300">
            <li className="flex items-start">
              <span className="inline-block mr-2 text-[#6964D3]">•</span>
              <span>VAT is applied to all invoices and is displayed separately on invoices.</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block mr-2 text-[#6964D3]">•</span>
              <span>General Tax is applied to all transactions and is included in the total amount.</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block mr-2 text-[#6964D3]">•</span>
              <span>These tax settings will be applied platform-wide to all transactions.</span>
            </li>
            <li className="flex items-start">
              <span className="inline-block mr-2 text-[#6964D3]">•</span>
              <span>Changes to tax rates will only affect future transactions.</span>
            </li>
          </ul>
        </div>
      </Card>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FormField, FormSection } from '@/components/ui/Form';
import { Alert } from '@/components/ui/Alert';
import { Card } from '@/components/ui/Card';
import { z } from 'zod';
import { getSecuritySettings, updateSecuritySettings, resetSecuritySettings } from '@/server/actions/admin-settings';
import { Select } from '@/components/ui/Select';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';


// Authentication methods
const authMethods = [
  { label: 'Email and Password', value: 'email' },
  { label: 'Email, Password and 2FA', value: 'email_2fa' }
];

// Session timeout options (in minutes)
const sessionTimeouts = [
  { label: '15 minutes', value: 15 },
  { label: '30 minutes', value: 30 },
  { label: '1 hour', value: 60 },
  { label: '2 hours', value: 120 },
  { label: '4 hours', value: 240 },
  { label: '8 hours', value: 480 },
  { label: '24 hours', value: 1440 },
];

// Validation schema
const securitySettingsSchema = z.object({
  authenticationMethod: z.string().min(1, 'Authentication method is required'),
  adminMfa: z.boolean(),
  sessionTimeout: z.coerce.number().min(1, 'Session timeout must be at least 1 minute'),
  maxLoginAttempts: z.coerce.number().min(1, 'Max login attempts must be at least 1'),
  passwordMinLength: z.coerce.number().min(6, 'Password minimum length must be at least 6'),
  passwordExpiryDays: z.coerce.number().min(0, 'Password expiry days cannot be negative'),
});

type SecuritySettings = z.infer<typeof securitySettingsSchema>;

export default function SecuritySettings() {
  const { success, error } = useToast();
  const [fetching, setFetching] = useState(true);

  // React Hook Form setup
  const form = useForm<SecuritySettings>({
    resolver: zodResolver(securitySettingsSchema),
    defaultValues: {
      authenticationMethod: 'email',
      adminMfa: true,
      sessionTimeout: 60,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      passwordExpiryDays: 90,
    }
  });

  const { control, handleSubmit, reset, setValue, formState: { errors, isSubmitting } } = form;

  // Fetch settings from the server
  useEffect(() => {
    const fetchSettings = async () => {
      setFetching(true);
      try {
        const response = await getSecuritySettings();

        if (response.success && response.data) {
          // Update form values with fetched data
          const formData = {
            authenticationMethod: response.data.authenticationMethod || 'email',
            adminMfa: response.data.adminMfa === true || response.data.adminMfa === 'true',
            sessionTimeout: Number(response.data.sessionTimeout || 60),
            maxLoginAttempts: Number(response.data.maxLoginAttempts || 5),
            passwordMinLength: Number(response.data.passwordMinLength || 8),
            passwordExpiryDays: Number(response.data.passwordExpiryDays || 90),
          };
          reset(formData);
        } else {
          error(response.error || 'Failed to fetch settings');
        }
      } catch (err) {
        logger.error("Error fetching security settings:", err);
        error(err instanceof Error ? err.message : 'Failed to fetch settings');
      } finally {
        setFetching(false);
      }
    };

    fetchSettings();
  }, [reset, error]);

  // Handle form submission
  const onSubmit = async (data: SecuritySettings) => {
    try {
      // Ensure we pass the key field
      const settingsData = {
        ...data,
        key: 'security'
      };

      // Update settings via server action
      const response = await updateSecuritySettings(settingsData);

      if (response.success) {
        success('Security settings saved successfully');
      } else {
        error(response.error || 'Failed to save settings');
      }
    } catch (err) {
      logger.error("Security settings save error:", err);
      error(err instanceof Error ? err.message : 'Failed to save settings');
    }
  };

  // Reset to default values
  const handleReset = async () => {
    try {
      // Call the server action to reset settings
      const response = await resetSecuritySettings();

      if (response.success && response.data) {
        // Update form with the reset values
        const formData = {
          authenticationMethod: response.data.authenticationMethod || 'email',
          adminMfa: response.data.adminMfa === true || response.data.adminMfa === 'true',
          sessionTimeout: Number(response.data.sessionTimeout || 60),
          maxLoginAttempts: Number(response.data.maxLoginAttempts || 5),
          passwordMinLength: Number(response.data.passwordMinLength || 8),
          passwordExpiryDays: Number(response.data.passwordExpiryDays || 90),
        };
        reset(formData);
        success('Settings reset to defaults successfully');
      } else {
        error(response.error || 'Failed to reset settings');
      }
    } catch (err) {
      logger.error("Error resetting security settings:", err);
      error(err instanceof Error ? err.message : 'Failed to reset settings');
    }
  };

  if (fetching) {
    return (
      <div className="py-8 text-center">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mx-auto mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Security Settings</h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 rounded-xl backdrop-blur-sm mb-6">
          <FormSection
            title="Authentication"
            description="Configure how users authenticate with the platform"
          >
            <FormField label="Authentication Method" htmlFor="authenticationMethod" required error={errors.authenticationMethod?.message}>
              <Controller
                name="authenticationMethod"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    id="authenticationMethod"
                    options={authMethods}
                    error={errors.authenticationMethod?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Require Admin 2FA"
              htmlFor="adminMfa"
              description="Force two-factor authentication for admin users"
              error={errors.adminMfa?.message}
            >
              <Controller
                name="adminMfa"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    id="adminMfa"
                    options={[
                      { label: 'Required', value: true },
                      { label: 'Optional', value: false }
                    ]}
                    error={errors.adminMfa?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Session Timeout"
              htmlFor="sessionTimeout"
              required
              description="Time before users are automatically logged out due to inactivity"
              error={errors.sessionTimeout?.message}
            >
              <Controller
                name="sessionTimeout"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    id="sessionTimeout"
                    options={sessionTimeouts}
                    error={errors.sessionTimeout?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Max Failed Login Attempts"
              htmlFor="maxLoginAttempts"
              required
              description="Number of failed login attempts before temporary lockout"
              error={errors.maxLoginAttempts?.message}
            >
              <Controller
                name="maxLoginAttempts"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="maxLoginAttempts"
                    type="number"
                    min="1"
                    max="10"
                    error={errors.maxLoginAttempts?.message}
                  />
                )}
              />
            </FormField>
          </FormSection>
        </Card>

        <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] p-6 rounded-xl backdrop-blur-sm">
          <FormSection
            title="Password Policy"
            description="Configure password requirements for your users"
          >
            <FormField
              label="Minimum Password Length"
              htmlFor="passwordMinLength"
              required
              error={errors.passwordMinLength?.message}
            >
              <Controller
                name="passwordMinLength"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="passwordMinLength"
                    type="number"
                    min="6"
                    max="32"
                    error={errors.passwordMinLength?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Password Expiry (Days)"
              htmlFor="passwordExpiryDays"
              description="Days before passwords expire and must be changed (0 for never)"
              error={errors.passwordExpiryDays?.message}
            >
              <Controller
                name="passwordExpiryDays"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="passwordExpiryDays"
                    type="number"
                    min="0"
                    error={errors.passwordExpiryDays?.message}
                  />
                )}
              />
            </FormField>
          </FormSection>

          <div className="mt-6 flex justify-between">
            <Button
              type="button"
              variant="secondary"
              onClick={handleReset}
              disabled={isSubmitting}
            >
              Reset to Defaults
            </Button>
            <Button
              type="submit"
              isLoading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </Card>
      </form>
    </div>
  );
}

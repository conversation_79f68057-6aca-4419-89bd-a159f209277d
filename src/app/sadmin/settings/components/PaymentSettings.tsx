'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FormField, FormSection } from '@/components/ui/Form';
import { Alert } from '@/components/ui/Alert';
import { z } from 'zod';
import { Select } from '@/components/ui/Select';
import { useToast } from '@/components/ui/Toast';

// Currency options
const currencies = [
  { label: 'USD - US Dollar', value: 'USD' },
  { label: 'NGN - Nigerian Naira', value: 'NGN' },
  { label: 'GHS - Ghanaian Cedi', value: 'GHS' },
  { label: 'KES - Kenyan Shilling', value: 'KES' },
  { label: 'ZAR - South African Rand', value: 'ZAR' },
  { label: 'GBP - British Pound', value: 'GBP' },
  { label: 'EUR - Euro', value: 'EUR' },
];

// Environment options
const environments = [
  { label: 'Test Mode', value: 'test' },
  { label: 'Live Mode', value: 'live' },
];

// Billing cycle options
const billingCycles = [
  { label: 'Monthly', value: 'monthly' },
  { label: 'Quarterly', value: 'quarterly' },
  { label: 'Annually', value: 'annually' },
];

// Validation schema
const paymentSettingsSchema = z.object({
  paymentProcessor: z.string().min(1, 'Payment processor is required'),
  environment: z.string().min(1, 'Environment is required'),
  defaultCurrency: z.string().min(1, 'Default currency is required'),
  defaultBillingCycle: z.string().min(1, 'Default billing cycle is required'),
  trialPeriodDays: z.coerce.number().min(0, 'Trial period cannot be negative'),
});

type PaymentSettings = z.infer<typeof paymentSettingsSchema>;

export default function PaymentSettings() {
  const { success, error } = useToast();

  // React Hook Form setup
  const form = useForm<PaymentSettings>({
    resolver: zodResolver(paymentSettingsSchema),
    defaultValues: {
      paymentProcessor: 'paystack',
      environment: 'test',
      defaultCurrency: 'NGN',
      defaultBillingCycle: 'monthly',
      trialPeriodDays: 14,
    }
  });

  const { control, handleSubmit, formState: { errors, isSubmitting } } = form;

  // Handle form submission
  const onSubmit = async (data: PaymentSettings) => {
    try {
      // Here would be the API call to save settings
      // For now, just simulate a delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      success('Payment settings saved successfully');
    } catch (err) {
      error(err instanceof Error ? err.message : 'Failed to save settings');
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Payment Settings</h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
        <FormSection
          title="Payment Processor"
          description="Configure your payment gateway provider"
        >
          <FormField label="Payment Provider" htmlFor="paymentProcessor" required>
            <Input
              id="paymentProcessor"
              value="Paystack"
              disabled
            />
          </FormField>

          <FormField label="Environment" htmlFor="environment" required error={errors.environment?.message}>
            <Controller
              name="environment"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  id="environment"
                  options={environments}
                  error={errors.environment?.message}
                />
              )}
            />
          </FormField>

          <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 rounded-md">
            <p className="text-sm">
              <strong>Note:</strong> API keys are loaded from environment variables for security reasons.
              To change API keys, please update your server environment configuration.
            </p>
          </div>
        </FormSection>

        <FormSection
          title="Billing Configuration"
          description="Default billing and subscription settings"
        >
          <FormField label="Default Currency" htmlFor="defaultCurrency" required error={errors.defaultCurrency?.message}>
            <Controller
              name="defaultCurrency"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  id="defaultCurrency"
                  options={currencies}
                  filter
                  error={errors.defaultCurrency?.message}
                />
              )}
            />
          </FormField>

          <FormField label="Default Billing Cycle" htmlFor="defaultBillingCycle" required error={errors.defaultBillingCycle?.message}>
            <Controller
              name="defaultBillingCycle"
              control={control}
              render={({ field }) => (
                <Select
                  {...field}
                  id="defaultBillingCycle"
                  options={billingCycles}
                  error={errors.defaultBillingCycle?.message}
                />
              )}
            />
          </FormField>

          <FormField
            label="Default Trial Period (Days)"
            htmlFor="trialPeriodDays"
            description="Number of days for free trial (0 for no trial)"
            error={errors.trialPeriodDays?.message}
          >
            <Controller
              name="trialPeriodDays"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  id="trialPeriodDays"
                  type="number"
                  min="0"
                  error={errors.trialPeriodDays?.message}
                />
              )}
            />
          </FormField>
        </FormSection>

        <div className="mt-6 flex justify-end">
          <Button
            type="submit"
            isLoading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </form>
    </div>
  );
}
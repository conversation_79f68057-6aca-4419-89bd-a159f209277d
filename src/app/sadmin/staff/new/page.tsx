'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { createAdminWithResetLink } from '@server/actions/admin';
import { getPermissionGroups } from '@server/actions/permissions';
import { isEmailDomainAccepted } from '@server/actions/admin-settings';
import { Card } from '@/components/ui/Card';
import { Select } from '@/components/ui/Select';
import { MultiSelect } from '@/components/ui/MultiSelect';
import { Input } from '@/components/ui/Input';
import { Switch } from '@/components/ui/Switch';
import { Button } from '@/components/ui/Button';
import { Alert } from '@/components/ui/Alert';
import { Form, FormLabel, FormMessage } from '@/components/ui/Form';
import Link from 'next/link';
import { ADMIN_ROLE_OPTIONS, AdminRole } from '@/constants/roles';
import { logger } from '@/utils/logger';

// Define types for permission groups
interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  permissions: string[];
}

interface PermissionGroupOption {
  label: string;
  value: string;
}

// Zod schema for form validation
const staffFormSchema = z.object({
  name: z.string().min(1, 'Name is required').min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  role: z.nativeEnum(AdminRole, { required_error: 'Role is required' }),
  permissionGroups: z.array(z.string()).optional(),
  active: z.boolean().default(true)
}).refine((data) => {
  // If role is STAFF, permission groups are required
  if (data.role === AdminRole.STAFF && (!data.permissionGroups || data.permissionGroups.length === 0)) {
    return false;
  }
  return true;
}, {
  message: 'Permission groups are required for staff members',
  path: ['permissionGroups']
});

type StaffFormData = z.infer<typeof staffFormSchema>;

export default function NewStaffPage() {
  const router = useRouter();

  // React Hook Form setup
  const form = useForm<StaffFormData>({
    resolver: zodResolver(staffFormSchema),
    defaultValues: {
      name: '',
      email: '',
      role: AdminRole.STAFF,
      permissionGroups: [],
      active: true
    }
  });

  const { control, handleSubmit, watch, reset, formState: { errors, isSubmitting } } = form;
  const watchedRole = watch('role');
  const watchedEmail = watch('email');

  // UI state
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [emailSent, setEmailSent] = useState(true);
  const [emailError, setEmailError] = useState('');
  const [resetUrl, setResetUrl] = useState('');
  const [allPermissionGroups, setAllPermissionGroups] = useState<PermissionGroupOption[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);

  // Domain validation state
  const [domainValidating, setDomainValidating] = useState(false);
  const [domainValidation, setDomainValidation] = useState<{
    valid: boolean;
    message: string;
    severity: 'info' | 'warn' | 'error' | 'success';
  } | null>(null);

  // Load permission groups
  useEffect(() => {
    async function loadPermissionGroups() {
      setPermissionsLoading(true);
      try {
        const result = await getPermissionGroups();
        if (result.success && result.data) {
          setAllPermissionGroups(result.data.groups.map((group: PermissionGroup) => ({
            label: group.name,
            value: group._id
          })));
        } else if (!result.success) {
          logger.error('Failed to load permission groups:', result.error);
        }
      } catch (err) {
        logger.error('Error loading permission groups:', err);
      } finally {
        setPermissionsLoading(false);
      }
    }

    loadPermissionGroups();
  }, []);

  // Validate email domain when email changes
  useEffect(() => {
    const checkEmailDomain = async () => {
      // Reset domain validation if email is empty
      if (!watchedEmail || !watchedEmail.includes('@')) {
        setDomainValidation(null);
        return;
      }

      setDomainValidating(true);
      try {
        const result = await isEmailDomainAccepted(watchedEmail);

        if (result.success && result.data) {
          if (result.data.reason === 'NO_RESTRICTIONS') {
            setDomainValidation({
              valid: true,
              message: 'No domain restrictions are in place. Any email domain is accepted.',
              severity: 'info'
            });
          } else if (result.data.accepted) {
            setDomainValidation({
              valid: true,
              message: `Domain ${result.data.domain} is in the allowed list.`,
              severity: 'success'
            });
          } else {
            setDomainValidation({
              valid: false,
              message: `Domain ${result.data.domain} is not in the list of accepted domains for admin accounts.`,
              severity: 'error'
            });
          }
        } else if (result.error) {
          setDomainValidation({
            valid: false,
            message: result.error,
            severity: 'error'
          });
        }
      } catch (err) {
        setDomainValidation({
          valid: false,
          message: 'Failed to validate email domain',
          severity: 'error'
        });
      } finally {
        setDomainValidating(false);
      }
    };

    const timeoutId = setTimeout(checkEmailDomain, 500);
    return () => clearTimeout(timeoutId);
  }, [watchedEmail]);

  // Handle form submission
  const onSubmit = async (data: StaffFormData) => {
    setError('');
    setSuccess(false);
    setEmailSent(true);
    setEmailError('');
    setResetUrl('');

    try {
      const result = await createAdminWithResetLink({
        name: data.name,
        email: data.email,
        role: data.role,
        permissionGroups: data.permissionGroups || [],
        active: data.active
      });

      if (result.success) {
        setSuccess(true);

        // Check if email was sent successfully
        if (result.data?.passwordResetSent === false) {
          setEmailSent(false);
          setEmailError(result.data?.emailError || 'Unknown error sending email');
        }

        // Set the reset URL if available (for development)
        if (result.data?.passwordResetUrl) {
          setResetUrl(result.data.passwordResetUrl);
        }

        // Reset form
        reset();

        // Redirect to staff list after a delay only if everything went well
        if (result.data?.passwordResetSent) {
          setTimeout(() => {
            router.push('/sadmin/staff');
          }, 3000);
        }
      } else {
        setError(result.error || 'Failed to create admin');
      }
    } catch (err) {
      logger.error('Error creating admin:', err);
      setError('An unexpected error occurred');
    }
  };

  return (
    <div className="p-4 md:p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">Add New Staff</h1>
        <Link href="/sadmin/staff">
          <Button variant="ghost" size="sm">
            ← Back to Staff List
          </Button>
        </Link>
      </div>

      <Card className="max-w-4xl mx-auto">
        {error && (
          <div className="mb-4">
            <Alert severity="error">{error}</Alert>
          </div>
        )}

        {success && (
          <div className="mb-4">
            <Alert severity="success">
              {emailSent
                ? "Staff member created successfully! A password setup link has been sent to their email."
                : "Staff member created successfully, but the password reset email could not be sent."}
            </Alert>
          </div>
        )}

        {success && !emailSent && (
          <div className="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-500 text-yellow-700 dark:text-yellow-300 rounded-md">
            <h3 className="font-medium mb-2">Email Sending Error</h3>
            <p className="text-sm mb-2">{emailError}</p>
            <p className="text-sm">Please check your SMTP settings or manually share the reset link with the user.</p>
            {resetUrl && (
              <div className="mt-3">
                <h4 className="text-sm font-medium mb-1">Preview Link (Development Only):</h4>
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={resetUrl}
                    readOnly
                    className="text-xs flex-1 p-2 border border-gray-300 rounded bg-gray-50 dark:bg-gray-800 dark:border-gray-700"
                  />
                  <Button
                    icon="pi pi-copy"
                    onClick={async () => {
                      await navigator.clipboard.writeText(resetUrl);
                    }}
                    className="!p-2 !bg-yellow-500 !border-yellow-600 !text-white"
                    tooltip="Copy to clipboard"
                    tooltipOptions={{ position: 'top' }}
                  />
                </div>
              </div>
            )}
          </div>
        )}

        <Form form={form} onSubmit={onSubmit} className="p-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 p-4 mb-6 rounded-md">
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              A welcome email with a password setup link will be sent to the staff {"member's"} email address.
              {"They'll"} need to set their password within 24 hours.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="field">
              <FormLabel htmlFor="name" required>Name</FormLabel>
              <Controller
                name="name"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="name"
                    placeholder="Enter name"
                    error={errors.name?.message}
                    autoFocus
                  />
                )}
              />
              <FormMessage>{errors.name?.message}</FormMessage>
            </div>

            <div className="field">
              <FormLabel htmlFor="email" required>Email</FormLabel>
              <Controller
                name="email"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    id="email"
                    type="email"
                    placeholder="Enter email address"
                    error={errors.email?.message || (domainValidation?.valid === false ? domainValidation.message : undefined)}
                    className={
                      domainValidation?.valid === false
                        ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                        : domainValidation?.valid === true
                          ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
                          : ''
                    }
                  />
                )}
              />
              {domainValidating && (
                <small className="text-blue-500 dark:text-blue-400 mt-1 block animate-pulse">
                  Validating domain...
                </small>
              )}
              {domainValidation && !domainValidating && (
                <div className="mt-1">
                  <Alert
                    severity={domainValidation.severity}
                    className="py-1 px-2 text-xs"
                  >
                    {domainValidation.message}
                  </Alert>
                </div>
              )}
              <FormMessage>{errors.email?.message}</FormMessage>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="field">
              <FormLabel htmlFor="role" required>Role</FormLabel>
              <Controller
                name="role"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    id="role"
                    options={ADMIN_ROLE_OPTIONS}
                    placeholder="Select a role"
                    error={errors.role?.message}
                  />
                )}
              />
              <FormMessage>{errors.role?.message}</FormMessage>
            </div>

            <div className="field">
              <FormLabel htmlFor="permissionGroups" required={watchedRole === AdminRole.STAFF}>
                Permission Groups
              </FormLabel>
              <Controller
                name="permissionGroups"
                control={control}
                render={({ field }) => (
                  <MultiSelect
                    {...field}
                    id="permissionGroups"
                    options={allPermissionGroups}
                    placeholder="Select permission groups"
                    display="chip"
                    value={field.value || []}
                    filter
                    loading={permissionsLoading}
                    disabled={watchedRole === AdminRole.SUPER_ADMIN || permissionsLoading}
                    error={errors.permissionGroups?.message}
                  />
                )}
              />
              {watchedRole === AdminRole.SUPER_ADMIN && (
                <small className="text-gray-500 dark:text-gray-400 mt-1 block">
                  Super Admins have all permissions by default
                </small>
              )}
              <FormMessage>{errors.permissionGroups?.message}</FormMessage>
            </div>
          </div>

          <div className="mt-6">
            <div className="field">
              <div className="flex gap-2 items-center">
                <Controller
                  name="active"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      checked={field.value}
                      onChange={(e) => field.onChange(e.value)}
                    />
                  )}
                />
                <FormLabel htmlFor="active">Active Account</FormLabel>
              </div>
              <small className="text-gray-500 dark:text-gray-400 mt-1 block">
                Inactive accounts cannot log in to the admin dashboard
              </small>
            </div>
          </div>

          <div className="mt-8 flex justify-end gap-3">
            <Link href="/sadmin/staff">
              <Button
                variant="secondary"
                type="button"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              isLoading={isSubmitting}
              disabled={isSubmitting || (domainValidation?.valid === false && watchedEmail.includes('@'))}
            >
              {isSubmitting ? 'Creating...' : 'Create Staff'}
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );
}

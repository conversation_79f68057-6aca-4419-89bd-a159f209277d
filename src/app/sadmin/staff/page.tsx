'use client';

import { useState, useEffect } from 'react';
import { getAdmins, deleteAdmin } from '@server/actions/admin';
import { AdminRole } from '@/constants/roles';
import { useRouter } from 'next/navigation';
import { logger } from '@/utils/logger';

// Icon components
import { PencilSquareIcon, TrashIcon, PlusIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

// UI components
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { Card, CardContent } from '@/components/ui/Card';
import { Form } from '@/components/ui/Form';
import { Badge } from '@/components/ui/Badge';
import { Alert } from '@/components/ui/Alert';
import { Skeleton } from '@/components/ui/Skeleton';

// Define Admin interface
interface Admin {
  _id: string;
  name: string;
  email: string;
  role: AdminRole;
  permissionGroups: {
    _id: string;
    name: string;
  }[];
  active: boolean;
  isRoot?: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export default function StaffPage() {
  const router = useRouter();

  // State for admin data
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for pagination
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [debounceSearch, setDebounceSearch] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // State for delete dialog
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [adminToDelete, setAdminToDelete] = useState<Admin | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState('');

  // State for sort
  const [sortField, setSortField] = useState<string>('createdAt');
  const [sortOrder, setSortOrder] = useState<number>(-1);
  
  // Refresh function
  const refreshData = async () => {
    setIsRefreshing(true);
    setError(null);
    try {
      const result = await getAdmins({
        page: page.toString(),
        limit: limit.toString(),
        search: debounceSearch
      });
      
      if (result.success && result.data) {
        setAdmins(result.data.admins);
        setTotal(result.data.pagination.total);
      } else if (!result.success) {
        setError(result.error || 'Failed to load staff data');
      }
    } catch (err) {
      logger.error('Error fetching admins:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
      setIsRefreshing(false);
    }
  };

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebounceSearch(search);
    }, 500);
    return () => clearTimeout(timer);
  }, [search]);

  // Load data when params change
  useEffect(() => {
    setLoading(true);
    refreshData();
  }, [page, limit, debounceSearch]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= Math.ceil(total / limit)) {
      setPage(newPage);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1); // Reset to first page on new search
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (admin: Admin) => {
    setAdminToDelete(admin);
    setShowDeleteDialog(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setShowDeleteDialog(false);
    setAdminToDelete(null);
  };

  // Handle admin deletion
  const handleDeleteAdmin = async () => {
    if (!adminToDelete) return;

    setDeleting(true);
    setDeleteError('');
    try {
      const result = await deleteAdmin({ id: adminToDelete._id });

      if (result.success) {
        // Remove the deleted admin from the state
        setAdmins(admins.filter(admin => admin._id !== adminToDelete._id));

        // If the last item on the page was deleted and we're not on page 1, go to the previous page
        if (admins.length === 1 && page > 1) {
          handlePageChange(page - 1);
        }

        closeDeleteDialog();
      } else {
        setDeleteError(result.error || 'Failed to delete admin');
        // Don't close dialog so user can see the error
      }
    } catch (err) {
      logger.error('Error deleting admin:', err);
      setDeleteError('An unexpected error occurred');
      // Don't close dialog so user can see the error
    } finally {
      setDeleting(false);
    }
  };

  // Get the role display label
  const getRoleLabel = (role: string) => {
    if (role === AdminRole.SUPER_ADMIN) return 'Super Admin';
    if (role === AdminRole.STAFF) return 'Staff';
    return role;
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Staff Management</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Manage administrator accounts and their permissions
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Button 
            variant="primary"
            size="default"
            onClick={() => router.push('/sadmin/staff/new')}
          >
            <PlusIcon className="h-5 w-5 mr-2" />
            Add Staff
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mt-4" hasBorder={false}>
        <CardContent>
          <Form onSubmit={handleSearch} className="w-full sm:max-w-xs">
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                name="search"
                id="search"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-10 w-full"
                placeholder="Search by name or email"
              />
            </div>
          </Form>
        </CardContent>
      </Card>

      {error && (
        <div className="mt-4">
          <Alert severity="error">{error}</Alert>
        </div>
      )}

      {/* Staff List */}
      <Card className="mt-4">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-6">
                  Name
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                  Email
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                  Role
                </th>
                <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                  Status
                </th>
                <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700 bg-white dark:bg-gray-800">
              {loading ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4">
                    <div className="flex flex-col gap-2">
                      <Skeleton className="h-8 w-full" variant="rectangular" />
                      <Skeleton className="h-8 w-full" variant="rectangular" />
                      <Skeleton className="h-8 w-full" variant="rectangular" />
                    </div>
                  </td>
                </tr>
              ) : admins.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                    No admins found
                  </td>
                </tr>
              ) : (
                admins.map((admin) => (
                  <tr key={admin._id}>
                    <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-6">
                      {admin.name}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {admin.email}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                      <Badge
                        variant={admin.role === AdminRole.SUPER_ADMIN ? "info" : "info"}
                        className={admin.role === AdminRole.SUPER_ADMIN ? "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300" : ""}
                      >
                        {getRoleLabel(admin.role)}
                      </Badge>
                      {admin.isRoot && (
                        <Badge
                          variant="danger"
                          className="ml-2"
                        >
                          Root Admin
                        </Badge>
                      )}
                    </td>
                    <td className="whitespace-nowrap px-3 py-4 text-sm">
                      <Badge
                        variant={admin.active ? "success" : "danger"}
                      >
                        {admin.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </td>
                    <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                      <div className="flex justify-end gap-2">
                        <Button
                          onClick={() => router.push(`/sadmin/staff/${admin._id}`)}
                          variant="link"
                          size="sm"
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <PencilSquareIcon className="h-5 w-5" />
                          <span className="sr-only">Edit {admin.name}</span>
                        </Button>
                        {!admin.isRoot && (
                          <Button
                            variant="link"
                            size="sm"
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                            onClick={() => openDeleteDialog(admin)}
                          >
                            <TrashIcon className="h-5 w-5" />
                            <span className="sr-only">Delete {admin.name}</span>
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {!loading && admins.length > 0 && (
          <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6">
            <div className="flex flex-1 justify-between sm:hidden">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handlePageChange(page - 1)}
                disabled={page === 1}
              >
                Previous
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handlePageChange(page + 1)}
                disabled={page === Math.ceil(total / limit)}
              >
                Next
              </Button>
            </div>
            <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700 dark:text-gray-300">
                  Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(page * limit, total)}
                  </span>{' '}
                  of <span className="font-medium">{total}</span> results
                </p>
              </div>
              <div>
                <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="rounded-l-md"
                    onClick={() => handlePageChange(page - 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  {Array.from({ length: Math.min(5, Math.ceil(total / limit)) }, (_, i) => {
                    const pageNum = i + 1;
                    return (
                      <Button
                        key={pageNum}
                        variant={pageNum === page ? "primary" : "secondary"}
                        size="sm"
                        onClick={() => handlePageChange(pageNum)}
                        className="rounded-none"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                  <Button
                    variant="secondary"
                    size="sm"
                    className="rounded-r-md"
                    onClick={() => handlePageChange(page + 1)}
                    disabled={page === Math.ceil(total / limit)}
                  >
                    Next
                  </Button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        title="Confirm Deletion"
        open={showDeleteDialog}
        onOpenChange={(open) => {
          if (!open) closeDeleteDialog();
        }}
        footer={
          <div className="flex justify-end gap-2">
            <Button
              variant="secondary"
              onClick={closeDeleteDialog}
              disabled={deleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAdmin}
              isLoading={deleting}
            >
              Delete
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          {deleteError && (
            <Alert severity="error">{deleteError}</Alert>
          )}
          <p>Are you sure you want to delete this administrator account? This action cannot be undone.</p>
        </div>
      </Dialog>
    </div>
  );
}

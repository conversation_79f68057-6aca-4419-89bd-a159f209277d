'use client';

import {useState, useEffect} from 'react';
import { useRouter, useParams } from 'next/navigation';
import { getAdmin, updateAdmin } from '@server/actions/admin';
import { getPermissionGroups } from '@server/actions/permissions';
import { isEmailDomainAccepted } from '@server/actions/admin-settings';
import { Card } from 'primereact/card';
import { Dropdown } from 'primereact/dropdown';
import { MultiSelect } from 'primereact/multiselect';
import { InputText } from 'primereact/inputtext';
import { InputSwitch } from 'primereact/inputswitch';
import { Button } from 'primereact/button';
import { Message } from 'primereact/message';
import { Password } from 'primereact/password';
import { ProgressSpinner } from 'primereact/progressspinner';
import Link from 'next/link';
import { ADMIN_ROLE_OPTIONS, AdminRole } from '@/constants/roles';
import { logger } from '@/utils/logger';

// Define types for admin and permission groups
interface Admin {
  _id: string;
  name: string;
  email: string;
  role: AdminRole;
  permissionGroups: {
    _id: string;
    name: string;
    description: string;
  }[];
  active: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  permissions: string[];
}

interface PermissionGroupOption {
  label: string;
  value: string;
}

export default function EditStaffPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState<AdminRole>(AdminRole.STAFF);
  const [permissionGroups, setPermissionGroups] = useState<string[]>([]);
  const [active, setActive] = useState(true);

  // Original data for comparison
  const [originalAdmin, setOriginalAdmin] = useState<Admin | null>(null);

  // UI state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [allPermissionGroups, setAllPermissionGroups] = useState<PermissionGroupOption[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);
  
  // Domain validation state
  const [domainValidating, setDomainValidating] = useState(false);
  const [domainValidation, setDomainValidation] = useState<{
    valid: boolean;
    message: string;
    severity: 'info' | 'warn' | 'error' | 'success';
  } | null>(null);

  // Load admin data and permission groups
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      try {
        const [adminResult, permissionsResult] = await Promise.all([
          getAdmin({ id }),
          getPermissionGroups()
        ]);

        // Process admin data
        if (adminResult.success && adminResult.data) {
          const admin = adminResult.data;
          setOriginalAdmin(admin);
          setName(admin.name);
          setEmail(admin.email);
          setRole(admin.role as AdminRole);
          setPermissionGroups(admin.permissionGroups.map((pg: { _id: string }) => pg._id));
          setActive(admin.active);
        } else if (!adminResult.success) {
          setError(adminResult.error || 'Failed to load admin data');
        }

        // Process permission groups
        if (permissionsResult.success && permissionsResult.data) {
          setAllPermissionGroups(permissionsResult.data.groups.map((group: PermissionGroup) => ({
            label: group.name,
            value: group._id
          })));
        } else if (!permissionsResult.success) {
          logger.error('Failed to load permission groups:', permissionsResult.error || 'Unknown error');
        }
      } catch (err) {
        logger.error('Error loading data:', err);
        setError('An unexpected error occurred while loading data');
      } finally {
        setLoading(false);
        setPermissionsLoading(false);
      }
    }

    if (id) {
      loadData();
    }
  }, [id]);

  // Validate email domain when email changes or first loads
  useEffect(() => {
    // Don't validate if the email is the same as the original
    if (originalAdmin && email === originalAdmin.email) {
      setDomainValidation(null);
      return;
    }
    
    const checkEmailDomain = async () => {
      // Reset domain validation if email is empty
      if (!email || !email.includes('@')) {
        setDomainValidation(null);
        return;
      }
      
      setDomainValidating(true);
      try {
        const result = await isEmailDomainAccepted(email);
        
        if (result.success && result.data) {
          if (result.data.reason === 'NO_RESTRICTIONS') {
            setDomainValidation({
              valid: true,
              message: 'No domain restrictions are in place. Any email domain is accepted.',
              severity: 'info'
            });
          } else if (result.data.accepted) {
            setDomainValidation({
              valid: true,
              message: `Domain ${result.data.domain} is in the allowed list.`,
              severity: 'success'
            });
          } else {
            setDomainValidation({
              valid: false,
              message: `Domain ${result.data.domain} is not in the list of accepted domains for admin accounts.`,
              severity: 'error'
            });
          }
        } else if (result.error) {
          setDomainValidation({
            valid: false,
            message: result.error,
            severity: 'error'
          });
        }
      } catch (err) {
        setDomainValidation({
          valid: false,
          message: 'Failed to validate email domain',
          severity: 'error'
        });
      } finally {
        setDomainValidating(false);
      }
    };
    
    const timeoutId = setTimeout(checkEmailDomain, 500);
    return () => clearTimeout(timeoutId);
  }, [email, originalAdmin]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Don't proceed if domain is invalid
    if (email !== originalAdmin?.email && domainValidation?.valid === false) {
      setError('Email domain is not allowed. Please use an accepted domain.');
      return;
    }
    
    setSaving(true);
    setError('');
    setSuccess(false);

    try {
      // Build update data - only include changed fields
      const updateData: any = { id };

      if (name !== originalAdmin?.name) updateData.name = name;
      if (email !== originalAdmin?.email) updateData.email = email;
      if (password) updateData.password = password;
      if (role !== originalAdmin?.role) updateData.role = role;
      if (active !== originalAdmin?.active) updateData.active = active;

      // Check if permission groups have changed
      const originalPermissionGroupIds = originalAdmin?.permissionGroups.map(pg => pg._id) || [];
      const permissionGroupsChanged =
        permissionGroups.length !== originalPermissionGroupIds.length ||
        permissionGroups.some(id => !originalPermissionGroupIds.includes(id));

      if (permissionGroupsChanged) {
        updateData.permissionGroups = permissionGroups;
      }

      // Only proceed if there are changes
      if (Object.keys(updateData).length <= 1 && !password) {
        setError('No changes detected');
        setSaving(false);
        return;
      }

      const result = await updateAdmin(updateData);

      if (result.success) {
        setSuccess(true);
        setPassword(''); // Clear password field

        // Update original admin data
        if (result.data) {
          setOriginalAdmin({
            ...originalAdmin!,
            ...result.data,
            permissionGroups: result.data.permissionGroups || originalAdmin!.permissionGroups
          });
        }

        // Redirect after success
        setTimeout(() => {
          router.push('/sadmin/staff');
        }, 2000);
      } else {
        setError(result.error || 'Failed to update staff member');
      }
    } catch (err) {
      logger.error('Error updating staff:', err);
      setError('An unexpected error occurred');
    } finally {
      setSaving(false);
    }
  };

  // Handle dropdown change with type safety
  const handleRoleChange = (e: { value: AdminRole }) => {
    setRole(e.value);
  };

  // Password strength feedback
  const getPasswordStrength = (password: string) => {
    if (!password) return { label: 'Empty', severity: 'danger' };

    if (password.length < 8) return { label: 'Weak', severity: 'danger' };

    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasDigit = /\d/.test(password);
    const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    const conditions = [hasLowercase, hasUppercase, hasDigit, hasSpecial];
    const metCount = conditions.filter(Boolean).length;

    if (metCount <= 2) return { label: 'Medium', severity: 'warning' };
    if (metCount === 3) return { label: 'Strong', severity: 'info' };
    return { label: 'Very Strong', severity: 'success' };
  };

  const passwordStrength = getPasswordStrength(password);

  // Password footer
  const passwordFooter = (
    <div className="flex flex-wrap justify-between items-center">
      <span className={`text-${passwordStrength.severity} text-xs`}>
        {passwordStrength.label}
      </span>
      <ul className="text-xs p-0 mt-2 list-none">
        <li>At least 8 characters</li>
        <li>At least 1 lowercase letter</li>
        <li>At least 1 uppercase letter</li>
        <li>At least 1 digit</li>
        <li>At least 1 special character</li>
      </ul>
    </div>
  );

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <div className="p-4 md:p-6 bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <ProgressSpinner style={{ width: '50px', height: '50px' }} strokeWidth="4" animationDuration=".5s" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading staff information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 md:p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-gray-800 dark:text-white">
          Edit Staff: {originalAdmin?.name}
        </h1>
        <Link href="/sadmin/staff">
          <Button
            label="Back to Staff List"
            icon="pi pi-arrow-left"
            text
            className="!text-blue-600 hover:!text-blue-800 dark:!text-blue-400 dark:hover:!text-blue-300"
          />
        </Link>
      </div>

      <Card className="!max-w-4xl !mx-auto !bg-white !shadow-md !rounded-lg !border-0 dark:!bg-gray-800 dark:!text-gray-100">
        {error && (
          <div className="mb-4">
            <Message
              severity="error"
              text={error}
              className="!w-full !border-l-4 !border-red-500 !bg-red-50 dark:!bg-red-900/30 !text-red-700 dark:!text-red-300"
            />
          </div>
        )}

        {success && (
          <div className="mb-4">
            <Message
              severity="success"
              text="Staff member updated successfully!"
              className="!w-full !border-l-4 !border-green-500 !bg-green-50 dark:!bg-green-900/30 !text-green-700 dark:!text-green-300"
            />
          </div>
        )}

        {originalAdmin && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500 dark:text-gray-400">Created</p>
                <p className="font-medium">{formatDate(originalAdmin.createdAt)}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Last Updated</p>
                <p className="font-medium">{formatDate(originalAdmin.updatedAt)}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Last Login</p>
                <p className="font-medium">{formatDate(originalAdmin.lastLogin)}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">ID</p>
                <p className="font-medium font-mono text-xs">{originalAdmin._id}</p>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="field">
              <label htmlFor="name" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Name <span className="text-red-500">*</span>
              </label>
              <InputText
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
                className="!w-full !p-3 !border !border-gray-300 !rounded-lg !shadow-sm focus:!border-blue-500 focus:!ring-1 focus:!ring-blue-500 dark:!bg-gray-700 dark:!border-gray-600 dark:!text-white"
                placeholder="Enter name"
              />
            </div>

            <div className="field">
              <label htmlFor="email" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Email <span className="text-red-500">*</span>
              </label>
              <InputText
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                type="email"
                className={`!w-full !p-3 !border !rounded-lg !shadow-sm focus:!ring-1 dark:!bg-gray-700 dark:!text-white ${
                  domainValidation?.valid === false 
                    ? '!border-red-500 focus:!border-red-500 focus:!ring-red-500 dark:!border-red-600' 
                    : domainValidation?.valid === true
                      ? '!border-green-500 focus:!border-green-500 focus:!ring-green-500 dark:!border-green-600'
                      : '!border-gray-300 focus:!border-blue-500 focus:!ring-blue-500 dark:!border-gray-600'
                }`}
                placeholder="Enter email address"
              />
              {domainValidating && (
                <small className="text-blue-500 dark:text-blue-400 mt-1 block animate-pulse">
                  Validating domain...
                </small>
              )}
              {domainValidation && !domainValidating && (
                <div className="mt-1">
                  <Message
                    severity={domainValidation.severity}
                    text={domainValidation.message}
                    className={`!py-1 !px-2 !text-xs !w-full ${
                      domainValidation.severity === 'error' 
                        ? '!bg-red-50 dark:!bg-red-900/30 !text-red-700 dark:!text-red-300'
                        : domainValidation.severity === 'success'
                          ? '!bg-green-50 dark:!bg-green-900/30 !text-green-700 dark:!text-green-300'
                          : '!bg-blue-50 dark:!bg-blue-900/30 !text-blue-700 dark:!text-blue-300'
                    }`}
                  />
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="field">
              <label htmlFor="password" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Password <span className="text-gray-400 font-normal">(Leave blank to keep current)</span>
              </label>
              <Password
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                toggleMask
                className="!w-full [&_.p-password-input]:!w-full [&_.p-password-input]:!p-3 [&_.p-password-input]:!border [&_.p-password-input]:!border-gray-300 [&_.p-password-input]:!rounded-lg [&_.p-password-input]:!shadow-sm [&_.p-password-input]:focus:!border-blue-500 [&_.p-password-input]:focus:!ring-1 [&_.p-password-input]:focus:!ring-blue-500 [&_.p-password-input]:dark:!bg-gray-700 [&_.p-password-input]:dark:!border-gray-600 [&_.p-password-input]:dark:!text-white [&_.p-password-panel]:!bg-white [&_.p-password-panel]:dark:!bg-gray-800 [&_.p-password-panel]:!shadow-lg [&_.p-password-panel]:!rounded-md [&_.p-password-panel]:!border [&_.p-password-panel]:!border-gray-200 [&_.p-password-panel]:dark:!border-gray-700 [&_.p-password-panel]:!mt-2"
                footer={passwordFooter}
                promptLabel="Enter a password"
                weakLabel="Weak"
                mediumLabel="Medium"
                strongLabel="Strong"
              />
            </div>

            <div className="field">
              <label htmlFor="role" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Role <span className="text-red-500">*</span>
              </label>
              <Dropdown
                id="role"
                value={role}
                options={ADMIN_ROLE_OPTIONS}
                onChange={handleRoleChange}
                optionLabel="label"
                optionValue="value"
                placeholder="Select a role"
                className="!w-full !p-0 [&_.p-dropdown-label]:!p-3 !border !border-gray-300 !rounded-lg !shadow-sm focus:!border-blue-500 focus:!ring-1 focus:!ring-blue-500 dark:!bg-gray-700 dark:!border-gray-600 dark:!text-white"
                required
              />
            </div>
          </div>

          <div className="mt-6">
            <div className="field">
              <label htmlFor="permissionGroups" className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                Permission Groups {role === AdminRole.STAFF && <span className="text-red-500">*</span>}
              </label>
              <MultiSelect
                id="permissionGroups"
                value={permissionGroups}
                options={allPermissionGroups}
                onChange={(e) => setPermissionGroups(e.value)}
                optionLabel="label"
                optionValue="value"
                placeholder="Select permission groups"
                className="!w-full !p-0 [&_.p-multiselect-label]:!p-3 !border !border-gray-300 !rounded-lg !shadow-sm focus:!border-blue-500 focus:!ring-1 focus:!ring-blue-500 dark:!bg-gray-700 dark:!border-gray-600 dark:!text-white"
                display="chip"
                filter
                required={role === AdminRole.STAFF}
                loading={permissionsLoading}
                disabled={role === AdminRole.SUPER_ADMIN || permissionsLoading}
              />
              {role === AdminRole.SUPER_ADMIN && (
                <small className="text-gray-500 dark:text-gray-400 mt-1 block">
                  Super Admins have all permissions by default
                </small>
              )}
            </div>
          </div>

          <div className="mt-6">
            <div className="field">
              <div className="flex gap-2 items-center">
                <InputSwitch
                  checked={active}
                  onChange={(e) => setActive(e.value || false)}
                  className="!inline-block"
                />
                <label htmlFor="active" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Active Account
                </label>
              </div>
              <small className="text-gray-500 dark:text-gray-400 mt-1 block">
                Inactive accounts cannot log in to the admin dashboard
              </small>
            </div>
          </div>

          <div className="mt-8 flex justify-end gap-3">
            <Link href="/sadmin/staff">
              <Button
                label="Cancel"
                type="button"
                severity="secondary"
                outlined
                className="!px-6 !py-2.5 !border-gray-300 !text-gray-700 !rounded-lg dark:!border-gray-600 dark:!text-gray-300"
                disabled={saving}
              />
            </Link>
            <Button
              label={saving ? 'Saving...' : 'Save Changes'}
              type="submit"
              className="!bg-gradient-to-r !from-[#8178E8] !to-[#6964D3] !px-6 !py-2.5 !rounded-lg !text-white !shadow-md"
              loading={saving}
              loadingIcon="pi pi-spin pi-spinner"
              disabled={saving || (email !== originalAdmin?.email && domainValidation?.valid === false)}
            />
          </div>
        </form>
      </Card>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { MultiSelect } from '@/components/ui/MultiSelect';
import { Checkbox } from '@/components/ui/Checkbox';
import { FormField, FormSection } from '@/components/ui/Form';
import { Card } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { createPermissionGroup } from '@server/actions/permissions';
import { Shield, Users, Settings } from 'lucide-react';
import { logger } from '@/utils/logger';

interface PermissionOption {
  label: string;
  value: string;
  module: string;
}

interface CreatePermissionGroupProps {
  permissions: any[];
  permissionsByModule: Record<string, PermissionOption[]>;
  onSuccess: () => void;
  toast: any;
}

// Zod schema for form validation
const permissionGroupSchema = z.object({
  name: z.string().min(1, 'Name is required').min(2, 'Name must be at least 2 characters'),
  description: z.string().min(1, 'Description is required').min(10, 'Description must be at least 10 characters'),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
  isDefault: z.boolean().default(false)
});

type PermissionGroupFormData = z.infer<typeof permissionGroupSchema>;

export default function CreatePermissionGroup({
  permissions,
  permissionsByModule,
  onSuccess,
  toast
}: CreatePermissionGroupProps) {
  const { success, error } = useToast();
  const [selectedModule, setSelectedModule] = useState<string>('');

  // React Hook Form setup
  const form = useForm<PermissionGroupFormData>({
    resolver: zodResolver(permissionGroupSchema),
    defaultValues: {
      name: '',
      description: '',
      permissions: [],
      isDefault: false
    }
  });

  const { control, handleSubmit, reset, watch, formState: { errors, isSubmitting } } = form;
  const watchedPermissions = watch('permissions');

  // Get all available modules
  const modules = Object.keys(permissionsByModule).sort();

  // Get permissions for selected module
  const getFilteredPermissions = () => {
    if (!selectedModule) {
      return Object.values(permissionsByModule).flat();
    }
    return permissionsByModule[selectedModule] || [];
  };

  // Handle form submission
  const onSubmit = async (data: PermissionGroupFormData) => {
    try {
      // Create new permission group
      const result = await createPermissionGroup({
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        isDefault: data.isDefault
      });

      if (result.success) {
        success('Permission group created successfully');
        // Reset form and notify parent
        reset();
        setSelectedModule('');
        onSuccess();
      } else {
        error(result.error || 'Failed to create permission group');
      }
    } catch (err) {
      logger.error('Error submitting form:', err);
      error('An unexpected error occurred');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-3 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-xl">
          <Shield className="w-5 h-5 text-[#6964D3]" />
        </div>
        <div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Create Permission Group</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">Define a new permission group with specific access rights</p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Basic Information */}
          <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
            <div className="p-6 space-y-6">
              <FormSection title="Basic Information" description="Enter the basic details for the permission group">
                <FormField
                  label="Group Name"
                  htmlFor="name"
                  required
                  error={errors.name?.message}
                >
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        id="name"
                        placeholder="Enter group name"
                        error={errors.name?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </FormField>

                <FormField
                  label="Description"
                  htmlFor="description"
                  required
                  error={errors.description?.message}
                >
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        id="description"
                        placeholder="Enter group description"
                        rows={3}
                        error={errors.description?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </FormField>

                <FormField
                  label="Default Group"
                  htmlFor="isDefault"
                  description="Set this as the default group for new users"
                >
                  <Controller
                    name="isDefault"
                    control={control}
                    render={({ field }) => (
                      <Checkbox
                        {...field}
                        id="isDefault"
                        label="Set as default group"
                        checked={field.value}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </FormField>
              </FormSection>
            </div>
          </Card>

          {/* Right Column - Permissions */}
          <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
            <div className="p-6 space-y-6">
              <FormSection title="Permissions" description="Select the permissions for this group">
                {/* Module Filter */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Filter by Module
                  </label>
                  <div className="flex gap-2 flex-wrap">
                    <Button
                      type="button"
                      variant={!selectedModule ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedModule('')}
                      className="rounded-full"
                    >
                      All
                    </Button>
                    {modules.map(module => (
                      <Button
                        key={module}
                        type="button"
                        variant={selectedModule === module ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedModule(module)}
                        className="rounded-full"
                      >
                        {module}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Permissions Selection */}
                <FormField
                  label="Select Permissions"
                  htmlFor="permissions"
                  required
                  error={errors.permissions?.message}
                  description={`${watchedPermissions.length} permissions selected`}
                >
                  <Controller
                    name="permissions"
                    control={control}
                    render={({ field }) => (
                      <MultiSelect
                        {...field}
                        id="permissions"
                        options={getFilteredPermissions()}
                        placeholder="Select permissions"
                        error={errors.permissions?.message}
                        disabled={isSubmitting}
                      />
                    )}
                  />
                </FormField>

                {/* Selected Permissions Display */}
                {watchedPermissions.length > 0 && (
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Selected Permissions
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {watchedPermissions.map(permissionId => {
                        const permission = permissions.find(p => p.id === permissionId);
                        return permission ? (
                          <Badge key={permissionId} variant="default" className="text-xs">
                            {permission.name}
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </FormSection>
            </div>
          </Card>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end gap-3 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              reset();
              setSelectedModule('');
            }}
            disabled={isSubmitting}
          >
            Reset Form
          </Button>
          <Button
            type="submit"
            isLoading={isSubmitting}
            disabled={isSubmitting}
            className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:opacity-90"
          >
            {isSubmitting ? 'Creating...' : 'Create Group'}
          </Button>
        </div>
      </form>
    </div>
  );
}
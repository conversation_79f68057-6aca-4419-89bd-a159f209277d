'use client';

import { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { Input } from '@/components/ui/Input';
import { Textarea } from '@/components/ui/Textarea';
import { MultiSelect } from '@/components/ui/MultiSelect';
import { Checkbox } from '@/components/ui/Checkbox';
import { FormField, FormSection } from '@/components/ui/Form';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { updatePermissionGroup } from '@server/actions/permissions';
import { logger } from '@/utils/logger';

interface Permission {
  _id: string;
  name: string;
  description: string;
  code: string;
  module: string;
}

interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isDefault: boolean;
  createdAt: string;
}

interface PermissionOption {
  label: string;
  value: string;
  module: string;
}

// Zod schema for form validation
const permissionGroupSchema = z.object({
  name: z.string().min(1, 'Name is required').min(2, 'Name must be at least 2 characters'),
  description: z.string().min(1, 'Description is required').min(10, 'Description must be at least 10 characters'),
  permissions: z.array(z.string()).min(1, 'At least one permission is required'),
  isDefault: z.boolean().default(false)
});

type PermissionGroupFormData = z.infer<typeof permissionGroupSchema>;

interface EditPermissionGroupProps {
  open: boolean;
  group: PermissionGroup | null;
  permissionsByModule: Record<string, PermissionOption[]>;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export default function EditPermissionGroup({
  open,
  group,
  permissionsByModule,
  onOpenChange,
  onSuccess
}: EditPermissionGroupProps) {
  const { success, error } = useToast();
  const [selectedModule, setSelectedModule] = useState<string>('');

  // React Hook Form setup
  const form = useForm<PermissionGroupFormData>({
    resolver: zodResolver(permissionGroupSchema),
    defaultValues: {
      name: '',
      description: '',
      permissions: [],
      isDefault: false
    }
  });

  const { control, handleSubmit, reset, setValue, watch, formState: { errors, isSubmitting } } = form;
  const watchedPermissions = watch('permissions');

  // Initialize form with group data when opened
  useEffect(() => {
    if (group) {
      setValue('name', group.name);
      setValue('description', group.description);
      setValue('permissions', group.permissions.map(p => p._id));
      setValue('isDefault', group.isDefault);
      setSelectedModule('');
    } else {
      reset();
    }
  }, [group, setValue, reset]);

  // Get all available modules
  const modules = Object.keys(permissionsByModule).sort();

  // Get permissions for selected module
  const getFilteredPermissions = () => {
    if (!selectedModule) {
      return Object.values(permissionsByModule).flat();
    }
    return permissionsByModule[selectedModule] || [];
  };

  // Handle form submission
  const onSubmit = async (data: PermissionGroupFormData) => {
    if (!group) {
      error('No group selected for editing');
      return;
    }

    try {
      // Update permission group
      const result = await updatePermissionGroup({
        id: group._id,
        name: data.name,
        description: data.description,
        permissions: data.permissions,
        isDefault: data.isDefault
      });

      if (result.success) {
        success('Permission group updated successfully');
        onSuccess();
        onOpenChange(false);
      } else {
        error(result.error || 'Failed to update permission group');
      }
    } catch (err) {
      logger.error('Error updating permission group:', err);
      error('An unexpected error occurred');
    }
  };

  // Dialog footer
  const dialogFooter = (
    <div className="flex justify-end gap-3">
      <Button
        variant="outline"
        onClick={() => onOpenChange(false)}
        disabled={isSubmitting}
      >
        Cancel
      </Button>
      <Button
        type="submit"
        form="edit-permission-group-form"
        isLoading={isSubmitting}
        disabled={isSubmitting}
        className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:opacity-90"
      >
        {isSubmitting ? 'Saving...' : 'Save Changes'}
      </Button>
    </div>
  );

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      title="Edit Permission Group"
      className="max-w-4xl"
      footer={dialogFooter}
    >
      <form onSubmit={handleSubmit(onSubmit)} id="edit-permission-group-form" className="space-y-6" noValidate>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Basic Information */}
          <div className="space-y-6">
            <FormSection title="Basic Information" description="Update the group details">
              <FormField
                label="Group Name"
                htmlFor="edit-name"
                required
                error={errors.name?.message}
              >
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="edit-name"
                      placeholder="Enter group name"
                      error={errors.name?.message}
                      disabled={isSubmitting}
                    />
                  )}
                />
              </FormField>

              <FormField
                label="Description"
                htmlFor="edit-description"
                required
                error={errors.description?.message}
              >
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      id="edit-description"
                      rows={3}
                      placeholder="Enter group description"
                      error={errors.description?.message}
                      disabled={isSubmitting}
                    />
                  )}
                />
              </FormField>

              <FormField
                label="Default Group"
                htmlFor="edit-isDefault"
                description="Set this as the default group for new users"
              >
                <Controller
                  name="isDefault"
                  control={control}
                  render={({ field }) => (
                    <Checkbox
                      {...field}
                      id="edit-isDefault"
                      label="Set as default group"
                      checked={field.value}
                      disabled={isSubmitting}
                    />
                  )}
                />
              </FormField>
            </FormSection>
          </div>

          {/* Right Column - Permissions */}
          <div className="space-y-6">
            <FormSection title="Permissions" description="Manage group permissions">
              {/* Module Filter */}
              <div className="space-y-3">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Filter by Module
                </label>
                <div className="flex gap-2 flex-wrap">
                  <Button
                    type="button"
                    variant={!selectedModule ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedModule('')}
                    className="rounded-full"
                  >
                    All
                  </Button>
                  {modules.map(module => (
                    <Button
                      key={module}
                      type="button"
                      variant={selectedModule === module ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedModule(module)}
                      className="rounded-full"
                    >
                      {module}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Permissions Selection */}
              <FormField
                label="Select Permissions"
                htmlFor="edit-permissions"
                required
                error={errors.permissions?.message}
                description={`${watchedPermissions.length} permissions selected`}
              >
                <Controller
                  name="permissions"
                  control={control}
                  render={({ field }) => (
                    <MultiSelect
                      {...field}
                      id="edit-permissions"
                      options={getFilteredPermissions()}
                      placeholder="Select permissions"
                      error={errors.permissions?.message}
                      disabled={isSubmitting}
                    />
                  )}
                />
              </FormField>

              {/* Selected Permissions Display */}
              {watchedPermissions.length > 0 && (
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Selected Permissions
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {watchedPermissions.map(permissionId => {
                      const allPermissions = Object.values(permissionsByModule).flat();
                      const permission = allPermissions.find(p => p.value === permissionId);
                      return permission ? (
                        <Badge key={permissionId} variant="default" className="text-xs">
                          {permission.label}
                        </Badge>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
            </FormSection>
          </div>
        </div>
      </form>
    </Dialog>
  );
}

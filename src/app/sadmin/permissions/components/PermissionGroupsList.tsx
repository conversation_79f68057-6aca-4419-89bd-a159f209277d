'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { Alert } from '@/components/ui/Alert';
import { Badge } from '@/components/ui/Badge';
import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';
import { useToast } from '@/components/ui/Toast';
import { deletePermissionGroup, getUsersByPermissionGroup, removeUserFromPermissionGroup } from '@server/actions/permissions';
import { formatDate } from '../utils/formatters';
import {
  Shield,
  Users,
  Edit,
  Trash2,
  Calendar,
  Star,
  Plus,
  AlertTriangle,
  UserMinus,
  Clock,
  Mail,
  User
} from 'lucide-react';
import { logger } from '@/utils/logger';

interface Permission {
  _id: string;
  name: string;
  description: string;
  code: string;
  module: string;
}

interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isDefault: boolean;
  createdAt: string;
}

interface User {
  _id: string;
  name: string;
  email: string;
  status: string;
  lastLogin?: string;
}

interface PermissionGroupsListProps {
  loading: boolean;
  error: string;
  permissionGroups: PermissionGroup[];
  onCreateNew: () => void;
  onEdit?: (group: PermissionGroup) => void;
  onGroupsChanged: () => Promise<void>;
}

export default function PermissionGroupsList({
  loading,
  error,
  permissionGroups,
  onCreateNew,
  onEdit,
  onGroupsChanged
}: PermissionGroupsListProps) {
  const { success, error: showError } = useToast();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [groupToDelete, setGroupToDelete] = useState<PermissionGroup | null>(null);
  const [deleteInProgress, setDeleteInProgress] = useState(false);

  // User management state
  const [usersDialogOpen, setUsersDialogOpen] = useState(false);
  const [currentGroup, setCurrentGroup] = useState<PermissionGroup | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);
  const [usersError, setUsersError] = useState('');
  const [removeInProgress, setRemoveInProgress] = useState<Record<string, boolean>>({});

  // Open delete confirmation dialog
  const openDeleteDialog = (group: PermissionGroup) => {
    setGroupToDelete(group);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setGroupToDelete(null);
  };

  // Open users dialog
  const openUsersDialog = async (group: PermissionGroup) => {
    setCurrentGroup(group);
    setUsersDialogOpen(true);
    await fetchGroupUsers(group._id);
  };

  // Close users dialog
  const closeUsersDialog = () => {
    setUsersDialogOpen(false);
    setCurrentGroup(null);
    setUsers([]);
    setUsersError('');
  };

  // Fetch users for a permission group
  const fetchGroupUsers = async (groupId: string) => {
    setUsersLoading(true);
    setUsersError('');

    try {
      const result = await getUsersByPermissionGroup(groupId);
      if (result.success && result.data) {
        setUsers(result.data.users);
      } else {
        setUsersError(result.error || 'Failed to load users');
      }
    } catch (err) {
      logger.error('Error loading users:', err);
      setUsersError('An unexpected error occurred');
    } finally {
      setUsersLoading(false);
    }
  };

  // Handle removing a user from a permission group
  const handleRemoveUser = async (userId: string) => {
    if (!currentGroup) return;

    setRemoveInProgress(prev => ({ ...prev, [userId]: true }));

    try {
      const result = await removeUserFromPermissionGroup({
        userId,
        groupId: currentGroup._id
      });

      if (result.success) {
        success('User removed from permission group');

        // Refresh users list
        await fetchGroupUsers(currentGroup._id);
      } else {
        showError(result.error || 'Failed to remove user from permission group');
      }
    } catch (err) {
      logger.error('Error removing user:', err);
      showError('An unexpected error occurred');
    } finally {
      setRemoveInProgress(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Handle permission group deletion
  const handleDeleteGroup = async () => {
    if (!groupToDelete) return;

    setDeleteInProgress(true);
    try {
      const result = await deletePermissionGroup(groupToDelete._id);

      if (result.success) {
        success('Permission group deleted successfully');

        // Refresh permission groups
        await onGroupsChanged();
        closeDeleteDialog();
      } else {
        showError(result.error || 'Failed to delete permission group');
      }
    } catch (err) {
      logger.error('Error deleting permission group:', err);
      showError('An unexpected error occurred');
    } finally {
      setDeleteInProgress(false);
    }
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {[...Array(8)].map((_, index) => (
        <Card key={index} className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-5 w-5 rounded-full" />
            </div>
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="flex items-center justify-between pt-2">
              <Skeleton className="h-6 w-16" />
              <div className="flex gap-2">
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-8 w-8 rounded-full" />
                <Skeleton className="h-8 w-8 rounded-full" />
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );

  if (loading) {
    return <LoadingSkeleton />;
  }

  // Empty state component
  const EmptyState = () => (
    <div className="text-center py-16">
      <div className="mx-auto w-24 h-24 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-full flex items-center justify-center mb-6">
        <Shield className="w-12 h-12 text-[#6964D3]" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        No Permission Groups Found
      </h3>
      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
        Get started by creating your first permission group to organize user access rights.
      </p>
      <Button
        onClick={onCreateNew}
        className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] hover:opacity-90"
      >
        <Plus className="w-4 h-4 mr-2" />
        Create Permission Group
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {error && (
        <Alert severity="error" className="mb-6">
          {error}
        </Alert>
      )}

      {/* Permission Groups Grid */}
      {permissionGroups.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {permissionGroups.map((group) => (
            <Card
              key={group._id}
              className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-lg hover:shadow-[#8178E8]/10 transition-all duration-300 hover:-translate-y-1 group"
            >
              <div className="p-6 space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg group-hover:from-[#8178E8]/30 group-hover:to-[#6964D3]/30 transition-all">
                      <Shield className="w-4 h-4 text-[#6964D3]" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white text-lg leading-tight">
                        {group.name}
                      </h3>
                      {group.isDefault && (
                        <div className="flex items-center gap-1 mt-1">
                          <Star className="w-3 h-3 text-yellow-500 fill-current" />
                          <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                            Default Group
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed overflow-hidden" style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' }}>
                  {group.description}
                </p>

                {/* Stats */}
                <div className="flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-1">
                      <Users className="w-4 h-4 text-gray-400" />
                      <Badge variant="default" className="text-xs">
                        {group.permissions.length} permissions
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                    <Calendar className="w-3 h-3" />
                    {formatDate(group.createdAt)}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openUsersDialog(group)}
                    className="flex items-center gap-2 text-xs"
                  >
                    <Users className="w-3 h-3" />
                    View Users
                  </Button>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEdit?.(group)}
                      className="p-2"
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openDeleteDialog(group)}
                      className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Permission Group"
        className="max-w-md"
      >
        <div className="space-y-4">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/50">
              <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Confirm Deletion
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                Are you sure you want to delete the permission group{' '}
                <span className="font-semibold text-gray-900 dark:text-white">
                  {groupToDelete?.name}
                </span>
                ? This action cannot be undone and will affect all users assigned to this group.
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              variant="outline"
              onClick={closeDeleteDialog}
              disabled={deleteInProgress}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteGroup}
              isLoading={deleteInProgress}
              disabled={deleteInProgress}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {deleteInProgress ? 'Deleting...' : 'Delete Group'}
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Users Dialog */}
      <Dialog
        open={usersDialogOpen}
        onOpenChange={setUsersDialogOpen}
        title={
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-lg">
                <Users className="w-4 h-4 text-[#6964D3]" />
              </div>
              <span className="text-lg font-semibold">
                Users in {currentGroup?.name}
              </span>
            </div>
            <Badge variant="default" className="text-xs">
              {users.length} users
            </Badge>
          </div>
        }
        className="max-w-4xl"
      >
        <div className="space-y-4">
          {usersLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[...Array(4)].map((_, index) => (
                <Card key={index} className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
                  <div className="p-4 space-y-3">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-2">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-3 w-24" />
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <Skeleton className="h-5 w-16" />
                      <Skeleton className="h-8 w-8 rounded-full" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          ) : usersError ? (
            <Alert severity="error" className="mb-4">
              {usersError}
            </Alert>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <div className="mx-auto w-16 h-16 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-full flex items-center justify-center mb-4">
                <Users className="w-8 h-8 text-[#6964D3]" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                No Users Assigned
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                No users are currently assigned to this permission group.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {users.map((user) => (
                <Card
                  key={user._id}
                  className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm hover:shadow-md transition-all"
                >
                  <div className="p-4 space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-full">
                        <User className="w-4 h-4 text-[#6964D3]" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 dark:text-white truncate">
                          {user.name}
                        </h4>
                        <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                          <Mail className="w-3 h-3" />
                          <span className="truncate">{user.email}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              user.status === 'active' ? 'success' :
                              user.status === 'pending' ? 'warning' : 'danger'
                            }
                            className="text-xs"
                          >
                            {user.status}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                          <Clock className="w-3 h-3" />
                          <span>
                            {user.lastLogin ? formatDate(user.lastLogin) : 'Never logged in'}
                          </span>
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRemoveUser(user._id)}
                        isLoading={removeInProgress[user._id]}
                        disabled={removeInProgress[user._id]}
                        className="p-2 text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                      >
                        <UserMinus className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={closeUsersDialog}
            >
              Close
            </Button>
          </div>
        </div>
      </Dialog>
    </div>
  );
}
'use client';

import { useState, useEffect } from 'react';
import { getPermissions, getPermissionGroups } from '@server/actions/permissions';
import PermissionGroupsList from './components/PermissionGroupsList';
import CreatePermissionGroup from './components/CreatePermissionGroup';
import EditPermissionGroup from './components/EditPermissionGroup';
import { Tabs } from '@/components/ui/Tabs';
import { Alert } from '@/components/ui/Alert';
import { Card } from '@/components/ui/Card';
import { useToast } from '@/components/ui/Toast';
import { Users, Plus, Shield } from 'lucide-react';
import { logger } from '@/utils/logger';

interface Permission {
  _id: string;
  name: string;
  description: string;
  code: string;
  module: string;
}

interface PermissionGroup {
  _id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isDefault: boolean;
  createdAt: string;
}

interface PermissionOption {
  label: string;
  value: string;
  module: string;
}

// Icon components
const ListIcon = () => <Users className="w-4 h-4" />;
const PlusIcon = () => <Plus className="w-4 h-4" />;

export default function PermissionsPage() {
  const { success, error: showError } = useToast();

  // State for permissions
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [permissionsByModule, setPermissionsByModule] = useState<Record<string, PermissionOption[]>>({});
  const [permissionGroups, setPermissionGroups] = useState<PermissionGroup[]>([]);

  // UI state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [editDialogVisible, setEditDialogVisible] = useState(false);
  const [groupToEdit, setGroupToEdit] = useState<PermissionGroup | null>(null);

  // Load permissions and permission groups on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError('');

      try {
        const [permissionsResult, groupsResult] = await Promise.all([
          getPermissions(),
          getPermissionGroups()
        ]);

        if (permissionsResult.success && permissionsResult.data) {
          const permissionList = permissionsResult.data.permissions;
          setPermissions(permissionList);

          // Group permissions by module for easier selection
          const byModule: Record<string, PermissionOption[]> = {};

          permissionList.forEach((permission: Permission) => {
            if (!byModule[permission.module]) {
              byModule[permission.module] = [];
            }

            byModule[permission.module].push({
              label: `${permission.name} (${permission.code})`,
              value: permission._id,
              module: permission.module
            });
          });

          setPermissionsByModule(byModule);
        } else if (!permissionsResult.success) {
          setError(permissionsResult.error || 'Failed to load permissions');
        }

        if (groupsResult.success && groupsResult.data) {
          setPermissionGroups(groupsResult.data.groups);
        } else if (!groupsResult.success) {
          setError(groupsResult.error || 'Failed to load permission groups');
        }
      } catch (err) {
        logger.error('Error loading data:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Switch to create tab
  const switchToCreateTab = () => {
    setActiveTabIndex(1);
  };

  // Refresh permission groups
  const refreshPermissionGroups = async () => {
    try {
      const groupsResult = await getPermissionGroups();
      if (groupsResult.success && groupsResult.data) {
        setPermissionGroups(groupsResult.data.groups);
      }
    } catch (err) {
      logger.error('Error refreshing permission groups:', err);
    }
  };

  // Open edit dialog
  const openEditDialog = (group: PermissionGroup) => {
    setGroupToEdit(group);
    setEditDialogVisible(true);
  };

  const tabs = [
    {
      id: 'groups',
      label: 'Permission Groups',
      icon: <ListIcon />,
      content: (
        <PermissionGroupsList
          loading={loading}
          error={error}
          permissionGroups={permissionGroups}
          onCreateNew={switchToCreateTab}
          onEdit={openEditDialog}
          onGroupsChanged={refreshPermissionGroups}
        />
      )
    },
    {
      id: 'create',
      label: 'Create New Group',
      icon: <PlusIcon />,
      content: (
        <CreatePermissionGroup
          permissions={permissions}
          permissionsByModule={permissionsByModule}
          onSuccess={() => {
            refreshPermissionGroups();
            setActiveTabIndex(0);
          }}
          toast={{ success, error: showError }}
        />
      )
    }
  ];

  return (
    <div className="p-6 max-w-7xl mx-auto space-y-6">
      {/* Header Section */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <div className="p-3 bg-gradient-to-r from-[#8178E8]/20 to-[#6964D3]/20 rounded-xl">
            <Shield className="w-6 h-6 text-[#6964D3]" />
          </div>
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent">
              Permission Groups
            </h1>
            <p className="text-gray-500 dark:text-gray-400 mt-1">
              Manage permission groups to control access to different parts of the application
            </p>
          </div>
        </div>
      </div>

      {error && (
        <Alert severity="error" className="mb-6">
          {error}
        </Alert>
      )}

      <Card className="bg-white/70 dark:bg-[#1e1e28]/70 border border-[#E0D7FF]/50 dark:border-[#2c2d3d] rounded-xl backdrop-blur-sm">
        <div className="p-6">
          <Tabs
            tabs={tabs}
            defaultActiveTab={activeTabIndex === 1 ? 'create' : 'groups'}
            variant="minimal"
            className="permissions-tabs"
          />
        </div>
      </Card>

      {/* Edit Permission Group Dialog */}
      <EditPermissionGroup
        open={editDialogVisible}
        group={groupToEdit}
        permissionsByModule={permissionsByModule}
        onOpenChange={setEditDialogVisible}
        onSuccess={refreshPermissionGroups}
      />
    </div>
  );
}

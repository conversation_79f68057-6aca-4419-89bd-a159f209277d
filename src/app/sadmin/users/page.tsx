'use client';

import { useState, useEffect } from 'react';
import { getUsers, deleteUser } from '@/server/actions/admin-user-actions';
import { useRouter } from 'next/navigation';
import { logger } from '@/utils/logger';

// Icon components
import { PencilSquareIcon, TrashIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

// UI components
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Dialog } from '@/components/ui/Dialog';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Alert } from '@/components/ui/Alert';
import { Skeleton } from '@/components/ui/Skeleton';
import { Pagination } from '@/components/ui/Pagination';
import {Select} from "@components/ui/Select";


type UserDetailsResponse = Awaited<ReturnType<typeof getUsers>>
type UserDetailsItem = NonNullable<UserDetailsResponse['data']>['users'][number];

export default function UsersPage() {
  const router = useRouter();

  // State for users data
  const [users, setUsers] = useState<NonNullable<UserDetailsResponse['data']>['users']>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // State for pagination
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);

  // State for search
  const [search, setSearch] = useState('');
  const [debounceSearch, setDebounceSearch] = useState('');

  // State for status filter
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'deleted'>('all');

  // State for delete confirmation
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserDetailsItem | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Load users data
  useEffect(() => {
    loadUsers();
  }, [page, limit, debounceSearch, statusFilter]);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebounceSearch(search);
    }, 500);
    return () => clearTimeout(timer);
  }, [search]);

  // Load users function
  const loadUsers = async () => {
    if (isRefreshing) return;

    setLoading(true);
    setError(null);

    try {
      const result = await getUsers({
        page: page.toString(),
        limit: limit.toString(),
        search: debounceSearch,
        status: statusFilter
      });

      if (result.success && result.data) {
        setUsers(result.data.users);
        setTotal(result.data.pagination.total);
      } else {
        setError(result.error || 'Failed to load users');
      }
    } catch (err) {
      logger.error('Error fetching users:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Refresh data
  const refreshData = async () => {
    setIsRefreshing(true);
    await loadUsers();
    setIsRefreshing(false);
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle delete user
  const handleDeleteUser = (user: UserDetailsItem) => {
    setUserToDelete(user);
    setDeleteDialogOpen(true);
  };

  // Confirm soft delete user
  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);
    try {
      const result = await deleteUser(userToDelete._id);

      if (result.success) {
        setDeleteDialogOpen(false);
        setUserToDelete(null);
        refreshData();
      } else {
        setError(result.error || 'Failed to delete user');
      }
    } catch (err) {
      logger.error('Error soft deleting user:', err);
      setError('An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">User Management</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Manage user accounts across all organizations
          </p>
        </div>
      </div>

      <Card className="mt-8 p-6">
        <CardContent className="p-6">
          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
            <div className="relative w-full sm:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                type="text"
                placeholder="Search users..."
                className="pl-10"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label htmlFor="status-filter" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Status:
                </label>
                <Select
                  options={[
                    { label: 'All', value: 'all' },
                    { label: 'Active', value: 'active' },
                    { label: 'Inactive', value: 'inactive' },
                    { label: 'Deleted', value: 'deleted' },
                  ]}
                  id="status-filter"
                  value={statusFilter}
                  onChange={(value) => setStatusFilter(value as 'all' | 'active' | 'inactive' | 'deleted')}
                />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={refreshData}
                disabled={isRefreshing}
              >
                {isRefreshing ? 'Refreshing...' : 'Refresh'}
              </Button>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <Alert severity="error" className="mb-4">
              {error}
            </Alert>
          )}

          {/* Users table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
              <thead>
                <tr>
                  <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-0">
                    Name
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                    Email
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                    Company
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                    Status
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                    Last Login
                  </th>
                  <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                    Created
                  </th>
                  <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-0">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-800">
                {loading ? (
                  // Loading skeletons
                  Array.from({ length: 5 }).map((_, index) => (
                    <tr key={`skeleton-${index}`}>
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 sm:pl-0">
                        <Skeleton className="h-5 w-32" />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4">
                        <Skeleton className="h-5 w-40" />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4">
                        <Skeleton className="h-5 w-24" />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4">
                        <Skeleton className="h-5 w-16" />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4">
                        <Skeleton className="h-5 w-24" />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4">
                        <Skeleton className="h-5 w-24" />
                      </td>
                      <td className="whitespace-nowrap px-3 py-4">
                        <Skeleton className="h-8 w-16" />
                      </td>
                    </tr>
                  ))
                ) : users.length === 0 ? (
                  // Empty state
                  <tr>
                    <td colSpan={7} className="py-8 text-center text-gray-500 dark:text-gray-400">
                      No users found. {search && 'Try a different search term.'}
                    </td>
                  </tr>
                ) : (
                  // User rows
                  users.map((user) => (
                    <tr key={user._id} className="hover:bg-gray-50 dark:hover:bg-gray-900/50">
                      <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-0">
                        {user.name}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {user.email}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {user.company}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm">
                        {user.deleted ? (
                          <Badge variant="danger">Deleted</Badge>
                        ) : (
                          <Badge
                            variant={user.active ? 'success' : 'danger'}
                          >
                            {user.active ? 'Active' : 'Inactive'}
                          </Badge>
                        )}
                        {user.emailVerified && !user.deleted && (
                          <Badge
                            variant="info"
                            className="ml-2"
                          >
                            Verified
                          </Badge>
                        )}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {user.lastLogin ? formatDate(user.lastLogin) : 'Never'}
                      </td>
                      <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                        {formatDate(user.createdAt)}
                      </td>
                      <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => router.push(`/sadmin/users/${user.id}`)}
                          >
                            <PencilSquareIcon className="h-4 w-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            onClick={() => handleDeleteUser(user)}
                          >
                            <TrashIcon className="h-4 w-4 mr-1" />
                            Deactivate
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {!loading && users.length > 0 && (
            <div className="mt-6">
              <Pagination
                currentPage={page}
                totalItems={total}
                pageSize={limit}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete confirmation dialog */}
      <Dialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Deactivate User"
      >
        <div className="space-y-4">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Are you sure you want to deactivate the user <span className="font-medium text-gray-900 dark:text-white">{userToDelete?.name}</span>?
          </p>

          <div className="bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-400 p-4 mb-4">
            <div className="flex">
              <div className="ml-3">
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Note: This will perform a soft delete. The user will be deactivated and their access to all organizations and products will be suspended.
                  Their data will be preserved in the system but they will no longer be able to log in.
                </p>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="ghost"
              onClick={() => setDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="danger"
              onClick={confirmDeleteUser}
              disabled={isDeleting}
            >
              {isDeleting ? 'Deactivating...' : 'Deactivate User'}
            </Button>
          </div>
        </div>
      </Dialog>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { getUser } from '@/server/actions/admin-user-actions';
import Link from 'next/link';

// UI components
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Alert } from '@/components/ui/Alert';
import { Skeleton } from '@/components/ui/Skeleton';
import { Badge } from '@/components/ui/Badge';

interface Organization {
  organizationId: string;
  organizationName: string;
  role: string;
  status: string;
  isOwner: boolean;
}

export default function UserOrganizationsPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  // State
  const [user, setUser] = useState<any>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const result = await getUser(id);
        
        if (result.success && result.data) {
          setUser(result.data);
          setOrganizations(result.data.organizations || []);
        } else {
          setError('error' in result ? String(result.error) : 'Failed to load user data');
        }
      } catch (err) {
        console.error('Error loading user:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      loadUser();
    }
  }, [id]);
  
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center mb-6">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">User Organizations</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            View organizations that this user belongs to
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link href={`/sadmin/users/${id}`}>
            <Button variant="outline">
              Back to User
            </Button>
          </Link>
        </div>
      </div>
      
      {error && (
        <Alert severity="error" className="mb-6">
          {error}
        </Alert>
      )}
      
      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-6">
              <Skeleton className="h-8 w-1/3" />
              <div className="space-y-4">
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
                <Skeleton className="h-16 w-full" />
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div className="mb-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">User Information</h2>
              <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                  <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.email}</p>
                </div>
              </div>
            </div>
            
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">Organizations</h2>
              
              {organizations.length === 0 ? (
                <div className="mt-4 p-6 text-center text-gray-500 dark:text-gray-400 border border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
                  <p>This user is not a member of any organizations.</p>
                </div>
              ) : (
                <div className="mt-4 overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-700">
                    <thead>
                      <tr>
                        <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 dark:text-white sm:pl-0">
                          Organization
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                          Role
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                          Status
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
                          Owner
                        </th>
                        <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-0">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-800">
                      {organizations.map((org) => (
                        <tr key={org.organizationId} className="hover:bg-gray-50 dark:hover:bg-gray-900/50">
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 dark:text-white sm:pl-0">
                            {org.organizationName}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                            {org.role}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm">
                            <Badge
                              variant={org.status === 'active' ? 'success' : org.status === 'invited' ? 'warning' : 'danger'}
                            >
                              {org.status}
                            </Badge>
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-400">
                            {org.isOwner ? (
                              <Badge variant="info">Owner</Badge>
                            ) : 'No'}
                          </td>
                          <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                            <Link href={`/sadmin/organizations/${org.organizationId}`}>
                              <Button
                                variant="ghost"
                                size="sm"
                              >
                                View
                              </Button>
                            </Link>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

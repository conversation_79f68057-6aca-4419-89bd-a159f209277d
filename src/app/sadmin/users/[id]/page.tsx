'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { getUser, updateUser } from '@/server/actions/admin-user-actions';
import Link from 'next/link';
import { logger } from '@/utils/logger';

// UI components
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Alert, SuccessAlert } from '@/components/ui/Alert';
import { Form, FormField, FormSection, FormActions } from '@/components/ui/Form';
import { Skeleton } from '@/components/ui/Skeleton';
import { Switch } from '@/components/ui/Switch';

// Define the response type using Awaited and ReturnType
type GetUserResponse = Awaited<ReturnType<typeof getUser>>;
type UpdateUserResponse = Awaited<ReturnType<typeof updateUser>>;

// User interface derived from the response type
interface User {
  id: string;
  name: string;
  email: string;
  company: string;
  active: boolean;
  emailVerified: boolean;
  deleted?: boolean;
  deletedAt?: string;
  lastLogin?: string;
  createdAt: string;
  organizations?: Array<{
    organizationId: string;
    organizationName: string;
    role: string;
    status: string;
    isOwner: boolean;
  }>;
}

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [active, setActive] = useState(true);

  // UI state
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Original data for comparison
  const [originalUser, setOriginalUser] = useState<User | null>(null);

  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      setLoading(true);
      setError(null);

      try {
        const result = await getUser(id);

        if (result.success && result.data) {
          // Cast the data to our User interface
          const userData = result.data as unknown as User;
          setName(userData.name);
          setEmail(userData.email);
          setCompany(userData.company);
          setActive(userData.active);
          setOriginalUser(userData);
        } else {
          setError((result as any).error || 'Failed to load user data');
        }
      } catch (err) {
        logger.error('Error loading user:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadUser();
    }
  }, [id]);

  // State for super admin check
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  // Check if current user is a super admin
  useEffect(() => {
    const checkSuperAdmin = async () => {
      try {
        // This is a placeholder - you would need to implement a proper check
        // For now, we'll assume the check is done on the server side
        // and we'll just show a message if the update fails
        setIsSuperAdmin(false);
      } catch (err) {
        logger.error('Error checking super admin status:', err);
      }
    };

    checkSuperAdmin();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(false);

    try {
      // Check for changes
      const updateData: any = { id };

      if (name !== originalUser?.name) updateData.name = name;
      if (email !== originalUser?.email) updateData.email = email;
      if (company !== originalUser?.company) updateData.company = company;
      if (active !== originalUser?.active) updateData.active = active;

      // Only proceed if there are changes
      if (Object.keys(updateData).length <= 1) {
        setError('No changes detected');
        setSaving(false);
        return;
      }

      const result = await updateUser(updateData);

      if (result.success) {
        setSuccess(true);

        // Update original user data
        if (result.data) {
          setOriginalUser({
            ...originalUser!,
            ...(result.data as unknown as User)
          });
        }

        // Redirect after success
        setTimeout(() => {
          router.push('/sadmin/users');
        }, 2000);
      } else {
        // Check if error is due to super admin restriction
        const errorMessage = (result as any).error || '';
        if (errorMessage.includes('Super admins can only view')) {
          setIsSuperAdmin(true);
          setError('As a super admin, you can only view user information but cannot modify users');
        } else {
          setError(errorMessage || 'Failed to update user');
        }
      }
    } catch (err) {
      logger.error('Error updating user:', err);
      setError('An unexpected error occurred');
    } finally {
      setSaving(false);
    }
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center mb-6">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Edit User</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Update user information and status
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link href="/sadmin/users">
            <Button variant="outline">
              Back to Users
            </Button>
          </Link>
        </div>
      </div>

      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-6">
              <Skeleton className="h-8 w-1/3" />
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-6 w-24" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {error && !success && (
            <Alert severity="error" className="mb-6">
              {error}
            </Alert>
          )}

          {success && (
            <SuccessAlert className="mb-6">
              User updated successfully. Redirecting...
            </SuccessAlert>
          )}

          {isSuperAdmin && (
            <Alert severity="warn" className="mb-6">
              <p className="font-medium">Super Admin View Mode</p>
              <p>As a super admin, you can view user information but cannot modify users. This is a security restriction.</p>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <Card>
                <CardContent className="p-6">
                  <Form onSubmit={handleSubmit}>
                    <FormSection title="User Information">
                      <FormField label="Name" required>
                        <Input
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          required
                          minLength={2}
                          disabled={saving || isSuperAdmin}
                        />
                      </FormField>

                      <FormField label="Email" required>
                        <Input
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          disabled={saving || isSuperAdmin}
                        />
                      </FormField>

                      <FormField label="Company" required>
                        <Input
                          type="text"
                          value={company}
                          onChange={(e) => setCompany(e.target.value)}
                          required
                          disabled={saving || isSuperAdmin}
                        />
                      </FormField>

                      <FormField label="Status">
                        <div className="flex items-center space-x-2">
                          <Switch
                            checked={active}
                            onChange={(e) => setActive(e.value)}
                            disabled={saving || isSuperAdmin}
                          />
                          <span className={active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                            {active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </FormField>
                    </FormSection>

                    <FormActions>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={() => router.push('/sadmin/users')}
                        disabled={saving || isSuperAdmin}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        variant="primary"
                        disabled={saving || isSuperAdmin}
                      >
                        {saving ? 'Saving...' : 'Save Changes'}
                      </Button>
                    </FormActions>
                  </Form>
                </CardContent>
              </Card>
            </div>

            <div>
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">User Details</h3>

                  <div className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">User ID</p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">{originalUser?.id}</p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email Verification</p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {originalUser?.emailVerified ? 'Verified' : 'Not Verified'}
                      </p>
                    </div>

                    {originalUser?.deleted && (
                      <div>
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Deletion Status</p>
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400 font-medium">
                          Deleted on {formatDate(originalUser?.deletedAt)}
                        </p>
                      </div>
                    )}

                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Login</p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {formatDate(originalUser?.lastLogin)}
                      </p>
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Created At</p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">
                        {formatDate(originalUser?.createdAt)}
                      </p>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3">Actions</h4>

                    <div className="space-y-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-center"
                        onClick={() => router.push(`/sadmin/users/${id}/reset-password`)}
                      >
                        Reset Password
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full justify-center"
                        onClick={() => router.push(`/sadmin/users/${id}/organizations`)}
                      >
                        View Organizations
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { getUser, sendPasswordResetEmail } from '@/server/actions/admin-user-actions';
import Link from 'next/link';

// UI components
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Alert, SuccessAlert } from '@/components/ui/Alert';
import { Skeleton } from '@/components/ui/Skeleton';

export default function ResetPasswordPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  // State
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [resetUrl, setResetUrl] = useState<string | null>(null);
  
  // Load user data
  useEffect(() => {
    const loadUser = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const result = await getUser(id);
        
        if (result.success && result.data) {
          setUser(result.data);
        } else {
          setError('error' in result ? String(result.error) : 'Failed to load user data');
        }
      } catch (err) {
        console.error('Error loading user:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };
    
    if (id) {
      loadUser();
    }
  }, [id]);
  
  // Handle send reset email
  const handleSendResetEmail = async () => {
    setSending(true);
    setError(null);
    setSuccess(false);
    setResetUrl(null);
    
    try {
      const result = await sendPasswordResetEmail(id);
      
      if (result.success) {
        setSuccess(true);
        
        if (result.data?.resetUrl) {
          setResetUrl(result.data.resetUrl);
        }
      } else {
        setError('error' in result ? String(result.error) : 'Failed to send password reset email');
      }
    } catch (err) {
      console.error('Error sending reset email:', err);
      setError('An unexpected error occurred');
    } finally {
      setSending(false);
    }
  };
  
  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center mb-6">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Reset Password</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Send a password reset email to the user
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link href={`/sadmin/users/${id}`}>
            <Button variant="outline">
              Back to User
            </Button>
          </Link>
        </div>
      </div>
      
      {loading ? (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-6">
              <Skeleton className="h-8 w-1/3" />
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-10 w-32" />
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {error && !success && (
            <Alert severity="error" className="mb-6">
              {error}
            </Alert>
          )}
          
          {success && (
            <SuccessAlert className="mb-6">
              Password reset email sent successfully.
            </SuccessAlert>
          )}
          
          <Card>
            <CardContent className="p-6">
              <div className="space-y-6">
                <div>
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">User Information</h2>
                  <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Name</p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.name}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Email</p>
                      <p className="mt-1 text-sm text-gray-900 dark:text-white">{user?.email}</p>
                    </div>
                  </div>
                </div>
                
                <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                  <h2 className="text-lg font-medium text-gray-900 dark:text-white">Reset Password</h2>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                    This will send an email to the user with instructions to reset their password.
                    The link will be valid for 24 hours.
                  </p>
                  
                  {resetUrl && (
                    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-md">
                      <p className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                        For development purposes, here is the password reset link:
                      </p>
                      <div className="bg-white dark:bg-gray-900 p-3 rounded border border-gray-200 dark:border-gray-700 overflow-x-auto">
                        <code className="text-sm text-gray-800 dark:text-gray-200">{resetUrl}</code>
                      </div>
                    </div>
                  )}
                  
                  <div className="mt-6">
                    <Button
                      variant="primary"
                      onClick={handleSendResetEmail}
                      disabled={sending}
                    >
                      {sending ? 'Sending...' : 'Send Reset Email'}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

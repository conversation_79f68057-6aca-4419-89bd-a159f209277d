'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { createUser } from '@/server/actions/admin-user-actions';
import Link from 'next/link';

// UI components
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent } from '@/components/ui/Card';
import { Alert, SuccessAlert } from '@/components/ui/Alert';
import { Form, FormField, FormSection, FormActions } from '@/components/ui/Form';
import { Switch } from '@/components/ui/Switch';

// Define the response type using Awaited and ReturnType
type CreateUserResponse = Awaited<ReturnType<typeof createUser>>;

export default function NewUserPage() {
  const router = useRouter();

  // Form state
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [active, setActive] = useState(true);
  const [sendWelcomeEmail, setSendWelcomeEmail] = useState(true);

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [emailError, setEmailError] = useState('');
  const [resetUrl, setResetUrl] = useState('');

  // State for super admin check
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);

  // Check if current user is a super admin
  useEffect(() => {
    const checkSuperAdmin = async () => {
      try {
        // This is a placeholder - you would need to implement a proper check
        // For now, we'll assume the check is done on the server side
        // and we'll just show a message if the create fails
        setIsSuperAdmin(false);
      } catch (err) {
        console.error('Error checking super admin status:', err);
      }
    };

    checkSuperAdmin();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If super admin, prevent form submission
    if (isSuperAdmin) {
      setError('As a super admin, you cannot create users. This is a security restriction.');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);
    setEmailSent(false);
    setEmailError('');
    setResetUrl('');

    try {
      const result = await createUser({
        name,
        email,
        company,
        active,
        sendWelcomeEmail
      });

      if (result.success) {
        setSuccess(true);

        // Check if email was sent successfully
        if (result.data?.emailSent === false) {
          setEmailSent(false);
          setEmailError('emailError' in (result.data || {}) ? String((result.data as any).emailError) : 'Unknown error sending email');
        } else {
          setEmailSent(true);
        }

        // Set the reset URL if available (for development)
        if (result.data?.resetUrl) {
          setResetUrl(result.data.resetUrl);
        }

        // Reset form
        setName('');
        setEmail('');
        setCompany('');
        setActive(true);
        setSendWelcomeEmail(true);

        // Redirect to user list after a delay only if everything went well
        if (result.data?.emailSent) {
          setTimeout(() => {
            router.push('/sadmin/users');
          }, 3000);
        }
      } else {
        // Check if error is due to super admin restriction
        const errorMessage = 'error' in result ? String(result.error) : 'Failed to create user';
        if (errorMessage.includes('Super admins can only view')) {
          setIsSuperAdmin(true);
          setError('As a super admin, you cannot create users. This is a security restriction.');
        } else {
          setError(errorMessage);
        }
      }
    } catch (err) {
      console.error('Error creating user:', err);
      setError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      <div className="sm:flex sm:items-center mb-6">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Add New User</h1>
          <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Create a new user account
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link href="/sadmin/users">
            <Button variant="outline">
              Back to Users
            </Button>
          </Link>
        </div>
      </div>

      {error && !success && (
        <Alert severity="error" className="mb-6">
          {error}
        </Alert>
      )}

      {success && (
        <SuccessAlert className="mb-6">
          User created successfully.
          {emailSent ? ' A welcome email has been sent to the user.' : ''}
          {emailSent && ' Redirecting...'}
        </SuccessAlert>
      )}

      {isSuperAdmin && (
        <Alert severity="warn" className="mb-6">
          <p className="font-medium">Super Admin Restriction</p>
          <p>As a super admin, you cannot create users. This is a security restriction to ensure proper separation of duties.</p>
        </Alert>
      )}

      {success && !emailSent && (
        <Alert severity="warn" className="mb-6">
          <p>The welcome email could not be sent: {emailError}</p>
          {resetUrl && (
            <div className="mt-2">
              <p>For development purposes, here is the password reset link:</p>
              <code className="block mt-1 p-2 bg-gray-100 dark:bg-gray-800 rounded text-sm overflow-x-auto">
                {resetUrl}
              </code>
            </div>
          )}
        </Alert>
      )}

      <Card>
        <CardContent className="p-6">
          <Form onSubmit={handleSubmit}>
            <FormSection title="User Information">
              <FormField label="Name" required>
                <Input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  minLength={2}
                  disabled={loading || isSuperAdmin}
                  placeholder="Full Name"
                />
              </FormField>

              <FormField label="Email" required>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={loading || isSuperAdmin}
                  placeholder="<EMAIL>"
                />
              </FormField>

              <FormField label="Company" required>
                <Input
                  type="text"
                  value={company}
                  onChange={(e) => setCompany(e.target.value)}
                  required
                  disabled={loading || isSuperAdmin}
                  placeholder="Company Name"
                />
              </FormField>

              <FormField label="Status">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={active}
                    onCheckedChange={setActive}
                    disabled={loading || isSuperAdmin}
                  />
                  <span className={active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}>
                    {active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </FormField>

              <FormField label="Send Welcome Email">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={sendWelcomeEmail}
                    onCheckedChange={setSendWelcomeEmail}
                    disabled={loading || isSuperAdmin}
                  />
                  <span>
                    {sendWelcomeEmail ? 'Yes' : 'No'}
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  If enabled, the user will receive an email with instructions to set their password.
                </p>
              </FormField>
            </FormSection>

            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-blue-600 dark:text-blue-300 text-sm mb-6">
              <p className="font-medium">Note:</p>
              <p>
                Creating a user account does not automatically add them to any organization.
                You will need to invite them to an organization separately.
              </p>
            </div>

            <FormActions>
              <Button
                type="button"
                variant="ghost"
                onClick={() => router.push('/sadmin/users')}
                disabled={loading || isSuperAdmin}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                disabled={loading || isSuperAdmin}
              >
                {loading ? 'Creating...' : 'Create User'}
              </Button>
            </FormActions>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}

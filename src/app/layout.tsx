import type { Metada<PERSON> } from "next";
import { Inter, Plus_Jakarta_Sans } from "next/font/google";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import "../../public/lara-light-blue/theme.css";
import "./globals.css";
// Import the ToastProvider from the new file
import { ToastProvider } from "@/components/ui/ToastProvider";
import { ConfirmDialog } from "@/components/ui/ConfirmDialog";
import { NotificationSoundProvider } from "@/providers";
import { NuqsAdapter } from 'nuqs/adapters/next/app'
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jakarta = Plus_Jakarta_Sans({
  variable: "--font-jakarta",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "New Instance | Modern Business Solutions",
  description: "A multi-service platform providing integrated business solutions for the modern digital enterprise",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <body suppressHydrationWarning
        className={`${inter.variable} ${jakarta.variable} antialiased`}
      >
        <NuqsAdapter>
        <NotificationSoundProvider>
          <ConfirmDialog/>
          <ToastProvider/>
          {children}
        </NotificationSoundProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
}

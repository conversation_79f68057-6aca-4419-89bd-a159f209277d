'use client';

import { Suspense } from 'react';
import { useRouter } from 'next/navigation';
import VerifyForm from '@/components/auth/VerifyForm';

function VerifyContent() {
  return (
    <VerifyForm />
  );
}

export default function VerifyPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center min-h-screen">Loading verification page...</div>}>
      <VerifyContent />
    </Suspense>
  );
}

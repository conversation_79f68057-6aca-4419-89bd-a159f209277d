'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormField } from '@/components/ui/Form';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Loader2 } from 'lucide-react';
import { verifyProductInviteToken, acceptProductInvite } from '@/server/actions/product-invite-actions';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';

// Form schema for completing profile
const profileSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Password confirmation is required'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

// Form schema for just accepting invite
const acceptSchema = z.object({});
type verifyProductInviteTokenRes = Awaited<ReturnType<typeof verifyProductInviteToken>>
type ProfileFormValues = z.infer<typeof profileSchema>;
type AcceptFormValues = z.infer<typeof acceptSchema>;

function AcceptProductInviteContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');
  const { success, error: showError } = useToast();

  const [isVerifying, setIsVerifying] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inviteData, setInviteData] = useState<verifyProductInviteTokenRes['data']>();

  // Set up form for new user profile
  const {
    control: profileControl,
    handleSubmit: handleProfileSubmit,
    formState: { errors: profileErrors },
  } = useForm<ProfileFormValues>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Set up form for existing user acceptance
  const {
    handleSubmit: handleAcceptSubmit,
  } = useForm<AcceptFormValues>({
    resolver: zodResolver(acceptSchema),
    defaultValues: {},
  });

  // Verify token on mount
  useEffect(() => {
    async function verifyToken() {
      if (!token) {
        setError('Invitation token is missing');
        setIsVerifying(false);
        return;
      }

      try {
        setIsVerifying(true);
        const response = await verifyProductInviteToken({ token });

        if (!response.success) {
          setError(response.error || 'Failed to verify invitation');
        } else if (response.data) {
          setInviteData(response.data);
        }
      } catch (err) {
        logger.error('Error verifying token:', err);
        setError('Failed to verify invitation token');
      } finally {
        setIsVerifying(false);
      }
    }

    verifyToken();
  }, [token]);

  // Handle submission for new user profile
  const onSubmitProfile = async (data: ProfileFormValues) => {
    if (!token) return;

    try {
      setIsLoading(true);
      const response = await acceptProductInvite({
        token,
        name: data.name,
        password: data.password,
      });

      if (!response.success) {
        showError('Error', response.error || 'Failed to accept invitation');
      } else if (response.data?.redirectUrl) {
        success('Success', 'Invitation accepted successfully');
        router.push(response.data.redirectUrl);
      }
    } catch (err) {
      logger.error('Error accepting invitation:', err);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle submission for existing user acceptance
  const onSubmitAccept = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      const response = await acceptProductInvite({ token });

      if (!response.success) {
        showError('Error', response.error || 'Failed to accept invitation');
      } else if (response.data?.redirectUrl) {
        success('Success', 'Invitation accepted successfully');
        router.push(response.data.redirectUrl);
      }
    } catch (err) {
      logger.error('Error accepting invitation:', err);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  if (isVerifying) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
        <div className="w-full max-w-md">
          <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
            <div className="text-center">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <Loader2 className="w-10 h-10 animate-spin text-white" />
              </div>
              <h2 className="mt-6 text-2xl font-bold text-gray-800 dark:text-white">Verifying invitation...</h2>
              <p className="mt-2 text-gray-500 dark:text-gray-400">This will only take a moment</p>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
        <div className="w-full max-w-md">
          <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
            <div className="text-center mb-8">
              <div className="bg-gradient-to-r from-red-500 to-pink-500 p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h1 className="mt-6 text-2xl font-bold text-gray-800 dark:text-white">Invitation Error</h1>
              <p className="mt-2 text-gray-500 dark:text-gray-400">{error}</p>
            </div>
            <Button
              variant="primary"
              className="w-full py-4 rounded-xl text-base font-medium shadow-lg transform transition hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center"
              onClick={() => router.push('/auth')}
            >
              Go to Login
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  if (!inviteData) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
      <div className="w-full max-w-lg">
        <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
          <div className="text-center mb-8">
            <div className="mb-6">
              <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl inline-flex">
                <Image src="/assets/logos/purple-cloud-yellow-dots.svg" alt="Logo" width={120} height={40} />
              </div>
            </div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-800 dark:text-white mb-2">
              {inviteData.isNewUser ? 'Complete Your Profile' : 'Product Invitation'}
            </h1>
            <p className="text-gray-500 dark:text-gray-400 text-lg max-w-md mx-auto">
              You have been invited to join <span className="font-semibold text-indigo-600 dark:text-indigo-400">{inviteData.productName}</span> in organization <span className="font-semibold text-indigo-600 dark:text-indigo-400">{inviteData.organizationName}</span>
            </p>
          </div>

          {inviteData.isNewUser ? (
            <form onSubmit={handleProfileSubmit(onSubmitProfile)} className="space-y-6" noValidate>
              <FormField
                label="Full Name"
                htmlFor="name"
                required
                error={profileErrors.name?.message}
              >
                <Controller
                  name="name"
                  control={profileControl}
                  render={({ field }) => (
                    <Input
                      id="name"
                      {...field}
                      placeholder="Enter your full name"
                      error={profileErrors.name?.message}
                    />
                  )}
                />
              </FormField>

              <FormField
                label="Password"
                htmlFor="password"
                required
                error={profileErrors.password?.message}
              >
                <Controller
                  name="password"
                  control={profileControl}
                  render={({ field }) => (
                    <Input
                      id="password"
                      type="password"
                      {...field}
                      placeholder="Create a password"
                      error={profileErrors.password?.message}
                    />
                  )}
                />
              </FormField>

              <FormField
                label="Confirm Password"
                htmlFor="confirmPassword"
                required
                error={profileErrors.confirmPassword?.message}
              >
                <Controller
                  name="confirmPassword"
                  control={profileControl}
                  render={({ field }) => (
                    <Input
                      id="confirmPassword"
                      type="password"
                      {...field}
                      placeholder="Confirm your password"
                      error={profileErrors.confirmPassword?.message}
                    />
                  )}
                />
              </FormField>

              <div className="mt-8">
                <Button
                  type="submit"
                  isLoading={isLoading}
                  className="w-full py-4 rounded-xl text-base font-medium bg-gradient-to-r from-[#8178E8] to-[#6964D3] shadow-lg shadow-indigo-500/30 dark:shadow-indigo-700/30 transform transition hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center"
                >
                  {isLoading ? 'Setting up your account' : 'Complete Profile & Accept Invitation'}
                </Button>
              </div>
            </form>
          ) : (
            <form onSubmit={handleAcceptSubmit(onSubmitAccept)} className="space-y-6" noValidate>
              <div className="backdrop-blur-md bg-gray-50/80 dark:bg-gray-800/80 p-6 rounded-xl border border-gray-200 dark:border-gray-700">
                <p className="text-base text-gray-700 dark:text-gray-300 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="font-medium">{inviteData.email}</span>
                </p>
              </div>

              <div className="mt-8">
                <Button
                  type="submit"
                  isLoading={isLoading}
                  className="w-full py-4 rounded-xl font-medium bg-gradient-to-r from-[#8178E8] to-[#6964D3] shadow-lg shadow-indigo-500/30 dark:shadow-indigo-700/30 transform transition hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center"
                >
                  {isLoading ? 'Processing' : 'Accept Invitation'}
                </Button>
              </div>
            </form>
          )}

          <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
            By accepting this invitation, you agree to our
            <a href="#" className="ml-1 text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">Terms of Service</a>
          </div>
        </Card>
      </div>
    </div>
  );
}

// Main component that wraps the content in a Suspense boundary
export default function AcceptProductInvitePage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
        <div className="w-full max-w-md">
          <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
            <div className="text-center">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <Loader2 className="w-10 h-10 animate-spin text-white" />
              </div>
              <h2 className="mt-6 text-2xl font-bold text-gray-800 dark:text-white">Loading...</h2>
              <p className="mt-2 text-gray-500 dark:text-gray-400">Please wait</p>
            </div>
          </Card>
        </div>
      </div>
    }>
      <AcceptProductInviteContent />
    </Suspense>
  );
}

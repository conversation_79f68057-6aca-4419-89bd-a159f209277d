'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Image from 'next/image';
import { z } from 'zod';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormField } from '@/components/ui/Form';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { Loader2 } from 'lucide-react';
import { verifyInviteToken, acceptInvite } from '@/server/actions/invite-actions';
import { useToast } from '@/components/ui/Toast';
import { logger } from '@/utils/logger';

// Define form schema
const inviteAcceptSchema = z.object({
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  confirmPassword: z.string().min(8, 'Confirm password must be at least 8 characters'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

type InviteAcceptFormData = z.infer<typeof inviteAcceptSchema>;

// Client component that uses useSearchParams
function InviteAcceptContent() {
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [inviteData, setInviteData] = useState<{
    organizationId: string;
    organizationName: string;
    email: string;
  } | null>(null);

  const searchParams = useSearchParams();
  const router = useRouter();
  const { success, error: showError } = useToast();

  // Get token from URL
  const token = searchParams.get('token');

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<InviteAcceptFormData>({
    resolver: zodResolver(inviteAcceptSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Verify token on mount
  useEffect(() => {
    async function verifyToken() {
      if (!token) {
        setError('Invitation token is missing');
        setIsVerifying(false);
        return;
      }

      try {
        setIsVerifying(true);
        const response = await verifyInviteToken({ token });

        if (!response.success) {
          setError(response.error || 'Failed to verify invitation');
        } else if (response.data) {
          setInviteData(response.data);
        }
      } catch (err) {
        logger.error('Error verifying token:', err);
        setError('Failed to verify invitation token');
      } finally {
        setIsVerifying(false);
      }
    }

    verifyToken();
  }, [token]);

  const onSubmit = async (data: InviteAcceptFormData) => {
    if (!token) {
      setError('Invalid invitation link. Please request a new invitation.');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await acceptInvite({
        token,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
      });

      if (response.success) {
        success('Success', 'Your account has been created successfully. Redirecting to login...');

        // Redirect to login page after 3 seconds
        setTimeout(() => {
          router.push('/dashboard');
        }, 3000);
      } else {
        setError(response.message || 'Failed to accept invitation.');
      }
    } catch (error) {
      logger.error('Error accepting invite:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isVerifying) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
        <div className="w-full max-w-md">
          <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
            <div className="text-center">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <Loader2 className="w-10 h-10 animate-spin text-white" />
              </div>
              <h2 className="mt-6 text-2xl font-bold text-gray-800 dark:text-white">Verifying invitation...</h2>
              <p className="mt-2 text-gray-500 dark:text-gray-400">This will only take a moment</p>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
        <div className="w-full max-w-md">
          <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
            <div className="text-center mb-8">
              <div className="bg-gradient-to-r from-red-500 to-pink-500 p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-10 h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h1 className="mt-6 text-2xl font-bold text-gray-800 dark:text-white">Invitation Error</h1>
              <p className="mt-2 text-gray-500 dark:text-gray-400">{error}</p>
            </div>
            <Button
              variant="primary"
              className="w-full py-4 rounded-xl text-base font-medium shadow-lg transform transition hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center"
              onClick={() => router.push('/auth')}
            >
              Go to Login
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  if (!inviteData) {
    return null;
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
      <div className="w-full max-w-lg">
        <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
          <div className="text-center mb-8">
            <div className="mb-6">
              <div className="p-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl inline-flex">
                <Image src="/assets/logos/purple-cloud-yellow-dots.svg" alt="Logo" width={120} height={40} />
              </div>
            </div>
            <h1 className="text-3xl font-bold tracking-tight text-gray-800 dark:text-white mb-2">
              Complete Your Profile
            </h1>
            <p className="text-gray-500 dark:text-gray-400 text-lg max-w-md mx-auto">
              You have been invited to join <span className="font-semibold text-indigo-600 dark:text-indigo-400">{inviteData.organizationName}</span>. Please complete your profile to continue.
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
            <FormField
              label="First Name"
              htmlFor="firstName"
              required
              error={errors.firstName?.message}
            >
              <Controller
                name="firstName"
                control={control}
                render={({ field }) => (
                  <Input
                    id="firstName"
                    {...field}
                    placeholder="Enter your first name"
                    error={errors.firstName?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Last Name"
              htmlFor="lastName"
              required
              error={errors.lastName?.message}
            >
              <Controller
                name="lastName"
                control={control}
                render={({ field }) => (
                  <Input
                    id="lastName"
                    {...field}
                    placeholder="Enter your last name"
                    error={errors.lastName?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Password"
              htmlFor="password"
              required
              error={errors.password?.message}
            >
              <Controller
                name="password"
                control={control}
                render={({ field }) => (
                  <Input
                    id="password"
                    type="password"
                    {...field}
                    placeholder="Create a password"
                    error={errors.password?.message}
                  />
                )}
              />
            </FormField>

            <FormField
              label="Confirm Password"
              htmlFor="confirmPassword"
              required
              error={errors.confirmPassword?.message}
            >
              <Controller
                name="confirmPassword"
                control={control}
                render={({ field }) => (
                  <Input
                    id="confirmPassword"
                    type="password"
                    {...field}
                    placeholder="Confirm your password"
                    error={errors.confirmPassword?.message}
                  />
                )}
              />
            </FormField>

            <div className="mt-8">
              <Button
                type="submit"
                isLoading={isSubmitting}
                className="w-full py-4 rounded-xl text-base font-medium bg-gradient-to-r from-[#8178E8] to-[#6964D3] shadow-lg shadow-indigo-500/30 dark:shadow-indigo-700/30 transform transition hover:scale-[1.02] active:scale-[0.98] flex items-center justify-center"
              >
                {isSubmitting ? 'Setting up your account' : 'Complete Profile & Accept Invitation'}
              </Button>
            </div>
          </form>

          <div className="mt-6 text-center text-sm text-gray-500 dark:text-gray-400">
            By accepting this invitation, you agree to our
            <a href="#" className="ml-1 text-indigo-600 hover:text-indigo-500 dark:text-indigo-400">Terms of Service</a>
          </div>
        </Card>
      </div>
    </div>
  );
}

// Main page component with Suspense
export default function InviteAcceptPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] p-4">
        <div className="w-full max-w-md">
          <Card className="backdrop-blur-md bg-white/80 dark:bg-gray-900/80 border border-gray-100 dark:border-gray-800 shadow-2xl rounded-2xl p-8 transition-all duration-300">
            <div className="text-center">
              <div className="bg-gradient-to-r from-indigo-500 to-purple-500 p-3 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
                <Loader2 className="w-10 h-10 animate-spin text-white" />
              </div>
              <h2 className="mt-6 text-2xl font-bold text-gray-800 dark:text-white">Loading...</h2>
              <p className="mt-2 text-gray-500 dark:text-gray-400">Please wait</p>
            </div>
          </Card>
        </div>
      </div>
    }>
      <InviteAcceptContent />
    </Suspense>
  );
}

import { NextRequest, NextResponse } from 'next/server';
import { verifyPayment } from '@/server/actions/transaction-actions';
import { connectToDatabase } from '@/utils/db';
import Transaction from '@/models/Transaction';
import { logger } from '@/utils/logger';

/**
 * Handle webhook callbacks from payment providers
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Verify the transaction reference
    const reference = body.reference || body.data?.reference;
    if (!reference) {
      return NextResponse.json({ success: false, message: 'Missing transaction reference' }, { status: 400 });
    }

    // Connect to database
    await connectToDatabase();

    // Find the transaction by reference
    const transaction = await Transaction.findOne({ reference });
    if (!transaction) {
      return NextResponse.json({ success: false, message: 'Transaction not found' }, { status: 404 });
    }

    // Verify the payment status
    const verificationResult = await verifyPayment(reference);

    return NextResponse.json(verificationResult);
  } catch (error: any) {
    logger.error('Error processing transaction webhook:', error);
    return NextResponse.json(
      { success: false, message: error.message || 'An error occurred processing the webhook' },
      { status: 500 }
    );
  }
}

/**
 * Handle verification requests for transactions
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const reference = searchParams.get('reference');

    if (!reference) {
      return NextResponse.json({ success: false, message: 'Missing transaction reference' }, { status: 400 });
    }

    // Verify the payment status
    const verificationResult = await verifyPayment(reference);

    return NextResponse.json(verificationResult);
  } catch (error: any) {
    logger.error('Error verifying transaction:', error);
    return NextResponse.json(
      { success: false, message: error.message || 'An error occurred verifying the transaction' },
      { status: 500 }
    );
  }
}

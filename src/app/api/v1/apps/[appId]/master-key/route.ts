import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/utils/db';
import App from '@/models/App';
import { getCache } from '@/utils/cache';
import { logAuditEvent } from '@/utils/audit';
import { AuditAction, ResourceType } from '@/models/AuditLog';
import mongoose from 'mongoose';

// Rate limit configuration
const RATE_LIMIT = 50; // 50 requests per hour for master key retrieval
const RATE_LIMIT_WINDOW = 60 * 60; // 1 hour in seconds
const RATE_LIMIT_PREFIX = 'rate_limit:master_key:';

/**
 * GET /api/v1/apps/[appId]/master-key
 *
 * Retrieves the encrypted Master Encryption Key for an application
 * This endpoint is used by the CLI in the Three-Key Model architecture
 *
 * Authentication:
 * - X-API-Key: The API key for authentication (must match the apiKey stored in the App model)
 *
 * Rate limiting:
 * - 50 requests per hour per API key
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ appId: string }> }
) {
  const clientIp = req.headers.get('x-forwarded-for') || 'unknown';
  let apiKeyValue: string | null = null;
  const { appId } = await context.params;

  try {
    // Get API key from header
    apiKeyValue = req.headers.get('X-API-Key');

    if (!apiKeyValue) {
      return createErrorResponse(401, 'Missing API key', clientIp);
    }

    if (!appId) {
      return createErrorResponse(400, 'Missing appId parameter', clientIp, apiKeyValue);
    }

    // Connect to database
    await connectToDatabase();

    // Check rate limit first to prevent brute force attempts
    const rateLimitResult = await checkRateLimit(apiKeyValue);
    if (!rateLimitResult.allowed) {
      return createErrorResponse(
        429,
        'Rate limit exceeded',
        clientIp,
        apiKeyValue,
        {
          'Retry-After': rateLimitResult.retryAfter.toString(),
          'X-RateLimit-Limit': RATE_LIMIT.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      );
    }

    // Check if app exists and validate API key
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(appId),
      active: true
    });

    if (!app) {
      return createErrorResponse(404, 'App not found', clientIp, apiKeyValue);
    }

    // Validate that the provided API key matches the one stored in the App record
    if (app.apiKey !== apiKeyValue) {
      return createErrorResponse(401, 'Invalid API key for this app', clientIp, apiKeyValue);
    }

    // Check if the app has an encrypted master key
    if (!app.encryptedMasterKey || !app.masterKeyIV || !app.masterKeyTag) {
      return createErrorResponse(404, 'Master key not found for this app', clientIp, apiKeyValue);
    }

    // Log successful access
    await logAuditEvent({
      userId: null, // No user ID since we're using the app's API key directly
      userName: `App API: ${app.name}`,
      action: AuditAction.READ,
      resourceType: ResourceType.APP,
      resourceId: appId,
      description: `API access to master key for app ${app.name}`,
      details: {
        appId,
        appName: app.name,
        clientIp
      },
      organizationId: app.organizationId
    });

    // Return the encrypted master key data
    return NextResponse.json(
      {
        success: true,
        data: {
          appId: app._id.toString(),
          appName: app.name,
          encryptedMasterKey: app.encryptedMasterKey,
          masterKeyIV: app.masterKeyIV,
          masterKeyTag: app.masterKeyTag
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': RATE_LIMIT.toString(),
          'X-RateLimit-Remaining': (rateLimitResult.remaining - 1).toString(),
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      }
    );
  } catch (error: any) {
    console.error('Error retrieving master key:', error);

    // Log error
    await logAuditEvent({
      userId: null,
      userName: 'System',
      action: AuditAction.ERROR,
      resourceType: ResourceType.APP,
      resourceId: appId,
      description: `Error accessing master key API: ${error.message}`,
      details: {
        appId,
        apiKey: apiKeyValue as string,
        clientIp,
        error: error.message,
        stack: error.stack
      }
    }).catch(err => console.error('Error logging audit event:', err));

    return createErrorResponse(500, 'Internal server error', clientIp, apiKeyValue);
  }
}

/**
 * Helper function to create error responses
 */
async function createErrorResponse(
  status: number,
  message: string,
  clientIp: string,
  apiKey?: string | null,
  headers: Record<string, string> = {}
) {
  // Log error access attempt
  if (apiKey) {
    await logAuditEvent({
      userId: null,
      userName: 'System',
      action: AuditAction.ERROR,
      resourceType: ResourceType.APP,
      resourceId: 'unknown',
      description: `Failed master key API access: ${message}`,
      details: {
        apiKey,
        clientIp,
        statusCode: status,
        errorMessage: message
      }
    }).catch(err => console.error('Error logging audit event:', err));
  }

  return NextResponse.json(
    { success: false, error: message },
    { status, headers }
  );
}

/**
 * Check rate limit for an API key
 */
async function checkRateLimit(apiKey: string) {
  const cache = getCache();
  const now = Math.floor(Date.now() / 1000);
  const key = `${RATE_LIMIT_PREFIX}${apiKey}`;

  // Get current count
  const currentValue = await cache.get(key);
  let currentCount = 0;
  let windowStart = now;

  if (currentValue) {
    const [storedCount, storedWindowStart] = currentValue.split(':').map(Number);
    currentCount = storedCount;
    windowStart = storedWindowStart;
  }

  // Calculate time elapsed in current window
  const timeElapsed = now - windowStart;

  // If window has expired, reset
  if (timeElapsed >= RATE_LIMIT_WINDOW) {
    windowStart = now;
    currentCount = 0;
  }

  // Calculate remaining requests
  const remaining = Math.max(0, RATE_LIMIT - currentCount);

  // Calculate when the rate limit will reset
  const resetTime = windowStart + RATE_LIMIT_WINDOW;
  const retryAfter = Math.max(0, resetTime - now);

  // Check if rate limit is exceeded
  const allowed = currentCount < RATE_LIMIT;

  // Update rate limit counter if allowed
  if (allowed) {
    await cache.set(key, `${currentCount + 1}:${windowStart}`, RATE_LIMIT_WINDOW);
  }

  return {
    allowed,
    remaining,
    resetTime,
    retryAfter
  };
}

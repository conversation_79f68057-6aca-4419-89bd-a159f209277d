import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/utils/db';
import App from '@/models/App';
import EnvironmentVariable from '@/models/EnvironmentVariable';
import InheritanceLink from '@/models/InheritanceLink';
import InheritanceLinkKey from '@/models/InheritanceLinkKeys';
import { getCache } from '@/utils/cache';
import { logAuditEvent } from '@/utils/audit';
import { AuditAction, ResourceType } from '@/models/AuditLog';
import mongoose, {Types} from 'mongoose';
import { EnvVarEnvironment } from '@/types/environment-variables';

// Rate limit configuration
const RATE_LIMIT = 100; // 100 requests per hour
const RATE_LIMIT_WINDOW = 60 * 60; // 1 hour in seconds
const RATE_LIMIT_PREFIX = 'rate_limit:env_vars:';

/**
 * GET /api/v1/environment-variables
 *
 * Retrieves environment variables for an application
 *
 * Authentication:
 * - X-API-Key: The API key for authentication (must match the apiKey stored in the App model)
 * - The API key is validated directly against the App model, not through the ApiKey collection
 *
 * Required query parameters:
 * - appId: The application ID
 *
 * Optional query parameters:
 * - environment: The environment to filter by (development, staging, production)
 *
 * Rate limiting:
 * - 100 requests per hour per API key
 */
export async function GET(req: NextRequest) {
  // Track request start time for potential performance logging
  const clientIp = req.headers.get('x-forwarded-for') || 'unknown';
  let apiKeyValue: string | null = null;
  let appId: string | null = null;

  try {
    // Get API key from header
    apiKeyValue = req.headers.get('X-API-Key');

    if (!apiKeyValue) {
      return createErrorResponse(401, 'Missing API key', clientIp);
    }

    // Get appId from query parameters
    const { searchParams } = new URL(req.url);
    appId = searchParams.get('appId');
    if (!appId) {
      return createErrorResponse(400, 'Missing appId parameter', clientIp, apiKeyValue);
    }

    // Get optional environment parameter
    const environment = searchParams.get('environment') as EnvVarEnvironment | null;

    // Connect to database
    await connectToDatabase();

    // Check rate limit first to prevent brute force attempts
    const rateLimitResult = await checkRateLimit(apiKeyValue);
    if (!rateLimitResult.allowed) {
      return createErrorResponse(
        429,
        'Rate limit exceeded',
        clientIp,
        apiKeyValue,
        {
          'Retry-After': rateLimitResult.retryAfter.toString(),
          'X-RateLimit-Limit': RATE_LIMIT.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      );
    }

    // Check if app exists and validate API key directly against the App model
    const app = await App.findOne({
      _id: new mongoose.Types.ObjectId(appId),
      active: true
    });

    if (!app) {
      return createErrorResponse(401, 'App not found', clientIp, apiKeyValue);
    }

    // Validate that the provided API key matches the one stored in the App record
    if (app.apiKey !== apiKeyValue) {
      return createErrorResponse(401, 'Invalid API key for this app', clientIp, apiKeyValue);
    }

    // Build query for environment variables
    const query: {
      appId: Types.ObjectId;
      organizationId: Types.ObjectId;
      environment?: string
    } = {
      appId: new mongoose.Types.ObjectId(appId),
      organizationId: app.organizationId
    };

    if (environment) {
      query.environment = environment;
    }

    // Get environment variables
    const envVars = await EnvironmentVariable.aggregate([
      {
        $match: query
      },
      {
        $sort: { key: 1 }
      },
      {
        $project: {
          _id: 1,
          key: 1,
          type: 1,
          description: 1,
          environment: 1,
          isInherited: 1,
          value: 1,
          sourceAppId: 1,
          version: 1,
          createdAt: 1,
          updatedAt: 1,
          // Include MEK-based encryption fields
          iv: 1,
          tag: 1
        }
      }
    ]);

    // Get inherited environment variables using MEK-based inheritance
    const inheritanceLinks = await InheritanceLink.find({
      targetAppId: new mongoose.Types.ObjectId(appId),
      ...(environment ? { environment } : {})
    });

    const inheritedVarsPromises = inheritanceLinks.map(async (link) => {
      const sourceApp = await App.findById(link.sourceAppId);

      if (!sourceApp) {
        return null;
      }

      // Get the InheritanceLinkKey for this source-target-environment combination
      const inheritanceLinkKeyQuery: any = {
        targetAppId: new mongoose.Types.ObjectId(appId!),
        sourceAppId: link.sourceAppId
      };
      if (environment) {
        inheritanceLinkKeyQuery.environment = environment;
      }
      const inheritanceLinkKey = await InheritanceLinkKey.findOne(inheritanceLinkKeyQuery);

      const sourceVars = await EnvironmentVariable.aggregate([
        {
          $match: {
            appId: link.sourceAppId,
            key: link.emvKey,
            ...(environment ? { environment } : {})
          }
        },
        {
          $project: {
            _id: 1,
            key: 1,
            type: 1,
            environment: 1,
            value: 1, // Include encrypted value
            updatedAt: 1,
            // Include MEK-based encryption fields
            iv: 1,
            tag: 1
          }
        }
      ]);

      return {
        sourceApp: {
          id: sourceApp._id.toString(),
          name: sourceApp.name
        },
        // Include InheritanceLinkKey data for MEK-based decryption
        inheritanceLinkKey: inheritanceLinkKey ? {
          encryptedMasterKey: inheritanceLinkKey.encryptedMasterKey,
          masterKeyIV: inheritanceLinkKey.masterKeyIV,
          masterKeyTag: inheritanceLinkKey.masterKeyTag
        } : null,
        envVars: sourceVars.map((v: any) => ({
          id: v._id.toString(),
          key: v.key,
          type: v.type,
          environment: v.environment,
          value: v.value,
          updatedAt: v.updatedAt,
          // Include MEK-based encryption fields
          iv: v.iv,
          tag: v.tag
        }))
      };
    });

    const inheritedVars = (await Promise.all(inheritedVarsPromises)).filter(Boolean);
    // Log successful access
    await logAuditEvent({
      userId: null, // No user ID since we're using the app's API key directly
      userName: `App API: ${app.name}`,
      action: AuditAction.READ,
      resourceType: ResourceType.ENV_VAR,
      resourceId: appId,
      description: `API access to environment variables for app ${app.name}`,
      details: {
        appId,
        environment: environment || 'all',
        appName: app.name,
        clientIp
      },
      organizationId: app.organizationId
    });

    // Return response with rate limit headers
    return NextResponse.json(
      {
        success: true,
        data: {
          app: {
            id: app._id.toString(),
            name: app.name
          },
          ownVars: envVars.map((v: any) => ({
            id: v._id.toString(),
            key: v.key,
            type: v.type,
            value: v.value, // Encrypted value
            description: v.description,
            environment: v.environment,
            version: v.version,
            createdAt: v.createdAt,
            updatedAt: v.updatedAt,
            // Include MEK-based encryption fields
            iv: v.iv,
            tag: v.tag
          })),
          inheritedVars
        }
      },
      {
        status: 200,
        headers: {
          'X-RateLimit-Limit': RATE_LIMIT.toString(),
          'X-RateLimit-Remaining': (rateLimitResult.remaining - 1).toString(),
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString()
        }
      }
    );
  } catch (error: any) {
    console.error('Error retrieving environment variables:', error);

    // Log error
    await logAuditEvent({
      userId: null, // Use null instead of 'system' string
      userName: 'System',
      action: AuditAction.ERROR,
      resourceType: ResourceType.ENV_VAR,
      resourceId: appId as string,
      description: `Error accessing environment variables API: ${error.message}`,
      details: {
        appId: appId as string,
        apiKey: apiKeyValue as string,
        clientIp,
        error: error.message,
        stack: error.stack
      }
    }).catch(err => console.error('Error logging audit event:', err));

    return createErrorResponse(500, 'Internal server error', clientIp, apiKeyValue);
  }
}

/**
 * Helper function to create error responses
 */
async function createErrorResponse(
  status: number,
  message: string,
  clientIp: string,
  apiKey?: string | null,
  headers: Record<string, string> = {}
) {
  // Log error access attempt
  if (apiKey) {
    await logAuditEvent({
      userId: null, // Use null instead of 'system' string
      userName: 'System',
      action: AuditAction.ERROR,
      resourceType: ResourceType.ENV_VAR,
      resourceId: 'unknown',
      description: `Failed API access: ${message}`,
      details: {
        apiKey,
        clientIp,
        statusCode: status,
        errorMessage: message
      }
    }).catch(err => console.error('Error logging audit event:', err));
  }

  return NextResponse.json(
    { success: false, error: message },
    { status, headers }
  );
}

/**
 * Check rate limit for an API key
 */
async function checkRateLimit(apiKey: string) {
  const cache = getCache();
  const now = Math.floor(Date.now() / 1000);
  const key = `${RATE_LIMIT_PREFIX}${apiKey}`;

  // Get current count
  const currentValue = await cache.get(key);
  let currentCount = 0;
  let windowStart = now;

  if (currentValue) {
    const [storedCount, storedWindowStart] = currentValue.split(':').map(Number);
    currentCount = storedCount;
    windowStart = storedWindowStart;
  }

  // Calculate time elapsed in current window
  const timeElapsed = now - windowStart;

  // If window has expired, reset
  if (timeElapsed >= RATE_LIMIT_WINDOW) {
    windowStart = now;
    currentCount = 0;
  }

  // Calculate remaining requests
  const remaining = Math.max(0, RATE_LIMIT - currentCount);

  // Calculate when the rate limit will reset
  const resetTime = windowStart + RATE_LIMIT_WINDOW;
  const retryAfter = Math.max(0, resetTime - now);

  // Check if rate limit is exceeded
  const allowed = currentCount < RATE_LIMIT;

  // Update rate limit counter if allowed
  if (allowed) {
    await cache.set(key, `${currentCount + 1}:${windowStart}`, RATE_LIMIT_WINDOW);
  }

  return {
    allowed,
    remaining,
    resetTime,
    retryAfter
  };
}

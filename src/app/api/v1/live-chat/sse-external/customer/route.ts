/**
 * Customer SSE endpoint for real-time live chat events
 *
 * Dual-access SSE endpoint supporting both session-based authentication (for dashboard)
 * and API key authentication (for external widgets).
 * Uses the same core business logic for both access methods.
 *
 * Endpoint: GET /api/v1/live-chat/sse-external/customer
 * Authentication:
 *   - Session-based: Authenticated dashboard users (cookies)
 *   - API Key: External widgets (x-app-id + x-api-key headers)
 *
 * Query Parameters:
 * - conversationId: string (required) - The live chat conversation ID
 *
 * Headers (for API key auth):
 * - x-app-id: string (optional) - Application ID
 * - x-api-key: string (optional) - API Key
 */

import { NextRequest } from 'next/server';
import { logger } from '@/utils/logger';
import { validateApiKey, getAuthUserWithDetails } from '@/utils/auth';
import {
  _coreValidateCustomerSSEConnection,
  _coreValidateAssignmentSSEConnection,
  _coreGetCustomerSSEChannels,
  _coreGetAssignmentSSEChannels,
  _coreCreateInitialSSEData,
  _coreCreateHeartbeatData,
  _coreProcessCustomerSSEEvent,
  _coreProcessAssignmentSSEEvent,
  _coreSubscribeToSSEChannels,
  _coreUnsubscribeFromSSEChannels
} from '@/server/core/live-chat';

export async function GET(request: NextRequest) {
  try {
    // Try session-based authentication first (for dashboard users)
    let organizationId: string | null = null;
    let authMethod: 'session' | 'api-key' = 'session';

    const sessionAuth = await getAuthUserWithDetails();
    if (sessionAuth.success && sessionAuth.user?.dbUser?.organizationId) {
      organizationId = sessionAuth.user.dbUser.organizationId.toString();
      authMethod = 'session';
      logger.log('🔐 SSE endpoint: Using session authentication', {
        userId: sessionAuth.user.userId,
        organizationId
      });
    } else {
      // Fallback to API key authentication (for external widgets)
      const appId = request.headers.get('x-app-id');
      const apiKey = request.headers.get('x-api-key');

      if (!appId || !apiKey) {
        return new Response('Authentication required. Use session authentication (dashboard) or include x-app-id and x-api-key headers (external).', { status: 401 });
      }

      // Validate API key
      const apiValidation = await validateApiKey(appId, apiKey);
      if (!apiValidation.isValid) {
        return new Response(apiValidation.error || 'Invalid API credentials', { status: 401 });
      }

      organizationId = apiValidation.organizationId!;
      authMethod = 'api-key';
      logger.log('🔐 SSE endpoint: Using API key authentication', {
        appId,
        organizationId
      });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');

    if (!conversationId) {
      return new Response('conversationId query parameter is required', { status: 400 });
    }

    if (!organizationId) {
      return new Response('Authentication failed - no organization ID available', { status: 401 });
    }

    // Determine if this is a conversation ID or assignment ID
    // Try conversation first, then assignment
    let validation = await _coreValidateCustomerSSEConnection(conversationId, organizationId);
    let isAssignmentMode = false;

    if (!validation.success) {
      // Try as assignment ID
      validation = await _coreValidateAssignmentSSEConnection(conversationId, organizationId);
      isAssignmentMode = true;

      if (!validation.success) {
        return new Response(validation.error || 'Conversation or assignment not found', { status: 404 });
      }
    }

    const conversation = validation.conversation;

    // Create SSE response
    const encoder = new TextEncoder();
    let isConnected = true;
    let subscriptions: string[] = [];

    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection confirmation using core function
        const initialData = _coreCreateInitialSSEData(
          conversationId,
          organizationId,
          conversation.customerName
        );

        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
        );

        // Get channels using appropriate core function based on mode
        const channels = isAssignmentMode
          ? _coreGetAssignmentSSEChannels(organizationId, conversationId)
          : _coreGetCustomerSSEChannels(organizationId, conversationId);

        // Subscribe to channels using core function
        _coreSubscribeToSSEChannels(
          channels,
          conversationId,
          (message: string, channel: string) => {
            if (!isConnected) return;

            // Process event using appropriate core function based on mode
            const result = isAssignmentMode
              ? _coreProcessAssignmentSSEEvent(message, conversationId, channel)
              : _coreProcessCustomerSSEEvent(message, conversationId, channel, conversation.customerId);

            if (!result.shouldSend || !result.enrichedEvent) {
              return;
            }

            // Send event to client
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(result.enrichedEvent)}\n\n`)
            );

            const logContext = isAssignmentMode ? 'assignment' : 'conversation';
            logger.log(`📡 Sent SSE event to customer in ${logContext} ${conversationId}:`, {
              type: result.enrichedEvent.type,
              channel,
              authMethod,
              organizationId,
              mode: isAssignmentMode ? 'assignment' : 'conversation'
            });
          }
        ).then((subscriptionResult) => {
          subscriptions = subscriptionResult.subscriptions;
          if (subscriptionResult.errors.length > 0) {
            logger.warn('Some external API SSE channel subscriptions failed:', subscriptionResult.errors);
          }
        }).catch((error) => {
          logger.error('Failed to subscribe to external API SSE channels:', error);
        });

        // Send periodic heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          if (!isConnected) {
            clearInterval(heartbeatInterval);
            return;
          }

          try {
            // Use core function to create heartbeat
            const heartbeat = _coreCreateHeartbeatData();

            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(heartbeat)}\n\n`)
            );
          } catch (error) {
            logger.error('Error sending external API SSE heartbeat:', error);
            clearInterval(heartbeatInterval);
          }
        }, 30000); // Every 30 seconds

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          isConnected = false;
          clearInterval(heartbeatInterval);

          // Unsubscribe from all channels using core function
          _coreUnsubscribeFromSSEChannels(subscriptions, conversationId);

          logger.log(`🔌 Customer SSE connection closed for conversation ${conversationId}`, {
            authMethod,
            organizationId
          });
          
          try {
            controller.close();
          } catch (error) {
            // Controller might already be closed
          }
        });
      },

      cancel() {
        isConnected = false;
        logger.log(`🔌 Customer SSE stream cancelled for conversation ${conversationId}`, {
          authMethod,
          organizationId
        });
      }
    });

    // Return SSE response with proper headers
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': authMethod === 'session' ? request.headers.get('origin') || '*' : '*',
        'Access-Control-Allow-Credentials': authMethod === 'session' ? 'true' : 'false',
        'Access-Control-Allow-Headers': 'Cache-Control, x-app-id, x-api-key, Cookie',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      },
    });

  } catch (error) {
    logger.error('Error in external API customer SSE endpoint:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Cache-Control, x-app-id, x-api-key, Cookie',
    },
  });
}

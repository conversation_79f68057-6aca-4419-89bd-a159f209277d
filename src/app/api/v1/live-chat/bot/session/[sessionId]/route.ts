/**
 * API Route: Get Bot Session
 * 
 * External API endpoint for retrieving bot conversation session information.
 * Uses API key authentication and calls the same core business logic
 * as the internal server action.
 * 
 * Endpoint: GET /api/v1/live-chat/bot/session/[sessionId]
 * Authentication: API Key (appId + apiKey in headers)
 * 
 * Path Parameters:
 * - sessionId: string (required) - The bot conversation session ID
 * 
 * Headers:
 * - x-app-id: string (required) - Application ID
 * - x-api-key: string (required) - API Key
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { _coreGetBotSession } from '@/server/core/live-chat';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';

interface RouteParams {
  params: Promise<{
    sessionId: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');

    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // Extract path parameters
    const { sessionId } = await params;

    if (!sessionId) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'sessionId path parameter is required'
        }),
        { status: 400 }
      );
    }

    // Validate sessionId format (MongoDB ObjectId)
    if (!/^[0-9a-fA-F]{24}$/.test(sessionId)) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid sessionId format'
        }),
        { status: 400 }
      );
    }

    // Call the same core function used by server actions
    const result = await _coreGetBotSession(sessionId, apiValidation.organizationId!);

    // Return the result with appropriate HTTP status
    const statusCode = result.success ? 200 : 404;
    
    logger.log('API: Get bot session result:', {
      appId,
      sessionId,
      organizationId: apiValidation.organizationId,
      success: result.success
    });

    return NextResponse.json(serializeResponse(result), { status: statusCode });

  } catch (error) {
    logger.error('API: Error getting bot session:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

/**
 * API Route: Initialize Bot Conversation
 *
 * External API endpoint for initializing bot conversations with automatic fallback to agent handoff.
 * Uses API key authentication and calls the same core business logic
 * as the internal server action.
 *
 * Endpoint: POST /api/v1/live-chat/bot/initialize
 * Authentication: API Key (appId + apiKey in headers)
 *
 * Request Body:
 * {
 *   "customerInfo": {
 *     "name": string (required),
 *     "email": string (optional)
 *   },
 *   "initialMessage": string (optional)
 * }
 *
 * Headers:
 * - x-app-id: string (required) - Application ID
 * - x-api-key: string (required) - API Key
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { _coreInitializeBotConversation } from '@/server/core/live-chat';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';
import { z } from 'zod';

// Validation schema for request body
const InitializeBotRequestSchema = z.object({
  customerInfo: z.object({
    name: z.string().min(1, 'Customer name is required').max(100, 'Customer name too long'),
    email: z.string().email('Invalid email format').optional().or(z.literal(''))
  }),
  initialMessage: z.string().max(1000, 'Initial message too long').optional(),
  existingHandoffData: z.object({
    handoffAssignmentId: z.string().optional(),
    conversationId: z.string().optional(),
    timestamp: z.number().optional()
  }).optional()
});

export async function POST(request: NextRequest) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');
    console.log('Initialize bot conversation request received',{apiKey, appId });
    
    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid JSON in request body'
        }),
        { status: 400 }
      );
    }

    // Validate request body schema
    const validation = InitializeBotRequestSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid request body',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        }),
        { status: 400 }
      );
    }

    const { customerInfo, initialMessage, existingHandoffData } = validation.data;

    console.log('🔍 BACKEND API v2: Initialize bot with deduplication:', {
      customerName: customerInfo.name,
      hasExistingData: !!existingHandoffData?.handoffAssignmentId,
      existingAssignmentId: existingHandoffData?.handoffAssignmentId,
      organizationId: apiValidation.organizationId
    });

    // Call the same core function used by server actions
    const result = await _coreInitializeBotConversation(
      apiValidation.organizationId!,
      customerInfo,
      initialMessage,
      existingHandoffData
    );

    // Return the result with appropriate HTTP status
    const statusCode = result.success ? 200 : 400;
    
    logger.log('API: Initialize bot conversation result:', {
      appId,
      organizationId: apiValidation.organizationId,
      customerName: customerInfo.name,
      success: result.success,
      hasInitialMessage: !!initialMessage
    });

    return NextResponse.json(serializeResponse(result), { status: statusCode });

  } catch (error) {
    logger.error('API: Error initializing bot conversation:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

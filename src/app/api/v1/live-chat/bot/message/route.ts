/**
 * API Route: Process Bot Message
 * 
 * External API endpoint for processing user messages through bot conversation flow.
 * Uses API key authentication and calls the same core business logic
 * as the internal server action.
 * 
 * Endpoint: POST /api/v1/live-chat/bot/message
 * Authentication: API Key (appId + apiKey in headers)
 * 
 * Request Body:
 * {
 *   "message": string (required),
 *   "conversationState": {
 *     "_id": string (required),
 *     "currentNodeId": string (required),
 *     "variables": object (required),
 *     "messageHistory": array (required),
 *     "flowId": string (required),
 *     "organizationId": string (required),
 *     "isComplete": boolean (required),
 *     "startedAt": string (required)
 *   }
 * }
 * 
 * Headers:
 * - x-app-id: string (required) - Application ID
 * - x-api-key: string (required) - API Key
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { _coreProcessBotMessage } from '@/server/core/live-chat';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';
import { z } from 'zod';

// Validation schema for conversation state
const ConversationStateSchema = z.object({
  _id: z.string().min(1, 'Session ID is required'),
  currentNodeId: z.string().min(1, 'Current node ID is required'),
  variables: z.record(z.any()),
  messageHistory: z.array(z.object({
    id: z.string(),
    type: z.enum(['user', 'bot', 'system']),
    content: z.string(),
    timestamp: z.string().or(z.date()),
    nodeId: z.string().optional(),
    metadata: z.record(z.any()).optional()
  })),
  flowId: z.string().min(1, 'Flow ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  isComplete: z.boolean(),
  startedAt: z.string().or(z.date())
});

// Validation schema for request body
const ProcessBotMessageRequestSchema = z.object({
  message: z.string().min(1, 'Message is required').max(1000, 'Message too long'),
  conversationState: ConversationStateSchema
});

export async function POST(request: NextRequest) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');

    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid JSON in request body'
        }),
        { status: 400 }
      );
    }

    // Validate request body schema
    const validation = ProcessBotMessageRequestSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid request body',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        }),
        { status: 400 }
      );
    }

    const { message, conversationState } = validation.data;

    // Verify organization ID matches API key
    if (conversationState.organizationId !== apiValidation.organizationId) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Organization ID mismatch'
        }),
        { status: 403 }
      );
    }

    // Convert string dates back to Date objects
    const processedConversationState = {
      ...conversationState,
      startedAt: new Date(conversationState.startedAt),
      messageHistory: conversationState.messageHistory.map(msg => ({
        ...msg,
        timestamp: new Date(msg.timestamp)
      }))
    };

    // Call the same core function used by server actions
    const result = await _coreProcessBotMessage(
      message,
      processedConversationState
    );

    // Return the result with appropriate HTTP status
    const statusCode = result.success ? 200 : 400;
    
    logger.log('API: Process bot message result:', {
      appId,
      organizationId: apiValidation.organizationId,
      sessionId: conversationState._id,
      messageLength: message.length,
      success: result.success
    });

    return NextResponse.json(serializeResponse(result), { status: statusCode });

  } catch (error) {
    logger.error('API: Error processing bot message:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

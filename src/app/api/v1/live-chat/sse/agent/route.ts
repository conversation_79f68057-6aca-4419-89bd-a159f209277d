/**
 * Agent SSE endpoint for real-time live chat events
 *
 * Dashboard SSE endpoint for authenticated agent connections using session-based authentication.
 * Provides organization-scoped event streaming for agent interfaces within the dashboard.
 *
 * Endpoint: GET /api/v1/live-chat/sse/agent
 * Authentication: Session-based (authenticated dashboard agents)
 *
 * Query Parameters:
 * - agentId: string (required) - The agent ID for filtering events
 * - conversationId: string (optional) - Specific conversation to monitor
 */

import { NextRequest } from 'next/server';
import { getAuthUserWithDetails } from '@/utils/auth';
import { redisPubSub } from '@/utils/RedisPubSub';
import { logger } from '@/utils/logger';
import { connectToDatabase } from '@/utils/db';
import LiveChatConversation from '@/models/LiveChatConversation';
import { Types } from 'mongoose';
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    // Authenticate using session (dashboard agents only)
    const sessionAuth = await getAuthUserWithDetails();
    console.log({sessionAuth});
    
    if (!sessionAuth.success) {
      return new Response('Authentication required. Please log in to access this endpoint.', { status: 401 });
    }

    const userId = sessionAuth.user?.userId;

 

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const agentId = sessionAuth.user?.userId;
    const organizationId = searchParams.get('organizationId') as string;
    const conversationId = searchParams.get('conversationId');
   logger.log('🔐 Agent SSE: Using session authentication', {
      userId,
      organizationId
    });
    if (!agentId) {
      return new Response('Agent ID is required', { status: 400 });
    }

    const agentName = `Agent-${agentId}`;

    // Verify agent has access to the organization
    await connectToDatabase();

    // If conversationId is provided, verify agent has access to that conversation
    if (conversationId) {
      const conversation = await LiveChatConversation.findOne({
        _id: new Types.ObjectId(conversationId),
        organizationId: new Types.ObjectId(organizationId),
        assignedAgentId: new Types.ObjectId(agentId)
      });

      if (!conversation) {
        return new Response('Conversation not found or not assigned to current agent', { status: 403 });
      }
    }

    // Create SSE response
    const encoder = new TextEncoder();
    let isConnected = true;
    const subscriptions: string[] = [];

    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection confirmation
        const initialData = {
          type: 'connection-established',
          agentId,
          agentName,
          organizationId,
          timestamp: new Date().toISOString()
        };

        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
        );

        // Define channels to subscribe to
        const channels = [
          `chat-assignments:${organizationId}`, // Pending chat requests
          `agent-conversations:${organizationId}:${agentId}`, // Agent-specific updates
        ];

        // Add conversation-specific channel if provided
        if (conversationId) {
          channels.push(`conversation:${organizationId}:${conversationId}`);
          channels.push(`typing:${organizationId}:${conversationId}`);
        }

        // Subscribe to Redis channels
        channels.forEach(async (channel) => {
          try {
            const success = await redisPubSub.subscribe(channel, (message) => {
              if (!isConnected) return;

              try {
                // Parse and validate the message
                const eventData = JSON.parse(message);

                // Filter out events that this agent originated
                if (shouldFilterEventForAgent(eventData, agentId)) {
                  return;
                }

                // Add metadata for client processing
                const enrichedEvent = {
                  ...eventData,
                  receivedAt: new Date().toISOString(),
                  channel
                };

                // Send event to client
                controller.enqueue(
                  encoder.encode(`data: ${JSON.stringify(enrichedEvent)}\n\n`)
                );

              } catch (error) {
                logger.error('Error processing SSE message for agent:', error);
              }
            });

            if (success) {
              subscriptions.push(channel);
            } else {
              logger.warn(`❌ Failed to subscribe agent ${agentId} to channel: ${channel}`);
            }
          } catch (error) {
            logger.error(`Error subscribing to channel ${channel}:`, error);
          }
        });

        // Send periodic heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          if (!isConnected) {
            clearInterval(heartbeatInterval);
            return;
          }

          try {
            const heartbeat = {
              type: 'heartbeat',
              timestamp: new Date().toISOString()
            };

            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(heartbeat)}\n\n`)
            );
          } catch (error) {
            logger.error('Error sending heartbeat:', error);
            clearInterval(heartbeatInterval);
          }
        }, 30000); // Every 30 seconds

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          isConnected = false;
          clearInterval(heartbeatInterval);

          // Unsubscribe from all channels
          subscriptions.forEach(async (channel) => {
            try {
              await redisPubSub.unsubscribe(channel);
            } catch (error) {
              logger.error(`Error unsubscribing from channel ${channel}:`, error);
            }
          });
          
          try {
            controller.close();
          } catch (error) {
            // Controller might already be closed
          }
        });
      },

      cancel() {
        isConnected = false;
      }
    });

    // Return SSE response with proper headers
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Headers': 'Cache-Control, Cookie',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      },
    });

  } catch (error) {
    logger.error('Error in agent SSE endpoint:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

/**
 * Filter events that agents should not receive (events they originated)
 */
function shouldFilterEventForAgent(eventData: any, agentId: string): boolean {
  // Check if the event has originator information
  if (eventData.originator) {
    // Filter out events where the agent is the originator
    if (eventData.originator.agentId === agentId) {
      return true;
    }

    // For typing events, also check userId
    if (eventData.type === 'typing-start' || eventData.type === 'typing-stop') {
      if (eventData.userId === agentId || eventData.originator.userId === agentId) {
        return true;
      }
    }
  }

  // For message events, check if the sender is this agent
  if (eventData.type === 'new-message' && eventData.message) {
    if (eventData.message.senderId === agentId) {
      return true;
    }
  }

  // For chat-accepted events, check if this agent accepted it
  if (eventData.type === 'chat-accepted' && eventData.agentInfo) {
    if (eventData.agentInfo.id === agentId) {
      return true;
    }
  }

  return false;
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Cache-Control, Cookie',
    },
  });
}

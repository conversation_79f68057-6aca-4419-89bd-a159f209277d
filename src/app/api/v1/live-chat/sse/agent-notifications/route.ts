/**
 * Agent Notifications SSE endpoint for cross-conversation real-time notifications
 *
 * Dashboard SSE endpoint for authenticated agent notifications using session-based authentication.
 * This endpoint handles dynamic conversation assignments and provides notifications
 * for ALL agent conversations without requiring SSE reconnection for new assignments.
 *
 * Endpoint: GET /api/v1/live-chat/sse/agent-notifications
 * Authentication: Session-based (authenticated dashboard agents)
 *
 * Query Parameters:
 * - agentId: string (required) - The agent ID for filtering notifications
 * - organizationId: string (required) - The organization ID for scoping
 */

import { NextRequest } from 'next/server';
import { getAuthUserWithDetails } from '@/utils/auth';
import { redisPubSub } from '@/utils/RedisPubSub';
import { logger } from '@/utils/logger';
import { connectToDatabase } from '@/utils/db';
import LiveChatConversation from '@/models/LiveChatConversation';
import { Types } from 'mongoose';
export async function GET(request: NextRequest) {
  try {
    await connectToDatabase();

    // Authenticate using session (dashboard agents only)
    const sessionAuth = await getAuthUserWithDetails();
    if (!sessionAuth.success) {
      return new Response('Authentication required. Please log in to access this endpoint.', { status: 401 });
    }

    const userId = sessionAuth.user?.userId;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const agentId = userId;
    const organizationId = searchParams.get('organizationId') as string;

    if (!agentId) {
      return new Response('Agent ID is required', { status: 400 });
    }

    if (!organizationId) {
      return new Response('Organization ID is required', { status: 400 });
    }

    logger.log('🔐 Agent notifications SSE: Using session authentication', {
      userId,
      agentId,
      organizationId
    });

    const agentName = `Agent-${agentId}`;
    
    const encoder = new TextEncoder();
    let isConnected = true;
    const subscriptions: string[] = [];

    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection confirmation
        const initialData = {
          type: 'notifications-connection-established',
          agentId,
          agentName,
          organizationId,
          connectionType: 'agent-notifications',
          timestamp: new Date().toISOString()
        };

        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
        );

        // Subscribe to organization-wide agent notifications channel
        // This channel receives notifications for all conversations in the organization
        const agentNotificationsChannel = `agent-notifications:${organizationId}`;

        // Subscribe to the agent notifications channel
        subscriptions.push(agentNotificationsChannel);
        try {
          const subscriptionResult = redisPubSub.subscribe(agentNotificationsChannel, async (message) => {
          if (!isConnected) return;

          try {
            const eventData = JSON.parse(message);

            // Debug logging for event reception
            logger.log(`🔔 Agent notifications received event:`, {
              type: eventData.type,
              agentId,
              organizationId,
              assignmentId: eventData.assignmentId,
              conversationId: eventData.conversationId
            });

            // Process different event types for notifications
            if (eventData.type === 'new-message') {
              // For new-message events, check agent assignment
              const isAssigned = await checkAgentAssignment(
                agentId,
                organizationId,
                eventData.conversationId
              );

              if (!isAssigned) {
                return;
              }
            } else if (eventData.type === 'chat-accepted') {
              // For chat-accepted events, send to all agents in organization
              // No assignment check needed since this is about removing pending chats
            } else {
              // For other event types, skip for now
              return;
            }

            // Filter out events that this agent originated
            if (shouldFilterEventForAgent(eventData, agentId)) {
              return;
            }

            // Add metadata for client processing
            const enrichedEvent = {
              ...eventData,
              receivedAt: new Date().toISOString(),
              channel: agentNotificationsChannel,
              notificationType: 'cross-conversation'
            };

            // Send event to client
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(enrichedEvent)}\n\n`)
            );

            // Debug logging for event sending
            logger.log(`🔔 Sent notification to agent ${agentId}:`, {
              type: eventData.type,
              assignmentId: eventData.assignmentId,
              conversationId: eventData.conversationId,
              organizationId
            });

          } catch (error) {
            logger.error('Error processing notification event:', error);
          }
        });

        // Set up heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          if (!isConnected) {
            clearInterval(heartbeatInterval);
            return;
          }

          try {
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({ type: 'heartbeat', timestamp: new Date().toISOString() })}\n\n`)
            );
          } catch (error) {
            logger.error('Error sending heartbeat:', error);
            isConnected = false;
            clearInterval(heartbeatInterval);
          }
        }, 30000); // 30 seconds

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          isConnected = false;
          clearInterval(heartbeatInterval);

          // Unsubscribe from all channels
          subscriptions.forEach(async (channel) => {
            try {
              await redisPubSub.unsubscribe(channel);
            } catch (error) {
              logger.error(`Error unsubscribing from notification channel ${channel}:`, error);
            }
          });
          
          try {
            controller.close();
          } catch (error) {
            // Controller might already be closed
          }
        });
        } catch (error) {
        }

      },

      cancel() {
        isConnected = false;
      }
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Cache-Control, Cookie',
      },
    });

  } catch (error) {
    logger.error('Error in agent notifications SSE endpoint:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}

// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Cache-Control, Cookie',
    },
  });
}

/**
 * Check if an agent is currently assigned to a conversation
 * This is called for each event to ensure real-time assignment validation
 */
async function checkAgentAssignment(
  agentId: string,
  organizationId: string,
  conversationId: string
): Promise<boolean> {
  try {
    const conversation = await LiveChatConversation.findOne({
      _id: new Types.ObjectId(conversationId),
      organizationId: new Types.ObjectId(organizationId),
      assignedAgentId: new Types.ObjectId(agentId),
      status: { $in: ['active', 'waiting'] } // Only active conversations
    }).select('_id').lean();

    return !!conversation;
  } catch (error) {
    logger.error('Error checking agent assignment:', error);
    return false;
  }
}

/**
 * Filter events that should not be sent to the agent (e.g., self-originated events)
 */
function shouldFilterEventForAgent(eventData: any, agentId: string): boolean {
  // For chat-accepted events, don't filter out - all agents need to know when chats are accepted
  if (eventData.type === 'chat-accepted') {
    return false;
  }

  // Filter out events originated by this agent
  if (eventData.originator?.agentId === agentId) {
    return true;
  }

  // Filter out agent messages (agents don't need notifications for their own messages)
  if (eventData.message?.type === 'agent' && eventData.originator?.agentId === agentId) {
    return true;
  }

  return false;
}

/**
 * Customer SSE endpoint for real-time live chat events
 *
 * Dashboard SSE endpoint for authenticated customer connections using session-based authentication.
 * Uses the same core business logic for customer SSE connections within the dashboard interface.
 *
 * Endpoint: GET /api/v1/live-chat/sse/customer
 * Authentication: Session-based (authenticated dashboard users)
 *
 * Query Parameters:
 * - conversationId: string (required) - The live chat conversation ID
 * - organizationId: string (required) - The organization ID for scoping
 */

import { NextRequest } from 'next/server';
import { logger } from '@/utils/logger';
import { getAuthUserWithDetails } from '@/utils/auth';
import {
  _coreValidateCustomerSSEConnection,
  _coreValidateAssignmentSSEConnection,
  _coreGetCustomerSSEChannels,
  _coreGetAssignmentSSEChannels,
  _coreCreateInitialSSEData,
  _coreCreateHeartbeatData,
  _coreProcessCustomerSSEEvent,
  _coreProcessAssignmentSSEEvent,
  _coreSubscribeToSSEChannels,
  _coreUnsubscribeFromSSEChannels
} from '@/server/core/live-chat';
export async function GET(request: NextRequest) {
  try {
    // Authenticate using session (dashboard users only)
    const sessionAuth = await getAuthUserWithDetails();
    if (!sessionAuth.success) {
      return new Response('Authentication required. Please log in to access this endpoint.', { status: 401 });
    }

    const userId = sessionAuth.user?.userId;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');

    if (!conversationId) {
      return new Response('Conversation ID is required', { status: 400 });
    }

    // Get organizationId from authenticated user (server-side)
    const organizationId = sessionAuth.user?.dbUser?.organizationId?.toString();
    if (!organizationId) {
      return new Response('Organization ID not available from authentication', { status: 401 });
    }

    if (!organizationId) {
      return new Response('Organization ID is required', { status: 400 });
    }

    logger.log('🔐 Customer SSE: Using session authentication', {
      userId,
      organizationId
    });

    // Determine if this is a conversation ID or assignment ID
    // Try conversation first, then assignment
    logger.log(`🔍 SSE endpoint: Attempting to validate ID as conversation: ${conversationId}`);
    let validation = await _coreValidateCustomerSSEConnection(conversationId, organizationId);
    let isAssignmentMode = false;

    if (!validation.success) {
      // Try as assignment ID
      logger.log(`🔍 SSE endpoint: Conversation validation failed, trying as assignment: ${conversationId}`);
      validation = await _coreValidateAssignmentSSEConnection(conversationId, organizationId);
      isAssignmentMode = true;

      if (!validation.success) {
        logger.error(`❌ SSE endpoint: Both conversation and assignment validation failed for ID: ${conversationId}`);
        return new Response(validation.error || 'Conversation or assignment not found', { status: 404 });
      }

      logger.log(`✅ SSE endpoint: Assignment validation successful for ID: ${conversationId}`);
    } else {
      logger.log(`✅ SSE endpoint: Conversation validation successful for ID: ${conversationId}`);
    }

    logger.log(`🔧 SSE endpoint: Using ${isAssignmentMode ? 'assignment' : 'conversation'} mode for ID: ${conversationId}`);
    const conversation = validation.conversation;

    // Create SSE response
    const encoder = new TextEncoder();
    let isConnected = true;
    let subscriptions: string[] = [];

    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection confirmation using core function
        const initialData = _coreCreateInitialSSEData(
          conversationId,
          organizationId,
          conversation.customerName
        );

        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`)
        );

        // Get channels using appropriate core function based on mode
        const channels = isAssignmentMode
          ? _coreGetAssignmentSSEChannels(organizationId, conversationId)
          : _coreGetCustomerSSEChannels(organizationId, conversationId);

        logger.log(`📡 SSE endpoint: Subscribing to channels in ${isAssignmentMode ? 'assignment' : 'conversation'} mode:`, {
          mode: isAssignmentMode ? 'assignment' : 'conversation',
          id: conversationId,
          organizationId,
          channels
        });

        // Subscribe to channels using core function
        _coreSubscribeToSSEChannels(
          channels,
          conversationId,
          (message: string, channel: string) => {
            if (!isConnected) return;

            // Process event using appropriate core function based on mode
            const result = isAssignmentMode
              ? _coreProcessAssignmentSSEEvent(message, conversationId, channel)
              : _coreProcessCustomerSSEEvent(message, conversationId, channel, conversation.customerId);

            logger.log(`📨 SSE endpoint: Processed event in ${isAssignmentMode ? 'assignment' : 'conversation'} mode:`, {
              mode: isAssignmentMode ? 'assignment' : 'conversation',
              channel,
              shouldSend: result.shouldSend,
              eventType: result.enrichedEvent?.type,
              id: conversationId
            });

            if (!result.shouldSend || !result.enrichedEvent) {
              return;
            }

            // Send event to client
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(result.enrichedEvent)}\n\n`)
            );

            const logContext = isAssignmentMode ? 'assignment' : 'conversation';
            logger.log(`📡 Sent SSE event to customer in ${logContext} ${conversationId}:`, {
              type: result.enrichedEvent.type,
              channel,
              mode: isAssignmentMode ? 'assignment' : 'conversation'
            });
          }
        ).then((subscriptionResult) => {
          subscriptions = subscriptionResult.subscriptions;
          if (subscriptionResult.errors.length > 0) {
            logger.warn('Some SSE channel subscriptions failed:', subscriptionResult.errors);
          }
        }).catch((error) => {
          logger.error('Failed to subscribe to SSE channels:', error);
        });

        // Send periodic heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          if (!isConnected) {
            clearInterval(heartbeatInterval);
            return;
          }

          try {
            // Use core function to create heartbeat
            const heartbeat = _coreCreateHeartbeatData();

            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(heartbeat)}\n\n`)
            );
          } catch (error) {
            logger.error('Error sending heartbeat:', error);
            clearInterval(heartbeatInterval);
          }
        }, 30000); // Every 30 seconds

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          isConnected = false;
          clearInterval(heartbeatInterval);

          // Unsubscribe from all channels using core function
          _coreUnsubscribeFromSSEChannels(subscriptions, conversationId);

          logger.log(`🔌 Customer SSE connection closed for conversation ${conversationId}`);

          try {
            controller.close();
          } catch (error) {
            // Controller might already be closed
          }
        });
      },

      cancel() {
        isConnected = false;
        logger.log(`🔌 Customer SSE stream cancelled for conversation ${conversationId}`);
      }
    });

    // Return SSE response with proper headers
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Allow-Headers': 'Cache-Control, Cookie',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      },
    });

  } catch (error) {
    logger.error('Error in customer SSE endpoint:', error);
    return new Response('Internal Server Error', { status: 500 });
  }
}



// Handle preflight requests
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': request.headers.get('origin') || '*',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Cache-Control, Cookie',
    },
  });
}

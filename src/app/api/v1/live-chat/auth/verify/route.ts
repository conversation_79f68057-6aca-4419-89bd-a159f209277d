/**
 * API Route: Authentication Verification
 *
 * External API endpoint for verifying API credentials for chat widget access.
 * Uses API key authentication exclusively for external integrations.
 *
 * This endpoint is specifically designed for the chat widget project to validate
 * API credentials before processing chat requests. It supports both header-based
 * and body-based authentication for maximum flexibility.
 *
 * Supported Methods: GET, POST, OPTIONS
 *
 * GET /api/v1/live-chat/auth/verify
 * - Simple credential validation using headers only
 * - Uses validateApiKey() utility for basic validation
 *
 * POST /api/v1/live-chat/auth/verify
 * - Comprehensive authentication verification
 * - Uses _coreVerifyAuthentication() for enhanced validation
 * - Supports credentials in headers or request body
 *
 * OPTIONS /api/v1/live-chat/auth/verify
 * - CORS preflight support for browser requests
 *
 * Authentication Headers:
 * - x-app-id: string (required) - Application ID (25-char hex)
 * - x-api-key: string (required) - API Key (lc_ prefix + 64-char hex)
 *
 * POST Body (optional):
 * - appId: string (optional) - Application ID (overrides header)
 * - apiKey: string (optional) - API Key (overrides header)
 *
 * Success Response (200):
 * {
 *   "success": true,
 *   "data": {
 *     "authMethod": "external",
 *     "appId": "c9a8a6295f376db6497d12314",
 *     "organizationId": "6818ca2ead616f307c84508f",
 *     "permissions": ["chat", "sse"]
 *   }
 * }
 *
 * Error Response (401):
 * {
 *   "success": false,
 *   "error": "Invalid API credentials"
 * }
 *
 * Integration:
 * - Used by chat widget middleware server (chat_wiget/src/server.ts)
 * - Called via validateApiKey() function in middleware
 * - Enables authentication for chat widget external access
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { _coreVerifyAuthentication } from '@/server/core/live-chat';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';

export async function POST(request: NextRequest) {
  try {
    // Extract API credentials from headers and body
    const body = await request.json().catch(() => ({}));
    const headerAppId = request.headers.get('x-app-id');
    const headerApiKey = request.headers.get('x-api-key');

    // Prioritize body parameters, fall back to headers
    const appId = body.appId || headerAppId;
    const apiKey = body.apiKey || headerApiKey;

    // Require API credentials for external access
    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key using core function
    const result = await _coreVerifyAuthentication(appId, apiKey);

    if (!result.success) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: result.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // API authentication successful
    return NextResponse.json(
      serializeResponse({
        success: true,
        data: {
          authMethod: 'external',
          appId,
          organizationId: result.data?.organizationId,
          permissions: ['chat', 'sse']
        }
      }),
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-app-id, x-api-key, Cache-Control',
          'Access-Control-Max-Age': '86400',
        }
      }
    );

  } catch (error) {
    logger.error('API: Error verifying authentication:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Support GET method for simple credential validation
export async function GET(request: NextRequest) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');

    // Require API credentials for external access
    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    return NextResponse.json(
      serializeResponse({
        success: true,
        data: {
          authMethod: 'external',
          appId,
          organizationId: apiValidation.organizationId,
          permissions: ['chat', 'sse']
        }
      }),
      {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-app-id, x-api-key, Cache-Control',
          'Access-Control-Max-Age': '86400',
        }
      }
    );

  } catch (error) {
    logger.error('API: Error verifying authentication (GET):', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

// Support OPTIONS method for CORS preflight requests
export async function OPTIONS() {
  return NextResponse.json(
    {},
    {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, x-app-id, x-api-key, Cache-Control',
        'Access-Control-Max-Age': '86400', // 24 hours
      }
    }
  );
}

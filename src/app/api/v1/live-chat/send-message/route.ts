/**
 * API Route: Send Customer Message
 * 
 * External API endpoint for sending customer messages to live chat conversations.
 * Uses API key authentication and calls the same core business logic
 * as the internal server action.
 * 
 * Endpoint: POST /api/v1/live-chat/send-message
 * Authentication: API Key (appId + apiKey in headers)
 * 
 * Request Body:
 * {
 *   "conversationId": "string", // required
 *   "content": "string"         // required
 * }
 * 
 * Headers:
 * - x-app-id: string (required) - Application ID
 * - x-api-key: string (required) - API Key
 * - Content-Type: application/json
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { _coreSendCustomerMessage } from '@/server/core/live-chat';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';

export async function POST(request: NextRequest) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');

    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid JSON in request body'
        }),
        { status: 400 }
      );
    }

    // Validate required fields
    const { conversationId, content } = body;

    if (!conversationId) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'conversationId is required in request body'
        }),
        { status: 400 }
      );
    }

    if (!content) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'content is required in request body'
        }),
        { status: 400 }
      );
    }

    // Call the same core function used by server actions
    const result = await _coreSendCustomerMessage(
      conversationId,
      content,
      apiValidation.organizationId!
    );

    // Return the result with appropriate HTTP status
    const statusCode = result.success ? 200 : 400;
    
    logger.log('API: Send customer message result:', {
      appId,
      conversationId,
      organizationId: apiValidation.organizationId,
      success: result.success,
      messageLength: content?.length || 0
    });

    return NextResponse.json(
      serializeResponse({
        success: result.success,
        error: result.error || null,
        data: result.data || null
      }),
      { status: statusCode }
    );

  } catch (error) {
    logger.error('API: Error sending customer message:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error',
        data: null
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

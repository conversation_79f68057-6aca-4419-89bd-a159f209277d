/**
 * API Route: Get Customer Live Chat Messages
 * 
 * External API endpoint for retrieving live chat conversation messages.
 * Uses API key authentication and calls the same core business logic
 * as the internal server action.
 * 
 * Endpoint: GET /api/v1/live-chat/messages
 * Authentication: API Key (appId + apiKey in headers)
 * 
 * Query Parameters:
 * - conversationId: string (required) - The live chat conversation ID
 * 
 * Headers:
 * - x-app-id: string (required) - Application ID
 * - x-api-key: string (required) - API Key
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { _coreGetCustomerMessages } from '@/server/core/live-chat';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';

export async function GET(request: NextRequest) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');

    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');

    if (!conversationId) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'conversationId query parameter is required'
        }),
        { status: 400 }
      );
    }

    // Call the same core function used by server actions
    const result = await _coreGetCustomerMessages(conversationId, apiValidation.organizationId!);

    // Return the result with appropriate HTTP status
    const statusCode = result.success ? 200 : 400;
    
    logger.log('API: Get customer messages result:', {
      appId,
      conversationId,
      organizationId: apiValidation.organizationId,
      success: result.success,
      messageCount: result.data?.length || 0
    });

    return NextResponse.json(serializeResponse(result), { status: statusCode });

  } catch (error) {
    logger.error('API: Error getting customer live chat messages:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

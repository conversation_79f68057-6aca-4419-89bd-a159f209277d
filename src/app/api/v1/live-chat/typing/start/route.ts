/**
 * API Route: Set Typing Status
 * 
 * External API endpoint for setting customer typing status in live chat conversations.
 * Uses API key authentication and calls the same core business logic
 * as the internal server action.
 * 
 * Endpoint: POST /api/v1/live-chat/typing/start
 * Authentication: API Key (appId + apiKey in headers)
 * 
 * Request Body:
 * {
 *   "conversationId": "string", // required
 *   "userType": "customer",     // required - always customer for external API
 *   "userName": "string"        // optional - customer name
 * }
 * 
 * Headers:
 * - x-app-id: string (required) - Application ID
 * - x-api-key: string (required) - API Key
 * - Content-Type: application/json
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/utils/auth';
import { setTypingStatus } from '@/server/actions/typing-actions';
import { serializeResponse } from '@/utils/helper';
import { logger } from '@/utils/logger';
import { z } from 'zod';

// Validation schema for request body
const SetTypingStatusRequestSchema = z.object({
  conversationId: z.string().min(1, 'Conversation ID is required'),
  userType: z.literal('customer'), // Only allow customer for external API
  userName: z.string().optional()
});

export async function POST(request: NextRequest) {
  try {
    // Extract API credentials from headers
    const appId = request.headers.get('x-app-id');
    const apiKey = request.headers.get('x-api-key');
    
    if (!appId || !apiKey) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'API credentials required. Include x-app-id and x-api-key headers.'
        }),
        { status: 401 }
      );
    }

    // Validate API key
    const apiValidation = await validateApiKey(appId, apiKey);
    if (!apiValidation.isValid) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: apiValidation.error || 'Invalid API credentials'
        }),
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json().catch(() => ({}));
    const validation = SetTypingStatusRequestSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        serializeResponse({
          success: false,
          error: 'Invalid request body',
          details: validation.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ')
        }),
        { status: 400 }
      );
    }

    const { conversationId, userType, userName } = validation.data;

    logger.log('API: Setting typing status:', {
      appId,
      organizationId: apiValidation.organizationId,
      conversationId,
      userType,
      userName
    });

    // Call the same server action used by the dashboard
    const result = await setTypingStatus(
      conversationId,
      userType,
      apiValidation.organizationId!,
      userName
    );

    // Return the result with appropriate HTTP status
    const statusCode = result.success ? 200 : 400;
    
    logger.log('API: Set typing status result:', {
      appId,
      organizationId: apiValidation.organizationId,
      conversationId,
      userType,
      success: result.success
    });

    return NextResponse.json(serializeResponse(result), { status: statusCode });

  } catch (error) {
    logger.error('API: Error setting typing status:', error);
    return NextResponse.json(
      serializeResponse({
        success: false,
        error: 'Internal server error'
      }),
      { status: 500 }
    );
  }
}

// Export other HTTP methods as not allowed
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

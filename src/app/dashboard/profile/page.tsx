'use client';

import { useState, useEffect } from 'react';
import { getUserData, updateUserProfile, changeUserPassword } from '@/server/actions/user-actions';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Alert, SuccessAlert } from '@/components/ui/Alert';
import { Tabs } from '@/components/ui/Tabs';
import { Skeleton } from '@/components/ui/Skeleton';
import DashboardHeader from '@/components/layout/DashboardHeader';
import Link from 'next/link';
import { logger } from '@/utils/logger';

// Icon imports
import { KeyIcon, UserIcon } from '@heroicons/react/24/outline';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  company?: string;
  emailVerified: boolean;
  lastLogin?: string;
}

export default function ProfilePage() {
  // State for profile data
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for profile update
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [company, setCompany] = useState('');
  const [updating, setUpdating] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const [updateError, setUpdateError] = useState<string | null>(null);

  // State for password change
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [changingPassword, setChangingPassword] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState({
    label: '',
    severity: ''
  });

  // Fetch user profile data
  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      setError(null);
      try {
        const result = await getUserData();
        if (result.success && result.data) {
          setProfile(result.data);
          setName(result.data.name);
          setEmail(result.data.email);
          setCompany(result.data.company || '');
        } else {
          setError(result.error || 'Failed to load profile data');
        }
      } catch (err) {
        logger.error('Error fetching profile:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  // Password strength check
  useEffect(() => {
    if (!newPassword) {
      setPasswordStrength({ label: '', severity: '' });
      return;
    }

    const getPasswordStrength = (password: string) => {
      if (password.length < 8) return { label: 'Weak', severity: 'danger' };

      const hasLowercase = /[a-z]/.test(password);
      const hasUppercase = /[A-Z]/.test(password);
      const hasDigit = /\d/.test(password);
      const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);

      const conditions = [hasLowercase, hasUppercase, hasDigit, hasSpecial];
      const metCount = conditions.filter(Boolean).length;

      if (metCount <= 2) return { label: 'Medium', severity: 'warning' };
      if (metCount === 3) return { label: 'Strong', severity: 'info' };
      return { label: 'Very Strong', severity: 'success' };
    };

    setPasswordStrength(getPasswordStrength(newPassword));
  }, [newPassword]);

  // Handle profile update submission
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setUpdating(true);
    setUpdateError(null);
    setUpdateSuccess(false);

    try {
      // Only include changed fields
      const updateData: any = {};
      if (name !== profile?.name) updateData.name = name;
      if (email !== profile?.email) updateData.email = email;
      if (company !== profile?.company) updateData.company = company;

      // Only proceed if there are changes
      if (Object.keys(updateData).length === 0) {
        setUpdateError('No changes detected');
        setUpdating(false);
        return;
      }

      const result = await updateUserProfile(updateData);

      if (result.success) {
        setUpdateSuccess(true);

        // Update profile data
        if (result.data) {
          setProfile(prevProfile => {
            if (!prevProfile) return null;
            return {
              ...prevProfile,
              ...result.data
            };
          });
        }
      } else {
        setUpdateError(result.error || 'Failed to update profile');
      }
    } catch (err) {
      logger.error('Error updating profile:', err);
      setUpdateError('An unexpected error occurred');
    } finally {
      setUpdating(false);
    }
  };

  // Handle password change submission
  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setChangingPassword(true);
    setPasswordError(null);
    setPasswordSuccess(false);

    // Validate passwords match
    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match');
      setChangingPassword(false);
      return;
    }

    try {
      const result = await changeUserPassword({
        currentPassword,
        newPassword,
        confirmPassword
      });

      if (result.success) {
        setPasswordSuccess(true);
        setCurrentPassword('');
        setNewPassword('');
        setConfirmPassword('');
      } else {
        setPasswordError(result.error || 'Failed to change password');
      }
    } catch (err) {
      logger.error('Error changing password:', err);
      setPasswordError('An unexpected error occurred');
    } finally {
      setChangingPassword(false);
    }
  };

  const rightContent = (
    <Link
      href="/dashboard"
      className="text-sm px-4 py-2 rounded-lg bg-[#E0D7FF] hover:bg-[#D0C7FF] text-[#6964D3] transition-colors dark:bg-[#2a2a38] dark:hover:bg-[#3a3a48] dark:text-[#B2A5FF]"
    >
      Back to Dashboard
    </Link>
  );

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-[#1e1e28] flex flex-col">
        <DashboardHeader userName={profile?.name} showProfileLink={false} />
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Profile</h1>
          <div className="bg-white dark:bg-[#2a2a38] rounded-lg shadow-md p-6">
            <Skeleton height="400px" />
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-[#1e1e28] flex flex-col">
        <DashboardHeader userName={profile?.name} showProfileLink={false} />
        <div className="container mx-auto px-4 py-8">
          <h1 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Profile</h1>
          <Alert severity="error">{error}</Alert>
        </div>
      </div>
    );
  }

  const tabItems = [
    {
      id: 'profile',
      label: 'Profile Details',
      icon: <UserIcon className="h-5 w-5 text-[#6964D3]" />,
      content: (
        <div className="bg-transparent">
          <div className="max-w-3xl mx-auto">
            <form onSubmit={handleProfileUpdate} className="space-y-6">
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Basic Information</h3>

                <div className="mb-4">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Name <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="name"
                    value={name}
                    onChange={e => setName(e.target.value)}
                    placeholder="Your name"
                    className="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-[#6964D3] focus:border-[#6964D3]"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={e => setEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-[#6964D3] focus:border-[#6964D3]"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Company
                  </label>
                  <Input
                    id="company"
                    value={company}
                    onChange={e => setCompany(e.target.value)}
                    placeholder="Your company name"
                    className="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-[#6964D3] focus:border-[#6964D3]"
                  />
                </div>
              </div>

              {updateError && (
                <Alert severity="error">{updateError}</Alert>
              )}

              {updateSuccess && (
                <SuccessAlert>Profile updated successfully</SuccessAlert>
              )}

              <div className="flex justify-end pt-6">
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={updating}
                  disabled={updating}
                  className="bg-[#6964D3] hover:bg-[#8178E8] text-white rounded-lg shadow-sm hover:shadow-md transition-all"
                >
                  Save Changes
                </Button>
              </div>
            </form>
          </div>
        </div>
      )
    },
    {
      id: 'password',
      label: 'Change Password',
      icon: <KeyIcon className="h-5 w-5 text-[#6964D3]" />,
      content: (
        <div className="bg-transparent">
          <div className="max-w-3xl mx-auto">
            <form onSubmit={handlePasswordChange} className="space-y-6">
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Change Password</h3>

                <div className="mb-4">
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Current Password <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={e => setCurrentPassword(e.target.value)}
                    placeholder="Your current password"
                    className="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-[#6964D3] focus:border-[#6964D3]"
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    New Password <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={e => setNewPassword(e.target.value)}
                    placeholder="New password"
                    className="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-[#6964D3] focus:border-[#6964D3]"
                  />
                  {passwordStrength.label ? (
                    <div className={`text-sm mt-1 ${
                      passwordStrength.severity === 'danger' ? 'text-red-500' :
                      passwordStrength.severity === 'warning' ? 'text-amber-500' :
                      passwordStrength.severity === 'info' ? 'text-[#6964D3]' :
                      'text-green-500'
                    }`}>
                      Password strength: {passwordStrength.label}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Password must be at least 8 characters
                    </div>
                  )}
                </div>

                <div className="mb-4">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Confirm New Password <span className="text-red-500">*</span>
                  </label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={e => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                    className="w-full bg-gray-50 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md focus:ring-[#6964D3] focus:border-[#6964D3]"
                  />
                </div>
              </div>

              {passwordError && (
                <Alert severity="error">{passwordError}</Alert>
              )}

              {passwordSuccess && (
                <SuccessAlert>Password changed successfully</SuccessAlert>
              )}

              <div className="flex justify-end pt-6">
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={changingPassword}
                  disabled={changingPassword || !currentPassword || !newPassword || !confirmPassword}
                  className="bg-[#6964D3] hover:bg-[#8178E8] text-white rounded-lg shadow-sm hover:shadow-md transition-all"
                >
                  Change Password
                </Button>
              </div>
            </form>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-[#1e1e28] flex flex-col">
      <DashboardHeader
        userName={profile?.name}
        rightContent={rightContent}
        showProfileLink={false}
      />
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Profile</h1>
        <div className="bg-white dark:bg-[#2a2a38] rounded-lg shadow-md p-6">
          <Tabs tabs={tabItems} />
        </div>
      </div>
    </div>
  );
}

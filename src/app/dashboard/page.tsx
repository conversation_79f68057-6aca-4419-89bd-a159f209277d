'use client';

import {useState, useEffect, useRef} from 'react';
import { useRouter } from 'next/navigation';
import { getUserData, getUserOrganizations } from '@/server/actions/user-actions';
import CreateOrganizationModal from '@/components/dashboard/CreateOrganizationModal';
import DashboardHeader from '@/components/layout/DashboardHeader';
import { Dialog } from '@/components/ui/Dialog';
import {Button} from "@components/ui/Button";
import { logger } from '@/utils/logger';

export default function DashboardPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState<any>(null);
  const [organizations, setOrganizations] = useState<any[]>([]);
  const [showOrganizationDialog, setShowOrganizationDialog] = useState(false);
  const isMounted = useRef(true);
  const organizationModalRef = useRef<any>(null);
  const [isCreatingOrg, setIsCreatingOrg] = useState(false);

  useEffect(() => {
    isMounted.current = true; // Flag to prevent state updates after unmount

    const fetchUserData = async () => {
      if (!isMounted.current) return;
      setIsLoading(true);

      try {
        // Fetch user data and organizations concurrently
        const [userResult, orgsResult] = await Promise.all([
          getUserData(),
          getUserOrganizations()
        ]);

        // Check if component is still mounted
        if (!isMounted.current) return;

        // Handle authentication
        if (!userResult?.success) {
          router.push('/auth?mode=login');
          return;
        }

        // Update state with fetched data
        setUserData(userResult.data);

        if (orgsResult.success) {
          const orgs = orgsResult.data || [];
          setOrganizations(orgs);

          // Show organization dialog if no organizations exist
          if (orgs.length === 0) {
            setShowOrganizationDialog(true);
          }
        }
      } catch (error) {
        if (isMounted.current) {
          logger.error('Error fetching user data:', error);
          router.push('/auth?mode=login');
        }
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    };

    fetchUserData();

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted.current = false;
    };
  }, [router]);

  // Handle organization creation
  const handleCreateOrganizationSuccess = (newOrg: any) => {
    setOrganizations(prev => [...prev, newOrg]);
    setShowOrganizationDialog(false);
  };

  // Update the dialog content in both places
  const renderDialogContent = () => (
    <CreateOrganizationModal
      ref={organizationModalRef}
      onClose={() => setShowOrganizationDialog(false)}
      onSuccess={handleCreateOrganizationSuccess}
    />
  );

  const renderDialogFooter = () => (
    <div className="flex justify-end gap-3">
      <button
        type="button"
        className="px-4 py-2 bg-[#F3F3F3] dark:bg-[#2a2a38] rounded-lg text-[#5E5E5E] dark:text-[#C6C6C6] hover:bg-[#E0D7FF] dark:hover:bg-[#3a3a48] transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8178E8]"
        onClick={() => setShowOrganizationDialog(false)}
        disabled={isCreatingOrg}
      >
        Cancel
      </button>
      <button
        type="button"
        className="px-4 py-2 bg-gradient-to-r from-[#8178E8] to-[#6964D3] rounded-lg text-white hover:shadow-lg transition-all focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#8178E8]"
        onClick={() => {
          setIsCreatingOrg(true);
          // Let the CreateOrganizationModal component handle the creation process
          if (organizationModalRef.current && organizationModalRef.current.handleSubmit) {
            organizationModalRef.current.handleSubmit().finally(() => {
              setIsCreatingOrg(false);
            });
          } else {
            setIsCreatingOrg(false);
          }
        }}
        disabled={isCreatingOrg}
      >
        {isCreatingOrg ? 'Creating...' : 'Create Organization'}
      </button>
    </div>
  );

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#6964D3] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // New Organization button component
  const NewOrgButton = (
    <Button
      onClick={() => setShowOrganizationDialog(true)}
      className="text-sm px-4 py-2 bg-[#F3F3F3] dark:bg-[#2a2a38] rounded-lg hover:bg-[#E0D7FF] dark:hover:bg-[#3a3a48] transition-colors"
    >
      + New Organization
    </Button>
  );

  // If no organizations and modal is shown
  if (organizations.length === 0) {
    return (
      <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex flex-col">
        <DashboardHeader userName={userData?.name} />

        <main className="flex-1 justify-center items-center alig max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-12">
            <h1 className="text-3xl font-bold mb-4 bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent">
              Welcome to New Instance
            </h1>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-8 max-w-2xl mx-auto">
              To get started, you need to create an organization. Organizations help you manage your customer support, error logging, and other services.
            </p>

            <button
              onClick={() => setShowOrganizationDialog(true)}
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-white py-3 px-6 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all"
            >
              Create Your First Organization
            </button>
          </div>
        </main>

        <Dialog
          title="Create Organization"
          open={showOrganizationDialog}
          onOpenChange={setShowOrganizationDialog}
          footer={renderDialogFooter()}
        >
          {renderDialogContent()}
        </Dialog>
      </div>
    );
  }
  // Dashboard with organizations
  return (
    <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex flex-col">
      <DashboardHeader
        userName={userData?.name}
        rightContent={NewOrgButton}
      />

      <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Your Organizations</h1>
          {/*<LogoutButton variant="text" />*/}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {organizations.map((org) => (
            <div
              key={org.id}
              className="bg-white dark:bg-[#1e1e28] rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 cursor-pointer"
              onClick={() => router.push(`/dashboard/organization/${org.id}`)}
            >
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-[#E0D7FF] dark:bg-[#2a2a38] rounded-lg flex items-center justify-center text-[#6964D3] font-bold text-xl">
                  {org.name.substring(0, 2).toUpperCase()}
                </div>
                <div className="ml-4">
                  <h3 className="font-bold text-lg">{org.name}</h3>
                  <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">{org.industry || 'Business'}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mt-4">
                <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-3 rounded-lg">
                  <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">Support Tickets</p>
                  <p className="font-semibold">{org.ticketCount || 0}</p>
                </div>
                <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-3 rounded-lg">
                  <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">Error Logs</p>
                  <p className="font-semibold">{org.errorCount || 0}</p>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-[#E0D7FF] dark:border-[#2a2a38]">
                <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">
                  Subscription: <span className="font-medium text-[#6964D3]">{org.plan || 'Free'}</span>
                </p>
              </div>
            </div>
          ))}
        </div>
      </main>

      <Dialog
        title="Create Organization"
        open={showOrganizationDialog}
        onOpenChange={setShowOrganizationDialog}
        footer={renderDialogFooter()}
      >
        {renderDialogContent()}
      </Dialog>
    </div>
  );
}

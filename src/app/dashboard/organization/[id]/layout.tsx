'use client';

import {useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import Sidebar from '@/components/dashboard/Sidebar';
import {Button} from '@/components/ui/Button';
import {getOrganizationById} from "@server/actions/user-actions";
import LoadingView from "@components/ui/LoadingView";
import ErrorView from "@components/ui/ErrorView";
import { logger } from '@/utils/logger';

export default function OrganizationLayout({ children }: { children: React.ReactNode }) {
  const params = useParams();
  const organizationId = params.id as string
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isMobileSidebarVisible, setIsMobileSidebarVisible] = useState(false);
  useEffect(() => {
    const fetchOrgName = async () => {
      setIsLoading(true);
      try {
        const response = await getOrganizationById(organizationId);
        if (response.success && response.data) {
        } else {
          setError(response.error);
        }
      } catch (error) {
        logger.error('Error fetching organization:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrgName();
  }, [organizationId]);

  // Toggle mobile sidebar
  const toggleMobileSidebar = () => {
    setIsMobileSidebarVisible(!isMobileSidebarVisible);
  };

  const closeMobileSidebar = () => {
    setIsMobileSidebarVisible(false);
  };
  if(isLoading) return <LoadingView/>
  if(error) return  <ErrorView
    error={error}
    title="Organization Error"
    buttonText="Back to Dashboard"
    onButtonClick={() => router.push('/dashboard')}
  />
  return (
    <div className="flex h-screen bg-white dark:bg-[#0a0a14] relative overflow-hidden">
      {/* Background gradient elements */}
      <div className="absolute top-0 right-0 w-1/3 h-1/2 bg-gradient-to-br from-[#E0D7FF]/20 to-transparent rounded-full blur-3xl animate-pulse-slow"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/2 bg-gradient-to-tr from-[#F0DAB8]/10 to-transparent rounded-full blur-3xl animate-float"></div>

      {/* Desktop sidebar */}
      <div className="hidden md:block w-64 z-10">
        <Sidebar />
      </div>

      {/* Mobile sidebar - displayed as a fixed sidebar when visible */}
      {isMobileSidebarVisible && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-50 backdrop-blur-sm" onClick={closeMobileSidebar}>
          <div
            className="fixed left-0 top-0 h-full w-64 animate-slideInLeft"
            onClick={(e) => e.stopPropagation()}
          >
            <Sidebar onClose={closeMobileSidebar} isMobile={true} />
          </div>
        </div>
      )}

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-auto z-10">
        {/* Mobile header */}
        <header className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800 md:hidden bg-white dark:bg-[#0a0a14] relative z-10">
          <Button
            icon="pi pi-bars"
            variant="ghost"
            size="sm"
            onClick={toggleMobileSidebar}
          />
          <h2 className="text-xl font-bold truncate bg-gradient-to-r from-[#424098] to-[#6964D3] bg-clip-text text-transparent">
            Organization
          </h2>
          <div className="w-10"></div> {/* Placeholder for balance */}
        </header>

        {/* Page content */}
        <main className="flex-1 overflow-auto relative z-10">
          {children}
        </main>
      </div>
    </div>
  );
}

'use client';

import {useState, useEffect} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {Input} from '@components/ui/Input';
import {
  calculateUserLimitProratedAmount,
  getActiveSubscription,
} from '@/server/actions/billing';
import {verifyPayment} from '@/server/actions/transaction-actions';
import {useToast} from '@components/ui/Toast';
import {Skeleton} from '@components/ui/Skeleton';
import {AlertCircle, UserPlus, Users, Sparkles, CreditCard, Zap} from 'lucide-react';
import {Currency} from '@/constants/pricing';
import {PaymentProvider} from '@/constants/transactions';
import {initializeUserLimitUpgrade} from '@/server/actions/billing';
import {Button} from "@components/ui/Button";
import {useDebounce} from '@/hooks/useDebounce';
import PaystackButton from '@/components/ui/PaystackButton';


export default function UpgradeUserLimitPage() {
  const params = useParams();
  const router = useRouter();
  const toast = useToast();
  const organizationId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [calculating, setCalculating] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [initializing, setInitializing] = useState(false);
  const [additionalUsers, setAdditionalUsers] = useState(1);
  // Use our custom debounce hook
  const debouncedAdditionalUsers = useDebounce(additionalUsers, 500);
  const [subscription, setSubscription] = useState<any>(null);
  const [proratedAmountData, setProratedAmountData] = useState<any>(null);
  const [currency, setCurrency] = useState<Currency>(Currency.NGN);
  const [originalCurrency, setOriginalCurrency] = useState<Currency | null>(null);
  const [currencyError, setCurrencyError] = useState<string | null>(null);
  const [transactionData, setTransactionData] = useState<any>(null);
  const [paymentProvider, setPaymentProvider] = useState<PaymentProvider>(PaymentProvider.PAYSTACK);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getActiveSubscription({organizationId});
        if (result.success && result.data) {
          setSubscription(result.data);
          // Set the currency from the subscription metadata
          const subCurrency = result.data.metadata?.currency || result.data.pricingPlan?.currency || Currency.NGN;
          setCurrency(subCurrency);
          setOriginalCurrency(subCurrency);
          // Calculate initial prorated amount for 1 additional user
          calculateProration(1, subCurrency, result.data);
        } else {
          toast.error('Subscription not found', 'You don\'t have an active subscription for this organization');
        }
      } catch (error) {
        toast.error('Error', 'Failed to load subscription data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [organizationId]);

  useEffect(() => {
    if (subscription && debouncedAdditionalUsers > 0) {
      calculateProration(debouncedAdditionalUsers, currency);
    }
  }, [debouncedAdditionalUsers, currency, subscription]);

  const calculateProration = async (users: number, selectedCurrency?: Currency, activeSub?: any) => {
    const sub = subscription || activeSub;
    if (!sub) return;
    const currencyToUse = selectedCurrency || currency;

    // Ensure the currency matches the original subscription currency
    if (originalCurrency && currencyToUse !== originalCurrency) {
      setCurrencyError(`You must continue with your original subscription currency (${originalCurrency}). To use a different currency, you need to cancel your current subscription first.`);
      setCurrency(originalCurrency); // Reset to original currency
      return;
    } else {
      setCurrencyError(null);
    }

    setCalculating(true);
    try {
      const result = await calculateUserLimitProratedAmount({
        organizationId,
        additionalUsers: users,
        currencyPreference: currencyToUse
      });

      if (result.success && result.data) {
        setProratedAmountData(result.data);
      } else {
        toast.error('Error', result.error || 'Failed to calculate prorated amount');
      }
    } catch (error) {
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setCalculating(false);
    }
  };

  const handleUserChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(e.target.value) || 1;
    if (value < 1) return;
    setAdditionalUsers(value);
  };

  const handleCurrencyChange = (selectedCurrency: Currency) => {
    // Check if selected currency matches original subscription currency
    if (originalCurrency && selectedCurrency !== originalCurrency) {
      setCurrencyError(`You must continue with your original subscription currency (${originalCurrency}). To use a different currency, you need to cancel your current subscription first.`);
      return;
    }

    setCurrencyError(null);
    setCurrency(selectedCurrency);
    calculateProration(additionalUsers, selectedCurrency);
  };

  const initializePayment = async () => {
    if (!proratedAmountData) {
      toast.error('Error', 'Please wait for amount calculation to complete');
      return;
    }

    setInitializing(true);
    try {
      // Initialize a transaction on the server first
      const result = await initializeUserLimitUpgrade({
        organizationId,
        additionalUsers,
        amount: proratedAmountData.proratedAmount,
        paymentProvider: paymentProvider
      });

      if (!result.success || !result.data) {
        // Handle both cases - when error is directly accessible or when we need a fallback
        const errorMessage = 'error' in result ? result.error : 'Failed to initialize payment';
        toast.error('Error', errorMessage);
        return;
      }

      // Store the transaction data returned from the server
      setTransactionData(result.data);

      // Now we have a valid transaction ID to use with the payment gateway
      return result.data;
    } catch (error: any) {
      toast.error('Error', error.message || 'An unexpected error occurred');
      return null;
    } finally {
      setInitializing(false);
    }
  };

  const handlePaymentInit = () => {
    return {
      reference: transactionData.transactionId as string,
      email: transactionData.email as string,
      amount: transactionData.amount ? Math.round(transactionData.amount * 100) : 0 as number,
      publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY as string,
      currency: currency as Currency,
      metadata: transactionData.metadata as object,
    };
  };

  const onSuccess = async (reference: any) => {
    setProcessing(true);
    try {
      // Verify the payment using the transaction reference
      const verificationResult = await verifyPayment(reference.reference);

      if (verificationResult.success) {
        // No need to create a new transaction record as it was already created during initialization
        // Just redirect to the verification page
        router.push(`/dashboard/organization/${organizationId}/billing/verification?reference=${reference.reference}`);
      } else {
        toast.error('Payment verification failed', verificationResult.message || 'Failed to verify payment');
      }
    } catch (error) {
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setProcessing(false);
    }
  };

  const onClose = () => {
    toast.info('Payment cancelled', 'You have cancelled the payment');
  };

  const getPaymentProviderName = (provider: PaymentProvider): string => {
    switch (provider) {
      case PaymentProvider.PAYSTACK:
        return 'Paystack';
      case PaymentProvider.NETAPPSPAY:
        return 'NetAppsPay';
      case PaymentProvider.STRIPE:
        return 'Stripe';
      case PaymentProvider.MANUAL:
        return 'our payment system';
      default:
        return 'our payment provider';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-5xl">
        <div
          className="bg-white/10 backdrop-blur-lg rounded-3xl shadow-[0_8px_30px_rgb(0,0,0,0.12)] p-6 border border-slate-100 dark:border-slate-800">
          <div className="space-y-4 animate-pulse">
            <Skeleton className="h-10 w-3/4 rounded-xl"/>
            <Skeleton className="h-6 w-1/2 rounded-xl"/>
            <div className="pt-6">
              <Skeleton className="h-24 w-full rounded-2xl mb-4"/>
              <Skeleton className="h-16 w-full rounded-2xl mb-4"/>
              <Skeleton className="h-40 w-full rounded-2xl mb-6"/>
              <Skeleton className="h-14 w-full rounded-xl"/>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-5xl">
        <div className="bg-red-50 backdrop-blur rounded-2xl shadow-lg p-6 border border-red-100">
          <div className="flex items-center gap-3 text-red-600 mb-2">
            <AlertCircle className="h-6 w-6"/>
            <h3 className="text-lg font-semibold">No active subscription</h3>
          </div>
          <p className="text-red-600/80 ml-9">
            You don&#39;t have an active subscription for this organization.
            Please subscribe to a plan first.
          </p>
        </div>
      </div>
    );
  }

  // Check if plan is per-user
  if (!subscription.pricingPlan?.isPerUser) {
    return (
      <div className="container mx-auto px-4 py-12 max-w-5xl">
        <div className="bg-blue-50 backdrop-blur rounded-2xl shadow-lg p-6 border border-blue-100">
          <div className="flex items-center gap-3 text-blue-600 mb-2">
            <AlertCircle className="h-6 w-6"/>
            <h3 className="text-lg font-semibold">Not applicable</h3>
          </div>
          <p className="text-blue-600/80 ml-9">
            Your current subscription plan does not support per-user pricing.
            Please contact support for assistance.
          </p>
        </div>
      </div>
    );
  }

  const currentLimit = subscription.numberOfUsers || 0;
  const newLimit = currentLimit + additionalUsers;

  return (
    <div className="container mx-auto px-4 py-12 max-w-5xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-slate-800 dark:text-white flex items-center gap-2">
          <UserPlus className="h-7 w-7 text-primary"/> Upgrade User Limit
        </h1>
        <p className="text-slate-500 dark:text-slate-400 mt-1">Expand your team capacity with additional user
          licenses</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div
            className="bg-white dark:bg-slate-900 rounded-3xl shadow-[0_8px_30px_rgb(0,0,0,0.08)] overflow-hidden border border-slate-100 dark:border-slate-800 transition-all hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)]">
            <div
              className="bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 p-6 border-b border-slate-100 dark:border-slate-800">
              <div className="flex items-center">
                <div className="bg-white dark:bg-slate-800 rounded-full p-3 shadow-md mr-4">
                  <Sparkles className="h-5 w-5 text-primary"/>
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-slate-800 dark:text-white">Current Subscription</h2>
                  <p className="text-sm text-slate-500 dark:text-slate-400">{subscription.pricingPlan?.name}</p>
                </div>
              </div>
            </div>

            <div className="p-6">
              <div
                className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-800/50 p-5 rounded-2xl mb-6">
                <div className="flex items-center mb-3">
                  <Users className="h-5 w-5 text-primary mr-2"/>
                  <span className="font-medium">User Capacity Details</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-slate-500 dark:text-slate-400">Current limit</span>
                  <span className="text-lg font-medium">{currentLimit}</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-slate-500 dark:text-slate-400">Additional users</span>
                  <div className="flex items-center">
                    <span className="text-lg font-medium text-primary">+{additionalUsers}</span>
                    <Zap className="w-4 h-4 text-amber-500 ml-1"/>
                  </div>
                </div>
                <div className="flex justify-between items-center py-2 mb-2">
                  <span className="text-slate-500 dark:text-slate-400">New limit</span>
                  <span className="text-lg font-medium">{newLimit}</span>
                </div>

                <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 mb-1">
                  <div
                    className="bg-gradient-to-r from-primary to-primary/80 h-2 rounded-full transition-all duration-500 ease-out"
                    style={{width: `${Math.min((newLimit / (newLimit + 5)) * 100, 100)}%`}}
                  ></div>
                </div>
                <div className="text-xs text-right text-slate-500 dark:text-slate-400">
                  {Math.min((newLimit / (newLimit + 5)) * 100, 100).toFixed(0)}% capacity
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <label htmlFor="additionalUsers"
                         className="block text-sm font-medium mb-2 text-slate-700 dark:text-slate-300">
                    Additional Users
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Users className="h-5 w-5 text-slate-400"/>
                    </div>
                    <Input
                      id="additionalUsers"
                      type="number"
                      min="1"
                      value={additionalUsers.toString()}
                      onChange={handleUserChange}
                      className="pl-10 h-12 rounded-xl border-slate-200 dark:border-slate-700 focus:ring-primary transition-shadow"
                    />
                  </div>
                </div>

                <div className="p-6 rounded-3xl border border-border/50 bg-white dark:bg-gray-900 shadow-sm">
                  <div className="mb-6">
                    <label className="block text-sm font-medium mb-2">Currency</label>
                    <div className="flex space-x-3">
                      <button
                        type="button"
                        onClick={() => handleCurrencyChange(Currency.NGN)}
                        disabled={originalCurrency !== null && originalCurrency !== Currency.NGN}
                        className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
                          currency === Currency.NGN
                            ? 'bg-primary text-white shadow-md'
                            : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                        } ${originalCurrency !== null && originalCurrency !== Currency.NGN ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        NGN (₦)
                      </button>
                      <button
                        type="button"
                        onClick={() => handleCurrencyChange(Currency.USD)}
                        disabled={originalCurrency !== null && originalCurrency !== Currency.USD}
                        className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${
                          currency === Currency.USD
                            ? 'bg-primary text-white shadow-md'
                            : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                        } ${originalCurrency !== null && originalCurrency !== Currency.USD ? 'opacity-50 cursor-not-allowed' : ''}`}
                      >
                        USD ($)
                      </button>
                    </div>
                    {currencyError && (
                      <div className="mt-2 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                        {currencyError}
                      </div>
                    )}
                    {originalCurrency && (
                      <div className="mt-2 text-xs text-gray-500">
                        Your subscription is currently in {originalCurrency}. To change currencies, you must cancel your
                        current subscription first.
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div>
          <div
            className="bg-white dark:bg-slate-900 rounded-3xl shadow-[0_8px_30px_rgb(0,0,0,0.08)] overflow-hidden border border-slate-100 dark:border-slate-800 h-full transition-all sticky top-6">
            <div
              className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-800/50 p-6 border-b border-slate-100 dark:border-slate-800">
              <h2 className="text-lg font-semibold text-slate-800 dark:text-white flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-primary"/>
                Payment Summary
              </h2>
            </div>

            <div className="p-6 space-y-5">
              <div className="space-y-3">
                {proratedAmountData && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-slate-500 dark:text-slate-400">Per-user price</span>
                      <span className="font-medium">
                        {currency === Currency.NGN ? '₦' : '$'}{proratedAmountData.perUserPrice.toLocaleString()}
                      </span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-slate-500 dark:text-slate-400">Billing period</span>
                      <span className="font-medium">
                        {proratedAmountData.daysRemaining} of {proratedAmountData.totalDaysInCycle} days
                      </span>
                    </div>

                    <div className="flex justify-between text-sm">
                      <span className="text-slate-500 dark:text-slate-400">Pro-ration</span>
                      <span className="font-medium">
                        {((proratedAmountData.daysRemaining / proratedAmountData.totalDaysInCycle) * 100).toFixed(0)}%
                      </span>
                    </div>
                  </>
                )}

                <div className="mt-4 mb-4">
                  <label className="block text-sm font-medium mb-2 text-slate-700 dark:text-slate-300">
                    Payment Provider
                  </label>
                  <div className="flex flex-wrap gap-2">
                    <button
                      type="button"
                      onClick={() => setPaymentProvider(PaymentProvider.PAYSTACK)}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex-1 ${
                        paymentProvider === PaymentProvider.PAYSTACK
                          ? 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                          : 'bg-primary text-white shadow-md'
                      }`}
                    >
                      Paystack
                    </button>
                    <button
                      type="button"
                      onClick={() => setPaymentProvider(PaymentProvider.NETAPPSPAY)}
                      className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex-1 ${
                        paymentProvider === PaymentProvider.NETAPPSPAY
                          ? 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                          : 'bg-primary text-white shadow-md'
                      }`}
                    >
                      NetAppsPay
                    </button>
                    {currency === Currency.USD && (
                      <button
                        type="button"
                        onClick={() => setPaymentProvider(PaymentProvider.STRIPE)}
                        className={`px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 flex-1 ${
                          paymentProvider === PaymentProvider.STRIPE
                            ? 'bg-primary text-white shadow-md'
                            : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                        }`}
                      >
                        Stripe
                      </button>
                    )}
                  </div>
                </div>

                <div
                  className="flex justify-between items-center pt-4 border-t border-dashed border-slate-200 dark:border-slate-700">
                  <span className="text-slate-800 dark:text-white font-medium">Total to pay</span>
                  <span className="text-xl font-semibold text-primary">
                    {calculating ? (
                      <span className="text-sm text-slate-500">Calculating...</span>
                    ) : proratedAmountData ? (
                      `${currency === Currency.NGN ? '₦' : '$'}${proratedAmountData.proratedAmount.toLocaleString()}`
                    ) : (
                      `${currency === Currency.NGN ? '₦' : '$'}0`
                    )}
                  </span>
                </div>
              </div>

              {!transactionData ? (
                <Button
                  isLoading={initializing}
                  className="w-full bg-primary hover:bg-primary/90 text-white font-medium py-3 px-4 rounded-xl transition-colors flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={calculating || initializing || !proratedAmountData || proratedAmountData.proratedAmount <= 0}
                  onClick={initializePayment}
                >
                  {initializing ? (
                    <>
                      <span>Initializing Payment...</span>
                    </>
                  ) : (
                    <>
                      <span>Continue to Payment</span>
                    </>
                  )}
                </Button>
              ) : (
                <PaystackButton
                  config={handlePaymentInit()}
                  onSuccess={onSuccess}
                  onClose={onClose}
                  disabled={processing}
                  processing={processing}
                >
                  <span className="flex items-center justify-center">
                    {processing ? (
                      <span className="flex items-center gap-2">
                        Processing...
                      </span>
                    ) : (
                      <>
                        Pay {currency === Currency.NGN ? '₦' : '$'}{proratedAmountData.proratedAmount.toLocaleString()}
                      </>
                    )}
                  </span>
                </PaystackButton>
              )}

              <div className="text-center text-xs text-slate-500 dark:text-slate-400 pt-2">
                Secure payment processed
                by {transactionData ? getPaymentProviderName(transactionData.paymentProvider) : getPaymentProviderName(paymentProvider)}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

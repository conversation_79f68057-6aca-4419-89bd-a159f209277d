'use client';

import {useState} from 'react';
import {format} from 'date-fns';
import {useToast} from '@/components/ui/Toast';
import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {Select} from '@/components/ui/Select';
import {CommentType, TicketPriority, TicketStatus} from '@/types/ticket-types';
import {addTicketComment, updateTicket} from '@/server/actions/support';

interface Member {
  id: string;
  name: string;
}

interface TicketDetailsProps {
  ticket: any;
  statusOptions: { label: string; value: string }[];
  priorityOptions: { label: string; value: string }[];
  members: Member[];
  currentUser: { id: string; name: string; email: string };
  onTicketUpdated: () => Promise<void>; // Callback to refresh ticket data after update
}

export default function TicketDetails({
  ticket,
  statusOptions,
  priorityOptions,
  members,
  currentUser,
  onTicketUpdated
}: TicketDetailsProps) {
  // Internal state for editing mode
  const [editingStatus, setEditingStatus] = useState(false);
  const [editingPriority, setEditingPriority] = useState(false);
  const [editingAssignee, setEditingAssignee] = useState(false);

  // Internal state for field values
  const [status, setStatus] = useState<string>(ticket.status);
  const [priority, setPriority] = useState<string>(ticket.priority);
  const [assigneeValue, setAssigneeValue] = useState<string | null>(ticket.assignedTo?.id || null);
  // State for loading indicators
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [updatingPriority, setUpdatingPriority] = useState(false);
  const [updatingAssignee, setUpdatingAssignee] = useState(false);

  // Toast for notifications
  const toast = useToast();

  // Check if current user is the creator
  const isCreator = ticket.creator?.id === currentUser.id;
  // Check if ticket is assigned to anyone
  const isAssigned = !!ticket.assignedTo;
  // Check if ticket is assigned to current user
  const isAssignedToCurrentUser = ticket.assignedTo?.id === currentUser.id;

  // Get appropriate color for status badge
  const getStatusVariant = (status: TicketStatus): 'default' | 'success' | 'warning' | 'danger' | 'info' => {
    switch(status) {
      case TicketStatus.OPEN:
        return 'info';
      case TicketStatus.IN_PROGRESS:
        return 'warning';
      case TicketStatus.RESOLVED:
        return 'success';
      case TicketStatus.CLOSED:
        return 'default';
      default:
        return 'info';
    }
  };

  // Get appropriate color for priority badge
  const getPriorityVariant = (priority: TicketPriority): 'default' | 'success' | 'warning' | 'danger' | 'info' => {
    switch(priority) {
      case TicketPriority.LOW:
        return 'info';
      case TicketPriority.MEDIUM:
        return 'warning';
      case TicketPriority.HIGH:
        return 'danger';
      case TicketPriority.URGENT:
        return 'danger';
      default:
        return 'info';
    }
  };

  // Format status text
  const formatStatus = (status: string) => {
    return status.replace('_', ' ');
  };

  // Handle status change
  const onStatusChange = (e: string | number | null) => {
    setStatus(e?.toString?.() || "");
  };

  // Handle priority change
  const onPriorityChange = (e: string | number | null) => {
    setPriority(e?.toString?.() || "");
  };

  // Handle assignee change
  const onAssigneeChange = (e: string | null | number) => {
    setAssigneeValue(e?.toString?.() || "");
  };

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!status || status === ticket.status) {
      setEditingStatus(false);
      return;
    }

    setUpdatingStatus(true);
    try {
      const result = await updateTicket({
        id: ticket.id,
        status: status as TicketStatus,
      });

      if (result.success) {
        toast.success('Status Updated', `Ticket status changed to ${status}`);

        // Add an internal comment about the status change
        await addTicketComment({
          ticketId: ticket.id,
          organizationId: ticket.organization?.id,
          content: `Status changed from ${ticket.status} to ${status}`,
          type: CommentType.INTERNAL,
        });

        await onTicketUpdated(); // Refresh data
      } else {
        toast.error('Error', result.error || 'Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setUpdatingStatus(false);
      setEditingStatus(false);
    }
  };

  // Handle priority update
  const handlePriorityUpdate = async () => {
    if (!priority || priority === ticket.priority) {
      setEditingPriority(false);
      return;
    }

    setUpdatingPriority(true);
    try {
      const result = await updateTicket({
        id: ticket.id,
        priority: priority as TicketPriority,
      });

      if (result.success) {
        toast.success('Priority Updated', `Ticket priority changed to ${priority}`);

        // Add an internal comment about the priority change
        await addTicketComment({
          ticketId: ticket.id,
          organizationId: ticket.organization?.id,
          content: `Priority changed from ${ticket.priority} to ${priority}`,
          type: CommentType.INTERNAL,
        });

        await onTicketUpdated(); // Refresh data
      } else {
        toast.error('Error', result.error || 'Failed to update priority');
      }
    } catch (error) {
      console.error('Error updating priority:', error);
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setUpdatingPriority(false);
      setEditingPriority(false);
    }
  };

  // Handle assignee update
  const handleAssigneeUpdate = async () => {
    if (assigneeValue === (ticket.assignedTo?.id || null)) {
      setEditingAssignee(false);
      return;
    }

    setUpdatingAssignee(true);
    try {
      const result = await updateTicket({
        id: ticket.id,
        assignedToId: assigneeValue,
      });

      if (result.success) {
        const newAssigneeName = assigneeValue
          ? (members.find(m => m.id === assigneeValue)?.name || currentUser.name)
          : 'nobody';

        const oldAssigneeName = ticket.assignedTo?.name || 'nobody';

        toast.success(
          'Assignee Updated',
          assigneeValue
            ? `Ticket assigned to ${newAssigneeName}`
            : 'Ticket unassigned'
        );

        // Add an internal comment about the assignee change
        await addTicketComment({
          ticketId: ticket.id,
          organizationId: ticket.organization?.id,
          content: `Assignee changed from ${oldAssigneeName} to ${newAssigneeName}`,
          type: CommentType.INTERNAL,
        });

        await onTicketUpdated(); // Refresh data
      } else {
        toast.error('Error', result.error || 'Failed to update assignee');
      }
    } catch (error) {
      console.error('Error updating assignee:', error);
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setUpdatingAssignee(false);
      setEditingAssignee(false);
    }
  };

  // Handle self-assign
  const handleSelfAssign = async () => {
    if (isAssignedToCurrentUser) {
      return;
    }

    setUpdatingAssignee(true);
    try {
      const result = await updateTicket({
        id: ticket.id,
        assignedToId: currentUser.id,
      });

      if (result.success) {
        toast.success('Success', `Ticket assigned to ${currentUser.name}`);

        // Add an internal comment about self-assignment
        await addTicketComment({
          ticketId: ticket.id,
          organizationId: ticket.organization?.id,
          content: `Ticket self-assigned by ${currentUser.name}`,
          type: CommentType.INTERNAL,
        });

        await onTicketUpdated(); // Refresh data
      } else {
        toast.error('Error', result.error || 'Failed to self-assign ticket');
      }
    } catch (error) {
      console.error('Error self-assigning ticket:', error);
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setUpdatingAssignee(false);
    }
  };

  // Handle unassign
  const handleUnassign = async () => {
    if (!isAssigned) {
      return;
    }

    setUpdatingAssignee(true);
    try {
      const result = await updateTicket({
        id: ticket.id,
        assignedToId: null,
      });

      if (result.success) {
        toast.success('Success', 'Ticket unassigned');

        // Add an internal comment about unassignment
        await addTicketComment({
          ticketId: ticket.id,
          organizationId: ticket.organization?.id,
          content: `Ticket unassigned by ${currentUser.name}`,
          type: CommentType.INTERNAL,
        });

        await onTicketUpdated(); // Refresh data
      } else {
        toast.error('Error', result.error || 'Failed to unassign ticket');
      }
    } catch (error) {
      console.error('Error unassigning ticket:', error);
      toast.error('Error', 'An unexpected error occurred');
    } finally {
      setUpdatingAssignee(false);
    }
  };

  return (
    <div className="space-y-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm p-5 relative overflow-hidden shadow-lg">
      <div className="absolute inset-0 bg-gradient-to-br from-[#6c5ce7]/5 to-transparent opacity-30 pointer-events-none"></div>
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#6c5ce7]/80 via-[#a29bfe]/30 to-transparent"></div>

      <div className="relative">
        <h2 className="text-lg font-semibold text-white/90 mb-5">Ticket Details</h2>

        <div className="space-y-5">
          {/* Status */}
          <div className="group">
            <h3 className="text-[13px] font-medium text-white/50 mb-1.5 flex items-center">
              Status
              <button
                onClick={() => setEditingStatus(!editingStatus)}
                className="ml-1.5 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                </svg>
              </button>
            </h3>

            {editingStatus ? (
              <div className="flex items-center gap-2">
                <Select
                  value={status}
                  onChange={onStatusChange}
                  options={statusOptions}
                />
                <div className="flex items-center gap-1.5">
                  <Button
                    onClick={handleStatusUpdate}
                    className="!p-1.5 min-h-0 h-auto bg-[#6c5ce7]/20 border-[#6c5ce7]/30 hover:bg-[#6c5ce7]/30"
                    variant="outline"
                    loading={updatingStatus}
                    disabled={updatingStatus}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-[#a29bfe]" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"/>
                    </svg>
                  </Button>
                  <Button
                    onClick={() => setEditingStatus(false)}
                    className="!p-1.5 min-h-0 h-auto bg-red-500/10 border-red-500/20 hover:bg-red-500/20"
                    variant="outline"
                    disabled={updatingStatus}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-red-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"/>
                      <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                  </Button>
                </div>
              </div>
            ) : (
              <Badge variant={getStatusVariant(ticket.status)} size="sm" className="text-[11px] uppercase tracking-wider font-semibold">
                {formatStatus(ticket.status)}
              </Badge>
            )}
          </div>

          {/* Priority */}
          <div className="group">
            <h3 className="text-[13px] font-medium text-white/50 mb-1.5 flex items-center">
              Priority
              <button
                onClick={() => setEditingPriority(!editingPriority)}
                className="ml-1.5 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                </svg>
              </button>
            </h3>

            {editingPriority ? (
              <div className="flex items-center gap-2">
                <Select
                  value={priority}
                  onChange={onPriorityChange}
                  options={priorityOptions}
                />
                <div className="flex items-center gap-1.5">
                  <Button
                    onClick={handlePriorityUpdate}
                    className="!p-1.5 min-h-0 h-auto bg-[#6c5ce7]/20 border-[#6c5ce7]/30 hover:bg-[#6c5ce7]/30"
                    variant="outline"
                    loading={updatingPriority}
                    disabled={updatingPriority}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-[#a29bfe]" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"/>
                    </svg>
                  </Button>
                  <Button
                    onClick={() => setEditingPriority(false)}
                    className="!p-1.5 min-h-0 h-auto bg-red-500/10 border-red-500/20 hover:bg-red-500/20"
                    variant="outline"
                    disabled={updatingPriority}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-red-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"/>
                      <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                  </Button>
                </div>
              </div>
            ) : (
              <Badge variant={getPriorityVariant(ticket.priority)} size="sm" className="text-[11px] uppercase tracking-wider font-semibold">
                {ticket.priority}
              </Badge>
            )}
          </div>

          {/* Assignee */}
          <div className="group">
            <h3 className="text-[13px] font-medium text-white/50 mb-1.5 flex items-center">
              Assigned To
              <button
                onClick={() => setEditingAssignee(!editingAssignee)}
                className={`ml-1.5 ${isCreator ? 'opacity-0 group-hover:opacity-100' : 'opacity-0'} transition-opacity`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                </svg>
              </button>
            </h3>

            {editingAssignee ? (
              <div className="flex items-center gap-2">
                <Select
                  value={assigneeValue}
                  onChange={onAssigneeChange}
                  options={isCreator ?
                    [
                      { label: 'Unassigned', value: '' },
                      ...members.map((member) => ({ label: member.name, value: member.id }))
                    ] :
                    [
                      { label: 'Unassigned', value: '' },
                      { label: currentUser.name + ' (You)', value: currentUser.id }
                    ]
                  }
                />
                <div className="flex items-center gap-1.5">
                  <Button
                    onClick={handleAssigneeUpdate}
                    className="!p-1.5 min-h-0 h-auto bg-[#6c5ce7]/20 border-[#6c5ce7]/30 hover:bg-[#6c5ce7]/30"
                    variant="outline"
                    loading={updatingAssignee}
                    disabled={updatingAssignee}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-[#a29bfe]" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"/>
                    </svg>
                  </Button>
                  <Button
                    onClick={() => setEditingAssignee(false)}
                    className="!p-1.5 min-h-0 h-auto bg-red-500/10 border-red-500/20 hover:bg-red-500/20"
                    variant="outline"
                    disabled={updatingAssignee}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-red-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"/>
                      <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 rounded-full bg-[#6c5ce7]/30 flex items-center justify-center text-[#a29bfe]">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <div className="flex items-center gap-2 w-full justify-between">
                  <span className="text-sm text-white/80 flex items-center gap-1">
                    {ticket.assignedTo?.name || 'Unassigned'}
                    {ticket.assignedTo?.id === currentUser.id && (
                      <span className="text-xs text-[#a29bfe] bg-[#6c5ce7]/20 px-1.5 py-0.5 rounded">You</span>
                    )}
                  </span>

                  {/* Show self-assign button if ticket is unassigned and user is not creator */}
                  {!isAssigned && !isCreator && (
                    <Button
                      onClick={handleSelfAssign}
                      className="text-xs py-1 px-2 bg-[#6c5ce7]/20 border-[#6c5ce7]/30 hover:bg-[#6c5ce7]/30 text-[#a29bfe]"
                      variant="outline"
                    >
                      Assign to me
                    </Button>
                  )}

                  {/* Allow unassigning if current user is assigned */}
                  {isAssignedToCurrentUser && !isCreator && (
                    <Button
                      onClick={handleUnassign}
                      className="text-xs py-1 px-2 bg-red-500/10 border-red-500/30 hover:bg-red-500/20 text-red-400"
                      variant="outline"
                    >
                      Unassign
                    </Button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {(ticket.customerName || ticket.customerEmail) && (
        <>
          <div className="h-px bg-gradient-to-r from-transparent via-[#2e2a50]/30 to-transparent my-2"></div>

          <div className="relative">
            <h2 className="text-lg font-semibold text-white/90 mb-3">Customer Information</h2>

            <div className="space-y-4">
              {ticket.customerName && (
                <div>
                  <h3 className="text-[13px] font-medium text-white/50 mb-1.5">Name</h3>
                  <p className="text-sm text-white/80">{ticket.customerName}</p>
                </div>
              )}

              {ticket.customerEmail && (
                <div>
                  <h3 className="text-[13px] font-medium text-white/50 mb-1.5">Email</h3>
                  <p className="text-sm text-white/80 break-all">{ticket.customerEmail}</p>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      <div className="h-px bg-gradient-to-r from-transparent via-[#2e2a50]/30 to-transparent my-2"></div>

      <div className="relative">
        <h2 className="text-lg font-semibold text-white/90 mb-3">Activity</h2>

        <div className="space-y-4">
          <div>
            <h3 className="text-[13px] font-medium text-white/50 mb-1.5">Created By</h3>
            <div className="flex items-center gap-2">
              <div className="w-5 h-5 rounded-full bg-[#6c5ce7]/30 flex items-center justify-center text-[#a29bfe]">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <div>
                <p className="text-sm text-white/80">{ticket.creator?.name || 'Unknown'}</p>
                <p className="text-xs text-white/50">
                  {format(new Date(ticket.createdAt), 'MMM d, yyyy h:mm a')}
                </p>
              </div>
            </div>
          </div>

          {ticket.updatedBy && (
            <div>
              <h3 className="text-[13px] font-medium text-white/50 mb-1.5">Last Updated By</h3>
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 rounded-full bg-[#00cec9]/20 flex items-center justify-center text-[#81ecec]">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 20h9"></path>
                    <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-white/80">{ticket.updatedBy.name}</p>
                  <p className="text-xs text-white/50">
                    {format(new Date(ticket.updatedAt), 'MMM d, yyyy h:mm a')}
                  </p>
                </div>
              </div>
            </div>
          )}

          {ticket.closedBy && (
            <div>
              <h3 className="text-[13px] font-medium text-white/50 mb-1.5">Closed By</h3>
              <div className="flex items-center gap-2">
                <div className="w-5 h-5 rounded-full bg-red-500/20 flex items-center justify-center text-red-400">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M18 6 6 18M6 6l12 12"></path>
                  </svg>
                </div>
                <div>
                  <p className="text-sm text-white/80">{ticket.closedBy.name}</p>
                  <p className="text-xs text-white/50">
                    {format(new Date(ticket.closedAt), 'MMM d, yyyy h:mm a')}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

'use client';

import {useState} from 'react';
import {format} from 'date-fns';
import {Button} from '@/components/ui/Button';
import {Textarea} from '@/components/ui/Textarea';
import {Select} from '@/components/ui/Select';
import {CommentType} from '@/types/ticket-types';

interface Comment {
  id: string;
  content: string;
  type: CommentType;
  createdAt: string;
  creator?: {
    id: string;
    name: string;
  };
}

interface TicketCommentsProps {
  comments: Comment[];
  currentUser: { id: string; name: string; email: string };
  commentType: CommentType;
  setCommentType: (type: CommentType) => void;
  onAddComment: (comment: string) => Promise<void>;
  submitting: boolean;
  commentTypeOptions: { label: string; value: string }[];
}

export default function TicketComments({
  comments,
  currentUser,
  commentType,
  setCommentType,
  onAddComment,
  submitting,
  commentTypeOptions
}: TicketCommentsProps) {
  const [newComment, setNewComment] = useState('');

  const handleCommentTypeChange = (e: any) => {
    setCommentType(e.value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      await onAddComment(newComment);
      setNewComment('');
    }
  };

  return (
    <div className="space-y-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm p-6 relative overflow-hidden shadow-lg animate-in fade-in duration-500">
      <div className="absolute inset-0 bg-gradient-to-br from-[#fd79a8]/5 to-transparent opacity-30 pointer-events-none"></div>
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#fd79a8]/80 via-[#fab1a0]/30 to-transparent"></div>

      <div className="relative">
        <h2 className="text-lg font-semibold text-white/90 mb-4">Comments</h2>

        {comments.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-white/30 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="1" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
              </svg>
            </div>
            <p className="text-white/50 italic">No comments yet</p>
          </div>
        ) : (
          <div className="space-y-4 mb-8">
            {comments.map((comment) => (
              <div
                key={comment.id}
                className={`p-4 rounded-lg border ${
                  comment.type === CommentType.INTERNAL 
                    ? 'bg-[#FFEAA7]/5 border-[#FFEAA7]/20' 
                    : 'bg-[#81ECEC]/5 border-[#81ECEC]/20'
                }`}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white ${
                    comment.type === CommentType.INTERNAL ? 'bg-[#FDCB6E]/80' : 'bg-[#00CEC9]/80'
                  }`}>
                    {comment.creator ? (
                      <span className="text-xs font-medium">
                        {comment.creator.name.substring(0, 2).toUpperCase()}
                      </span>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center">
                      <p className="font-medium text-white/90">
                        {comment.creator?.name || 'System'}
                      </p>
                      {comment.type === CommentType.INTERNAL && (
                        <span className="ml-2 text-xs font-medium px-1.5 py-0.5 rounded-sm bg-[#FFEAA7]/20 text-[#FFEAA7]">
                          Internal Note
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-white/50">
                      {format(new Date(comment.createdAt), 'MMM d, yyyy h:mm a')}
                    </p>
                  </div>
                </div>

                <div className="ml-11 text-white/80 whitespace-pre-line">
                  {comment.content}
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="pt-4 border-t border-[#2e2a50]/30">
          <h3 className="text-lg font-semibold text-white/90 mb-3">Add a Comment</h3>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <Textarea
                name="comment"
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                rows={4}
                className="w-full bg-[#131129]/80 border-[#fd79a8]/20 focus:border-[#fd79a8] focus:shadow-[0_0_12px_rgba(253,121,168,0.3)] rounded-lg transition-all duration-200"
                placeholder="Type your comment here..."
              />
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-between items-center">
              <Select
                value={commentType}
                onChange={handleCommentTypeChange}
                options={commentTypeOptions}
              />

              <Button
                type="submit"
                variant="default"
                disabled={submitting || !newComment.trim()}
                loading={submitting}
                className="w-full sm:w-auto bg-gradient-to-r from-[#fd79a8] to-[#fd79a8]/80 hover:from-[#fd79a8] hover:to-[#fabebe] shadow-lg hover:shadow-[0_0_20px_rgba(253,121,168,0.4)] transition-all duration-300 px-6 "
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="mr-1.5 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
                Add Comment
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

'use client';

import {useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {useToast} from '@/components/ui/Toast';
import {addTicketComment, getOrganizationTickets, getTicketById, updateTicket} from '@/server/actions/support';
import {CommentType, TicketPriority, TicketStatus} from '@/types/ticket-types';
import {TicketComments, TicketDescription, TicketDetails, TicketHeader} from './components';
import { logger } from '@/utils/logger';

// Dropdown options
const statusOptions = [
  {label: 'Open', value: TicketStatus.OPEN},
  {label: 'In Progress', value: TicketStatus.IN_PROGRESS},
  {label: 'Resolved', value: TicketStatus.RESOLVED},
  {label: 'Closed', value: TicketStatus.CLOSED},
];

const priorityOptions = [
  {label: 'Low', value: TicketPriority.LOW},
  {label: 'Medium', value: TicketPriority.MEDIUM},
  {label: 'High', value: TicketPriority.HIGH},
  {label: 'Urgent', value: TicketPriority.URGENT},
];

const commentTypeOptions = [
  {label: 'Public - Visible to customer', value: CommentType.PUBLIC},
  {label: 'Internal - Staff only', value: CommentType.INTERNAL},
];

export default function TicketDetailPage() {
  const params = useParams();
  const router = useRouter();
  const toast = useToast();

  const organizationId = params.id as string;
  const ticketId = params.ticketId as string;

  // Ticket data state
  const [ticket, setTicket] = useState<any>(null);
  const [comments, setComments] = useState<any[]>([]);
  const [members, setMembers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentUser, setCurrentUser] = useState<any>(null);

  // New comment state
  const [commentType, setCommentType] = useState(CommentType.PUBLIC);
  const [submittingComment, setSubmittingComment] = useState(false);

  // Fetch ticket data
  const fetchTicket = async () => {
    setLoading(true);
    try {
      const result = await getTicketById(ticketId);

      if (result.success && result.data) {
        setTicket(result.data.ticket);
        setComments(result.data.comments);
        setCurrentUser(result.data.currentUser);
      } else {
        toast.error('Failed to load ticket', result.error || 'Unable to retrieve ticket details');
      }
    } catch (error) {
      logger.error('Error loading ticket:', error);
      toast.error('An unexpected error occurred', 'Please try again later');
    } finally {
      setLoading(false);
    }
  };

  // Fetch members for assignee dropdown
  const fetchMembers = async () => {
    try {
      // This is a simplified version - in a real app, you'd fetch this data
      const result = await getOrganizationTickets({
        organizationId,
        limit: 1, // Just to get the members list
      });

      if (result.success && result.data && result.data.members) {
        setMembers(result.data.members);
      }
    } catch (error) {
      logger.error('Error fetching members:', error);
    }
  };

  // Add a comment to the ticket
  const handleAddComment = async (comment: string) => {
    if (!comment.trim()) {
      return;
    }

    setSubmittingComment(true);

    try {
      const result = await addTicketComment({
        ticketId,
        organizationId,
        content: comment,
        type: commentType,
      });

      if (result.success) {
        toast.success('Comment Added', 'Your comment has been added');

        await fetchTicket(); // Refresh data
      } else {
        toast.error('Failed to add comment', result.error || 'Unable to save your comment');
      }
    } catch (error) {
      logger.error('Error adding comment:', error);
      toast.error('An unexpected error occurred', 'Please try again later');
    } finally {
      setSubmittingComment(false);
    }
  };

  // Close ticket confirmation
  const confirmCloseTicket = () => {
    toast.confirm({
      message: 'Are you sure you want to close this ticket?',
      header: 'Close Confirmation',
      accept: () => handleCloseTicket(),
    });
  };

  // Close ticket
  const handleCloseTicket = async () => {
    try {
      const result = await updateTicket({
        id: ticketId,
        status: TicketStatus.CLOSED,
      });

      if (result.success) {
        toast.success('Ticket Closed', 'The ticket has been closed');

        // Add an internal comment about the ticket being closed
        await addTicketComment({
          ticketId,
          organizationId,
          content: 'Ticket has been closed',
          type: CommentType.INTERNAL,
        });

        await fetchTicket(); // Refresh data
      } else {
        toast.error('Failed to close ticket', result.error || 'Unable to close this ticket');
      }
    } catch (error) {
      logger.error('Error closing ticket:', error);
      toast.error('An unexpected error occurred', 'Please try again later');
    }
  };

  // Reopen ticket confirmation
  const confirmReopenTicket = () => {
    toast.confirm({
      message: 'Are you sure you want to reopen this ticket?',
      header: 'Reopen Confirmation',
      accept: () => handleReopenTicket(),
    });
  };

  // Reopen ticket
  const handleReopenTicket = async () => {
    try {
      const result = await updateTicket({
        id: ticketId,
        status: TicketStatus.OPEN,
      });

      if (result.success) {
        toast.success('Ticket Reopened', 'The ticket has been reopened');

        // Add an internal comment about the ticket being reopened
        await addTicketComment({
          ticketId,
          organizationId,
          content: 'Ticket has been reopened',
          type: CommentType.INTERNAL,
        });

        await fetchTicket(); // Refresh data
      } else {
        toast.error('Failed to reopen ticket', result.error || 'Unable to reopen this ticket');
      }
    } catch (error) {
      logger.error('Error reopening ticket:', error);
      toast.error('An unexpected error occurred', 'Please try again later');
    }
  };

  // Initialize
  useEffect(() => {
    fetchTicket();
    fetchMembers();
  }, [ticketId, organizationId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-[#0F0F1A]">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-t-blue-500 border-r-blue-500 border-b-blue-200 border-l-blue-200 rounded-full animate-spin"></div>
          <p className="mt-4 text-white/50">Loading ticket details...</p>
        </div>
      </div>
    );
  }

  // If ticket not found
  if (!ticket) {
    return (
      <div className="flex justify-center items-center min-h-screen p-4 bg-[#0F0F1A]">
        <div
          className="max-w-md w-full p-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm shadow-lg">
          <div className="text-center mb-6">
            <div
              className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-500/10 text-red-400 mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="12"/>
                <line x1="12" y1="16" x2="12.01" y2="16"/>
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-white/90 mb-2">Ticket Not Found</h2>
            <p className="text-white/60 mb-4">The ticket you're looking for doesn't exist or you don't have permission
              to view it.</p>
            <button
              onClick={() => router.push(`/dashboard/organization/${organizationId}/support`)}
              className="px-4 py-2 bg-[#6c5ce7]/20 border border-[#6c5ce7]/30 rounded-lg text-[#a29bfe] text-sm font-semibold hover:bg-[#6c5ce7]/30 transition-colors"
            >
              Back to Support Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Ensure ticket has the organization id for routing
  if (!ticket.organization) {
    ticket.organization = {id: organizationId};
  }

  return (
    <div className="min-h-screen bg-[#0F0F1A] p-6">
      {/* Ticket header with title and actions */}
      <TicketHeader
        ticket={ticket}
        confirmCloseTicket={confirmCloseTicket}
        confirmReopenTicket={confirmReopenTicket}
      />

      <div className="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column: ticket details */}


        {/* Right column: description and comments */}
        <div className="lg:col-span-2 space-y-6">
          <TicketDescription description={ticket.description}/>

          <TicketComments
            comments={comments}
            currentUser={currentUser}
            commentType={commentType}
            setCommentType={setCommentType}
            commentTypeOptions={commentTypeOptions}
            onAddComment={handleAddComment}
            submitting={submittingComment}
          />
        </div>
        <div className="lg:col-span-1 space-y-6">
          <TicketDetails
            ticket={ticket}
            statusOptions={statusOptions}
            priorityOptions={priorityOptions}
            members={members}
            currentUser={currentUser}
            onTicketUpdated={fetchTicket}
          />
        </div>
      </div>
    </div>
  );
}

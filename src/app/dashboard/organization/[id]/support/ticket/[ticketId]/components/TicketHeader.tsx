'use client';

import {useRouter} from 'next/navigation';
import {formatDistanceToNow} from 'date-fns';
import {Button} from '@/components/ui/Button';
import {TicketStatus} from '@/types/ticket-types';

interface TicketHeaderProps {
  ticket: {
    id: string;
    title: string;
    status: TicketStatus;
    createdAt: string;
    organization: {
      id: string;
    };
  };
  confirmCloseTicket: () => void;
  confirmReopenTicket: () => void;
}

export default function TicketHeader({
  ticket,
  confirmCloseTicket,
  confirmReopenTicket
}: TicketHeaderProps) {
  const router = useRouter();

  return (
    <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6 animate-in fade-in duration-300">
      <div className="flex-1">
        <h1 className="text-2xl font-bold text-white/90 mb-1 leading-tight">{ticket.title}</h1>
        <div className="flex items-center flex-wrap gap-x-3 gap-y-1 text-sm">
          <span className="text-[#6c5ce7] font-medium">Ticket #{ticket.id.substring(0, 8)}</span>
          <span className="text-white/40">•</span>
          <span className="text-white/60">
            Created {formatDistanceToNow(new Date(ticket.createdAt), { addSuffix: true })}
          </span>
        </div>
      </div>

      <div className="flex items-center gap-3 self-end lg:self-auto">
        {(ticket.status === TicketStatus.CLOSED || ticket.status === TicketStatus.RESOLVED) ? (
          <Button
            variant="outline"
            className="border-[#6c5ce7]/20 bg-[#131129]/40 text-[#a29bfe] hover:bg-[#6c5ce7]/10 hover:border-[#a29bfe]/40 transition-all duration-200"
            onClick={confirmReopenTicket}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="mr-1.5 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38" />
            </svg>
            Reopen
          </Button>
        ) : (
          <Button
            variant="outline"
            className="border-red-400/20 bg-[#131129]/40 text-red-400 hover:bg-red-500/10 hover:border-red-400/40 transition-all duration-200"
            onClick={confirmCloseTicket}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="mr-1.5 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 6 6 18M6 6l12 12" />
            </svg>
            Close
          </Button>
        )}

        <Button
          variant="outline"
          className="border-white/10 bg-[#131129]/40 text-white/70 hover:bg-white/5 hover:text-white hover:border-white/20 transition-all duration-200"
          onClick={() => router.push(`/dashboard/organization/${ticket.organization.id}/support`)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="mr-1.5 h-4 w-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <path d="m12 19-7-7 7-7M5 12h14" />
          </svg>
          Back
        </Button>
      </div>
    </div>
  );
}

'use client';

interface TicketDescriptionProps {
  description: string;
  tags?: string[];
}

export default function TicketDescription({ description, tags }: TicketDescriptionProps) {
  return (
    <div className="space-y-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm p-6 relative overflow-hidden shadow-lg animate-in fade-in duration-300">
      <div className="absolute inset-0 bg-gradient-to-br from-[#00cec9]/5 to-transparent opacity-30 pointer-events-none"></div>
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00cec9]/80 via-[#81ecec]/30 to-transparent"></div>

      <div className="relative">
        <h2 className="text-lg font-semibold text-white/90 mb-4">Description</h2>
        <div className="text-white/80 leading-relaxed whitespace-pre-line">
          {description}
        </div>

        {tags && tags.length > 0 && (
          <div className="mt-6 pt-6 border-t border-[#2e2a50]/30">
            <h3 className="text-[13px] font-medium text-white/50 mb-3">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#00cec9]/10 text-[#81ecec]"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

'use client';

import {useCallback, useMemo, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {Textarea} from '@/components/ui/Textarea';
import {Select} from '@/components/ui/Select';
import {useToast} from '@/components/ui/Toast';
import PageHeader from '@/components/dashboard/PageHeader';
import {createTicket} from '@/server/actions/support';
import {TicketPriority} from '@/types/ticket-types';
import { logger } from '@/utils/logger';

const priorityOptions = [
  { label: 'Low', value: TicketPriority.LOW },
  { label: 'Medium', value: TicketPriority.MEDIUM },
  { label: 'High', value: TicketPriority.HIGH },
  { label: 'Urgent', value: TicketPriority.URGENT },
];

export default function NewTicketPage() {
  const params = useParams();
  const router = useRouter();
  const { success, error: showError } = useToast();
  const organizationId = params.id as string;

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState(TicketPriority.MEDIUM);
  const [customerName, setCustomerName] = useState('');
  const [customerEmail, setCustomerEmail] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState('');

  // Loading state
  const [submitting, setSubmitting] = useState(false);

  // Form validation
  const [errors, setErrors] = useState<{
    title?: string;
    description?: string;
    customerEmail?: string;
  }>({});

  const validateForm = useCallback(() => {
    const newErrors: {
      title?: string;
      description?: string;
      customerEmail?: string;
    } = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (customerEmail && !/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(customerEmail)) {
      newErrors.customerEmail = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [title, description, customerEmail]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitting(true);

    try {
      const result = await createTicket({
        organizationId,
        title,
        description,
        priority,
        customerName,
        customerEmail,
        tags,
      });

      if (result.success) {
        success('Success', 'Ticket created successfully');

        // Redirect to ticket view page after a short delay
        setTimeout(() => {
          router.push(`/dashboard/organization/${organizationId}/support/ticket/${result.data.id}`);
        }, 1500);
      } else {
        showError('Error', result.error || 'Failed to create ticket');
      }
    } catch (err) {
      logger.error('Error creating ticket:', err);
      showError('Error', 'An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  }, [title, description, organizationId, priority, customerName, customerEmail, tags, validateForm, success, showError, router]);

  // Add a tag
  const addTag = useCallback(() => {
    if (tagInput.trim() && !tags.includes(tagInput.trim())) {
      setTags([...tags, tagInput.trim()]);
      setTagInput('');
    }
  }, [tagInput, tags]);

  // Remove a tag
  const removeTag = useCallback((tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  }, [tags]);

  // Handle tag input key down
  const handleTagKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  }, [addTag]);

  // Navigate back
  const handleCancel = useCallback(() => {
    router.push(`/dashboard/organization/${organizationId}/support`);
  }, [router, organizationId]);

  const TagsSection = useMemo(() => (
    <div className="mt-2">
      <div className="flex flex-wrap gap-2 mb-2">
        {tags.map(tag => (
          <div
            key={tag}
            className="inline-flex items-center px-3 py-1 bg-[#6c5ce7]/20 text-[#a29bfe] rounded-full text-sm group transition-all hover:bg-[#6c5ce7]/30"
          >
            <span>{tag}</span>
            <button
              type="button"
              onClick={() => removeTag(tag)}
              className="ml-2 opacity-70 hover:opacity-100 focus:outline-none transition-opacity duration-200"
              aria-label={`Remove tag ${tag}`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        ))}
      </div>
    </div>
  ), [tags, removeTag]);

  return (
    <div className="p-6 md:p-8 animate-in fade-in duration-500">
      <PageHeader
        title="Create New Ticket"
        subtitle="Submit a new support ticket for your organization"
      />

      <div className="max-w-4xl mx-auto">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Ticket Details */}
          <div className="space-y-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm p-6 relative overflow-hidden shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-[#6c5ce7]/5 to-transparent opacity-30 pointer-events-none"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#6c5ce7]/80 via-[#a29bfe]/30 to-transparent"></div>

            <div className="relative">
              <h2 className="text-lg font-semibold text-white/90 mb-4">Ticket Details</h2>

              <div className="mb-4">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title <span className="text-red-500">*</span>
                </label>
                <Input
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="w-full bg-[#131129]/80 border-[#6c5ce7]/20 focus:border-[#6c5ce7] focus:shadow-[0_0_12px_rgba(108,92,231,0.3)] rounded-lg transition-all duration-200"
                  placeholder="Brief summary of the issue"
                />
                {errors.title && (
                  <div className="text-red-500 text-sm mt-1">{errors.title}</div>
                )}
              </div>

              <div className="mb-4">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description <span className="text-red-500">*</span>
                </label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={5}
                  className="w-full bg-[#131129]/80 border-[#6c5ce7]/20 focus:border-[#6c5ce7] focus:shadow-[0_0_12px_rgba(108,92,231,0.3)] rounded-lg transition-all duration-200"
                  placeholder="Detailed explanation of the issue"
                />
                {errors.description && (
                  <div className="text-red-500 text-sm mt-1">{errors.description}</div>
                )}
              </div>

              <div className="mb-4">
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Priority
                </label>
                <Select
                  id="priority"
                  value={priority}
                  onChange={(value) => value !== null && setPriority(value as TicketPriority)}
                  options={priorityOptions}
                />
              </div>
            </div>
          </div>

          {/* Customer Information */}
          <div className="space-y-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm p-6 relative overflow-hidden shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-[#00cec9]/5 to-transparent opacity-30 pointer-events-none"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#00cec9]/80 via-[#81ecec]/30 to-transparent"></div>

            <div className="relative">
              <h2 className="text-lg font-semibold text-white/90 mb-2">Customer Information</h2>
              <p className="text-xs text-white/50 mb-4">Optional details about the customer related to this ticket</p>

              <div className="mb-4">
                <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Customer Name
                </label>
                <Input
                  id="customerName"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  className="w-full bg-[#131129]/80 border-[#00cec9]/20 focus:border-[#00cec9] focus:shadow-[0_0_12px_rgba(0,206,201,0.3)] rounded-lg transition-all duration-200"
                  placeholder="Name of the customer if applicable"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Customer Email
                </label>
                <Input
                  id="customerEmail"
                  value={customerEmail}
                  onChange={(e) => setCustomerEmail(e.target.value)}
                  className="w-full bg-[#131129]/80 border-[#00cec9]/20 focus:border-[#00cec9] focus:shadow-[0_0_12px_rgba(0,206,201,0.3)] rounded-lg transition-all duration-200"
                  placeholder="Email of the customer if applicable"
                />
                {errors.customerEmail && (
                  <div className="text-red-500 text-sm mt-1">{errors.customerEmail}</div>
                )}
              </div>
            </div>
          </div>

          {/* Tags Section */}
          <div className="space-y-6 rounded-xl border border-[#2e2a50]/30 bg-[#131129]/30 backdrop-blur-sm p-6 relative overflow-hidden shadow-lg">
            <div className="absolute inset-0 bg-gradient-to-br from-[#fd79a8]/5 to-transparent opacity-30 pointer-events-none"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#fd79a8]/80 via-[#fab1a0]/30 to-transparent"></div>

            <div className="relative">
              <h2 className="text-lg font-semibold text-white/90 mb-2">Categorization</h2>
              <p className="text-xs text-white/50 mb-4">Help organize this ticket with relevant tags</p>

              <div className="mb-4">
                <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tags
                </label>
                <div className="flex gap-2">
                  <Input
                    id="tags"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyDown={handleTagKeyDown}
                    className="w-full bg-[#131129]/80 border-[#fd79a8]/20 focus:border-[#fd79a8] focus:shadow-[0_0_12px_rgba(253,121,168,0.3)] rounded-lg transition-all duration-200"
                    placeholder="Add tags and press Enter"
                  />
                  <Button
                    type="button"
                    onClick={addTag}
                    variant="outline"
                    className="border-[#fd79a8]/20 bg-[#131129]/50 text-[#fd79a8] hover:bg-[#fd79a8]/10 hover:border-[#fd79a8]/50 transition-all duration-200"
                    aria-label="Add tag"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                  </Button>
                </div>
                <div className="text-xs text-white/50 mt-1">Press Enter to add each tag</div>
                {TagsSection}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 py-4">
            <Button
              type="button"
              variant="ghost"
              onClick={handleCancel}
              disabled={submitting}
              className="text-white/70 hover:text-white hover:bg-white/5 transition-colors duration-200 px-6"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={submitting}
              variant="primary"
              className="bg-gradient-to-r from-[#6c5ce7] to-[#6c5ce7]/80 hover:from-[#6c5ce7] hover:to-[#a29bfe] shadow-lg hover:shadow-[0_0_20px_rgba(108,92,231,0.4)] transition-all duration-300 px-6"
            >
              <span>Create Ticket</span>
              {!submitting && (
                <svg className="ml-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="9 18 15 12 9 6"></polyline>
                </svg>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

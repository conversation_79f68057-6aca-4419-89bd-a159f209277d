'use client';

import {memo, useCallback, useMemo, useRef, useState, useEffect} from 'react';
import {useParams, useSearchParams} from 'next/navigation';
import {Button} from '@/components/ui/Button';
import {Select} from '@/components/ui/Select';
import {Input} from '@/components/ui/Input';
import {Card} from '@/components/ui/Card';
import {useToast} from '@/components/ui/Toast';
import PageHeader from '@/components/dashboard/PageHeader';
import {TicketPriority, TicketStatus} from '@/types/ticket-types';
import ProductUserManagement from './components/ProductUserManagement';
import SupportTicketsTab from './components/SupportTicketsTab';
import PortalSetupRequired from './components/PortalSetupRequired';
import {getSupportPortal} from '@/server/actions/support-portal';
import {Settings, ExternalLink, AlertCircle, CheckCircle} from 'lucide-react';
import ProductCode from '@scripts/ProductCodes.json'

const ticketStatusOptions = [
  { label: 'All', value: '' },
  { label: 'Open', value: TicketStatus.OPEN },
  { label: 'In Progress', value: TicketStatus.IN_PROGRESS },
  { label: 'Resolved', value: TicketStatus.RESOLVED },
  { label: 'Closed', value: TicketStatus.CLOSED },
];

const ticketPriorityOptions = [
  { label: 'All', value: '' },
  { label: 'Low', value: TicketPriority.LOW },
  { label: 'Medium', value: TicketPriority.MEDIUM },
  { label: 'High', value: TicketPriority.HIGH },
  { label: 'Urgent', value: TicketPriority.URGENT },
];

// Move StatsCards component outside the main component function and memoize it
const StatsCard = memo(({ title, count, icon, iconColor, titleColor, borderColor }: {
  title: string;
  count: number;
  icon: string;
  iconColor: string;
  titleColor: string;
  borderColor?: string;
}) => (
  <div className="bg-opacity-10 bg-gradient-to-br from-[#2A2A3C] to-[#1C1C28] rounded-xl p-4 border border-[#323074]/20 shadow-xl animate-fadeIn relative overflow-hidden group">
    <div className="absolute inset-0 bg-gradient-to-r from-[#E0D7FF]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    <div className="flex items-center gap-4">
      <div className={`flex justify-center items-center w-12 h-12 rounded-xl bg-[#323074]/20 border ${borderColor || 'border-[#5451B8]/20'}`}>
        <i className={`${icon} text-xl ${iconColor}`} />
      </div>
      <div>
        <h3 className={`${titleColor} text-lg font-medium`}>{title}</h3>
        <p className="text-white text-xl font-semibold">{count} tickets</p>
      </div>
    </div>
  </div>
));

StatsCard.displayName = 'StatsCard';

// Extract FilterControls component and memoize it
type FilterControlsProps = {
  searchInput: string;
  setSearchInput: (value: string) => void;
  status: string;
  setStatus: (value: string) => void;
  priority: string;
  setPriority: (value: string) => void;
  applyFilters: () => void;
  goToNewTicket: () => void;
}

const FilterControls = memo(({
  searchInput,
  setSearchInput,
  status,
  setStatus,
  priority,
  setPriority,
  applyFilters,
  goToNewTicket
}: FilterControlsProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
  }, [setSearchInput]);

  const handleStatusChange = useCallback((value: string | number | null) => {
    if (value !== null) setStatus(value.toString());
  }, [setStatus]);

  const handlePriorityChange = useCallback((value: string | number | null) => {
    if (value !== null) setPriority(value.toString());
  }, [setPriority]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      applyFilters();
    }
  }, [applyFilters]);

  return (
    <div className="mb-8 animate-fadeIn">
      <div className="bg-opacity-10 bg-gradient-to-br from-[#2A2A3C] to-[#1C1C28] rounded-xl p-5 border border-[#323074]/20 shadow-xl">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="w-full md:w-64 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="pi pi-search text-[#998EF8]/70" />
            </div>
            <Input
              ref={searchInputRef}
              value={searchInput}
              onChange={handleSearchChange}
              placeholder="Search tickets..."
              className="w-full pl-10 bg-[#1E1E2E] border-[#323074]/30 focus:border-[#998EF8] focus:shadow-[0_0_0_1px_rgba(153,142,248,0.3)] rounded-lg"
              onKeyDown={handleKeyDown}
            />
          </div>

          <div className="w-full md:w-40">
            <Select
              value={status}
              onChange={handleStatusChange}
              options={ticketStatusOptions}
              placeholder="Status"
            />
          </div>

          <div className="w-full md:w-40">
            <Select
              value={priority}
              onChange={handlePriorityChange}
              options={ticketPriorityOptions}
              placeholder="Priority"
            />
          </div>

          <div className="flex gap-3 w-full md:w-auto md:ml-auto">
            <Button
              label="Apply Filters"
              icon="pi pi-filter"
              onClick={applyFilters}
              className="w-full md:w-auto shadow-md"
              variant="primary"
            />

            <Button
              label="New Ticket"
              icon="pi pi-plus"
              onClick={goToNewTicket}
              className="w-full md:w-auto shadow-md"
              variant="primary"
            />
          </div>
        </div>
      </div>
    </div>
  );
});

FilterControls.displayName = 'FilterControls';

// Memoized ProductUserManagement wrapper to prevent rerenders
const MemoizedProductUserManagement = memo(({
  organizationId
}: {
  organizationId: string
}) => {
  return (
    <ProductUserManagement
      product={ProductCode['CUSTOMER-SUPPORT']}
      organizationId={organizationId}
    />
  );
});

MemoizedProductUserManagement.displayName = 'MemoizedProductUserManagement';

// Main component
export default function OrganizationSupportPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const organizationId = params.id as string;
  const { success, error: showError } = useToast();

  // Portal status for operational display only
  const [portalStatus, setPortalStatus] = useState<{
    exists: boolean;
    isPublished: boolean;
    isConfigured: boolean;
    setupComplete: boolean;
    hasRequiredFields: boolean;
    portalUrl: string;
    loading: boolean;
  }>({
    exists: false,
    isPublished: false,
    isConfigured: false,
    setupComplete: false,
    hasRequiredFields: false,
    portalUrl: '',
    loading: true,
  });

  // Get active tab from URL - derived state (back to 2 tabs)
  const activeTabIndex = useMemo(() => {
    const tab = searchParams.get('tab');
    if (tab === 'users') return 1;
    return 0;
  }, [searchParams]);

  // Load portal status for access control and operational display
  useEffect(() => {
    const loadPortalStatus = async () => {
      try {
        const result = await getSupportPortal(organizationId);
        if (result.success && result.data) {
          const portal = result.data;

          // Validate required fields for basic portal functionality
          const hasRequiredFields = !!(
            portal.branding?.companyName &&
            portal.content?.contactEmail &&
            portal.content?.welcomeMessage &&
            portal.defaultSubdomain
          );

          // Validate complete configuration for full functionality
          const isConfigured = !!(
            hasRequiredFields &&
            portal.branding?.primaryColor &&
            portal.content?.helpText
          );

          // Setup is complete when portal exists and is configured (publication status doesn't affect admin access)
          const setupComplete = isConfigured;

          // Use correct domain format: subdomain-support.newinstance.com (hyphen, not dot)
          const portalUrl = portal.customDomain
            ? `https://${portal.customDomain}`
            : `https://${portal.defaultSubdomain}-support.newinstance.com`;

          setPortalStatus({
            exists: true,
            isPublished: portal.isPublished,
            isConfigured,
            hasRequiredFields,
            setupComplete,
            portalUrl,
            loading: false,
          });
        } else {
          setPortalStatus({
            exists: false,
            isPublished: false,
            isConfigured: false,
            hasRequiredFields: false,
            setupComplete: false,
            portalUrl: '',
            loading: false,
          });
        }
      } catch (error) {
        console.error('Error loading portal status:', error);
        setPortalStatus({
          exists: false,
          isPublished: false,
          isConfigured: false,
          hasRequiredFields: false,
          setupComplete: false,
          portalUrl: '',
          loading: false,
        });
      }
    };

    loadPortalStatus();
  }, [organizationId]);

  // Update URL when tab changes - this function doesn't use state
  const handleTabChange = useCallback((tabIndex: number) => {
    const tabParam = tabIndex === 1 ? 'users' : 'tickets';
    const newUrl = `/dashboard/organization/${organizationId}/support?tab=${tabParam}`;
    window.history.pushState({}, '', newUrl);
  }, [organizationId]);




  // Portal status banner for operational awareness (only shown when setup is complete)
  const PortalStatusBanner = useMemo(() => {
    if (!portalStatus.setupComplete) return null;

    // Show different banner based on publication status
    if (portalStatus.isPublished) {
      return (
        <Card className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200">
                  Customer Portal Active
                </h4>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Your portal is live and accepting customer tickets at {portalStatus.portalUrl}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(portalStatus.portalUrl, '_blank')}
                className="flex items-center border-green-300 text-green-700 hover:bg-green-100"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Visit Portal
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = `/dashboard/organization/${organizationId}/settings?tab=support`}
                className="flex items-center border-green-300 text-green-700 hover:bg-green-100"
              >
                <Settings className="w-4 h-4 mr-2" />
                Manage
              </Button>
            </div>
          </div>
        </Card>
      );
    } else {
      return (
        <Card className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              <div>
                <h4 className="font-medium text-blue-800 dark:text-blue-200">
                  Portal Configured but Not Published
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  Your portal is configured but not yet live. Publish it to allow customers to submit tickets.
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = `/dashboard/organization/${organizationId}/settings?tab=support`}
                className="flex items-center border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                <Settings className="w-4 h-4 mr-2" />
                Publish Portal
              </Button>
            </div>
          </div>
        </Card>
      );
    }
  }, [portalStatus, organizationId]);

  // Render appropriate tab based on active index - derived state
  const TabContent = useMemo(() => {
    if (activeTabIndex === 0) {
      return <SupportTicketsTab organizationId={organizationId} portalStatus={portalStatus} />;
    } else if (activeTabIndex === 1) {
      return (
        <div className="mt-6">
          <MemoizedProductUserManagement organizationId={organizationId} />
        </div>
      );
    }
    return null;
  }, [activeTabIndex, organizationId, portalStatus]);

  // Memoize tab navigation to prevent re-renders
  const TabNavigation = useMemo(() => (
    <div className="mb-6 border-b border-[#E0D7FF] dark:border-[#2a2a38]">
      <div className="flex overflow-x-auto">
        <button
          className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
            activeTabIndex === 0
              ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
              : 'text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-indigo-600 dark:hover:text-indigo-400'
          }`}
          onClick={() => handleTabChange(0)}
        >
          Support Tickets
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm whitespace-nowrap ${
            activeTabIndex === 1
              ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
              : 'text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-indigo-600 dark:hover:text-indigo-400'
          }`}
          onClick={() => handleTabChange(1)}
        >
          Support User Management
        </button>
      </div>
    </div>
  ), [activeTabIndex, handleTabChange]);

  // Loading state
  if (portalStatus.loading) {
    return (
      <div className="p-6 md:p-8 animate-fadeIn">
        <PageHeader title="Support Dashboard" subtitle="Manage tickets and product access" />
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  // Portal setup required - block access to support features
  if (!portalStatus.setupComplete) {
    return (
      <div className="p-6 md:p-8 animate-fadeIn">
        <PageHeader title="Support Dashboard" subtitle="Customer portal setup required" />
        <PortalSetupRequired organizationId={organizationId} portalStatus={portalStatus} />
      </div>
    );
  }

  // Full support dashboard - portal setup is complete
  return (
    <div className="p-6 md:p-8 animate-fadeIn">
      <PageHeader title="Support Dashboard" subtitle="Manage customer support and tickets" />

      {/* Portal Status Banner */}
      {PortalStatusBanner}

      {/* Tabs Navigation */}
      {TabNavigation}

      {/* Tab Content */}
      <div className="animate-fadeIn">
        {TabContent}
      </div>
    </div>
  );
}

'use client';

import {useEffect, useState} from 'react';
import {Dialog} from '@/components/ui/Dialog';
import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {useToast} from '@/components/ui/Toast';
import {Controller, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {inviteProductUser} from '@/server/actions/user-management';
import {SelectUserPermissionGroup} from "@components/ui/SelectUserPermissionGroup";

// Form validation schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  permissionGroupId: z.string().min(1, 'Please select a role'),
  productCode: z.string().min(1, 'Please select a product')
});

type FormValues = z.infer<typeof formSchema>;

interface AddUserToProductModalProps {
  visible: boolean;
  onHide: () => void;
  organizationId: string;
  onSuccess: () => void;
  productCode: string;
}

export default function AddUserToProductModal({
                                                visible,
                                                onHide,
                                                organizationId,
                                                productCode,
                                                onSuccess,
                                              }: AddUserToProductModalProps) {
  const {success, error: showError} = useToast();
  const [loading, setLoading] = useState(false);

  const {
    control,
    handleSubmit,
    reset,
    formState: {errors},
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      permissionGroupId: '',
      productCode: productCode || '',
    },
  });

  // Reset form when modal is closed
  useEffect(() => {
    if (!visible) {
      reset();
    }
  }, [visible, reset]);

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    try {
      // Parse the role value to determine type
      // Prepare invite data based on role type
      const inviteData: {
        organizationId: string;
        productCode: string;
        email: string;
        permissionGroupId: string;
      } = {
        organizationId,
        productCode,
        permissionGroupId: data.permissionGroupId,
        email: data.email,
      };


      const result = await inviteProductUser(inviteData); // Type assertion as a temporary fix

      if (result.success) {
        success(
          "Success",
          result.data?.email
            ? `Invitation sent to ${result.data.email}`
            : 'User added to product successfully'
        );
        onSuccess();
        onHide();
      } else {
        showError(
          "Error",
          result.error || 'Failed to add user to product'
        );
      }
    } catch (err) {
      showError(
        "Error",
        'An unexpected error occurred'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={visible}
      onOpenChange={(open) => !open && onHide()}
      title="Invite User to Product Access"
    >
      <div className="mb-4 text-sm text-gray-600 dark:text-gray-300">
        Add a user to gain specific access to this product. If {"they're"} new to the platform, {"they'll"} receive an
        email invitation.
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-5 pt-2">
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
            </svg>
            User Information
          </h3>
          <div className="space-y-2">
            <label htmlFor="email" className="block text-sm font-medium">
              Email Address
            </label>
            <Controller
              name="email"
              control={control}
              render={({field}) => (
                <Input
                  id={field.name}
                  value={field.value}
                  onChange={(e) => field.onChange(e.target.value)}
                  placeholder="Enter email address"
                  className={`w-full ${errors.email ? 'border-red-500' : ''}`}
                />
              )}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
            <p className="text-gray-500 text-xs italic">
              New users will receive an invitation email to join the platform.
            </p>
          </div>
        </div>

        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-200 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd"
                    d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"/>
            </svg>
            Access Configuration
          </h3>

          <div className="space-y-2">
            <label htmlFor="role" className="block text-sm font-medium">
              Permission Group
            </label>
            <Controller
              name="permissionGroupId"
              control={control}
              render={({field}) => (
                <SelectUserPermissionGroup
                  organizationId={organizationId}
                  id={field.name}
                  value={field.value || ''}
                  onChange={(value) => value !== null && field.onChange(value)}
                  placeholder="Select permissions"
                />
              )}
            />
            {errors.permissionGroupId && (
              <p className="text-sm text-red-500">{errors.permissionGroupId.message}</p>
            )}
            <p className="text-gray-500 text-xs italic">
              This determines what actions the user can perform within the product.
            </p>
          </div>
        </div>

        <div className="flex justify-end gap-3 mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <Button
            type="button"
            variant="outline"
            onClick={onHide}
            disabled={loading}
            className="px-4"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            loading={loading}
            className="px-4 flex items-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path
                d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
            </svg>
            {loading ? 'Sending Invitation...' : 'Send Invitation'}
          </Button>
        </div>
      </form>
    </Dialog>
  );
}

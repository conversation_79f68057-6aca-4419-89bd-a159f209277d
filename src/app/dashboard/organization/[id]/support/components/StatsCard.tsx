'use client';

import {memo} from 'react';

interface StatsCardProps {
  title: string;
  count: number;
  icon: string;
  iconColor: string;
  titleColor: string;
  borderColor?: string;
}

export const StatsCard = memo(({
  title,
  count,
  icon,
  iconColor,
  titleColor,
  borderColor
}: StatsCardProps) => (
  <div className="bg-opacity-10 bg-gradient-to-br from-[#2A2A3C] to-[#1C1C28] rounded-xl p-4 border border-[#323074]/20 shadow-xl animate-fadeIn relative overflow-hidden group">
    <div className="absolute inset-0 bg-gradient-to-r from-[#E0D7FF]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
    <div className="flex items-center gap-4">
      <div className={`flex justify-center items-center w-12 h-12 rounded-xl bg-[#323074]/20 border ${borderColor || 'border-[#5451B8]/20'}`}>
        <i className={`${icon} text-xl ${iconColor}`} />
      </div>
      <div>
        <h3 className={`${titleColor} text-lg font-medium`}>{title}</h3>
        <p className="text-white text-xl font-semibold">{count} tickets</p>
      </div>
    </div>
  </div>
));

StatsCard.displayName = 'StatsCard';

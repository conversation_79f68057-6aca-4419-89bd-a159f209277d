'use client';

import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useRouter, useSearchParams} from 'next/navigation';
import {Button} from '@/components/ui/Button';
import {useToast} from '@/components/ui/Toast';
import {Badge} from '@/components/ui/Badge';
import {getOrganizationTickets} from '@/server/actions/support';
import {TicketPriority, TicketStatus} from '@/types/ticket-types';
import {formatDistanceToNow} from 'date-fns';
import { logger } from '@/utils/logger';

// Import components from the parent page or create local versions
import {FilterControls} from './FilterControls';
import {StatsCard} from './StatsCard';
import {Card} from '@/components/ui/Card';

interface SupportTicketsTabProps {
  organizationId: string;
  portalStatus?: {
    exists: boolean;
    isPublished: boolean;
    isConfigured: boolean;
    hasRequiredFields: boolean;
    setupComplete: boolean;
    portalUrl: string;
    loading: boolean;
  };
}

// Memoized button component to prevent re-renders
const ActionButton = ({
  onClick,
  label,
  icon
}: {
  onClick: (e: React.MouseEvent<HTMLButtonElement>) => void;
  label: string;
  icon: string
}) => (
  <Button
    variant="ghost"
    onClick={onClick}
    className="text-indigo-500 hover:text-indigo-700 hover:bg-indigo-100/10 rounded-full p-2"
  >
    <span className="flex items-center">
      <i className={`${icon} mr-1`} />
      <span className="sr-only">{label}</span>
    </span>
  </Button>
);

export default function SupportTicketsTab({ organizationId, portalStatus }: SupportTicketsTabProps) {
  const { success, error: showError } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Read initial filter values from URL to avoid unnecessary state changes
  const initialStatus = useMemo(() => searchParams.get('status') || '', [searchParams]);
  const initialPriority = useMemo(() => searchParams.get('priority') || '', [searchParams]);
  const initialAssignedToId = useMemo(() => searchParams.get('assignedTo') || '', [searchParams]);
  const initialSearch = useMemo(() => searchParams.get('search') || '', [searchParams]);
  const initialPage = useMemo(() => {
    const pageParam = searchParams.get('page');
    return pageParam ? parseInt(pageParam, 10) : 1;
  }, [searchParams]);

  // Filter states initialized with URL values
  const [status, setStatus] = useState(initialStatus);
  const [priority, setPriority] = useState(initialPriority);
  const [assignedToId, setAssignedToId] = useState(initialAssignedToId);
  const [search, setSearch] = useState(initialSearch);
  const [searchInput, setSearchInput] = useState(initialSearch);

  // Pagination states
  const [page, setPage] = useState(initialPage);
  const [limit] = useState(10); // No need for setLimit if it never changes

  // Data states
  const [loading, setLoading] = useState(true);
  const [tickets, setTickets] = useState([]);
  const [members, setMembers] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    open: 0,
    inProgress: 0,
    resolved: 0,
    closed: 0,
  });
  const [pagination, setPagination] = useState({
    totalItems: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPrevPage: false,
  });

  // Avoid unnecessary rerenders by storing the fetchTickets function in a ref
  const paramsRef = useRef({ organizationId, status, priority, assignedToId, search, page, limit });

  // Update ref when dependencies change
  useEffect(() => {
    paramsRef.current = { organizationId, status, priority, assignedToId, search, page, limit };
  }, [organizationId, status, priority, assignedToId, search, page, limit]);

  // Main fetch function
  const fetchTickets = useCallback(async () => {
    setLoading(true);
    try {
      const params = paramsRef.current;
      const result = await getOrganizationTickets({
        organizationId: params.organizationId,
        status: params.status,
        priority: params.priority,
        assignedToId: params.assignedToId,
        search: params.search,
        page: params.page,
        limit: params.limit,
      });

      if (result.success && 'data' in result) {
        setTickets(result.data.tickets);
        setMembers(result.data.members);
        setStats(result.data.stats);
        if (result.pagination) {
          setPagination(result.pagination);
        }
      } else {
        showError('Failed to fetch tickets');
      }
    } catch (error) {
      logger.error('Error fetching tickets:', error);
      showError('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  }, []); // No dependencies - uses ref values instead

  // Update URL with filters - prevent rerenders by using a ref
  const updateUrlWithFilters = useCallback(() => {
    const params = paramsRef.current;
    const urlParams = new URLSearchParams();
    if (params.status) urlParams.set('status', params.status);
    if (params.priority) urlParams.set('priority', params.priority);
    if (params.assignedToId) urlParams.set('assignedTo', params.assignedToId);
    if (params.search) urlParams.set('search', params.search);
    if (params.page > 1) urlParams.set('page', params.page.toString());

    const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
    window.history.pushState({}, '', newUrl);
  }, []);

  // Apply filters function
  const applyFilters = useCallback(() => {
    setSearch(searchInput);
    setPage(1);

    // Update ref with new values
    paramsRef.current = {
      ...paramsRef.current,
      search: searchInput,
      page: 1
    };

    fetchTickets();
    updateUrlWithFilters();
  }, [searchInput, fetchTickets, updateUrlWithFilters]);

  // Page change handler
  const onPageChange = useCallback((event: any) => {
    const newPage = event.page + 1; // PrimeReact DataTable uses 0-based indexing
    setPage(newPage);

    // Update ref with new page
    paramsRef.current = {
      ...paramsRef.current,
      page: newPage
    };

    fetchTickets();
    updateUrlWithFilters();
  }, [fetchTickets, updateUrlWithFilters]);

  // Navigation functions
  const goToTicket = useCallback((id: string) => {
    router.push(`/dashboard/organization/${organizationId}/support/ticket/${id}`);
  }, [router, organizationId]);

  const goToNewTicket = useCallback(() => {
    router.push(`/dashboard/organization/${organizationId}/support/new`);
  }, [router, organizationId]);

  // Initialize
  useEffect(() => {
    fetchTickets();
  }, [fetchTickets]);

  // Template functions - memoize them
  const renderStatus = useCallback((status: string) => {
    let variant;
    switch(status) {
      case TicketStatus.OPEN:
        variant = 'info';
        break;
      case TicketStatus.IN_PROGRESS:
        variant = 'warning';
        break;
      case TicketStatus.RESOLVED:
        variant = 'success';
        break;
      case TicketStatus.CLOSED:
        variant = 'default';
        break;
      default:
        variant = 'info';
    }

    return <Badge variant={variant as any}>{status.replace('_', ' ')}</Badge>;
  }, []);

  const renderPriority = useCallback((priority: string) => {
    let variant:string;

    switch(priority) {
      case TicketPriority.LOW:
        variant = 'info';
        break;
      case TicketPriority.MEDIUM:
        variant = 'warning';
        break;
      case TicketPriority.HIGH:
        variant = 'danger';
        break;
      case TicketPriority.URGENT:
        variant = 'danger';
        break;
      default:
        variant = 'info';
    }

    return <Badge variant={variant as any}>{priority}</Badge>;
  }, []);

  const renderAssignedTo = useCallback((assignedTo: any) => {
    return assignedTo ? assignedTo.name : 'Unassigned';
  }, []);

  const renderDate = useCallback((date: string) => {
    return formatDistanceToNow(new Date(date), { addSuffix: true });
  }, []);

  // Memoize filter props to prevent unnecessary re-renders
  const filterControlsProps = useMemo(() => ({
    searchInput,
    setSearchInput,
    status,
    setStatus,
    priority,
    setPriority,
    applyFilters,
    goToNewTicket
  }), [searchInput, status, priority, applyFilters, goToNewTicket]);

  // Memoize the stats cards to prevent re-renders
  const StatsCards = useMemo(() => (
    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
      <StatsCard
        title="Total"
        count={stats.total}
        icon="pi pi-ticket"
        iconColor="text-[#8178E8]"
        titleColor="text-[#B2A5FF]"
      />
      <StatsCard
        title="Open"
        count={stats.open}
        icon="pi pi-inbox"
        iconColor="text-[#998EF8]"
        titleColor="text-[#B2A5FF]"
      />
      <StatsCard
        title="In Progress"
        count={stats.inProgress}
        icon="pi pi-spin pi-spinner"
        iconColor="text-[#E2C28D]"
        titleColor="text-[#E2C28D]"
        borderColor="border-[#D1AB66]/20"
      />
      <StatsCard
        title="Resolved"
        count={stats.resolved}
        icon="pi pi-check-circle"
        iconColor="text-[#998EF8]"
        titleColor="text-[#B2A5FF]"
      />
      <StatsCard
        title="Closed"
        count={stats.closed}
        icon="pi pi-times-circle"
        iconColor="text-gray-400"
        titleColor="text-gray-400"
        borderColor="border-[#323074]/20"
      />
    </div>
  ), [stats.total, stats.open, stats.inProgress, stats.resolved, stats.closed]);

  // Memoize the empty state to prevent re-renders
  const EmptyState = useMemo(() => (
    <div className="flex flex-col items-center justify-center py-24 text-center">
      <div className="flex justify-center items-center w-24 h-24 rounded-full bg-[#1e1e2d] mb-6">
        <i className="pi pi-search text-5xl text-[#6964D3]/40"></i>
      </div>
      <p className="text-gray-300 font-medium text-xl mb-2">No tickets found</p>
      <p className="text-gray-500 max-w-md mx-auto">
        Create your first support ticket or adjust your filters to view existing tickets.
      </p>
    </div>
  ), []);

  // Memoize the loading state
  const LoadingState = useMemo(() => (
    <div className="py-8 flex justify-center items-center">
      <div className="flex items-center space-x-2">
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <span>Loading tickets...</span>
      </div>
    </div>
  ), []);

  // Memoize the pagination controls
  const PaginationControls = useMemo(() => {
    const totalPages = pagination.totalPages;
    const currentPage = page;

    return (
      <div className="flex justify-between items-center py-4 px-6 border-t border-[#323074]/20">
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Showing {tickets.length > 0 ? (currentPage - 1) * limit + 1 : 0} - {Math.min(currentPage * limit, pagination.totalItems)} of {pagination.totalItems} tickets
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => onPageChange({ page: currentPage - 2 })}
            disabled={!pagination.hasPrevPage}
            className="px-4 py-2 text-sm"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            onClick={() => onPageChange({ page: currentPage })}
            disabled={!pagination.hasNextPage}
            className="px-4 py-2 text-sm"
          >
            Next
          </Button>
        </div>
      </div>
    );
  }, [pagination, page, tickets.length, limit, onPageChange]);

  // Memoize the Tailwind-styled tickets table
  const TicketsTable = useMemo(() => (
    <Card className="overflow-hidden">
      <div className="overflow-x-auto">
        {loading ? (
          LoadingState
        ) : tickets.length === 0 ? (
          EmptyState
        ) : (
          <>
            <table className="min-w-full divide-y divide-[#323074]/20">
              <thead className="bg-[#1e1e2e]">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Subject</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Customer</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Priority</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Assigned To</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Created</th>
                  <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-400 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-[#171721] divide-y divide-[#323074]/20">
                {tickets.map((ticket: any) => (
                  <tr
                    key={ticket.id}
                    className="hover:bg-[#202032] transition-colors cursor-pointer"
                    onClick={() => goToTicket(ticket.id)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div>
                        <div className="font-medium">{ticket.title}</div>
                        {ticket.customerEmail && (
                          <div className="text-xs text-gray-400 mt-1">
                            #{ticket.id.slice(-8).toUpperCase()}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {ticket.customerEmail ? (
                        <div>
                          <div className="font-medium">{ticket.customerName || 'Customer'}</div>
                          <div className="text-xs text-gray-400">{ticket.customerEmail}</div>
                        </div>
                      ) : (
                        <span className="text-gray-400">Internal</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{renderStatus(ticket.status)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{renderPriority(ticket.priority)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{renderAssignedTo(ticket.assignedTo)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">{renderDate(ticket.createdAt)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <ActionButton
                        onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                          e.stopPropagation();
                          goToTicket(ticket.id);
                        }}
                        label="View ticket"
                        icon="pi pi-eye"
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {PaginationControls}
          </>
        )}
      </div>
    </Card>
  ), [
    tickets,
    loading,
    EmptyState,
    LoadingState,
    PaginationControls,
    goToTicket,
    renderStatus,
    renderPriority,
    renderAssignedTo,
    renderDate
  ]);

  // Portal status banner (informational only - setup validation handled at page level)
  const PortalStatusBanner = useMemo(() => {
    // Only show banner if portal is setup and we have status info
    if (!portalStatus || !portalStatus.setupComplete) return null;

    return (
      <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Customer Portal Active
              </h3>
              <div className="mt-1 text-sm text-green-700 dark:text-green-300">
                <p>
                  Customers can submit tickets at: {portalStatus.portalUrl}
                </p>
              </div>
            </div>
          </div>
          <div className="flex space-x-2">
            <a
              href={portalStatus.portalUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm font-medium text-green-800 dark:text-green-200 hover:text-green-900 dark:hover:text-green-100"
            >
              Visit Portal →
            </a>
          </div>
        </div>
      </div>
    );
  }, [portalStatus]);

  return (
    <div>
      {PortalStatusBanner}
      <FilterControls {...filterControlsProps} />
      {StatsCards}
      {TicketsTable}
    </div>
  );
}

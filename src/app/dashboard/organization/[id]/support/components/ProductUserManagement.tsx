'use client';

import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {Button} from '@/components/ui/Button';
import {useToast} from '@/components/ui/Toast';
import {
  cancelProductInvite,
  getProductUsers,
  removeProductUser,
  resendProductInvite
} from '@/server/actions/user-management';
import AddUserToProductModal from './AddUserToProductModal';
import {Skeleton} from '@/components/ui/Skeleton';
import {CustomColumn as Column} from "@components/ui/CustomColumn";
import {CustomDataTable as DataTable} from '@components/ui/CustomDataTable';
import {ConfirmDialog, confirmDialog} from '@/components/ui/ConfirmDialog';
import ProductCode from '@scripts/ProductCodes.json'
import { logger } from '@/utils/logger';

// Add icons import
import {RefreshCw, Trash2} from 'lucide-react';

// Helper function to replace missing getUserStatus from @/lib/user-status
function getUserStatus(status: string) {
  switch (status) {
    case 'active':
      return {
        label: 'Active',
        bgColor: 'bg-green-100',
        textColor: 'text-green-800'
      };
    case 'invited':
      return {
        label: 'Invited',
        bgColor: 'bg-amber-100',
        textColor: 'text-amber-800'
      };
    case 'suspended':
      return {
        label: 'Suspended',
        bgColor: 'bg-red-100',
        textColor: 'text-red-800'
      };
    default:
      return {
        label: status || 'Unknown',
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-800'
      };
  }
}

// Updated interface to match our new data structure
interface ProductUser {
  userId: string;
  name: string;
  email: string;
  role: string;
  status: string;
  addedAt: Date;
  isOrgWide: boolean;
  isInvite?: boolean;
  _id?: string; // For invites, this will be the invite ID
}

interface ProductUserManagementProps {
  organizationId: string;
  product: typeof ProductCode[keyof typeof ProductCode];
}

// Memoized UserRow component to prevent re-renders
const UserRow = memo(({user, handleRemoveUser}: {
  user: ProductUser,
  handleRemoveUser: (userId: string, userName: string, isOrgWide: boolean, isInvite?: boolean, inviteId?: string) => void
}) => {
  const status = getUserStatus(user.status);

  return (
    <div className="flex items-center justify-between py-3 border-b last:border-b-0">
      <div className="flex flex-col">
        <span className="font-medium text-white">{user.name}</span>
        <span className="text-sm text-gray-300">{user.email}</span>
        <span className="text-xs text-gray-300">{user.role}</span>
      </div>
      <div className="flex items-center gap-3">
        <span className={`text-sm px-2 py-1 rounded-full ${status.bgColor} ${status.textColor}`}>
          {status.label}
        </span>
        {!user.isOrgWide && (
          <Button
            variant="destructive"
            onClick={() => handleRemoveUser(user.userId, user.name, user.isOrgWide, user.isInvite || false, user._id)}
            size="sm"
          >
            Remove
          </Button>
        )}
        {user.isOrgWide && (
          <span className="text-xs text-gray-300 px-2">Organization-wide access</span>
        )}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for the UserRow component
  // Only rerender if one of these key properties has changed
  return (
    prevProps.user.userId === nextProps.user.userId &&
    prevProps.user.name === nextProps.user.name &&
    prevProps.user.email === nextProps.user.email &&
    prevProps.user.role === nextProps.user.role &&
    prevProps.user.isOrgWide === nextProps.user.isOrgWide &&
    prevProps.user.addedAt === nextProps.user.addedAt &&
    prevProps.user.isInvite === nextProps.user.isInvite &&
    prevProps.user._id === nextProps.user._id &&
    prevProps.handleRemoveUser === nextProps.handleRemoveUser
  );
});

UserRow.displayName = 'UserRow';

// Main component
export default function ProductUserManagement({
                                                organizationId,
                                                product,
                                              }: ProductUserManagementProps) {
  // State for UI components
  const [users, setUsers] = useState<ProductUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [productName, setProductName] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [resendingInviteId, setResendingInviteId] = useState<string | null>(null);
  const {success, error: showError, warn} = useToast();

  // Memoize loadData function to prevent unnecessary re-renders
  const loadData = useCallback(async () => {
    if (!organizationId || !product.code) return;

    setLoading(true);
    try {
      const response = await getProductUsers(
        organizationId,
        product.code,
      );
      if (!response.success) {
        throw new Error(response.error || 'Failed to load users');
      }

      // Transform the data structure to match what component expects
      const combinedUsers: ProductUser[] = [
        // Convert org users to ProductUser format
        ...(response.data?.orgUsers || []).map(user => ({
          userId: user.userId,
          name: user.name,
          email: user.email,
          role: user.role,
          status: user.status,
          addedAt: new Date(),
          isOrgWide: true
        })),
        // Convert product users to ProductUser format
        ...(response.data?.productUsers || []).map(user => ({
          _id: user.userId ? undefined : user._id,
          userId: user.userId,
          name: user.name,
          email: user.email,
          role: user.role,
          status: user.status,
          addedAt: user.addedAt ? new Date(user.addedAt) : new Date(),
          isOrgWide: false
        }))
      ];
      setUsers(combinedUsers);
    } catch (error) {
      logger.error('Error loading users:', error);
      showError(
        "Error",
        error instanceof Error ? error.message : 'Failed to load users'
      );
    } finally {
      setLoading(false);
    }
  }, [organizationId, product.code, showError]);

  // Load data on component mount only, not when loadData changes
  useEffect(() => {
    loadData();
    // Don't add loadData to dependency array to avoid infinite loop
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [organizationId, product.code]);

  // Handle removing a user from the product
  const handleRemoveUser = useCallback(async (userId: string, userName: string, isOrgWide: boolean, isInvite: boolean = false, inviteId?: string) => {
    if (isOrgWide) {
      warn(
        "Cannot Remove Organization-Wide User",
        "This user has organization-wide access. To remove them, go to organization settings."
      );
      return;
    }

    const confirmMessage = isInvite
      ? `Are you sure you want to cancel the invitation for ${userName}?`
      : `Are you sure you want to remove ${userName} from ${productName}?`;

    const headerMessage = isInvite ? "Cancel Invitation" : "Remove User";

    confirmDialog({
      message: confirmMessage,
      header: headerMessage,
      icon: <Trash2 className="h-6 w-6 text-red-500"/>,
      acceptLabel: isInvite ? "Cancel Invitation" : "Remove",
      rejectLabel: "No",
      accept: async () => {
        try {
          if (isInvite && inviteId) {
            // Cancel the invitation
            const result = await cancelProductInvite({
              inviteId,
              organizationId
            });

            if (result.success) {
              success("Success", `Invitation for ${userName} was cancelled`);
              loadData(); // Reload the data
            } else {
              showError("Error", result.error || "Failed to cancel invitation");
            }
          } else {
            // Remove the user from the product
            const result = await removeProductUser({
              userId,
              organizationId,
              productCode: product.code
            });

            if (result.success) {
              success("Success", `User ${userName} was removed from ${productName}`);
              loadData(); // Reload the data
            } else {
              showError("Error", result.error || "Failed to remove user");
            }
          }
        } catch (error) {
          logger.error('Error removing user or invitation:', error);
          showError("Error", "An unexpected error occurred");
        } finally {
        }
      }
    });
  }, [organizationId, productName, success, showError, warn, loadData]);

  // Add a function to handle resending invitations
  const handleResendInvite = useCallback(async (inviteId: string, email: string) => {
    setResendingInviteId(inviteId);
    try {
      const result = await resendProductInvite({
        inviteId,
        organizationId
      });

      if (result.success) {
        success("Success", `Invitation was resent to ${email}`);
      } else {
        showError("Error", result.error || "Failed to resend invitation");
      }
    } catch (error) {
      logger.error('Error resending invitation:', error);
      showError("Error", "An unexpected error occurred");
    } finally {
      setResendingInviteId(null);
    }
  }, [organizationId, success, showError]);

  // Memoize the modal component to prevent re-renders
  const addUserModal = useMemo(() => (
    showAddModal && (
      <AddUserToProductModal
        visible={showAddModal}
        onHide={() => setShowAddModal(false)}
        organizationId={organizationId}
        productCode={product.code}
        onSuccess={loadData}
      />
    )
  ), [
    showAddModal,
    organizationId,
    product.code,
    loadData
  ]);


  // Generate user rows with memoization - we'll now use a body template for the DataTable
  const nameBodyTemplate = (rowData: ProductUser) => (
    <div className="flex flex-col">
      <span className="font-medium text-white">{rowData.name}</span>
      <span className="text-sm text-gray-300">{rowData.email}</span>
    </div>
  );

  const roleBodyTemplate = (rowData: ProductUser) => (
    <span className="text-xs text-gray-300">{rowData.role}</span>
  );

  const statusBodyTemplate = (rowData: ProductUser) => {
    const status = getUserStatus(rowData.status);
    return (
      <span className={`text-sm px-2 py-1 rounded-full ${status.bgColor} ${status.textColor}`}>
        {status.label}
      </span>
    );
  };

  const accessTypeBodyTemplate = (rowData: ProductUser) => {
    return (
      <div className="flex items-center justify-between gap-2">
        {rowData.isOrgWide ? (
          <div className="flex items-center gap-2">
            <span
              className="bg-purple-100 text-purple-800 dark:bg-purple-900/40 dark:text-purple-300 text-xs font-medium px-2.5 py-1 rounded-full">
              Organization-wide
            </span>
            <span className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">
              (All products)
            </span>
          </div>
        ) : (
          <div className="flex items-center gap-2">
            <span
              className="bg-purple-100 text-purple-800 dark:bg-purple-900/40 dark:text-purple-300 text-xs font-medium px-2.5 py-1 rounded-full">
              Product-specific
            </span>
          </div>
        )}
      </div>
    );
  };

  // Add a new actionsBodyTemplate for buttons
  const actionsBodyTemplate = (rowData: ProductUser) => {
    const isInvited = rowData.status === 'invited';

    if (rowData.isOrgWide) {
      return (
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-300">
            Managed in organization settings
          </span>
        </div>
      );
    }

    return (
      <div className="flex items-center gap-2">
        {isInvited && (
          <>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 text-indigo-500 hover:text-indigo-700 hover:bg-indigo-100/20"
              onClick={() => handleResendInvite(rowData._id!, rowData.email)}
              disabled={resendingInviteId === rowData._id}
              title="Resend invitation"
            >
              {resendingInviteId === rowData._id ? (
                <RefreshCw className="h-5 w-5 animate-spin"/>
              ) : (
                <RefreshCw className="h-5 w-5"/>
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-1 text-red-500 hover:text-red-700 hover:bg-red-100/20"
              onClick={() => handleRemoveUser(rowData.userId || '', rowData.name, rowData.isOrgWide, true, rowData._id)}
              title="Cancel invitation"
            >
              <Trash2 className="h-5 w-5"/>
            </Button>
          </>
        )}
        {!isInvited && (
          <Button
            variant="ghost"
            size="sm"
            className="p-1 text-red-500 hover:text-red-700 hover:bg-red-100/20"
            onClick={() => handleRemoveUser(rowData.userId, rowData.name, rowData.isOrgWide)}
            title="Remove user"
          >
            <Trash2 className="h-5 w-5"/>
          </Button>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-full"/>
        <Skeleton className="h-20 w-full"/>
        <Skeleton className="h-20 w-full"/>
        <Skeleton className="h-20 w-full"/>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">{productName} Users</h2>
        <Button
          variant="primary"
          onClick={() => setShowAddModal(true)}
          className="px-4 py-2 flex items-center gap-2 shadow-md hover:shadow-lg transition-all"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"
            />
          </svg>
          <span>Invite User</span>
        </Button>
      </div>

      <div
        className="p-3 mb-4 bg-purple-50 dark:bg-purple-900/20 border-l-4 border-purple-500 text-sm text-white rounded-r-md">
        <p className="flex items-start">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20"
               fill="currentColor">
            <path fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"/>
          </svg>
          <span>
            This view shows all users who have access to <strong>{productName}</strong>, including organization-wide users who automatically have access to all products.
          </span>
        </p>
      </div>

      <DataTable
        value={users}
        loading={loading}
        emptyMessage={`No users assigned to ${productName}`}
        dataKey="userId"
        className="text-white"
      >
        <Column
          field="name"
          header="Name"
          body={nameBodyTemplate}
          headerClassName="text-left text-sm font-medium text-white"
        />
        <Column
          field="role"
          header="Role"
          body={roleBodyTemplate}
          headerClassName="text-left text-sm font-medium text-white"
        />
        <Column
          field="status"
          header="Status"
          body={statusBodyTemplate}
          headerClassName="text-left text-sm font-medium text-white"
        />
        <Column
          field="accessType"
          header="Access Type"
          body={accessTypeBodyTemplate}
          headerClassName="text-left text-sm font-medium text-white"
        />
        <Column
          field="actions"
          header="Actions"
          body={actionsBodyTemplate}
          headerClassName="text-left text-sm font-medium text-white"
          style={{width: '120px'}}
        />
      </DataTable>

      {addUserModal}
      <ConfirmDialog/>
    </div>
  );
}

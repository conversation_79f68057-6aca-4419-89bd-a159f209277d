'use client';

import React from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  Settings,
  Globe,
  Palette,
  FileText,
  Rocket,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ArrowRight,
  Users,
  MessageSquare
} from 'lucide-react';

interface PortalSetupRequiredProps {
  organizationId: string;
  portalStatus: {
    exists: boolean;
    isPublished: boolean;
    isConfigured: boolean;
    hasRequiredFields: boolean;
    setupComplete: boolean;
    portalUrl: string;
    loading: boolean;
  };
}

export default function PortalSetupRequired({ organizationId, portalStatus }: PortalSetupRequiredProps) {
  const setupSteps = [
    {
      id: 'create',
      title: 'Create Portal',
      description: 'Initialize your customer support portal',
      icon: Globe,
      completed: portalStatus.exists,
      required: true,
    },
    {
      id: 'configure',
      title: 'Basic Configuration',
      description: 'Set company name, contact email, and welcome message',
      icon: FileText,
      completed: portalStatus.hasRequiredFields,
      required: true,
    },
    {
      id: 'branding',
      title: 'Complete Setup',
      description: 'Configure branding colors and help text',
      icon: Palette,
      completed: portalStatus.isConfigured,
      required: true,
    },
    {
      id: 'publish',
      title: 'Publish Portal',
      description: 'Make your portal live for customers',
      icon: Rocket,
      completed: portalStatus.isPublished,
      required: true,
    },
  ];

  const getStepStatus = (step: typeof setupSteps[0]) => {
    if (step.completed) {
      return { icon: CheckCircle, color: 'text-green-500', bgColor: 'bg-green-100 dark:bg-green-900/30' };
    }
    return { icon: XCircle, color: 'text-red-500', bgColor: 'bg-red-100 dark:bg-red-900/30' };
  };

  const completedSteps = setupSteps.filter(step => step.completed).length;
  const totalSteps = setupSteps.length;
  const progressPercentage = (completedSteps / totalSteps) * 100;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <Card className="p-8 text-center bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-200 dark:border-blue-800">
        <div className="flex justify-center mb-4">
          <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
            <AlertTriangle className="w-8 h-8 text-blue-600 dark:text-blue-400" />
          </div>
        </div>
        <h1 className="text-2xl font-bold text-[#0f0f18] dark:text-white mb-4">
          Customer Portal Setup Required
        </h1>
        <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-lg mb-6 max-w-2xl mx-auto">
          Before you can manage customer support tickets, you need to set up your customer-facing support portal.
          This ensures your customers have a proper way to submit and track their support requests.
        </p>
        <div className="flex justify-center">
          <Button
            onClick={() => window.location.href = `/dashboard/organization/${organizationId}/settings?tab=support`}
            className="flex items-center bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
          >
            <Settings className="w-5 h-5 mr-2" />
            Set Up Customer Portal
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </Card>

      {/* Progress Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-[#0f0f18] dark:text-white">Setup Progress</h2>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">
              Complete all steps to unlock support ticket management
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-[#0f0f18] dark:text-white">
              {completedSteps}/{totalSteps}
            </div>
            <div className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Steps Complete</div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-2">
            <span>Progress</span>
            <span>{Math.round(progressPercentage)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className="bg-blue-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Setup Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {setupSteps.map((step, index) => {
            const status = getStepStatus(step);
            const StepIcon = step.icon;
            const StatusIcon = status.icon;

            return (
              <div
                key={step.id}
                className={`p-4 rounded-lg border transition-all ${
                  step.completed
                    ? 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20'
                    : 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${status.bgColor}`}>
                    <StepIcon className="w-5 h-5 text-[#0f0f18] dark:text-white" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-semibold text-[#0f0f18] dark:text-white">
                        {step.title}
                      </h3>
                      <StatusIcon className={`w-5 h-5 ${status.color}`} />
                    </div>
                    <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
                      {step.description}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </Card>

      {/* What You'll Get */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold text-[#0f0f18] dark:text-white mb-4">
          What You'll Get After Setup
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Globe className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h3 className="font-semibold text-[#0f0f18] dark:text-white mb-2">
              Customer Portal
            </h3>
            <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
              A branded portal where customers can submit and track support tickets
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <MessageSquare className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="font-semibold text-[#0f0f18] dark:text-white mb-2">
              Ticket Management
            </h3>
            <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
              Full access to manage customer tickets and internal support workflows
            </p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <h3 className="font-semibold text-[#0f0f18] dark:text-white mb-2">
              Team Collaboration
            </h3>
            <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
              Support user management and team collaboration tools
            </p>
          </div>
        </div>
      </Card>

      {/* Current Status */}
      {portalStatus.exists && (
        <Card className="p-6 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5" />
            <div>
              <h3 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                Portal Configuration In Progress
              </h3>
              <p className="text-yellow-700 dark:text-yellow-300 mb-4">
                Your portal has been created but needs additional configuration before it can be used for customer support.
              </p>
              <div className="space-y-2 text-sm">
                {!portalStatus.hasRequiredFields && (
                  <div className="flex items-center text-yellow-700 dark:text-yellow-300">
                    <XCircle className="w-4 h-4 mr-2" />
                    Missing required fields (company name, contact email, welcome message)
                  </div>
                )}
                {portalStatus.hasRequiredFields && !portalStatus.isConfigured && (
                  <div className="flex items-center text-yellow-700 dark:text-yellow-300">
                    <XCircle className="w-4 h-4 mr-2" />
                    Missing complete configuration (branding colors, help text)
                  </div>
                )}
                {portalStatus.isConfigured && !portalStatus.isPublished && (
                  <div className="flex items-center text-yellow-700 dark:text-yellow-300">
                    <XCircle className="w-4 h-4 mr-2" />
                    Portal not published - customers cannot access it yet
                  </div>
                )}
              </div>
              <div className="mt-4">
                <Button
                  onClick={() => window.location.href = `/dashboard/organization/${organizationId}/settings?tab=support`}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  Continue Setup
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Help Section */}
      <Card className="p-6 bg-gray-50 dark:bg-gray-900/20">
        <h3 className="font-semibold text-[#0f0f18] dark:text-white mb-3">
          Need Help?
        </h3>
        <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
          Setting up your customer portal is quick and easy. The setup wizard will guide you through each step,
          and you can always modify your settings later.
        </p>
        <div className="flex space-x-4">
          <Button
            variant="outline"
            onClick={() => window.location.href = `/dashboard/organization/${organizationId}/settings?tab=support`}
            className="flex items-center"
          >
            <Settings className="w-4 h-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </Card>
    </div>
  );
}

'use client';

import {useEffect, useState} from 'react';
import {useParams, useSearchParams} from 'next/navigation';
import {Card} from '@/components/ui/Card';
import {Button} from '@/components/ui/Button';
import {useToast} from '@/components/ui/Toast';
import {verifyPayment} from '@/server/actions/transaction-actions';
import {verifyProratedPayment} from '@/server/actions/billing';
import Link from 'next/link';
import { logger } from '@/utils/logger';

export default function PaymentVerificationPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const organizationId = params.id as string;
  const reference = searchParams.get('reference') || searchParams.get('trxref');
  // Check if this is a prorated payment
  const isProrated = searchParams.get('prorated') === 'true';

  const toast = useToast();
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'failed'>('pending');
  const [statusMessage, setStatusMessage] = useState('');

  useEffect(() => {
    const verifyTransaction = async () => {
      if (!reference) {
        setVerificationStatus('failed');
        setStatusMessage('Invalid transaction reference');
        return;
      }

      setIsVerifying(true);

      try {
        // Use the appropriate verification function based on the payment type
        const result = isProrated
          ? await verifyProratedPayment(reference)
          : await verifyPayment(reference);

        if (result.success) {
          setVerificationStatus('success');
          setStatusMessage('Payment verification successful');
        } else {
          setVerificationStatus('failed');
          setStatusMessage(result.message || 'Payment verification failed');
        }
      } catch (error: any) {
        logger.error('Error verifying payment:', error);
        setVerificationStatus('failed');
        setStatusMessage(error.message || 'An error occurred during verification');
      } finally {
        setIsVerifying(false);
      }
    };

    verifyTransaction();
  }, [reference, isProrated]);

  const showSuccessToast = (message: string) => {
    toast.success('Success', message);
  };

  const showErrorToast = (message: string) => {
    toast.error('Error', message);
  };

  const handleRetryVerification = async () => {
    if (!reference) {
      showErrorToast('Missing transaction reference');
      return;
    }

    setIsVerifying(true);
    setVerificationStatus('pending');

    try {
      // Use the appropriate verification function for retry as well
      const result = isProrated
        ? await verifyProratedPayment(reference)
        : await verifyPayment(reference);

      if (result.success) {
        setVerificationStatus('success');
        setStatusMessage('Payment verification successful');
        showSuccessToast('Payment verified successfully');
      } else {
        setVerificationStatus('failed');
        setStatusMessage(result.message || 'Payment verification failed');
        showErrorToast(result.message || 'Failed to verify payment');
      }
    } catch (error: any) {
      logger.error('Error verifying payment:', error);
      setVerificationStatus('failed');
      setStatusMessage(error.message || 'An error occurred during verification');
      showErrorToast(error.message || 'An error occurred during verification');
    } finally {
      setIsVerifying(false);
    }
  };

  // Add success message for prorated payments
  const getSuccessMessage = () => {
    if (isProrated) {
      return 'Your payment has been verified successfully. Your subscription has been upgraded.';
    }
    return 'Your payment has been verified successfully. Your subscription is now active.';
  };

  const renderVerificationStatus = () => {
    switch (verificationStatus) {
      case 'pending':
        return (
          <div className="flex flex-col items-center justify-center text-center p-6">
            <div className="w-16 h-16 border-4 border-t-blue-500 border-r-blue-500 border-b-blue-200 border-l-blue-200 rounded-full animate-spin mb-4"></div>
            <h2 className="text-xl font-semibold mb-2">Verifying Payment</h2>
            <p className="text-gray-500 dark:text-gray-400">
              Please wait while we verify your payment...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="flex flex-col items-center justify-center text-center p-6">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2">Payment Successful</h2>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {getSuccessMessage()}
            </p>
            <Link href={`/dashboard/organization/${organizationId}/billing`}>
              <Button>
                View Subscription
              </Button>
            </Link>
          </div>
        );

      case 'failed':
        return (
          <div className="flex flex-col items-center justify-center text-center p-6">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-full flex items-center justify-center mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2">Payment Verification Failed</h2>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {statusMessage || 'We could not verify your payment. Your subscription has not been activated.'}
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                variant="outline"
                onClick={handleRetryVerification}
                disabled={isVerifying}
              >
                {isVerifying ? 'Retrying...' : 'Retry Verification'}
              </Button>
              <Link href={`/dashboard/organization/${organizationId}/billing/upgrade`}>
                <Button>
                  Try Again
                </Button>
              </Link>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="h-screen flex items-center justify-center content-center justify-items-center text-center">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col gap-2">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Payment Verification
          </h1>
          <p className="text-gray-500 dark:text-gray-400">
            Verifying your payment status
          </p>
        </div>

        <Card>
          {renderVerificationStatus()}
        </Card>

        <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
          <p>Transaction Reference: {reference || 'Not available'}</p>
          {isProrated && <p>Prorated Payment</p>}
        </div>
      </div>
    </div>
  );
}

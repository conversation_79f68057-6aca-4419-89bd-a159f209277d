'use client';

import {useCallback, useEffect, useMemo, useState} from 'react';
import {use<PERSON><PERSON><PERSON>, useRouter} from 'next/navigation';
import {getUserData} from '@/server/actions/user-actions';
import {getPublicPricingPlans} from '@/server/actions/pricing';
import {Currency, TimeUnit} from '@/constants/pricing';
import {useToast} from "@/components/ui/Toast";
import {PaymentProvider} from '@/constants/transactions';
import {
  calculateTransactionFeeAction,
} from '@/server/actions/transaction-actions';
import {
  initializeCheckout,
  getActiveSubscription,
  calculatePlanProratedAmount,
  initializeProratedCheckout
} from '@/server/actions/billing';
import {useNetAppsPay} from '@netappsng/react-netappspaysdk';
import Step1PlanSelection from "@components/billing/Steps/Step1PlanSelection";
import Step2UserConfig from "@components/billing/Steps/Step2UserConfig";
import Step3PaymentMethod from "@components/billing/Steps/Step3PaymentMethod";
import Step4Payment from "@components/billing/Steps/Step4Payment";
import {getTaxRates, calculateTaxAmounts} from '@/server/actions/tax-settings';
import ProgressIndicator from "@components/billing/ProgressIndicator";
import { logger } from '@/utils/logger';

//     import('react-paystack').then((mod) => mod.usePaystackPayment),
//   {ssr: false}
// );

interface Feature {
  _id: string;
  name: string;
  description?: string;
}

interface Product {
  _id: string;
  name: string;
  description?: string;
  category?: 'core' | 'addon' | 'premium';
}

interface PricingPlan {
  _id: string;
  name: string;
  code: string;
  description?: string;
  products: Product[]; // Multiple products
  productItems?: Array<{
    productId: string;
    features: string[];
  }>; // For product-specific features
  amountUSD: number;
  amountNGN?: number;
  timeUnit: TimeUnit;
  duration: number;
  features: Feature[];
  isPerUser: boolean;
  active: boolean;
  discountPercentage?: number;
  discountActive?: boolean;
  level: number; // Add level to the interface
}


const NetAppsPayHookComponent = ({
                                   reference,
                                   email,
                                   amount,
                                   currency,
                                   publicKey,
                                   metadata,
                                   onSuccess,
                                   onClose
                                 }: {
  reference: string;
  email: string;
  amount: number;
  currency: Currency;
  publicKey: string;
  metadata: any;
  onSuccess: (response: any) => void;
  onClose: () => void;
}) => {
  const {isReady, initPayment} = useNetAppsPay({
    publicKey,
    isDev: process.env.NODE_ENV !== 'production',
    onSuccess,
    onError: onClose
  });

  return (
    <button
      className="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
      disabled={!isReady}
      onClick={() => {
        if (isReady) {
          initPayment({
            email,
            amount,
            currency,
            ref: reference,
            metadata
          });
        }
      }}
    >
      Pay with NetAppsPay
    </button>
  );
};

const UPGRADE_STEPS = [
  {id: 1, label: 'Select Plan'},
  {id: 2, label: 'Configure'},
  {id: 3, label: 'Payment Method'},
  {id: 4, label: 'Pay'}
];

type SubscriptionDetailsResponse = Awaited<ReturnType<typeof initializeProratedCheckout & typeof initializeCheckout>>

export default function UpgradePage() {
  const router = useRouter();
  const params = useParams();
  const organizationId = params.id as string;
  const toast = useToast();

  // Pricing and subscription states
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlanId, setSelectedPlanId] = useState<string>('');
  const [subscription, setSubscription] = useState<SubscriptionDetailsResponse>();
  const [userCount, setUserCount] = useState<number>(5);

  // Payment and checkout states
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [selectedGateway, setSelectedGateway] = useState<PaymentProvider>(PaymentProvider.PAYSTACK);
  const [transactionResult, setTransactionResult] = useState<SubscriptionDetailsResponse['data']>();
  const [processingPayment, setProcessingPayment] = useState<boolean>(false);
  const [userData, setUserData] = useState<any>(null);

  // Billing period state with yearly discount
  const [isYearly, setIsYearly] = useState(false);
  const [yearlyDiscount, setYearlyDiscount] = useState(0);

  // Currency state - add originalCurrency state to track subscription currency
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>(Currency.USD);
  const [originalCurrency, setOriginalCurrency] = useState<Currency | null>(null);
  const [currencyError, setCurrencyError] = useState<string | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState<boolean>(false);

  // Transaction fee states
  const [transactionFee, setTransactionFee] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);

  // Tax and VAT states
  const [vatPercentage, setVatPercentage] = useState(0);
  const [vatAmount, setVatAmount] = useState(0);
  const [taxPercentage, setTaxPercentage] = useState(0);
  const [taxAmount, setTaxAmount] = useState(0);

  // Proration states
  const [proratedAmountData, setProratedAmountData] = useState<any>(null);
  const [calculatingProration, setCalculatingProration] = useState<boolean>(false);
  const [isDowngrade, setIsDowngrade] = useState<boolean>(false);

  // Helper function to get selected plan details
  const getSelectedPlanDetails = useCallback(() => {
    if (!selectedPlanId) return null;
    return pricingPlans.find(plan => plan._id === selectedPlanId) || null;
  }, [selectedPlanId, pricingPlans]);

  // Calculate base amounts and prices as memoized values
  const selectedPlanDetails = useMemo(() => getSelectedPlanDetails(), [getSelectedPlanDetails]);

  const baseAmount = useMemo(() => {
    if (!selectedPlanDetails) return 0;
    const price = selectedCurrency === Currency.USD ?
      (selectedPlanDetails.amountUSD || 0) : (selectedPlanDetails.amountNGN || 0);
    return price * userCount;
  }, [selectedPlanDetails, selectedCurrency, userCount]);

  // Calculate the adjusted total amount (for proration) at the component level
  const adjustedTotalAmount = useMemo(() => {
    if (isDowngrade || !proratedAmountData || !hasActiveSubscription) {
      return totalAmount;
    }
    // For upgrades, use the prorated amount calculation
    return proratedAmountData.netAmountDue + transactionFee + vatAmount + taxAmount;
  }, [isDowngrade, proratedAmountData, hasActiveSubscription, totalAmount, transactionFee, vatAmount, taxAmount]);

  // Fetch tax rates and calculate tax amounts
  useEffect(() => {
    const fetchTaxRates = async () => {
      try {
        const taxRatesResponse = await getTaxRates();
        if (taxRatesResponse.success && taxRatesResponse.data) {
          setVatPercentage(taxRatesResponse.data.vatPercentage);
          setTaxPercentage(taxRatesResponse.data.taxPercentage);
        }
      } catch (error) {
        logger.error('Error fetching tax rates:', error);
      }
    };

    fetchTaxRates();
  }, []);

  // Calculate tax amounts when base amount changes
  useEffect(() => {
    const calculateTaxes = async () => {
      try {
        const taxCalculation = await calculateTaxAmounts(baseAmount);
        if (taxCalculation.success && taxCalculation.data) {
          setVatAmount(taxCalculation.data.vatAmount);
          setTaxAmount(taxCalculation.data.taxAmount);
        }
      } catch (error) {
        logger.error('Error calculating tax amounts:', error);
      }
    };

    if (baseAmount > 0) {
      calculateTaxes();
    }
  }, [baseAmount]);

  // Calculate fee for step 3
  useEffect(() => {
    // Only run calculations when on step 3 and when dependencies change
    if (currentStep !== 3) return;

    // Set initial amount immediately before async calculation
    setTotalAmount(baseAmount + vatAmount + taxAmount);

    let isMounted = true;

    const calculateFee = async () => {
      try {
        const result = await getTotalWithFee(baseAmount + vatAmount + taxAmount, selectedCurrency, selectedGateway);
        if (isMounted) {
          setTransactionFee(result.fee);
          setTotalAmount(result.total);
        }
      } catch (error) {
        logger.error('Error calculating fee:', error);
        if (isMounted) {
          setTransactionFee(0);
          setTotalAmount(baseAmount + vatAmount + taxAmount);
        }
      }
    };

    calculateFee();

    return () => {
      isMounted = false;
    };
  }, [currentStep, baseAmount, selectedCurrency, selectedGateway, vatAmount, taxAmount]);

  // Calculate fee for step 4
  useEffect(() => {
    // Only run when on step 4 and transaction result exists
    if (currentStep !== 4 || !transactionResult) return;

    let isMounted = true;

    // If we have transaction data from the server, use that
    if (transactionResult && transactionResult.amount) {
      setTotalAmount(transactionResult.amount);
      setTransactionFee(transactionResult.transactionFee || 0);
      setVatAmount(transactionResult.vatAmount || 0);
      setTaxAmount(transactionResult.taxAmount || 0);
    } else {
      // Set initial amount immediately
      setTotalAmount(baseAmount + vatAmount + taxAmount);

      // Otherwise calculate it client-side
      const calculateFee = async () => {
        try {
          const result = await getTotalWithFee(baseAmount + vatAmount + taxAmount, selectedCurrency, selectedGateway);
          if (isMounted) {
            setTransactionFee(result.fee);
            setTotalAmount(result.total);
          }
        } catch (error) {
          logger.error('Error calculating fee:', error);
          if (isMounted) {
            setTransactionFee(0);
            setTotalAmount(baseAmount + vatAmount + taxAmount);
          }
        }
      };

      calculateFee();
    }

    return () => {
      isMounted = false;
    };
  }, [currentStep, transactionResult, baseAmount, selectedCurrency, selectedGateway, vatAmount, taxAmount]);

  // Calculate proration when plan or users change
  const calculateProration = useCallback(async () => {
    if (!hasActiveSubscription || !selectedPlanId || !subscription) return;

    try {
      setCalculatingProration(true);

      const result = await calculatePlanProratedAmount({
        organizationId,
        newPlanId: selectedPlanId,
        currencyPreference: selectedCurrency,
      });

      if (result.success && result.data) {
        setProratedAmountData(result.data);

        // Check if this is a downgrade (current plan level > new plan level)
        const currentPlanLevel = subscription?.data?.level || 0;
        const newPlanLevel = pricingPlans.find(p => p._id === selectedPlanId)?.level || 0;

        setIsDowngrade(currentPlanLevel > newPlanLevel);
      } else {
        logger.error('Proration calculation failed:', !result.success ? result.error : 'Unknown error');
        setProratedAmountData(null);
      }
    } catch (error) {
      logger.error('Error calculating proration:', error);
      setProratedAmountData(null);
    } finally {
      setCalculatingProration(false);
    }
  }, [hasActiveSubscription, selectedPlanId, subscription, organizationId, selectedCurrency, pricingPlans]);

  // Trigger proration calculation when relevant values change
  useEffect(() => {
    if (currentStep >= 2 && hasActiveSubscription) {
      calculateProration();
    }
  }, [currentStep, selectedPlanId, userCount, isYearly, hasActiveSubscription, calculateProration]);

  // Fetch pricing plans, active subscription, and user data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch pricing plans
        const plansResponse = await getPublicPricingPlans();
        if (!plansResponse.success) {
          throw new Error(plansResponse.error || 'Failed to fetch pricing plans');
        }

        // Fetch active subscription
        const subscriptionResponse = await getActiveSubscription({organizationId});
        if (subscriptionResponse.success && subscriptionResponse.data) {
          setHasActiveSubscription(true);
          setSubscription(subscriptionResponse.data);

          // Set current currency and billing period
          if (subscriptionResponse.data.currency) {
            setSelectedCurrency(subscriptionResponse.data.currency);
            setOriginalCurrency(subscriptionResponse.data.currency);
          }

          // Set yearly billing based on existing subscription
          const isCurrentSubscriptionYearly =
            subscriptionResponse.data.billingPeriod === TimeUnit.YEAR;
          setIsYearly(isCurrentSubscriptionYearly);

          // Filter out the current plan and lower-level plans
          const currentPlanLevel = subscriptionResponse.data.pricingPlan?.level || 0;
          if (plansResponse.data && plansResponse.data.monthly && plansResponse.data.yearly) {
            // Get the appropriate plans based on billing period
            const availablePlans = isYearly
              ? plansResponse.data.yearly.filter((plan: any) => plan.level > currentPlanLevel)
              : plansResponse.data.monthly.filter((plan: any) => plan.level > currentPlanLevel);

            setPricingPlans(availablePlans);

            // Set yearly discount if available
            if (plansResponse.data.yearlyDiscount) {
              setYearlyDiscount(plansResponse.data.yearlyDiscount);
            }
          }
        } else {
          // No active subscription, show all active plans
          if (plansResponse.data) {
            const availablePlans = isYearly
              ? (plansResponse.data.yearly || []).filter((plan: any) => plan.active)
              : (plansResponse.data.monthly || []).filter((plan: any) => plan.active);

            setPricingPlans(availablePlans);

            // Set yearly discount if available
            if (plansResponse.data.yearlyDiscount) {
              setYearlyDiscount(plansResponse.data.yearlyDiscount);
            }
          }
        }

        // Fetch user data for payment information
        const userResponse = await getUserData();
        if (userResponse.success) {
          setUserData(userResponse.data);
        }

        setLoading(false);
      } catch (error: any) {
        logger.error('Error fetching data:', error);
        setError(error.message || 'Failed to load data');
        setLoading(false);
      }
    };

    fetchData();
  }, [organizationId, isYearly]);

  // Calculate transaction fee utility
  const getTotalWithFee = async (baseAmount: number, currency: Currency, provider: PaymentProvider): Promise<{
    baseAmount: number;
    fee: number;
    total: number
  }> => {
    try {
      const result = await calculateTransactionFeeAction({
        amount: baseAmount,
        currency,
        provider
      });

      if (result.success && result.data) {
        return {
          baseAmount,
          fee: result.data.fee,
          total: result.data.total
        };
      }

      // Return original amount if calculation fails
      return {
        baseAmount,
        fee: 0,
        total: baseAmount
      };
    } catch (error) {
      logger.error('Error calculating transaction fee:', error);
      return {
        baseAmount,
        fee: 0,
        total: baseAmount
      };
    }
  };

  // Create final checkout for payment
  const handleCheckout = async () => {
    try {
      setProcessingPayment(true);

      // Determine checkout method based on proration
      let checkoutResponse:SubscriptionDetailsResponse;

      if (hasActiveSubscription && proratedAmountData) {
        // Use prorated checkout
        checkoutResponse = await initializeProratedCheckout({
          organizationId,
          pricingPlanId: selectedPlanId,
          numberOfUsers: userCount,
          gateway: selectedGateway,
          currency: selectedCurrency,
          isYearly,
          callbackUrl: window.location.origin
        });
      } else {
        // Use standard checkout
        checkoutResponse = await initializeCheckout({
          organizationId,
          pricingPlanId: selectedPlanId,
          numberOfUsers: userCount,
          gateway: selectedGateway,
          currency: selectedCurrency,
          isYearly
        });
      }

      if (checkoutResponse.success && checkoutResponse.data) {
        setTransactionResult(checkoutResponse.data);
        setCurrentStep(4);
      } else {
        // Safe way to handle the error without type issues
        const errorMessage = !checkoutResponse.success &&
        typeof checkoutResponse.error === 'string' ?
          checkoutResponse.error : 'Failed to initialize payment';
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      logger.error('Checkout error:', error);
      toast.error(error.message || 'Failed to initialize payment');
    } finally {
      setProcessingPayment(false);
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = useCallback(async () => {
    try {
      toast.success('Payment successful! Redirecting...');
      const isProrated = hasActiveSubscription && proratedAmountData ? '&prorated=true' : '';
      setTimeout(() => {
        router.push(
          `/dashboard/organization/${organizationId}/billing/verification?reference=${transactionResult?.transactionId}${isProrated}`
        );
      }, 2000);
    } catch (error) {
      logger.error('Error handling payment success:', error);
    }
  }, [hasActiveSubscription, organizationId, proratedAmountData, transactionResult?.transactionId]);

  // Handle payment cancel/close
  const handlePaymentClose = () => {
    toast.info('Payment cancelled');
  };

  // Step navigation handlers
  const handleNextStep = () => {
    setCurrentStep(current => Math.min(current + 1, UPGRADE_STEPS.length));
  };

  const handlePreviousStep = () => {
    setCurrentStep(current => Math.max(current - 1, 1));
  };

  // Currency change handler
  const handleCurrencyChange = (currency: Currency) => {
    if (hasActiveSubscription && originalCurrency && currency !== originalCurrency) {
      setCurrencyError('You cannot change currency for an existing subscription upgrade.');
      return;
    }

    setCurrencyError(null);
    setSelectedCurrency(currency);
  };

  // Content rendering based on current step
  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-8 text-center">Upgrade Your Subscription</h1>

      {/* Progress indicator for steps */}
      <ProgressIndicator currentStep={currentStep} steps={UPGRADE_STEPS}/>

      {/* Step 1: Plan Selection */}
      {currentStep === 1 && (
        <Step1PlanSelection
          loading={loading}
          error={error}
          pricingPlans={pricingPlans}
          selectedPlanId={selectedPlanId}
          selectedCurrency={selectedCurrency}
          isYearly={isYearly}
          yearlyDiscount={yearlyDiscount}
          hasActiveSubscription={hasActiveSubscription}
          subscription={subscription}
          organizationId={organizationId}
          handlePlanSelect={setSelectedPlanId}
          handlePeriodChange={setIsYearly}
          handleCurrencyChange={handleCurrencyChange}
          handleNextStep={handleNextStep}
        />
      )}

      {/* Step 2: User Configuration */}
      {currentStep === 2 && (
        <Step2UserConfig
          selectedPlanDetails={selectedPlanDetails}
          userCount={userCount}
          selectedCurrency={selectedCurrency}
          hasActiveSubscription={hasActiveSubscription}
          processingPayment={processingPayment}
          handleUserCountChange={setUserCount}
          handlePreviousStep={handlePreviousStep}
          handleNextStep={handleNextStep}
        />
      )}

      {/* Step 3: Payment Method */}
      {currentStep === 3 && (
        <Step3PaymentMethod
          selectedPlanDetails={selectedPlanDetails}
          hasActiveSubscription={hasActiveSubscription}
          subscription={subscription}
          proratedAmountData={proratedAmountData}
          calculatingProration={calculatingProration}
          isDowngrade={isDowngrade}
          selectedCurrency={selectedCurrency}
          originalCurrency={originalCurrency}
          currencyError={currencyError}
          handleCurrencyChange={handleCurrencyChange}
          baseAmount={baseAmount}
          transactionFee={transactionFee}
          totalAmount={totalAmount}
          adjustedTotalAmount={adjustedTotalAmount}
          userCount={userCount}
          selectedGateway={selectedGateway}
          processingPayment={processingPayment}
          handleGatewayChange={setSelectedGateway}
          handlePreviousStep={handlePreviousStep}
          handleCheckout={handleCheckout}
          vatPercentage={vatPercentage}
          vatAmount={vatAmount}
          taxPercentage={taxPercentage}
          taxAmount={taxAmount}
        />
      )}

      {/* Step 4: Payment */}
      {currentStep === 4 && (
        <Step4Payment
          transactionResult={transactionResult}
          selectedPlanDetails={selectedPlanDetails}
          hasActiveSubscription={hasActiveSubscription}
          isDowngrade={isDowngrade}
          isUpgradeWithProration={!!proratedAmountData}
          proratedAmountData={proratedAmountData}
          userCount={userCount}
          selectedCurrency={selectedCurrency}
          selectedGateway={selectedGateway}
          handlePaymentSuccess={handlePaymentSuccess}
          handlePaymentClose={handlePaymentClose}
          processingPayment={processingPayment}
          handlePreviousStep={handlePreviousStep}
        />
      )}
    </div>
  );
}

'use client';

import {useEffect, useState} from 'react';
import {usePara<PERSON>, useRouter} from 'next/navigation';
import {Card} from '@/components/ui/Card';
import {Button} from '@/components/ui/Button';
import {Badge} from '@/components/ui/Badge';
import {Skeleton} from '@/components/ui/Skeleton';
import {getCurrentUser} from '@/server/actions/user-auth';
import {
  getActiveSubscriptionWithDetails,
  getTransactionHistory,
  cancelSubscriptionRenewal,
  reactivateSubscription
} from '@/server/actions/billing';
import {getOrgById} from '@/server/actions/organization-actions';
import {SubscriptionStatus} from '@/constants/pricing';
import {formatCurrency, formatDate} from '@/utils/format-utils';
import {PaymentStatus} from '@/constants/transactions';
import Link from "next/link";
import {LucideUserPlus2} from "lucide-react";
import {ConfirmDialog, confirmDialog} from '@/components/ui/ConfirmDialog';
import {useToast} from "@components/ui/Toast";
import {getFaqsByType} from '@/server/actions/faq-actions';
import {getOrganizationById} from "@server/actions/user-actions";
import ErrorView from "@components/ui/ErrorView";
import { logger } from '@/utils/logger';

// Type definitions for API responses
interface UserData {
  id: string;
  name: string;
  email: string;
  company: string;
  emailVerified: boolean;
}

interface Organization {
  _id: string;
  id: string;
  name: string;
  description?: string;
  logoUrl?: string;
  email?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  organizationProductMemberCount?: number;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

type SubscriptionDetailsResponse = Awaited<ReturnType<typeof getActiveSubscriptionWithDetails>>

interface TransactionHistoryResponse extends ApiResponse<any[]> {
}


export default function BillingPage() {
  const router = useRouter();
  const params = useParams();
  const organizationId = params.id as string;

  const [subscription, setSubscription] = useState<SubscriptionDetailsResponse['data']>();
  const [transactions, setTransactions] = useState<any[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'transactions' | 'faq'>('transactions');
  const [isProcessing, setIsProcessing] = useState(false);
  const [faqs, setFaqs] = useState<any[]>([]);
  const {error: showError} = useToast();
  useEffect(() => {
    const fetchData = async () => {
      if (!organizationId) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch user data
        const userResult = await getCurrentUser();
        if (!userResult.success || !userResult.data) {
          router.push('/auth?mode=login');
          return;
        }


        // Update the organization's subscription plan based on active subscriptions
        // await updateOrganizationSubscriptionPlan(organizationId);

        // Fetch organization data
        const orgResult = await getOrganizationById(organizationId);
        if (!orgResult.success) {
          setError(orgResult.error);
          setIsLoading(false);
          return;
        }

        // Fetch billing FAQs
        const faqsResult = await getFaqsByType('billing');
        if (faqsResult.success && faqsResult.data) {
          setFaqs(faqsResult.data);
        }

        // Convert to string representation for our UI
        const orgData = {
          ...orgResult.data,
          _id: orgResult?.data?.id.toString(),
          id: orgResult?.data?.id.toString(),
        };

        setOrganization(orgData as unknown as Organization);

        // Fetch subscription with complete details in a single query
        const subscriptionResult = await getActiveSubscriptionWithDetails({organizationId});
        if (subscriptionResult.success && subscriptionResult.data) {
          setSubscription(subscriptionResult.data);

          // Set organization member count from the subscription data
          const memberCount = subscriptionResult.data.organizationProductMemberCount;
          if (memberCount !== undefined) {
            setOrganization(prev => prev ? {
              ...prev,
              organizationProductMemberCount: memberCount
            } as Organization : prev);
          }
        } else {
          setSubscription(undefined);
        }

        // Fetch transaction history with proper type assertion
        const transactionsResult = await getTransactionHistory({
          organizationId,
          limit: 20
        }) as TransactionHistoryResponse;

        if (transactionsResult.success && transactionsResult.data) {
          setTransactions(transactionsResult.data);
        } else {
          setTransactions([]);
        }
      } catch (err: any) {
        logger.error('Error loading billing data:', err);
        setError(err.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [organizationId, router]);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
      case PaymentStatus.PAID:
        return 'success';
      case SubscriptionStatus.CANCELED:
      case PaymentStatus.FAILED:
        return 'danger';
      case SubscriptionStatus.TRIAL:
      case PaymentStatus.PENDING:
        return 'warning';
      case SubscriptionStatus.PAST_DUE:
        return 'danger';
      default:
        return 'default';
    }
  };

  const handleUpgrade = () => {
    router.push(`/dashboard/organization/${organizationId}/billing/upgrade`);
  };

  const handleManagePayment = () => {
    router.push(`/dashboard/organization/${organizationId}/billing/payment-methods`);
  };

  const handleCancelRenewal = async () => {
    if (!subscription) return;

    confirmDialog({
      message: `Your subscription will remain active until ${formatDate(subscription.endDate)}. After that date, your subscription will expire.`,
      header: 'Cancel Subscription Renewal?',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'bg-red-500 hover:bg-red-600',
      accept: async () => {
        setIsProcessing(true);
        try {
          const result = await cancelSubscriptionRenewal({
            organizationId,
            subscriptionId: subscription.id
          });

          if (result.success) {
            // Refresh the subscription data
            const subscriptionResult = await getActiveSubscriptionWithDetails({organizationId}) as SubscriptionDetailsResponse;
            if (subscriptionResult.success && subscriptionResult.data) {
              setSubscription(subscriptionResult.data);
            }
          } else {
            // TypeScript safety by checking if error exists in the result
            const errorMessage = 'error' in result ? result.error : 'Failed to cancel subscription';
            setError(errorMessage);
          }
        } catch (error: any) {
          logger.error('Error canceling subscription:', error);
          setError(error.message || 'An unexpected error occurred');
        } finally {
          setIsProcessing(false);
        }
      },
      reject: () => {
        // Do nothing on reject
      }
    });
  };

  const handleReactivate = async () => {
    if (!subscription) return;

    confirmDialog({
      message: 'Your auto renew will be reactivated and will automatically renew at the end of the billing cycle.',
      header: 'Reactivate auto renew Subscription?',
      icon: 'pi pi-info-circle',
      acceptClassName: 'bg-gradient-to-r from-[#8178E8] to-[#6964D3]',
      accept: async () => {
        setIsProcessing(true);
        try {
          const result = await reactivateSubscription({
            organizationId,
            subscriptionId: subscription.id
          });

          if (result.success) {
            // Refresh the subscription data
            const subscriptionResult = await getActiveSubscriptionWithDetails({organizationId}) as SubscriptionDetailsResponse;
            if (subscriptionResult.success && subscriptionResult.data) {
              setSubscription(subscriptionResult.data);
            }
          } else {
            // TypeScript safety by checking if error exists in the result
            const errorMessage = 'error' in result ? result.error : 'Failed to reactivate subscription';
            showError(errorMessage);
          }
        } catch (error: any) {
          logger.error('Error reactivating subscription:', error);
        } finally {
          setIsProcessing(false);
        }
      },
      reject: () => {
        // Do nothing on reject
      }
    });
  };

  // Helper to determine the subscription status display
  const getSubscriptionStatusDisplay = () => {
    if (!subscription) return null;

    if (subscription.status === SubscriptionStatus.CANCELED) {
      return (
        <div className="flex items-center">
          <Badge className="ml-2" variant="warning">
            Pending Cancellation
          </Badge>
        </div>
      );
    } else if (subscription.status === SubscriptionStatus.EXPIRED) {
      return (
        <div className="flex items-center">
          <Badge className="ml-2" variant="danger">
            Expired
          </Badge>
        </div>
      );
    } else if (subscription.status === SubscriptionStatus.ACTIVE) {
      return (
        <div className="flex items-center">
          <Badge className="ml-2" variant="success">
            Active
          </Badge>
        </div>
      );
    }

    return (
      <Badge className="ml-2" variant={getStatusBadgeVariant(subscription.status)}>
        {subscription.status}
      </Badge>
    );
  };

  // Helper to render subscription action buttons based on status
  const renderSubscriptionActions = () => {
    if (!subscription) {
      return (
        <Button
          className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
          onClick={handleUpgrade}
        >
          Subscribe
        </Button>
      );
    }

    if (!subscription.autoRenewal) {
      return (
        <div className="space-y-3">
          <div className="text-sm text-amber-600 dark:text-amber-400">
            Your subscription will remain active until {formatDate(subscription.endDate)}
          </div>
          <Button
            className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
            onClick={handleReactivate}
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : 'Reactivate'}
          </Button>
        </div>
      );
    } else if (subscription.status === SubscriptionStatus.EXPIRED) {
      return (
        <Button
          className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
          onClick={handleUpgrade}
        >
          Subscribe
        </Button>
      );
    } else if (subscription.status === SubscriptionStatus.ACTIVE) {
      return (
        <div className="space-y-3">
          <Button
            className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
            onClick={handleUpgrade}
          >
            Upgrade
          </Button>
          <Button
            variant="outline"
            className="w-full border-red-500 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20"
            onClick={handleCancelRenewal}
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : 'Cancel Renewal'}
          </Button>
        </div>
      );
    }

    // Default action
    return (
      <Button
        variant="outline"
        className="w-full border-[#6964D3] text-[#6964D3]"
        onClick={handleUpgrade}
      >
        Manage Subscription
      </Button>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F5F7FA] dark:bg-[#0a0a14] p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center mb-8">
            <div className="w-10 h-10 rounded-full bg-[#F3F3F3] dark:bg-[#1e1e28] mr-4">
              <Skeleton className="h-10 w-10 rounded-full"/>
            </div>
            <div>
              <Skeleton className="h-6 w-48 mb-1"/>
              <Skeleton className="h-4 w-24"/>
            </div>
          </div>

          <div className="flex justify-between items-center mb-6">
            <Skeleton className="h-8 w-64"/>
            <Skeleton className="h-10 w-32"/>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Skeleton className="h-64 rounded-xl"/>
            <Skeleton className="h-64 rounded-xl"/>
            <Skeleton className="h-64 rounded-xl"/>
          </div>

          <Skeleton className="h-96 rounded-xl mb-8"/>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorView error={error} buttonText="Back to Dashboard" onButtonClick={() => router.push('/dashboard')}/>
    );
  }

  return (
    <div className="min-h-screen bg-[#F5F7FA] dark:bg-[#0a0a14]">
      {/* Organization header */}
      <div
        className="bg-white dark:bg-[#1e1e28] border-b border-gray-200 dark:border-gray-700 py-4 px-6 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <button
              className="mr-4 text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-[#6964D3] dark:hover:text-white transition-colors"
              onClick={() => router.push(`/dashboard/organization/${organizationId}`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
            </button>
            <div
              className="w-10 h-10 rounded-full bg-[#6964D3] flex items-center justify-center text-white text-lg font-semibold mr-3">
              {organization?.name?.[0] || 'O'}
            </div>
            <div>
              <h1 className="text-xl font-semibold">{organization?.name || 'Organization'}</h1>
              <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
                {organization?.organizationProductMemberCount || 0} Members
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              className="border-[#6964D3] text-[#6964D3]"
              onClick={() => router.push(`/dashboard/organization/${organizationId}/settings`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                <circle cx="12" cy="12" r="3"></circle>
                <path
                  d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
              Settings
            </Button>
            <Button
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
              onClick={handleUpgrade}
            >
              {subscription?.status === SubscriptionStatus.ACTIVE
                ? 'Manage Subscription'
                : 'Upgrade Plan'
              }
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <div className="max-w-7xl mx-auto">
          <div className="mb-6">
            <h2 className="text-2xl font-bold mb-2">Billing & Subscription</h2>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">
              Manage your subscription, payment methods, and billing history
            </p>
          </div>

          {/* Subscription Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            {/* Current Plan */}
            <Card className="bg-white dark:bg-[#1e1e28] shadow-sm rounded-xl overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-800">
                <h3 className="text-lg font-semibold">Current Plan</h3>
              </div>
              <div className="p-6">
                {subscription ? (
                  <>
                    <div className="flex items-center justify-between mb-4">
                      <span className="font-medium text-xl">{subscription.pricingPlan?.name || 'Standard Plan'}</span>
                      {getSubscriptionStatusDisplay()}
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 mb-5">
                      {subscription.pricingPlan?.description || 'Basic access to all features'}
                    </p>

                    <div className="bg-[#F8F9FD] dark:bg-[#15151e] rounded-lg p-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Billing Cycle</span>
                        <span className="font-medium">
                          {subscription.pricingPlan?.timeUnit === 'month' ? 'Monthly' :
                            subscription.pricingPlan?.timeUnit === 'year' ? 'Yearly' :
                              `${subscription.pricingPlan?.duration || 1} ${subscription.pricingPlan?.timeUnit || 'month'}(s)`}
                        </span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Renewal Date</span>
                        <span className="font-medium">{formatDate(subscription.endDate)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Price</span>
                        <span className="font-medium">
                          {subscription.pricingPlan?.isPerUser
                            ? `${formatCurrency(subscription.perUserPrice || 0, subscription.currency)} / user`
                            : formatCurrency(subscription.pricingPlan?.amountUSD || 0, subscription.currency)}
                        </span>
                      </div>
                    </div>

                    <div className="text-center">
                      {renderSubscriptionActions()}
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="rounded-full bg-[#F3F3F3] dark:bg-[#15151e] p-4 mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                      </svg>
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 mb-4 text-center">No active subscription found</p>
                    <Button
                      className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
                      onClick={handleUpgrade}
                    >
                      Select a Plan
                    </Button>
                  </div>
                )}
              </div>
            </Card>

            {/* Usage Summary */}
            <Card className="bg-white dark:bg-[#1e1e28] shadow-sm rounded-xl overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-800">
                <h3 className="text-lg font-semibold">Usage Summary</h3>
              </div>
              <div className="p-6">
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Active Users</span>
                    <span className="font-medium text-lg">{subscription?.numberOfUsers || 0}</span>
                  </div>
                  <div className="h-2 bg-[#F3F3F3] dark:bg-[#15151e] rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
                      style={{width: `${Math.min(((subscription?.organizationProductMemberCount || 0) / subscription?.numberOfUsers!) * 100, 100)}%`}}
                    ></div>
                  </div>
                  <div className="flex justify-between mt-1">
                    <span className="text-xs text-gray-500">{subscription?.organizationProductMemberCount || 0}</span>
                    <span
                      className="text-xs text-gray-500">{subscription?.numberOfUsers || 0} user{subscription?.numberOfUsers! > 1 ? 's' : ''}</span>
                  </div>
                </div>
                <div className="bg-[#F8F9FD] dark:bg-[#15151e] rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Per User Price</span>
                    <span className="font-medium">
                      {subscription?.pricingPlan?.isPerUser
                        ? formatCurrency(subscription.perUserPrice || 0, subscription.currency)
                        : 'N/A'}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500 dark:text-gray-400">Current Total</span>
                    <span className="font-medium">
                      {subscription?.pricingPlan?.isPerUser
                        ? formatCurrency(
                          (subscription.perUserPrice || 0) * (subscription.numberOfUsers || 0),
                          subscription.currency
                        )
                        : formatCurrency(
                          subscription?.pricingPlan?.amountUSD || 0,
                          subscription?.currency
                        )}
                    </span>
                  </div>
                  <Link href={`/dashboard/organization/${organizationId}/upgrade-user-limit`}>
                    <div className="flex justify-between items-center mt-2">
                      <span className="text-sm text-gray-500 dark:text-gray-400">Upgrade Limit</span>
                      <LucideUserPlus2 size={23}/>
                    </div>
                  </Link>
                </div>
                <div className="text-center">
                  <Button
                    variant="outline"
                    className="w-full border-[#6964D3] text-[#6964D3]"
                    onClick={() => router.push(`/dashboard/organization/${organizationId}/team`)}
                  >
                    Manage Users
                  </Button>
                </div>
              </div>
            </Card>

            {/* Billing Summary */}
            <Card className="bg-white dark:bg-[#1e1e28] shadow-sm rounded-xl overflow-hidden">
              <div className="p-6 border-b border-gray-100 dark:border-gray-800">
                <h3 className="text-lg font-semibold">Payment Details</h3>
              </div>
              <div className="p-6">
                {subscription ? (
                  <>
                    <div className="mb-5">
                      <div className="flex items-center mb-3">
                        <div
                          className="w-8 h-8 rounded bg-[#F3F3F3] dark:bg-[#15151e] flex items-center justify-center mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                               stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                            <line x1="1" y1="10" x2="23" y2="10"></line>
                          </svg>
                        </div>
                        <div>
                          <span className="block font-medium">Card ending in 4242</span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">Expires 12/25</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-[#F8F9FD] dark:bg-[#15151e] rounded-lg p-4 mb-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Current Period</span>
                        <span className="font-medium">
                          {formatDate(subscription.startDate)} - {formatDate(subscription.endDate)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Next Payment</span>
                        <span className="font-medium">
                          {formatDate(subscription.endDate)}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500 dark:text-gray-400">Amount</span>
                        <span className="font-medium">
                          {subscription.pricingPlan?.isPerUser
                            ? formatCurrency(
                              (subscription.perUserPrice || 0) * (subscription.numberOfUsers || 0),
                              subscription.currency
                            )
                            : formatCurrency(
                              subscription.pricingPlan?.amountUSD || 0,
                              subscription.currency
                            )}
                        </span>
                      </div>
                    </div>

                    <div className="text-center">
                      <Button
                        variant="outline"
                        className="w-full border-[#6964D3] text-[#6964D3]"
                        onClick={handleManagePayment}
                      >
                        Update Payment Method
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="flex flex-col items-center justify-center py-10">
                    <div className="rounded-full bg-[#F3F3F3] dark:bg-[#15151e] p-4 mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                        <line x1="1" y1="10" x2="23" y2="10"></line>
                      </svg>
                    </div>
                    <p className="text-gray-500 dark:text-gray-400 mb-4 text-center">No payment method found</p>
                    <Button
                      className="bg-gradient-to-r from-[#8178E8] to-[#6964D3]"
                      onClick={handleManagePayment}
                    >
                      Add Payment Method
                    </Button>
                  </div>
                )}
              </div>
            </Card>
          </div>

          {/* Transaction History & FAQ Tabs */}
          <Card className="bg-white dark:bg-[#1e1e28] shadow-sm rounded-xl overflow-hidden mb-8">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <div className="flex">
                <button
                  className={`px-6 py-4 font-medium border-b-2 transition-colors ${
                    activeTab === 'transactions'
                      ? 'border-[#6964D3] text-[#6964D3]'
                      : 'border-transparent text-gray-700 dark:text-gray-200 hover:text-[#6964D3]'
                  }`}
                  onClick={() => setActiveTab('transactions')}
                >
                  Transaction History
                </button>
                <button
                  className={`px-6 py-4 font-medium border-b-2 transition-colors ${
                    activeTab === 'faq'
                      ? 'border-[#6964D3] text-[#6964D3]'
                      : 'border-transparent text-gray-700 dark:text-gray-200 hover:text-[#6964D3]'
                  }`}
                  onClick={() => setActiveTab('faq')}
                >
                  Billing FAQ
                </button>
              </div>
            </div>

            {activeTab === 'transactions' ? (
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Transaction History</h3>
                {transactions.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full">
                      <thead>
                      <tr className="bg-[#F8F9FD] dark:bg-[#15151e]">
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Description
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status
                        </th>
                        <th
                          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Payment
                          Method
                        </th>
                      </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {transactions.map((transaction) => (
                        <tr key={transaction.id}
                            className="hover:bg-[#F8F9FD] dark:hover:bg-[#15151e] transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap text-sm">{formatDate(transaction.createdAt)}</td>
                          <td className="px-6 py-4 text-sm">{transaction.description}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {formatCurrency(transaction.amount || 0, transaction.currency || subscription?.currency)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            <Badge variant={getStatusBadgeVariant(transaction.status)}>
                              {transaction.status}
                            </Badge>
                          </td>
                          <td
                            className="px-6 py-4 whitespace-nowrap text-sm capitalize">{transaction.paymentMethod}</td>
                        </tr>
                      ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="rounded-full bg-[#F3F3F3] dark:bg-[#15151e] p-4 inline-block mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                           stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                    </div>
                    <p className="text-gray-500 dark:text-gray-400">No transactions yet</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="p-6">
                <h3 className="text-lg font-semibold mb-4">Billing FAQs</h3>
                <div className="space-y-6">
                  {faqs.length > 0 ? (
                    faqs.map((faq) => (
                      <div key={faq._id} className="bg-[#F8F9FD] dark:bg-[#15151e] rounded-lg p-4">
                        <h4 className="font-medium mb-2">{faq.question}</h4>
                        <p className="text-gray-500 dark:text-gray-400 text-sm">
                          {faq.answer}
                        </p>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <div className="rounded-full bg-[#F3F3F3] dark:bg-[#15151e] p-4 inline-block mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                             stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"></circle>
                          <line x1="12" y1="16" x2="12" y2="16.01"></line>
                          <path d="M12 13a1.5 1.5 0 0 0 1.5-1.5v-2a1.5 1.5 0 0 0-3 0v2a1.5 1.5 0 0 0 1.5 1.5z"></path>
                        </svg>
                      </div>
                      <p className="text-gray-500 dark:text-gray-400">No FAQs available</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>
      <ConfirmDialog/>
    </div>
  );
}

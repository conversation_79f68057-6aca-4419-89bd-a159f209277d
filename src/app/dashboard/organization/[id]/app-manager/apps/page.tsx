import { Metadata } from 'next';
import AppsList from '@/components/app-manager/AppsList';
import DashboardShell from '@/components/dashboard/DashboardShell';

export const metadata: Metadata = {
  title: 'App Manager - Apps',
  description: 'Manage your applications',
};

export default async function AppsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return (
    <DashboardShell
      title="App Manager"
      subtitle="Register applications once and manage multiple services"
    >
      <AppsList organizationId={id} />
    </DashboardShell>
  );
}

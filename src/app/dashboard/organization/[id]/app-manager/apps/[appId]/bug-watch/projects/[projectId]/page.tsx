import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchProjectDetails from '@/components/bugwatch/BugWatchProjectDetails';

export const metadata: Metadata = {
  title: 'Bug Watch - Project Details',
  description: 'View and manage Bug Watch project configuration',
};

interface ProjectDetailsPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
    projectId: string;
  }>;
}

export default async function ProjectDetailsPage({ params }: ProjectDetailsPageProps) {
  const { id, appId, projectId } = await params;

  return (
    <DashboardShell
      title="Project Details"
      subtitle="View and manage Bug Watch project configuration"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/bug-watch/projects`}
      backLinkText="Back to Projects"
    >
      <BugWatchProjectDetails 
        organizationId={id} 
        appId={appId} 
        projectId={projectId} 
      />
    </DashboardShell>
  );
}

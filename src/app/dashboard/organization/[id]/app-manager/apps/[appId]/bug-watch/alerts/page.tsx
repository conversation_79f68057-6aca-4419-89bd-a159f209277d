import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchAlerts from '@/components/bugwatch/BugWatchAlerts';

export const metadata: Metadata = {
  title: 'Bug Watch - Alerts',
  description: 'Configure error alerts and notifications',
};

interface AlertsPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
  }>;
}

export default async function AlertsPage({ params }: AlertsPageProps) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Alert Rules"
      subtitle="Configure error alerts and notifications"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/bug-watch`}
      backLinkText="Back to Bug Watch"
    >
      <BugWatchAlerts organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

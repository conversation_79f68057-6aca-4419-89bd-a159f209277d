import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchProjects from '@/components/bugwatch/BugWatchProjects';

export const metadata: Metadata = {
  title: 'Bug Watch - Projects',
  description: 'Manage Bug Watch projects and configurations',
};

interface ProjectsPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
  }>;
}

export default async function ProjectsPage({ params }: ProjectsPageProps) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Projects"
      subtitle="Manage Bug Watch projects and configurations"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/bug-watch`}
      backLinkText="Back to Bug Watch"
    >
      <BugWatchProjects organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

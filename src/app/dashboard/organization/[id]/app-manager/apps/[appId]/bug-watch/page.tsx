import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchDashboard from '@/components/bugwatch/BugWatchDashboard';

export const metadata: Metadata = {
  title: 'Bug Watch Dashboard',
  description: 'Monitor and manage application errors and bugs',
};

interface BugWatchPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
  }>;
}

export default async function BugWatchPage({ params }: BugWatchPageProps) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Bug Watch"
      subtitle="Monitor and manage application errors"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}`}
      backLinkText="Back to App Details"
    >
      <BugWatchDashboard organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

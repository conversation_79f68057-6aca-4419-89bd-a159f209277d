import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchErrorDetails from '@/components/bugwatch/BugWatchErrorDetails';

export const metadata: Metadata = {
  title: 'Bug Watch - Error Details',
  description: 'View detailed error information and manage resolution',
};

interface ErrorDetailsPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
    errorId: string;
  }>;
}

export default async function ErrorDetailsPage({ params }: ErrorDetailsPageProps) {
  const { id, appId, errorId } = await params;

  return (
    <DashboardShell
      title="Error Details"
      subtitle="View detailed error information and manage resolution"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/bug-watch/errors`}
      backLinkText="Back to Errors"
    >
      <BugWatchErrorDetails 
        organizationId={id} 
        appId={appId} 
        errorId={errorId} 
      />
    </DashboardShell>
  );
}

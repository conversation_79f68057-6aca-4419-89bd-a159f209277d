import { Metadata } from 'next';
import AppDetails from '@/components/app-manager/AppDetails';
import DashboardShell from '@/components/dashboard/DashboardShell';

export const metadata: Metadata = {
  title: 'App Details',
  description: 'View and manage application details',
};

export default async function AppDetailsPage({ params }: { params: Promise<{ id: string; appId: string }> }) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="App Details"
      subtitle="View and manage your application"
      backLink={`/dashboard/organization/${id}/app-manager/apps`}
      backLinkText="Back to Apps"
    >
      <AppDetails organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

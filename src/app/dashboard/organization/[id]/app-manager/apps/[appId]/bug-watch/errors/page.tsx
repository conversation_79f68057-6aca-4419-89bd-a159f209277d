import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchErrorsList from '@/components/bugwatch/BugWatchErrorsList';

export const metadata: Metadata = {
  title: 'Bug Watch - Errors',
  description: 'View and manage application errors',
};

interface ErrorsPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
  }>;
}

export default async function ErrorsPage({ params }: ErrorsPageProps) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Errors"
      subtitle="View and manage application errors"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/bug-watch`}
      backLinkText="Back to Bug Watch"
    >
      <BugWatchErrorsList organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

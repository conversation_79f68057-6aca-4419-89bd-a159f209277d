import { Metadata } from 'next';
import InheritanceManager from '@/components/app-manager/InheritanceManager';
import DashboardShell from '@/components/dashboard/DashboardShell';

export const metadata: Metadata = {
  title: 'Inheritance Manager',
  description: 'Manage environment variable inheritance between applications',
};

export default async function InheritancePage({ params }: { params: Promise<{ id: string; appId: string }> }) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Inheritance Manager"
      subtitle="Manage environment variable inheritance between applications"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/secret-keys`}
      backLinkText="Back to Secret Keys"
    >
      <InheritanceManager organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

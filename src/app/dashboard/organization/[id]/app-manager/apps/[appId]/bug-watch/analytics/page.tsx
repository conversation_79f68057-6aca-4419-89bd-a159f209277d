import { Metadata } from 'next';
import DashboardShell from '@/components/dashboard/DashboardShell';
import BugWatchAnalytics from '@/components/bugwatch/BugWatchAnalytics';

export const metadata: Metadata = {
  title: 'Bug Watch - Analytics',
  description: 'View error analytics and trends',
};

interface AnalyticsPageProps {
  params: Promise<{ 
    id: string; 
    appId: string; 
  }>;
}

export default async function AnalyticsPage({ params }: AnalyticsPageProps) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Analytics"
      subtitle="View error analytics and trends"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}/bug-watch`}
      backLinkText="Back to Bug Watch"
    >
      <BugWatchAnalytics organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

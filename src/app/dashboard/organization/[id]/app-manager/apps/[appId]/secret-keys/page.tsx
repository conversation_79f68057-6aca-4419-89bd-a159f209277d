import { Metada<PERSON> } from 'next';
import Secret<PERSON>eyManager from '@/components/app-manager/SecretKeyManager';
import DashboardShell from '@/components/dashboard/DashboardShell';

export const metadata: Metadata = {
  title: 'Secret Key Manager',
  description: 'Manage encrypted environment variables',
};

export default async function SecretKeyManagerPage({ params }: { params: Promise<{ id: string; appId: string }> }) {
  const { id, appId } = await params;

  return (
    <DashboardShell
      title="Secret Key Manager"
      subtitle="Manage encrypted environment variables for your application"
      backLink={`/dashboard/organization/${id}/app-manager/apps/${appId}`}
      backLinkText="Back to App Details"
    >
      <SecretKeyManager organizationId={id} appId={appId} />
    </DashboardShell>
  );
}

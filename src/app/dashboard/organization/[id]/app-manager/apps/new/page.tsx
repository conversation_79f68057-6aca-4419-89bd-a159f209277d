import { Metadata } from 'next';
import CreateAppForm from '@/components/app-manager/CreateAppForm';
import DashboardShell from '@/components/dashboard/DashboardShell';

export const metadata: Metadata = {
  title: 'Create New App',
  description: 'Create a new application in App Manager',
};

export default async function CreateAppPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;

  return (
    <DashboardShell
      title="Create New App"
      subtitle="Register a new application to manage its services"
      backLink={`/dashboard/organization/${id}/app-manager/apps`}
      backLinkText="Back to Apps"
    >
      <CreateAppForm organizationId={id} />
    </DashboardShell>
  );
}

'use client';

import {useState} from 'react';
import {Dialog} from '@/components/ui/Dialog';
import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {UserOrgRole} from '@/constants/role-constants';
import {useToast} from '@/components/ui/Toast';
import {createInvite, createProductInvite} from '@/server/actions/invite-actions';
import {SelectUserPermissionGroup} from "@components/ui/SelectUserPermissionGroup";
import {FormField} from "@components/ui/Form";

interface InviteUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  organizationId: string;
  type: 'organization' | 'product';
  productId?: string;
  onSuccess?: () => void;
}

export default function InviteUserModal({
  isOpen,
  onClose,
  organizationId,
  type,
  productId,
  onSuccess
}: InviteUserModalProps) {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState(type === 'organization' ? UserOrgRole.MEMBER : 'PRODUCT_USER');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { success, error: showError, warn, info } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);

    try {
      if (type === 'organization') {
        const result = await createInvite({
          organizationId,
          email,
          role,
        });

        if (!result.success) {
          setError(result.message || 'Failed to invite user');
          return;
        }

        success('Invitation sent', `An invitation email has been sent to ${email}`);
      } else if (type === 'product' && productId) {
        const result = await createProductInvite({
          organizationId,
          productCode: productId,
          email,
          role,
        });

        if (!result.success) {
          setError(result.message || 'Failed to invite user');
          return;
        }

        success('Invitation sent', `An invitation email has been sent to ${email}`);
      } else {
        setError('Invalid invitation type or missing product ID');
        return;
      }

      // Reset form and close modal
      setEmail('');
      setRole(type === 'organization' ? UserOrgRole.MEMBER : 'PRODUCT_USER');
      onSuccess?.();
      onClose();
    } catch (error: any) {
      setError(error.message || 'An error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      title={type === 'organization' ? 'Invite Organization User' : 'Invite Product User'}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 p-4 text-red-800 dark:text-red-300 text-sm">
            {error}
          </div>
        )}

        <div className="space-y-2">
          <FormField label="Email Address" required>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </FormField>
        </div>

        <div className="space-y-2">
          <FormField label="Role" required>
            <SelectUserPermissionGroup
              organizationId={organizationId}
              id="role"
              value={role}
              onChange={(value) => setRole(value as string)}
              required
            />
          </FormField>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-blue-600 dark:text-blue-300 text-sm">
          <p className="font-medium">Note:</p>
          <p>
            {type === 'organization'
              ? 'Organization users can access all products within the organization based on their role.'
              : 'Product users can only access the specific product they are invited to.'}
          </p>
          <p className="mt-2">
            An invitation email will be sent to the user with instructions to set up their account.
          </p>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="ghost"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending Invitation...' : 'Send Invitation'}
          </Button>
        </div>
      </form>
    </Dialog>
  );
}

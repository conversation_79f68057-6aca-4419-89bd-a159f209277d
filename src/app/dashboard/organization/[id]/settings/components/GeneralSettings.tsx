'use client';

import {useCallback, useEffect, useState} from 'react';
import {
  configureOrganizationWebhook,
  deleteOrganization,
  testOrganizationWebhook,
  updateOrganization
} from '@/server/actions/organization-actions';
import {Card} from '@/components/ui/Card';
import {Input} from '@/components/ui/Input';
import {Button} from '@/components/ui/Button';
import {Textarea} from '@/components/ui/Textarea';
import {Select} from '@/components/ui/Select';
import {Switch} from '@/components/ui/Switch';
import {Badge} from '@/components/ui/Badge';
import {useToast} from '@/components/ui/Toast';

// Industry options
const industryOptions = [
  { label: 'Technology', value: 'technology' },
  { label: 'Healthcare', value: 'healthcare' },
  { label: 'Financial Services', value: 'financial_services' },
  { label: 'Education', value: 'education' },
  { label: 'Retail', value: 'retail' },
  { label: 'Manufacturing', value: 'manufacturing' },
  { label: 'Professional Services', value: 'professional_services' },
  { label: 'Media & Entertainment', value: 'media_entertainment' },
  { label: 'Non-profit', value: 'non_profit' },
  { label: 'Government', value: 'government' },
  { label: 'Other', value: 'other' }
];

interface OrganizationData {
  id: string;
  name: string;
  description: string | null;
  logoUrl: string | null;
  email: string | null;
  domain: string | null;
  industry: string | null;
  subscriptionPlan: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  memberCount: number;
  members: {
    userId: string;
    role: string;
    name: string;
    email: string;
  }[];
  userRole: string | null;
  settings?: {
    webhook?: {
      url: string;
      enabled: boolean;
    };
  };
}

interface GeneralSettingsProps {
  organizationId: string;
  organizationData: OrganizationData;
  onUpdateSuccess: (updatedData: Partial<OrganizationData>) => void;
}

export default function GeneralSettings({
  organizationId,
  organizationData,
  onUpdateSuccess
}: GeneralSettingsProps) {
  const { success, error, info, warn } = useToast();

  // Form state
  const [name, setName] = useState(organizationData.name || '');
  const [description, setDescription] = useState(organizationData.description || '');
  const [email, setEmail] = useState(organizationData.email || '');
  const [domain, setDomain] = useState(organizationData.domain || '');
  const [industry, setIndustry] = useState(organizationData.industry || '');
  const [isActive, setIsActive] = useState(organizationData.isActive);
  const [webhookUrl, setWebhookUrl] = useState(organizationData.settings?.webhook?.url || '');
  const [webhookEnabled, setWebhookEnabled] = useState(organizationData.settings?.webhook?.enabled || false);

  // UI state
  const [isSaving, setIsSaving] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [isWebhookTestLoading, setIsWebhookTestLoading] = useState(false);

  // Form Validation
  const [nameError, setNameError] = useState('');
  const [emailError, setEmailError] = useState('');
  const [webhookError, setWebhookError] = useState('');

  // Track form changes
  useEffect(() => {
    const isDirty =
      name !== organizationData.name ||
      description !== organizationData.description ||
      email !== organizationData.email ||
      domain !== organizationData.domain ||
      industry !== organizationData.industry ||
      isActive !== organizationData.isActive;

    setIsDirty(isDirty);
  }, [name, description, email, domain, industry, isActive, organizationData]);

  const validateForm = useCallback(() => {
    let isValid = true;

    // Validate name
    if (!name.trim()) {
      setNameError('Organization name is required');
      isValid = false;
    } else if (name.length < 2) {
      setNameError('Organization name must be at least 2 characters');
      isValid = false;
    } else {
      setNameError('');
    }

    // Validate email if provided
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    } else {
      setEmailError('');
    }

    // Validate webhook URL if enabled
    if (webhookEnabled && webhookUrl) {
      try {
        new URL(webhookUrl);
        setWebhookError('');
      } catch (e) {
        setWebhookError('Please enter a valid URL');
        isValid = false;
      }
    } else {
      setWebhookError('');
    }

    return isValid;
  }, [name, email, webhookEnabled, webhookUrl]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSaving(true);

    try {
      const result = await updateOrganization({
        id: organizationId,
        name,
        description,
        email,
        domain,
        industry,
        isActive
      });

      if (result.success) {
        onUpdateSuccess({
          name,
          description,
          email,
          domain,
          industry,
          isActive
        });

        setIsDirty(false);
      } else {
        error("Error", result.error || "Failed to update organization settings");
      }
    } catch (error: any) {
      console.error('Error updating organization:', error);
      error("Error", "An unexpected error occurred");
    } finally {
      setIsSaving(false);
    }
  }, [organizationId, name, description, email, domain, industry, isActive, validateForm, onUpdateSuccess, error]);

  const handleCancel = useCallback(() => {
    // Reset form to original values
    setName(organizationData.name || '');
    setDescription(organizationData.description || '');
    setEmail(organizationData.email || '');
    setDomain(organizationData.domain || '');
    setIndustry(organizationData.industry || '');
    setIsActive(organizationData.isActive);

    setIsDirty(false);
  }, [organizationData]);

  const testWebhook = useCallback(async () => {
    if (!webhookUrl) {
      setWebhookError('Webhook URL is required');
      return;
    }

    try {
      new URL(webhookUrl);
    } catch (e) {
      setWebhookError('Please enter a valid URL');
      return;
    }

    setIsWebhookTestLoading(true);

    try {
      const result = await testOrganizationWebhook({
        organizationId,
        webhookUrl
      });

      if (result.success) {
        success("Webhook Test", "Test notification sent successfully");
      } else {
        error("Webhook Test Failed", result.error || "Failed to send test notification");
      }
    } catch (error: any) {
      console.error('Error testing webhook:', error);
      error("Error", "An unexpected error occurred while testing the webhook");
    } finally {
      setIsWebhookTestLoading(false);
    }
  }, [organizationId, webhookUrl, success, error]);

  const saveWebhookSettings = useCallback(async () => {
    if (!webhookUrl && webhookEnabled) {
      setWebhookError('Webhook URL is required when webhooks are enabled');
      return;
    }

    if (webhookEnabled) {
      try {
        new URL(webhookUrl);
      } catch (e) {
        setWebhookError('Please enter a valid URL');
        return;
      }
    }

    setIsWebhookTestLoading(true);

    try {
      const result = await configureOrganizationWebhook({
        organizationId,
        webhookUrl,
        enabled: webhookEnabled
      });

      if (result.success) {
        success("Success", "Webhook settings updated successfully");
      } else {
        error("Error", result.error || "Failed to update webhook settings");
      }
    } catch (error: any) {
      console.error('Error saving webhook settings:', error);
      error("Error", "An unexpected error occurred");
    } finally {
      setIsWebhookTestLoading(false);
    }
  }, [organizationId, webhookUrl, webhookEnabled, success, error]);

  // Add state and function to handle organization deletion confirmation
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [deleteConfirmationText, setDeleteConfirmationText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteOrganization = useCallback(async () => {
    if (deleteConfirmationText !== 'DELETE') {
      error("Error", 'Please type "DELETE" to confirm');
      return;
    }

    setIsDeleting(true);

    try {
      const result = await deleteOrganization({
        organizationId,
        confirmationText: deleteConfirmationText
      });

      if (result.success) {
        success("Success", "Organization deleted successfully");

        // Redirect to dashboard after successful deletion
        window.location.href = '/dashboard';
      } else {
        error("Error", result.error || "Failed to delete organization");
      }
    } catch (error: any) {
      console.error('Error deleting organization:', error);
      error("Error", "An unexpected error occurred");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  }, [organizationId, deleteConfirmationText, success, error]);

  return (
    <>
      <Card className="mb-8">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6">
            {/* Basic Information */}
            <div className="border-b border-[#E0D7FF] dark:border-[#2a2a38] pb-6">
              <h2 className="text-xl font-semibold mb-4">Basic Information</h2>

              {/* Organization Name */}
              <div className="mb-4">
                <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1" htmlFor="name">
                  Organization Name <span className="text-red-500">*</span>
                </label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className={nameError ? 'border-red-500' : ''}
                  placeholder="Enter organization name"
                />
                {nameError && (
                  <p className="text-red-500 text-sm mt-1">{nameError}</p>
                )}
              </div>

              {/* Description */}
              <div className="mb-4">
                <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1" htmlFor="description">
                  Description
                </label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter organization description"
                  rows={4}
                />
              </div>

              {/* Industry */}
              <div className="mb-4">
                <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1" htmlFor="industry">
                  Industry
                </label>
                <Select
                  id="industry"
                  value={industry}
                  onChange={(value) => value && setIndustry(value.toString())}
                  options={industryOptions}
                  placeholder="Select industry"
                />
              </div>

              {/* Status */}
              <div>
                <label className="flex items-center space-x-3">
                  <Switch
                    checked={isActive}
                    onChange={(e: { value: boolean }) => setIsActive(e.value)}
                  />
                  <span className="text-[#5E5E5E] dark:text-[#C6C6C6] font-medium">
                    {isActive ? 'Active' : 'Inactive'}
                  </span>
                </label>
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
                  {isActive
                    ? 'Your organization is currently active and visible to members.'
                    : 'Your organization is currently inactive and hidden from members.'}
                </p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="border-b border-[#E0D7FF] dark:border-[#2a2a38] pb-6">
              <h2 className="text-xl font-semibold mb-4">Contact Information</h2>

              {/* Email */}
              <div className="mb-4">
                <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1" htmlFor="email">
                  Contact Email
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email || ''}
                  onChange={(e) => setEmail(e.target.value)}
                  className={emailError ? 'border-red-500' : ''}
                  placeholder="Enter contact email"
                />
                {emailError && (
                  <p className="text-red-500 text-sm mt-1">{emailError}</p>
                )}
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
                  This email will be used for notifications and communications.
                </p>
              </div>

              {/* Domain */}
              <div>
                <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1" htmlFor="domain">
                  Domain
                </label>
                <Input
                  id="domain"
                  value={domain || ''}
                  onChange={(e) => setDomain(e.target.value)}
                  placeholder="example.com"
                />
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
                  Used for email domain verification. Only enter the domain (e.g., example.com).
                </p>
              </div>
            </div>

            {/* Webhook Configuration */}
            <div className="border-b border-[#E0D7FF] dark:border-[#2a2a38] pb-6">
              <h2 className="text-xl font-semibold mb-4">Webhook Notifications</h2>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-4 text-sm">
                Configure a webhook to notify your systems when there are changes to your organization&#39;s general settings.
              </p>

              <div className="mb-4">
                <label className="flex items-center space-x-3 mb-3">
                  <Switch
                    checked={webhookEnabled}
                    onChange={(e: { value: boolean }) => setWebhookEnabled(e.value)}
                  />
                  <span className="text-[#5E5E5E] dark:text-[#C6C6C6] font-medium">
                    Enable Webhook Notifications
                  </span>
                </label>

                {webhookEnabled && (
                  <div className="mt-4 space-y-3">
                    <div>
                      <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1" htmlFor="webhookUrl">
                        Webhook URL
                      </label>
                      <div className="flex gap-2">
                        <Input
                          id="webhookUrl"
                          value={webhookUrl}
                          onChange={(e) => setWebhookUrl(e.target.value)}
                          className={`flex-grow ${webhookError ? 'border-red-500' : ''}`}
                          placeholder="https://your-server.com/webhooks/organization-settings"
                        />
                        <Button
                          type="button"
                          variant="secondary"
                          loading={isWebhookTestLoading}
                          disabled={!webhookUrl || isWebhookTestLoading}
                          onClick={testWebhook}
                        >
                          Test
                        </Button>
                      </div>
                      {webhookError && (
                        <p className="text-red-500 text-sm mt-1">{webhookError}</p>
                      )}
                    </div>

                    <Button
                      type="button"
                      variant="primary"
                      loading={isWebhookTestLoading}
                      disabled={!webhookUrl || isWebhookTestLoading}
                      onClick={saveWebhookSettings}
                      className="mt-2"
                    >
                      Save Webhook Settings
                    </Button>

                    <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 rounded-lg text-sm">
                      <h4 className="font-medium mb-2">Webhook Payload Format</h4>
                      <pre className="text-xs whitespace-pre-wrap text-[#5E5E5E] dark:text-[#C6C6C6] bg-[#E7E7E7] dark:bg-[#1a1a28] p-2 rounded overflow-auto">
{`{
  "event": "organization.updated",
  "organization": {
    "id": "${organizationId}",
    "name": "Organization Name",
    "changedFields": ["name", "email", "description"]
  },
  "timestamp": "2023-04-10T15:30:45Z"
}`}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Subscription Information */}
            <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 rounded-lg mb-6">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-semibold">Current Plan</h3>
                <Badge variant="default">{organizationData?.subscriptionPlan || 'Free'}</Badge>
              </div>
              <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-2">
                {organizationData?.subscriptionPlan === 'Free'
                  ? 'You have access to all services with the Free plan, but limited to only one team member per organization.'
                  : `You have access to all features in the ${organizationData?.subscriptionPlan} plan with multiple team members.`}
              </p>
              {organizationData?.subscriptionPlan === 'Free' && (
                <Button
                  variant="primary"
                  type="button"
                  onClick={() => window.location.href = '/dashboard/billing'}
                >
                  Upgrade Plan
                </Button>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex items-center justify-end mt-6 space-x-3">
              {isDirty && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={handleCancel}
                  disabled={isSaving}
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                loading={isSaving}
                disabled={isSaving || !isDirty}
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </form>
      </Card>

      {/* Danger Zone */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4 text-red-600 dark:text-red-400">Danger Zone</h2>
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
            These actions are irreversible and should be used with caution.
          </p>

          <div className="flex items-center justify-between p-4 border border-red-500/20 bg-red-50 dark:bg-red-900/10 rounded-lg">
            <div>
              <h3 className="font-medium text-red-600 dark:text-red-400">Delete Organization</h3>
              <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
                This will permanently delete your organization and all associated data.
              </p>
            </div>
            <Button
              variant="destructive"
              onClick={() => setIsDeleteModalOpen(true)}
              type="button"
            >
              Delete
            </Button>
          </div>

          {isDeleteModalOpen && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-[#1e1e28] rounded-lg p-6 max-w-md w-full mx-4">
                <h3 className="text-xl font-semibold mb-4 text-red-600 dark:text-red-400">Delete Organization</h3>
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
                  This action is irreversible. All organization data, including tickets, members, and settings will be permanently deleted.
                </p>
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
                  Type <span className="font-bold">DELETE</span> to confirm:
                </p>

                <Input
                  value={deleteConfirmationText}
                  onChange={(e) => setDeleteConfirmationText(e.target.value)}
                  className="mb-4"
                  placeholder="Type DELETE to confirm"
                />

                <div className="flex justify-end space-x-3">
                  <Button
                    variant="secondary"
                    onClick={() => {
                      setIsDeleteModalOpen(false);
                      setDeleteConfirmationText('');
                    }}
                    type="button"
                    disabled={isDeleting}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="destructive"
                    onClick={handleDeleteOrganization}
                    type="button"
                    loading={isDeleting}
                    disabled={deleteConfirmationText !== 'DELETE' || isDeleting}
                  >
                    Delete Organization
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
    </>
  );
}

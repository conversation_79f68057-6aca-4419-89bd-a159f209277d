'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { SubDomainInput } from '@/components/ui/SubDomainInput';
import { useToast } from '@/components/ui/Toast';
import { createSupportPortal } from '@/server/actions/support-portal';
import { Globe, Palette, FileText, Rocket, CheckCircle, ArrowRight, ArrowLeft } from 'lucide-react';
import { logger } from '@/utils/logger';

interface NavigationItem {
  label: string;
  url: string;
  openInNewTab: boolean;
  order: number;
}

interface PortalSetupWizardProps {
  organizationId: string;
  existingPortal: any;
  onComplete: (portal: any) => void;
}

export default function PortalSetupWizard({ organizationId, existingPortal, onComplete }: PortalSetupWizardProps) {
  const { success, error: showError } = useToast();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state
  const [domainData, setDomainData] = useState({
    defaultSubdomain: existingPortal?.defaultSubdomain || '',
    customDomain: existingPortal?.customDomain,
  });

  const [brandingData, setBrandingData] = useState({
    companyName: existingPortal?.branding?.companyName || '',
    primaryColor: existingPortal?.branding?.primaryColor || '#8178E8',
    secondaryColor: existingPortal?.branding?.secondaryColor || '#6964D3',
    accentColor: existingPortal?.branding?.accentColor || '#D1AB66',
    backgroundColor: existingPortal?.branding?.backgroundColor || '#ffffff',
    textColor: existingPortal?.branding?.textColor || '#0f0f18',
    fontFamily: existingPortal?.branding?.fontFamily || 'Inter, system-ui, sans-serif',
    logoUrl: existingPortal?.branding?.logoUrl || '',
    logoPosition: existingPortal?.branding?.logoPosition || 'left',
  });

  const [contentData, setContentData] = useState({
    welcomeMessage: existingPortal?.content?.welcomeMessage || 'Welcome to our support portal. How can we help you today?',
    helpText: existingPortal?.content?.helpText || 'Submit a support ticket and our team will get back to you shortly.',
    contactEmail: existingPortal?.content?.contactEmail || '',
    supportHours: existingPortal?.content?.supportHours || 'Monday - Friday, 9:00 AM - 5:00 PM EST',
  });

  const [navigationData, setNavigationData] = useState<NavigationItem[]>(
    existingPortal?.navigation || [
      { label: 'Home', url: '/', openInNewTab: false, order: 1 },
      { label: 'Contact', url: '/contact', openInNewTab: false, order: 2 },
    ]
  );

  const steps = [
    {
      id: 'domain',
      title: 'Domain Setup',
      description: 'Configure your support portal domain',
      icon: Globe,
    },
    {
      id: 'content',
      title: 'Content',
      description: 'Set up your portal content',
      icon: FileText,
    },
    {
      id: 'navigation',
      title: 'Navigation',
      description: 'Configure portal navigation menu',
      icon: FileText,
    },
    {
      id: 'branding',
      title: 'Branding & Preview',
      description: 'Customize appearance with live preview',
      icon: Palette,
    },
    {
      id: 'launch',
      title: 'Launch',
      description: 'Review and publish your portal',
      icon: Rocket,
    },
  ];

  const handleNext = async () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      await handleLaunch();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleLaunch = async () => {
    setIsSubmitting(true);
    try {
      // Create the portal with all configuration in one atomic operation
      const result = await createSupportPortal({
        organizationId,
        domain: {
          defaultSubdomain: domainData.defaultSubdomain,
          customDomain: domainData.customDomain || undefined,
        },
        branding: brandingData,
        content: contentData,
        navigation: navigationData,
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to create support portal');
      }

      onComplete(result.data);
    } catch (error) {
      logger.error('Setup error:', error);
      showError(error instanceof Error ? error.message : 'Failed to set up portal');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#0f0f18] dark:text-white">
                Configure Your Portal Domain
              </h3>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
                Set up how customers will access your support portal.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                Subdomain *
              </label>
              <SubDomainInput
                value={domainData.defaultSubdomain}
                onChange={(value) => setDomainData(prev => ({ ...prev, defaultSubdomain: value }))}
                placeholder="your-company"
                domainSuffix="-support.newinstance.com"
                required
                aria-label="Portal subdomain"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                Custom Domain (Optional)
              </label>
              <Input
                value={domainData.customDomain}
                onChange={(e) => setDomainData(prev => ({ ...prev, customDomain: e.target.value }))}
                placeholder="support.yourcompany.com"
              />
              <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6] mt-1">
                Use your own domain for the support portal
              </p>
            </div>
          </div>
        );

      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#0f0f18] dark:text-white">
                Configure Portal Content
              </h3>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
                Set up the messages and information your customers will see.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                Welcome Message *
              </label>
              <Input
                value={contentData.welcomeMessage}
                onChange={(e) => setContentData(prev => ({ ...prev, welcomeMessage: e.target.value }))}
                placeholder="Welcome to our support portal..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                Help Text *
              </label>
              <Input
                value={contentData.helpText}
                onChange={(e) => setContentData(prev => ({ ...prev, helpText: e.target.value }))}
                placeholder="How we can help you..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                Contact Email *
              </label>
              <Input
                type="email"
                value={contentData.contactEmail}
                onChange={(e) => setContentData(prev => ({ ...prev, contactEmail: e.target.value }))}
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                Support Hours
              </label>
              <Input
                value={contentData.supportHours}
                onChange={(e) => setContentData(prev => ({ ...prev, supportHours: e.target.value }))}
                placeholder="Monday - Friday, 9:00 AM - 5:00 PM EST"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4 text-[#0f0f18] dark:text-white">
                Configure Portal Navigation
              </h3>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
                Set up navigation menu items for your support portal (up to 5 items).
              </p>
            </div>

            <div className="space-y-4">
              {navigationData.map((item, index) => (
                <div key={index} className="border border-[#E0D7FF] dark:border-[#2a2a38] rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                        Label *
                      </label>
                      <Input
                        value={item.label}
                        onChange={(e) => {
                          const newNav = [...navigationData];
                          newNav[index] = { ...item, label: e.target.value };
                          setNavigationData(newNav);
                        }}
                        placeholder="Home"
                        maxLength={50}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                        URL *
                      </label>
                      <Input
                        value={item.url}
                        onChange={(e) => {
                          const newNav = [...navigationData];
                          newNav[index] = { ...item, url: e.target.value };
                          setNavigationData(newNav);
                        }}
                        placeholder="/home or https://example.com"
                      />
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={item.openInNewTab}
                        onChange={(e) => {
                          const newNav = [...navigationData];
                          newNav[index] = { ...item, openInNewTab: e.target.checked };
                          setNavigationData(newNav);
                        }}
                        className="mr-2"
                      />
                      <span className="text-sm text-[#0f0f18] dark:text-white">Open in new tab</span>
                    </label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newNav = navigationData.filter((_, i) => i !== index);
                        setNavigationData(newNav);
                      }}
                      className="text-red-600 hover:text-red-700"
                    >
                      Remove
                    </Button>
                  </div>
                </div>
              ))}

              {navigationData.length < 5 && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setNavigationData([
                      ...navigationData,
                      {
                        label: '',
                        url: '',
                        openInNewTab: false,
                        order: navigationData.length + 1,
                      },
                    ]);
                  }}
                  className="w-full"
                >
                  Add Navigation Item
                </Button>
              )}
            </div>
          </div>
        );

      case 3:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Branding Configuration */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 text-[#0f0f18] dark:text-white">
                  Customize Your Portal Branding
                </h3>
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
                  Set up your company branding and see a live preview of your portal.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                  Company Name *
                </label>
                <Input
                  value={brandingData.companyName}
                  onChange={(e) => setBrandingData(prev => ({ ...prev, companyName: e.target.value }))}
                  placeholder="Your Company Name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                  Logo URL (Optional)
                </label>
                <Input
                  value={brandingData.logoUrl}
                  onChange={(e) => setBrandingData(prev => ({ ...prev, logoUrl: e.target.value }))}
                  placeholder="https://yourcompany.com/logo.png"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                    Background Color
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={brandingData.backgroundColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, backgroundColor: e.target.value }))}
                      className="w-12 h-10 rounded border border-[#E0D7FF] dark:border-[#2a2a38]"
                    />
                    <Input
                      value={brandingData.backgroundColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, backgroundColor: e.target.value }))}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                    Text Color
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={brandingData.textColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, textColor: e.target.value }))}
                      className="w-12 h-10 rounded border border-[#E0D7FF] dark:border-[#2a2a38]"
                    />
                    <Input
                      value={brandingData.textColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, textColor: e.target.value }))}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                    Primary Color
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={brandingData.primaryColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, primaryColor: e.target.value }))}
                      className="w-12 h-10 rounded border border-[#E0D7FF] dark:border-[#2a2a38]"
                    />
                    <Input
                      value={brandingData.primaryColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, primaryColor: e.target.value }))}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                    Secondary Color
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={brandingData.secondaryColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, secondaryColor: e.target.value }))}
                      className="w-12 h-10 rounded border border-[#E0D7FF] dark:border-[#2a2a38]"
                    />
                    <Input
                      value={brandingData.secondaryColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, secondaryColor: e.target.value }))}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2 text-[#0f0f18] dark:text-white">
                    Accent Color
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="color"
                      value={brandingData.accentColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, accentColor: e.target.value }))}
                      className="w-12 h-10 rounded border border-[#E0D7FF] dark:border-[#2a2a38]"
                    />
                    <Input
                      value={brandingData.accentColor}
                      onChange={(e) => setBrandingData(prev => ({ ...prev, accentColor: e.target.value }))}
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Live Preview */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-4 text-[#0f0f18] dark:text-white">
                  Live Preview
                </h3>
                <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
                  See how your portal will look to customers.
                </p>
              </div>

              <div className="border border-[#E0D7FF] dark:border-[#2a2a38] rounded-lg overflow-hidden">
                <div
                  className="p-4 border-b border-[#E0D7FF] dark:border-[#2a2a38]"
                  style={{ backgroundColor: brandingData.backgroundColor }}
                >
                  <div className="flex items-center space-x-3">
                    {brandingData.logoUrl && (
                      <img
                        src={brandingData.logoUrl}
                        alt="Logo"
                        className="h-8 w-auto"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    )}
                    <h4
                      className="font-semibold"
                      style={{ color: brandingData.textColor }}
                    >
                      {brandingData.companyName || 'Your Company'}
                    </h4>
                  </div>

                  {/* Navigation Preview */}
                  <div className="flex flex-wrap gap-1 mt-4">
                    {navigationData.slice(0, 3).map((item, index) => {
                      const isActive = index === 0; // First item appears as active/current page
                      return (
                        <div
                          key={index}
                          className={`
                            relative px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer
                            hover:scale-105 hover:shadow-sm
                            ${isActive ? 'shadow-sm' : ''}
                          `}
                          style={{
                            backgroundColor: isActive
                              ? brandingData.primaryColor
                              : `${brandingData.textColor}10`,
                            color: isActive
                              ? '#ffffff'
                              : brandingData.textColor,
                            borderWidth: '1px',
                            borderStyle: 'solid',
                            borderColor: isActive
                              ? brandingData.primaryColor
                              : `${brandingData.textColor}20`,
                          }}
                          onMouseEnter={(e) => {
                            if (!isActive) {
                              e.currentTarget.style.backgroundColor = `${brandingData.secondaryColor}20`;
                              e.currentTarget.style.borderColor = `${brandingData.secondaryColor}40`;
                              e.currentTarget.style.color = brandingData.secondaryColor;
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isActive) {
                              e.currentTarget.style.backgroundColor = `${brandingData.textColor}10`;
                              e.currentTarget.style.borderColor = `${brandingData.textColor}20`;
                              e.currentTarget.style.color = brandingData.textColor;
                            }
                          }}
                        >
                          {/* Active indicator */}
                          {isActive && (
                            <div
                              className="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-6 h-0.5 rounded-full"
                              style={{ backgroundColor: '#ffffff' }}
                            />
                          )}

                          <span className="relative z-10 tracking-wide">
                            {item.label || `Nav ${index + 1}`}
                          </span>
                        </div>
                      );
                    })}

                    {/* Show "More" indicator if there are additional navigation items */}
                    {navigationData.length > 3 && (
                      <div
                        className="px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 opacity-60"
                        style={{
                          backgroundColor: `${brandingData.textColor}08`,
                          color: brandingData.textColor,
                          borderWidth: '1px',
                          borderStyle: 'dashed',
                          borderColor: `${brandingData.textColor}20`,
                        }}
                      >
                        <span className="tracking-wide">
                          +{navigationData.length - 3} more
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div
                  className="p-6"
                  style={{ backgroundColor: brandingData.backgroundColor }}
                >
                  <h2
                    className="text-2xl font-bold mb-2"
                    style={{ color: brandingData.textColor }}
                  >
                    {contentData.welcomeMessage || 'Welcome to our support portal'}
                  </h2>
                  <p
                    className="mb-4"
                    style={{ color: `${brandingData.textColor}80` }}
                  >
                    {contentData.helpText || 'How can we help you today?'}
                  </p>
                  <div className="flex space-x-2">
                    <div
                      className="px-4 py-2 rounded-lg text-white text-sm font-medium"
                      style={{ backgroundColor: brandingData.primaryColor }}
                    >
                      Submit Ticket
                    </div>
                    <div
                      className="px-4 py-2 rounded-lg text-sm font-medium border"
                      style={{
                        borderColor: brandingData.secondaryColor,
                        color: brandingData.secondaryColor
                      }}
                    >
                      Track Ticket
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-4 text-[#0f0f18] dark:text-white">
                Ready to Launch!
              </h3>
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">
                Your support portal is configured and ready to go live. Review your settings below.
              </p>
            </div>

            <div className="bg-[#F8F9FA] dark:bg-[#1e1e2e] rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="font-medium text-[#0f0f18] dark:text-white">Portal URL:</span>
                <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">
                  {domainData.customDomain || `${domainData.defaultSubdomain}-support.newinstance.com`}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-[#0f0f18] dark:text-white">Company:</span>
                <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">{brandingData.companyName}</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-[#0f0f18] dark:text-white">Contact Email:</span>
                <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">{contentData.contactEmail}</span>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                <strong>Note:</strong> Once published, your customers will be able to access the support portal and submit tickets.
                You can always modify these settings later from the portal management section.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep;
            const isCompleted = index < currentStep;

            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  isCompleted
                    ? 'bg-green-500 border-green-500 text-white'
                    : isActive
                      ? 'bg-indigo-600 border-indigo-600 text-white'
                      : 'border-gray-300 text-gray-400'
                }`}>
                  {isCompleted ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <Icon className="w-5 h-5" />
                  )}
                </div>
                <div className="ml-3">
                  <p className={`text-sm font-medium ${
                    isActive ? 'text-indigo-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <ArrowRight className="w-4 h-4 text-gray-400 mx-4" />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="p-8">
        {renderStepContent()}

        {/* Navigation */}
        <div className="flex justify-between mt-8 pt-6 border-t border-[#E0D7FF] dark:border-[#2a2a38]">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>

          <Button
            onClick={handleNext}
            disabled={isSubmitting}
            className="flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Publishing...
              </>
            ) : currentStep === steps.length - 1 ? (
              <>
                <Rocket className="w-4 h-4 mr-2" />
                Launch Portal
              </>
            ) : (
              <>
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </Card>
    </div>
  );
}

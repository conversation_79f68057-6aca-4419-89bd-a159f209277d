'use client';

import {useEffect, useState} from 'react';
import {Card} from '@/components/ui/Card';
import {Badge} from '@/components/ui/Badge';
import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {CustomDataTable} from '@/components/ui/CustomDataTable';
import {CustomColumn} from '@/components/ui/CustomColumn';
import {Alert, InfoAlert} from '@/components/ui/Alert';
import {Dialog} from '@/components/ui/Dialog';
import {UserOrgRole} from '@/constants/role-constants';
import {useToast} from '@/components/ui/Toast';
import {removeUserFromOrganization, updateUserRole} from '@/server/actions/user-management';
import {cancelInvite, getOrganizationInvites, resendInvite} from '@/server/actions/invite-actions';
import InviteUserModal from './InviteUserModal';

interface OrganizationData {
  id: string;
  name: string;
  memberCount: number;
  subscriptionPlan: string | null;
  members: {
    userId: string;
    role: string;
    name: string;
    email: string;
  }[];
  userRole: string | null;
}

interface Invite {
  _id: string;
  email: string;
  role: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  invitedBy: {
    _id: string;
    name: string;
    email: string;
  };
}

interface UserManagementProps {
  organizationId: string;
  organizationData: OrganizationData | null;
}

export default function UserManagement({
  organizationId,
  organizationData
}: UserManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [pendingInvites, setPendingInvites] = useState<Invite[]>([]);
  const [loadingInvites, setLoadingInvites] = useState(false);

  // Dialog states
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmDialogConfig, setConfirmDialogConfig] = useState<{
    title: string;
    message: string;
    confirmAction: () => Promise<void>;
  }>({ title: '', message: '', confirmAction: async () => {} });

  // Loading states
  const [isRemovingUser, setIsRemovingUser] = useState<string | null>(null);
  const [isResendingInvite, setIsResendingInvite] = useState<string | null>(null);
  const [isCancellingInvite, setIsCancellingInvite] = useState<string | null>(null);
  const [isChangingRole, setIsChangingRole] = useState<string | null>(null);

  const { success, error, warn, info } = useToast();

  // Load pending invites
  useEffect(() => {
    const loadPendingInvites = async () => {
      if (!organizationId) return;

      setLoadingInvites(true);
      try {
        const result = await getOrganizationInvites(organizationId);
        if (result.success && result.data) {
          setPendingInvites(result.data);
        } else {
          console.error('Failed to load invites:', result.message);
        }
      } catch (err) {
        console.error('Error loading invites:', err);
      } finally {
        setLoadingInvites(false);
      }
    };

    loadPendingInvites();
  }, [organizationId]);

  // Filter organization members by search query
  const filteredOrgMembers = organizationData?.members?.filter(member => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase?.();
    return (
      member.name.toLowerCase?.().includes(query) ||
      member.email.toLowerCase?.().includes(query) ||
      member.role.toLowerCase?.().includes(query)
    );
  }) || [];

  // Filter pending invites
  const filteredInvites = pendingInvites.filter(invite => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase?.();
    return (
      invite.email.toLowerCase?.().includes(query) ||
      invite.role.toLowerCase?.().includes(query)
    );
  });

  // Calculate total users for billing purposes
  const totalUsers = organizationData?.memberCount || 0;

  // Open confirm dialog with given config
  const openConfirmDialog = (title: string, message: string, confirmAction: () => Promise<void>) => {
    setConfirmDialogConfig({
      title,
      message,
      confirmAction
    });
    setShowConfirmDialog(true);
  };

  // Handle edit organization user role
  const handleEditUserRole = async (userId: string, newRole: string) => {
    try {
      setIsChangingRole(userId);
      const result = await updateUserRole({
        organizationId,
        userId,
        role: newRole
      });

      if (result.success) {
        info('User role updated successfully');
        // Refresh the user list (this would require adding a way to refresh the organizationData)
      } else {
        error(result.error || 'Failed to update user role');
      }
    } catch (err) {
      console.error('Error updating user role:', err);
      error('An unexpected error occurred');
    } finally {
      setIsChangingRole(null);
    }
  };

  // Handle remove organization user
  const handleRemoveUser = async (userId: string) => {
    openConfirmDialog(
      'Remove User',
      'Are you sure you want to remove this user from the organization? This action cannot be undone.',
      async () => {
        try {
          setIsRemovingUser(userId);
          const result = await removeUserFromOrganization({
            organizationId,
            userId
          });

          if (result.success) {
            info('User removed successfully');
            // Refresh the user list (this would require adding a way to refresh the organizationData)
          } else {
            error(result.error || 'Failed to remove user');
          }
        } catch (err) {
          console.error('Error removing user:', err);
          error('An unexpected error occurred');
        } finally {
          setIsRemovingUser(null);
        }
      }
    );
  };

  // Handle cancel invitation
  const handleCancelInvite = async (inviteId: string) => {
    openConfirmDialog(
      'Cancel Invitation',
      'Are you sure you want to cancel this invitation? The user will no longer be able to join your organization with this link.',
      async () => {
        try {
          setIsCancellingInvite(inviteId);
          const result = await cancelInvite(inviteId);

          if (result.success) {
            // Update the local state by removing the cancelled invite
            setPendingInvites(pendingInvites.filter(invite => invite._id !== inviteId));
            success('Invitation cancelled successfully');
          } else {
            error(result.message || 'Failed to cancel invitation');
          }
        } catch (err) {
          console.error('Error cancelling invitation:', err);
          error('An unexpected error occurred');
        } finally {
          setIsCancellingInvite(null);
        }
      }
    );
  };

  // Handle resend invitation
  const handleResendInvite = async (inviteId: string) => {
    try {
      setIsResendingInvite(inviteId);
      const result = await resendInvite(inviteId);
      console.log(result);
      if (result.success) {
        success('Invitation resent successfully');
      } else {
        error(result.message || 'Failed to resend invitation');
      }
    } catch (err) {
      console.error('Error resending invitation:', err);
      error('An unexpected error occurred');
    } finally {
      setIsResendingInvite(null);
    }
  };

  // Handle invite modal success callback
  const handleInviteSuccess = () => {
    // Reload pending invites
    const loadPendingInvites = async () => {
      try {
        const result = await getOrganizationInvites(organizationId);
        if (result.success && result.data) {
          setPendingInvites(result.data);
        }
      } catch (err) {
        console.error('Error reloading invites:', err);
      }
    };

    loadPendingInvites();
  };

  // Handle role change dialog
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [roleDialogConfig, setRoleDialogConfig] = useState<{
    userId: string;
    currentRole: string;
  }>({ userId: '', currentRole: '' });
  const [newRole, setNewRole] = useState('');

  const openRoleDialog = (userId: string, currentRole: string) => {
    setRoleDialogConfig({
      userId,
      currentRole
    });
    setNewRole(currentRole);
    setShowRoleDialog(true);
  };

  const confirmRoleChange = async () => {
    if (newRole && newRole !== roleDialogConfig.currentRole) {
      await handleEditUserRole(roleDialogConfig.userId, newRole);
    }
    setShowRoleDialog(false);
  };

  // Custom renderers for DataTable
  const emailBodyTemplate = (rowData: any) => {
    return <div className="font-medium text-black dark:text-white">{rowData.email}</div>;
  };

  const roleBodyTemplate = (rowData: any) => {
    return <RoleBadge role={rowData.role} />;
  };

  const invitedByBodyTemplate = (rowData: any) => {
    return <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">{`${rowData.invitedBy?.name || ''}`.trim()}</span>;
  };

  const expiresBodyTemplate = (rowData: any) => {
    return <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">{new Date(rowData.expiresAt).toLocaleDateString()}</span>;
  };

  const actionsBodyTemplate = (rowData: any) => {
    return (
      <div className="flex space-x-2">
        <button
          className="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 disabled:opacity-50"
          onClick={() => handleResendInvite(rowData._id)}
          disabled={isResendingInvite === rowData._id}
        >
          {isResendingInvite === rowData._id ? 'Sending...' : 'Resend'}
        </button>
        <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">|</span>
        <button
          className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 disabled:opacity-50"
          onClick={() => handleCancelInvite(rowData._id)}
          disabled={isCancellingInvite === rowData._id}
        >
          {isCancellingInvite === rowData._id ? 'Cancelling...' : 'Cancel'}
        </button>
      </div>
    );
  };

  const memberNameBodyTemplate = (rowData: any) => {
    return <div className="font-medium text-black dark:text-white">{rowData.name}</div>;
  };

  const memberEmailBodyTemplate = (rowData: any) => {
    return <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">{rowData.email}</span>;
  };

  const memberActionsBodyTemplate = (rowData: any) => {
    return (
      <div className="flex space-x-2">
        <button
          className="text-sm text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 disabled:opacity-50"
          disabled={organizationData?.userRole !== UserOrgRole.ADMIN || isChangingRole === rowData.userId}
          onClick={() => openRoleDialog(rowData.userId, rowData.role)}
        >
          {isChangingRole === rowData.userId ? 'Updating...' : 'Edit'}
        </button>
        <span className="text-[#5E5E5E] dark:text-[#C6C6C6]">|</span>
        <button
          className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 disabled:opacity-50"
          disabled={organizationData?.userRole !== UserOrgRole.ADMIN || rowData.role === UserOrgRole.ADMIN || isRemovingUser === rowData.userId}
          onClick={() => handleRemoveUser(rowData.userId)}
        >
          {isRemovingUser === rowData.userId ? 'Removing...' : 'Remove'}
        </button>
      </div>
    );
  };

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold">User Management</h2>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
              Manage users in your organization.
            </p>
          </div>
          <div className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300 text-sm px-3 py-1 rounded-full font-medium">
            {totalUsers} Total Users
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
            <div className="relative w-full md:w-64">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-[#5E5E5E] dark:text-[#C6C6C6]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                </svg>
              </div>
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search members..."
                className="pl-10"
              />
            </div>

            <Button
              variant="primary"
              disabled={organizationData && organizationData.subscriptionPlan === 'Free' && (organizationData?.memberCount || 0) >= 1}
              onClick={() => setShowInviteModal(true)}
            >
              Invite Organization User
            </Button>
          </div>

          {organizationData && organizationData.subscriptionPlan === 'Free' && (organizationData?.memberCount || 0) >= 1 && (
            <Alert severity="warn" title="Upgrade to add more users">
              <p>The Free plan is limited to 1 user. Upgrade to add more team members.</p>
              <Button
                variant="primary"
                className="mt-2"
                onClick={() => window.location.href = `/dashboard/organization/${organizationId}/billing`}
              >
                Upgrade Plan
              </Button>
            </Alert>
          )}

          {/* Pending Invitations Section */}
          {pendingInvites.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-medium mb-4">Pending Invitations</h3>
              <CustomDataTable
                value={filteredInvites}
                emptyMessage={searchQuery
                  ? 'No pending invitations match your search query'
                  : 'No pending invitations found'}
                className="w-full"
                tableClassName="min-w-full shadow-sm rounded-lg"
                loading={loadingInvites}
                bgColor="#F3F3F3"
                darkBgColor="#2a2a38"
                hoverBgColor="#E8E8E8"
                darkHoverBgColor="#353545"
              >
                <CustomColumn field="email" header="Email" body={emailBodyTemplate}
                  headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
                  className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
                />
                <CustomColumn field="role" header="Role" body={roleBodyTemplate}
                  headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
                  className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
                />
                <CustomColumn field="invitedBy" header="Invited By" body={invitedByBodyTemplate}
                  headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
                  className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
                />
                <CustomColumn field="expiresAt" header="Expires" body={expiresBodyTemplate}
                  headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
                  className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
                />
                <CustomColumn field="actions" header="Actions" body={actionsBodyTemplate}
                  headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
                  className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
                />
              </CustomDataTable>
            </div>
          )}

          {/* Active Users Section */}
          <h3 className="text-lg font-medium mb-4">Active Users</h3>
          <CustomDataTable
            value={filteredOrgMembers}
            emptyMessage={searchQuery
              ? 'No members match your search query'
              : 'No members found in this organization'}
            className="w-full"
            tableClassName="min-w-full shadow-sm rounded-lg"
            bgColor="#F3F3F3"
            darkBgColor="#2a2a38"
            hoverBgColor="#E8E8E8"
            darkHoverBgColor="#353545"
          >
            <CustomColumn field="name" header="Name" body={memberNameBodyTemplate}
              headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
              className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
            />
            <CustomColumn field="email" header="Email" body={memberEmailBodyTemplate}
              headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
              className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
            />
            <CustomColumn field="role" header="Role" body={roleBodyTemplate}
              headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
              className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
            />
            <CustomColumn field="actions" header="Actions" body={memberActionsBodyTemplate}
              headerClassName="text-left text-sm font-medium text-[#5E5E5E] dark:text-[#C6C6C6]"
              className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
            />
          </CustomDataTable>

        </div>

        <div className="mt-8 bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 rounded-lg">
          <h3 className="font-medium text-lg mb-2">User Roles & Permissions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="text-md font-medium mb-2">Admin</h4>
              <ul className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] space-y-1">
                <li>• Full access to organization settings</li>
                <li>• Can invite and manage users</li>
                <li>• Can create and manage products</li>
                <li>• Can access all support tickets</li>
              </ul>
            </div>

            <div>
              <h4 className="text-md font-medium mb-2">Member</h4>
              <ul className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] space-y-1">
                <li>• Can use all products and services</li>
                <li>• Cannot modify organization settings</li>
                <li>• Limited user management access</li>
                <li>• Can create and handle support tickets</li>
              </ul>
            </div>

            <div>
              <h4 className="text-md font-medium mb-2">Guest</h4>
              <ul className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] space-y-1">
                <li>• Limited product access</li>
                <li>• View-only for most features</li>
                <li>• Cannot modify settings</li>
                <li>• Can create support tickets</li>
              </ul>
            </div>
          </div>
        </div>

        <InfoAlert title="Billing Information" className="mt-6">
          <p>Your current plan: {organizationData?.subscriptionPlan || 'Free'}</p>
          <p>Total organization users: {totalUsers}</p>
          <p className="mt-2">
            For each user in your organization, you will be billed according to your subscription plan&#39;s per-user-per-month rate.
          </p>
        </InfoAlert>

        <div className="mt-8 border-t border-[#E0D7FF] dark:border-[#2a2a38] pt-6 opacity-60 pointer-events-none">
          <h3 className="font-medium text-lg mb-4">Advanced User Management <span className="text-sm font-normal text-blue-600 dark:text-blue-400">(Coming Soon)</span></h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-md font-medium mb-2">Custom Roles & Permissions</h4>
              <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
                Create custom roles with granular permissions to match your team structure.
              </p>
              <Button variant="primary" disabled>Configure Custom Roles</Button>
            </div>

            <div>
              <h4 className="text-md font-medium mb-2">Role-Based Access Control</h4>
              <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
                Define access controls based on roles for different parts of the system.
              </p>
              <Button variant="primary" disabled>Configure RBAC</Button>
            </div>
          </div>
        </div>
      </div>

      {/* User invitation modal */}
      {showInviteModal && (
        <InviteUserModal
          isOpen={showInviteModal}
          onClose={() => setShowInviteModal(false)}
          organizationId={organizationId}
          type="organization"
          onSuccess={handleInviteSuccess}
        />
      )}

      {/* Confirmation Dialog */}
      <Dialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        title={confirmDialogConfig.title}
      >
        <p className="mb-4 text-gray-600 dark:text-gray-300">{confirmDialogConfig.message}</p>
        <div className="flex justify-end space-x-2">
          <Button
            variant="primary"
            onClick={() => setShowConfirmDialog(false)}
          >
            Cancel
          </Button>
          <Button
            variant="danger"
            onClick={async () => {
              setShowConfirmDialog(false);
              await confirmDialogConfig.confirmAction();
            }}
          >
            Confirm
          </Button>
        </div>
      </Dialog>

      {/* Role Edit Dialog */}
      <Dialog
        open={showRoleDialog}
        onOpenChange={setShowRoleDialog}
        title="Edit User Role"
      >
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Select New Role
          </label>
          <select
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
            value={newRole}
            onChange={(e) => setNewRole(e.target.value)}
          >
            {Object.values(UserOrgRole).map((role) => (
              <option key={role} value={role}>
                {role.charAt(0).toUpperCase() + role.slice(1).toLowerCase?.()}
              </option>
            ))}
          </select>
        </div>
        <div className="flex justify-end space-x-2">
          <Button
            variant="secondary"
            onClick={() => setShowRoleDialog(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={confirmRoleChange}
            disabled={isChangingRole === roleDialogConfig.userId}
          >
            {isChangingRole === roleDialogConfig.userId ? 'Updating...' : 'Update Role'}
          </Button>
        </div>
      </Dialog>
    </Card>
  );
}

// Helper component for role badges
function RoleBadge({ role }: { role: string }) {
  let variant: 'default' | 'success' | 'warning' | 'danger' | 'info';

  switch (role) {
    case UserOrgRole.ADMIN:
      variant = 'danger';
      break;
    case UserOrgRole.MEMBER:
      variant = 'success';
      break;
    case UserOrgRole.GUEST:
      variant = 'info';
      break;
    default:
      variant = 'default';
  }

  return (
    <Badge variant={variant}>
      {role.charAt(0).toUpperCase() + role.slice(1).toLowerCase?.()}
    </Badge>
  );
}

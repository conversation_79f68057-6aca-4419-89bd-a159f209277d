'use client';

import {useEffect, useState} from 'react';
import {Card} from '@/components/ui/Card';
import {useToast} from '@/components/ui/Toast';
import {ApiKeyResponse, generateApiKey, getApiKeys, revokeApiKey} from '@/server/actions/api-settings';
import {deleteWebhook, getWebhooks, testWebhook, WebhookResponse} from '@/server/actions/webhook-settings';
import { confirmDialog } from '@/components/ui/ConfirmDialog';
import { logger } from '@/utils/logger';

interface ApiSettingsProps {
  organizationId: string;
}

export default function ApiSettings({ organizationId }: ApiSettingsProps) {
  // API Key state
  const [showKey, setShowKey] = useState<{[key: string]: boolean}>({});
  const [apiKeys, setApiKeys] = useState<ApiKeyResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [showNewKeyDialog, setShowNewKeyDialog] = useState(false);
  const [newKey, setNewKey] = useState<ApiKeyResponse | null>(null);
  const [errorMessage, setErrorMessage] = useState('');

  // Webhook state
  const [webhooks, setWebhooks] = useState<WebhookResponse[]>([]);
  const [isLoadingWebhooks, setIsLoadingWebhooks] = useState(true);
  const [showWebhookModal, setShowWebhookModal] = useState(false);
  const [currentWebhook, setCurrentWebhook] = useState<WebhookResponse | undefined>(undefined);
  const [testingWebhookId, setTestingWebhookId] = useState<string | null>(null);

  const { success, error, warn } = useToast();

  // Form state for creating new key
  const [keyName, setKeyName] = useState('');
  const [keyType, setKeyType] = useState<'public' | 'private' | 'both'>('both');
  const [keyPermissions, setKeyPermissions] = useState<string[]>(['read']);

  // Load API keys and webhooks on mount
  useEffect(() => {
    loadApiKeys();
    loadWebhooks();
  }, [organizationId]);

  // Function to load API keys
  const loadApiKeys = async () => {
    setLoading(true);
    setErrorMessage('');

    try {
      const result = await getApiKeys(organizationId);

      if (result.success && result.data) {
        setApiKeys(result.data);
      } else {
        setErrorMessage(result.error || 'Failed to load API keys');
      }
    } catch (err) {
      logger.error('Error loading API keys:', err);
      setErrorMessage('An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Function to load webhooks
  const loadWebhooks = async () => {
    setIsLoadingWebhooks(true);

    try {
      const result = await getWebhooks(organizationId);

      if (result.success && result.data) {
        setWebhooks(result.data);
      } else {
        error('Error', result.error || 'Failed to load webhooks');
      }
    } catch (err) {
      logger.error('Error loading webhooks:', err);
      error('Error', 'An unexpected error occurred');
    } finally {
      setIsLoadingWebhooks(false);
    }
  };

  // Function to handle editing a webhook
  const handleEditWebhook = (webhook: WebhookResponse) => {
    setCurrentWebhook(webhook);
    setShowWebhookModal(true);
  };

  // Function to handle deleting a webhook
  const handleDeleteWebhook = (webhookId: string) => {
    // Show custom confirmation dialog
    confirmDialog({
      message: 'Are you sure you want to delete this webhook? This action cannot be undone.',
      header: 'Delete Webhook',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      acceptClassName: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0',
      accept: async () => {
        try {
          const result = await deleteWebhook({
            organizationId,
            webhookId
          });

          if (result.success) {
            success('Success', 'Webhook deleted successfully');
            loadWebhooks();
          } else {
            error('Error', result.error || 'Failed to delete webhook');
          }
        } catch (err) {
          logger.error('Error deleting webhook:', err);
          error('Error', 'An unexpected error occurred');
        }
      },
      reject: () => {
        // User cancelled - no action needed
        logger.log('Webhook deletion cancelled by user');
      }
    });
  };

  // Function to handle testing a webhook
  const handleTestWebhook = async (webhookId: string) => {
    setTestingWebhookId(webhookId);

    try {
      const result = await testWebhook({
        organizationId,
        webhookId
      });

      if (result.success && result.data) {
        if (result.data.success) {
          success('Success', result.data.message);
        } else {
          warn('Warning', result.data.message);
        }

        // Refresh the webhooks list to get updated status
        loadWebhooks();
      } else {
        error('Error', result.error || 'Failed to test webhook');
      }
    } catch (err) {
      logger.error('Error testing webhook:', err);
      error('Error', 'An unexpected error occurred');
    } finally {
      setTestingWebhookId(null);
    }
  };

  // Handle webhook modal success
  const handleWebhookSuccess = (webhook: WebhookResponse) => {
    loadWebhooks();
  };

  // Function to toggle showing the full key
  const toggleShowKey = (keyId: string) => {
    setShowKey(prev => ({
      ...prev,
      [keyId]: !prev[keyId]
    }));
  };

  // Function to handle creating a new API key
  const handleCreateKey = async () => {
    if (!keyName.trim()) {
      error('Validation Error', 'Please enter a name for your API key');
      return;
    }

    try {
      setErrorMessage('');

      const result = await generateApiKey({
        organizationId,
        name: keyName,
        keyType: keyType,
        permissions: keyPermissions,
      });

      if (result.success && result.data) {
        setNewKey(result.data);
        setShowNewKeyDialog(true);
        setIsCreateModalOpen(false);
        setKeyName('');
        setKeyType('both');
        setKeyPermissions(['read']);
        loadApiKeys();

        success('Success', 'API key created successfully');
      } else {
        setErrorMessage(result.error || 'Failed to create API key');
        error('Error', result.error || 'Failed to create API key');
      }
    } catch (err) {
      logger.error('Error creating API key:', err);
      setErrorMessage('An unexpected error occurred');
      error('Error', 'An unexpected error occurred');
    }
  };

  // Function to handle revoking an API key
  const handleRevokeKey = (keyId: string) => {
    // Show custom confirmation dialog
    confirmDialog({
      message: 'Are you sure you want to revoke this API key? This action cannot be undone.',
      header: 'Revoke API Key',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Revoke',
      rejectLabel: 'Cancel',
      acceptClassName: 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0',
      accept: async () => {
        try {
          setErrorMessage('');

          const result = await revokeApiKey({
            organizationId,
            apiKeyId: keyId,
          });

          if (result.success) {
            loadApiKeys();
            success('Success', 'API key revoked successfully');
          } else {
            setErrorMessage(result.error || 'Failed to revoke API key');
            error('Error', result.error || 'Failed to revoke API key');
          }
        } catch (err) {
          logger.error('Error revoking API key:', err);
          setErrorMessage('An unexpected error occurred');
          error('Error', 'An unexpected error occurred');
        }
      },
      reject: () => {
        // User cancelled - no action needed
      }
    });
  };

  // Function to copy text to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      success('Copied!', 'API key copied to clipboard');
    });
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold mb-4">API Settings</h2>

        {/* API Keys Section */}
      <Card className="p-6">
        <h3 className="text-lg font-medium mb-4">API Keys</h3>
        {/* Rest of UI components would go here */}
      </Card>

      {/* Webhooks Section */}
      <Card className="p-6">
        <h3 className="text-lg font-medium mb-4">Webhooks</h3>
        {/* Rest of UI components would go here */}
      </Card>
    </div>
  );
}

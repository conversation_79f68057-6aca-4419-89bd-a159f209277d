'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { useToast } from '@/components/ui/Toast';
import { togglePortalPublication } from '@/server/actions/support-portal';
import {
  Globe,
  Eye,
  EyeOff,
  ExternalLink,
  Settings,
  BarChart3,
  Users,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Clock,
  Copy,
  ArrowLeft
} from 'lucide-react';

interface PortalManagementProps {
  organizationId: string;
  portal: any;
  onPortalUpdate: (portal: any) => void;
  onBackToSupport?: () => void;
}

export default function PortalManagement({ organizationId, portal, onPortalUpdate, onBackToSupport }: PortalManagementProps) {
  const { success, error: showError } = useToast();
  const [isToggling, setIsToggling] = useState(false);



  const portalUrl = portal.customDomain
    ? `https://${portal.customDomain}`
    : `https://${portal.defaultSubdomain}-support.newinstance.com`;

  const handleTogglePublication = async () => {
    setIsToggling(true);
    try {
      const result = await togglePortalPublication({
        organizationId,
        isPublished: !portal.isPublished,
      });

      if (result.success) {
        onPortalUpdate(result.data);
        success(portal.isPublished ? 'Portal unpublished successfully' : 'Portal published successfully');
      } else {
        showError(result.error || 'Failed to update portal status');
      }
    } catch (error) {
      showError('An unexpected error occurred');
    } finally {
      setIsToggling(false);
    }
  };

  const copyPortalUrl = async () => {
    try {
      await navigator.clipboard.writeText(portalUrl);
      success('Portal URL copied to clipboard');
    } catch (error) {
      showError('Failed to copy URL');
    }
  };

  const getStatusBadge = () => {
    if (portal.isPublished) {
      return <Badge variant="success" className="flex items-center"><CheckCircle className="w-3 h-3 mr-1" />Published</Badge>;
    } else {
      return <Badge variant="warning" className="flex items-center"><Clock className="w-3 h-3 mr-1" />Draft</Badge>;
    }
  };

  const getDomainStatusBadge = () => {
    if (portal.customDomain) {
      if (portal.domainVerified) {
        return <Badge variant="success" className="flex items-center"><CheckCircle className="w-3 h-3 mr-1" />Verified</Badge>;
      } else {
        return <Badge variant="warning" className="flex items-center"><AlertCircle className="w-3 h-3 mr-1" />Pending</Badge>;
      }
    }
    return <Badge variant="default">Default</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header with Back Button */}
      {onBackToSupport && (
        <div className="flex items-center space-x-4 mb-6">
          <Button
            variant="outline"
            onClick={onBackToSupport}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Support Dashboard
          </Button>
        </div>
      )}

      {/* Portal Status Overview */}
      <Card className="p-6">
        <div className="flex items-start justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold text-[#0f0f18] dark:text-white mb-2">
              Customer Support Portal
            </h2>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">
              Manage your customer-facing support portal
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusBadge()}
            <Button
              onClick={handleTogglePublication}
              disabled={isToggling}
              variant={portal.isPublished ? "outline" : "default"}
              className="flex items-center"
            >
              {isToggling ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
              ) : portal.isPublished ? (
                <EyeOff className="w-4 h-4 mr-2" />
              ) : (
                <Eye className="w-4 h-4 mr-2" />
              )}
              {portal.isPublished ? 'Unpublish' : 'Publish'}
            </Button>
          </div>
        </div>

        {/* Portal URL */}
        <div className="bg-[#F8F9FA] dark:bg-[#1e1e2e] rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-[#0f0f18] dark:text-white mb-1">Portal URL</p>
              <div className="flex items-center space-x-2">
                <Globe className="w-4 h-4 text-[#5E5E5E] dark:text-[#C6C6C6]" />
                <span className="text-[#5E5E5E] dark:text-[#C6C6C6] font-mono text-sm">{portalUrl}</span>
                {getDomainStatusBadge()}
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={copyPortalUrl}
                className="flex items-center"
              >
                <Copy className="w-3 h-3 mr-1" />
                Copy
              </Button>
              {portal.isPublished && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(portalUrl, '_blank')}
                  className="flex items-center"
                >
                  <ExternalLink className="w-3 h-3 mr-1" />
                  Visit
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Portal Info Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-white dark:bg-[#1e1e2e] rounded-lg p-4 border border-[#E0D7FF] dark:border-[#2a2a38]">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Company</p>
                <p className="font-semibold text-[#0f0f18] dark:text-white">{portal.branding.companyName}</p>
              </div>
              <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: portal.branding.primaryColor }}>
                <span className="text-white text-xs font-bold">
                  {portal.branding.companyName.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-[#1e1e2e] rounded-lg p-4 border border-[#E0D7FF] dark:border-[#2a2a38]">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Contact Email</p>
                <p className="font-semibold text-[#0f0f18] dark:text-white">{portal.content.contactEmail}</p>
              </div>
              <MessageSquare className="w-5 h-5 text-[#5E5E5E] dark:text-[#C6C6C6]" />
            </div>
          </div>

          <div className="bg-white dark:bg-[#1e1e2e] rounded-lg p-4 border border-[#E0D7FF] dark:border-[#2a2a38]">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Support Hours</p>
                <p className="font-semibold text-[#0f0f18] dark:text-white text-xs">{portal.content.supportHours}</p>
              </div>
              <Clock className="w-5 h-5 text-[#5E5E5E] dark:text-[#C6C6C6]" />
            </div>
          </div>
        </div>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg flex items-center justify-center">
              <Settings className="w-5 h-5 text-indigo-600 dark:text-indigo-400" />
            </div>
            <div>
              <p className="font-medium text-[#0f0f18] dark:text-white">Portal Settings</p>
              <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">Customize branding & content</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="font-medium text-[#0f0f18] dark:text-white">Analytics</p>
              <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">View portal metrics</p>
            </div>
          </div>
        </Card>

        <Card
          className="p-4 hover:shadow-md transition-shadow cursor-pointer"
          onClick={() => window.location.href = `/dashboard/organization/${organizationId}/support`}
        >
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <p className="font-medium text-[#0f0f18] dark:text-white">Customer Tickets</p>
              <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">Manage customer requests</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
              <Globe className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="font-medium text-[#0f0f18] dark:text-white">Domain Settings</p>
              <p className="text-xs text-[#5E5E5E] dark:text-[#C6C6C6]">Manage custom domain</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Portal Preview */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-[#0f0f18] dark:text-white mb-4">Portal Preview</h3>
        <div className="border border-[#E0D7FF] dark:border-[#2a2a38] rounded-lg overflow-hidden">
          {/* Header with Logo, Company Name, and Navigation */}
          <div
            className="p-4 border-b border-[#E0D7FF] dark:border-[#2a2a38]"
            style={{ backgroundColor: portal.branding.backgroundColor }}
          >
            <div className="flex items-center justify-between">
              {/* Left side: Logo and Company Name */}
              <div className="flex items-center space-x-3">
                {portal.branding.logoUrl && (
                  <img
                    src={portal.branding.logoUrl}
                    alt="Logo"
                    className="h-8 w-auto"
                  />
                )}
                <h4
                  className="font-semibold"
                  style={{ color: portal.branding.textColor }}
                >
                  {portal.branding.companyName}
                </h4>
              </div>

              {/* Right side: Navigation */}
              {portal.navigation && portal.navigation.length > 0 ? (
                <div className="flex items-center gap-1">
                  {portal.navigation
                    .sort((a: any, b: any) => a.order - b.order)
                    .slice(0, 3)
                    .map((item: any, index: number) => {
                      const isActive = index === 0; // First item appears as active/current page
                      return (
                        <div
                          key={index}
                          className={`
                            relative px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 cursor-pointer
                            hover:scale-105 hover:shadow-sm
                            ${isActive ? 'shadow-sm' : ''}
                          `}
                          style={{
                            backgroundColor: isActive
                              ? portal.branding.primaryColor
                              : `${portal.branding.textColor}10`,
                            color: isActive
                              ? '#ffffff'
                              : portal.branding.textColor,
                            borderWidth: '1px',
                            borderStyle: 'solid',
                            borderColor: isActive
                              ? portal.branding.primaryColor
                              : `${portal.branding.textColor}20`,
                          }}
                          onMouseEnter={(e) => {
                            if (!isActive) {
                              e.currentTarget.style.backgroundColor = `${portal.branding.secondaryColor}20`;
                              e.currentTarget.style.borderColor = `${portal.branding.secondaryColor}40`;
                              e.currentTarget.style.color = portal.branding.secondaryColor;
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isActive) {
                              e.currentTarget.style.backgroundColor = `${portal.branding.textColor}10`;
                              e.currentTarget.style.borderColor = `${portal.branding.textColor}20`;
                              e.currentTarget.style.color = portal.branding.textColor;
                            }
                          }}
                        >
                          {/* Active indicator */}
                          {isActive && (
                            <div
                              className="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-6 h-0.5 rounded-full"
                              style={{ backgroundColor: '#ffffff' }}
                            />
                          )}

                          <span className="relative z-10 tracking-wide">
                            {item.label}
                          </span>
                        </div>
                      );
                    })}

                  {/* Show "More" indicator if there are additional navigation items */}
                  {portal.navigation.length > 3 && (
                    <div
                      className="px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 opacity-60"
                      style={{
                        backgroundColor: `${portal.branding.textColor}08`,
                        color: portal.branding.textColor,
                        borderWidth: '1px',
                        borderStyle: 'dashed',
                        borderColor: `${portal.branding.textColor}20`,
                      }}
                    >
                      <span className="tracking-wide">
                        +{portal.navigation.length - 3} more
                      </span>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-sm opacity-60" style={{ color: portal.branding.textColor }}>
                  No navigation configured
                </div>
              )}
            </div>
          </div>

          {/* Banner Section */}
          <div
            className="px-4 py-3 border-b border-[#E0D7FF] dark:border-[#2a2a38]"
            style={{ backgroundColor: `${portal.branding.backgroundColor}f0` }}
          >
            <div className="text-center">
              <span
                className="text-sm opacity-50"
                style={{ color: portal.branding.textColor }}
              >
                Banner space reserved for announcements
              </span>
            </div>
          </div>
          <div
            className="p-6"
            style={{ backgroundColor: portal.branding.backgroundColor }}
          >
            <h2
              className="text-2xl font-bold mb-2"
              style={{ color: portal.branding.textColor }}
            >
              {portal.content.welcomeMessage}
            </h2>
            <p
              className="mb-4"
              style={{ color: `${portal.branding.textColor}80` }}
            >
              {portal.content.helpText}
            </p>
            <div className="flex space-x-2">
              <div
                className="px-4 py-2 rounded-lg text-white text-sm font-medium"
                style={{ backgroundColor: portal.branding.primaryColor }}
              >
                Submit Ticket
              </div>
              <div
                className="px-4 py-2 rounded-lg text-sm font-medium border"
                style={{
                  borderColor: portal.branding.secondaryColor,
                  color: portal.branding.secondaryColor
                }}
              >
                Track Ticket
              </div>
            </div>
          </div>
        </div>
        <div className="mt-4 text-center">
          <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
            This is how your portal will appear to customers
          </p>
        </div>
      </Card>

      {/* Integration Info */}
      {!portal.isPublished && (
        <Card className="p-6 bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Portal Not Published</h4>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                Your portal is configured but not yet live. Click "Publish" to make it available to your customers.
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* Success Message for Published Portal */}
      {portal.isPublished && (
        <Card className="p-6 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-800 dark:text-green-200 mb-1">Portal Successfully Published!</h4>
                <p className="text-sm text-green-700 dark:text-green-300 mb-3">
                  Your customer support portal is now live and accepting tickets. You can now return to the support dashboard to manage incoming customer requests.
                </p>
              </div>
            </div>
            <Button
              onClick={() => window.location.href = `/dashboard/organization/${organizationId}/support`}
              className="flex items-center bg-green-600 hover:bg-green-700 text-white"
            >
              <Users className="w-4 h-4 mr-2" />
              Go to Support Dashboard
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}

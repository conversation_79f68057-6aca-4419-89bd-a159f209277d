'use client';

import {useEffect, useState} from 'react';
import {Dialog} from '@/components/ui/Dialog';
import {Button} from '@/components/ui/Button';
import {Input} from '@/components/ui/Input';
import {FormField} from '@/components/ui/Form';
import {WebhookEvent, WebhookResponse} from '@/types/webhooks';
import {createWebhook, updateWebhook} from '@/server/actions/webhook-settings';
import {useToast} from '@/components/ui/Toast';

interface WebhookModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  organizationId: string;
  webhook?: WebhookResponse; // For editing existing webhook
  onSuccess?: (webhook: any) => void; // Using any to avoid type conflicts between server and client types
}

export default function WebhookModal({
  open,
  onOpenChange,
  organizationId,
  webhook,
  onSuccess
}: WebhookModalProps) {
  const [name, setName] = useState('');
  const [url, setUrl] = useState('');
  const [selectedEvents, setSelectedEvents] = useState<WebhookEvent[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const { success, error: showError } = useToast();
  const [showSecret, setShowSecret] = useState(false);
  const [secretKey, setSecretKey] = useState('');

  // Initialize form when editing
  useEffect(() => {
    if (webhook) {
      setName(webhook.name);
      setUrl(webhook.url);
      setSelectedEvents(webhook.events);
    } else {
      setName('');
      setUrl('');
      setSelectedEvents([]);
    }
  }, [webhook, open]);

  // Available webhook events with descriptions
  const availableEvents: { value: WebhookEvent; label: string; description: string }[] = [
    { value: 'ticket.created', label: 'Ticket Created', description: 'When a new support ticket is created' },
    { value: 'ticket.updated', label: 'Ticket Updated', description: 'When a ticket is updated' },
    { value: 'ticket.resolved', label: 'Ticket Resolved', description: 'When a ticket is marked as resolved' },
    { value: 'ticket.reopened', label: 'Ticket Reopened', description: 'When a resolved ticket is reopened' },
    { value: 'ticket.assigned', label: 'Ticket Assigned', description: 'When a ticket is assigned to an agent' },
    { value: 'ticket.comment.added', label: 'Ticket Comment Added', description: 'When a comment is added to a ticket' },
    { value: 'error.logged', label: 'Error Logged', description: 'When a new error is logged in the system' },
    { value: 'error.resolved', label: 'Error Resolved', description: 'When an error is marked as resolved' },
    { value: 'user.invited', label: 'User Invited', description: 'When a new user is invited to the organization' },
  ];

  // Toggle event selection
  const toggleEvent = (event: WebhookEvent) => {
    setSelectedEvents(prev =>
      prev.includes(event)
        ? prev.filter(e => e !== event)
        : [...prev, event]
    );
  };

  // Validate form
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!url.trim()) {
      newErrors.url = 'Endpoint URL is required';
    } else {
      try {
        new URL(url);
      } catch (e) {
        newErrors.url = 'Please enter a valid URL';
      }
    }

    if (selectedEvents.length === 0) {
      newErrors.events = 'Select at least one event to subscribe to';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      let result;

      if (webhook) {
        // Update existing webhook
        result = await updateWebhook({
          organizationId,
          webhookId: webhook.id,
          name,
          url,
          events: selectedEvents,
        });
      } else {
        // Create new webhook
        result = await createWebhook({
          organizationId,
          name,
          url,
          events: selectedEvents,
        });
      }

      if (result.success && result.data) {
        success(
          webhook ? 'Webhook updated successfully' : 'Webhook created successfully'
        );

        onOpenChange(false);
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        showError(
          'Error',
          result.error || 'Failed to save webhook'
        );
      }
    } catch (err) {
      console.error('Error saving webhook:', err);
      showError(
        'Error',
        'An unexpected error occurred'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const generateSecret = () => {
    // Implement secret generation logic
  };

  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
      title={webhook ? 'Edit Webhook' : 'Create New Webhook'}
    >
      <div className="mt-4 space-y-4">
        <FormField
          label="Webhook Name"
          error={errors.name}
        >
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Order Notifications"
          />
        </FormField>

        <FormField
          label="Endpoint URL"
          error={errors.url}
        >
          <Input
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="https://example.com/webhooks"
          />
        </FormField>

        <FormField
          label="Events to Subscribe"
          error={errors.events}
        >
          <div className="space-y-3 mt-2 max-h-60 overflow-y-auto">
            {availableEvents.map((event) => (
              <div key={event.value} className="flex items-start gap-2 pb-2 border-b border-gray-100 dark:border-gray-800">
                <input
                  type="checkbox"
                  id={event.value}
                  checked={selectedEvents.includes(event.value)}
                  onChange={() => toggleEvent(event.value)}
                  className="mt-1"
                />
                <div>
                  <label htmlFor={event.value} className="font-medium cursor-pointer">
                    {event.label}
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">
                    {event.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </FormField>

        <FormField
          label="Secret Key"
          error={errors.secret}
        >
          <div className="flex">
            <Input
              type={showSecret ? 'text' : 'password'}
              value={secretKey}
              onChange={(e) => setSecretKey(e.target.value)}
              placeholder="••••••••••••••••"
              className="flex-1"
            />
            <Button
              type="button"
              variant="secondary"
              className="ml-2"
              onClick={() => setShowSecret(!showSecret)}
            >
              {showSecret ? 'Hide' : 'Show'}
            </Button>
            <Button
              type="button"
              variant="secondary"
              className="ml-2"
              onClick={generateSecret}
            >
              Generate
            </Button>
          </div>
        </FormField>

        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md text-sm text-blue-700 dark:text-blue-300 mt-4">
          <p className="font-medium">About Webhook Security</p>
          <p className="mt-1">
            Each webhook will be created with a unique secret that will be used to sign payloads.
            You should verify the signature to ensure the request is authentic.
          </p>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button
            variant="secondary"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSubmit}
            loading={isLoading}
          >
            {webhook ? 'Save Changes' : 'Create Webhook'}
          </Button>
        </div>
      </div>
    </Dialog>
  );
}

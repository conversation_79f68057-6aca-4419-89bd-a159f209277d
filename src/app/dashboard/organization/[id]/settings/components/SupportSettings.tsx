'use client';

import {useCallback, useEffect, useState} from 'react';
import {Card} from '@/components/ui/Card';
import {Switch} from '@/components/ui/Switch';
import {Button} from '@/components/ui/Button';
import {Select} from '@/components/ui/Select';
import {useToast} from '@/components/ui/Toast';
import {Input} from '@/components/ui/Input';
import {Dialog} from '@/components/ui/Dialog';
import {getSupportSettings, updateBusinessHours, updateSupportSettings} from '@/server/actions/support-settings';
import {getSupportPortal} from '@/server/actions/support-portal';
import PortalSetupWizard from './PortalSetupWizard';
import PortalManagement from './PortalManagement';
import { logger } from '@/utils/logger';

interface SupportSettingsProps {
  organizationId: string;
}

// Define business hours type
type BusinessHour = {
  day: string;
  isOpen: boolean;
  openTime?: string;
  closeTime?: string;
};

// Time options for selects
const responseTimeOptions = [
  { label: '1 hour', value: '1' },
  { label: '2 hours', value: '2' },
  { label: '4 hours', value: '4' },
  { label: '8 hours', value: '8' },
  { label: '1 business day', value: '24' },
];

const resolutionTimeOptions = [
  { label: '1 business day', value: '24' },
  { label: '2 business days', value: '48' },
  { label: '3 business days', value: '72' },
  { label: '1 week', value: '168' },
  { label: '2 weeks', value: '336' },
];

// Time format helper
const formatTime = (time: string) => {
  if (!time) return '';
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours, 10);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

export default function SupportSettings({ organizationId }: SupportSettingsProps) {
  const { success, error: showError, warn, info } = useToast();

  // State for settings
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [firstResponse, setFirstResponse] = useState('4');
  const [resolution, setResolution] = useState('72');
  const [roundRobin, setRoundRobin] = useState(true);
  const [autoEscalate, setAutoEscalate] = useState(false);
  const [businessHours, setBusinessHours] = useState<BusinessHour[]>([]);
  const [isDirty, setIsDirty] = useState(false);

  // Portal configuration state
  const [portal, setPortal] = useState<any>(null);
  const [activeTab, setActiveTab] = useState<'general' | 'portal' | 'email'>('portal');

  // Portal status for access control
  const [portalStatus, setPortalStatus] = useState<{
    exists: boolean;
    isPublished: boolean;
    isConfigured: boolean;
    hasRequiredFields: boolean;
    setupComplete: boolean;
    loading: boolean;
  }>({
    exists: false,
    isPublished: false,
    isConfigured: false,
    hasRequiredFields: false,
    setupComplete: false,
    loading: true,
  });

  // Portal handlers
  const handlePortalSetupComplete = useCallback((newPortal: any) => {
    setPortal(newPortal);

    // Update portal status after setup completion
    const hasRequiredFields = !!(
      newPortal.branding?.companyName &&
      newPortal.content?.contactEmail &&
      newPortal.content?.welcomeMessage &&
      newPortal.defaultSubdomain
    );

    const isConfigured = !!(
      hasRequiredFields &&
      newPortal.branding?.primaryColor &&
      newPortal.content?.helpText
    );

    const setupComplete = isConfigured && newPortal.isPublished;

    setPortalStatus({
      exists: true,
      isPublished: newPortal.isPublished,
      isConfigured,
      hasRequiredFields,
      setupComplete,
      loading: false,
    });

    success('Support portal has been set up successfully!');
  }, [success]);

  const handlePortalUpdate = useCallback((updatedPortal: any) => {
    setPortal(updatedPortal);

    // Update portal status after any portal changes
    const hasRequiredFields = !!(
      updatedPortal.branding?.companyName &&
      updatedPortal.content?.contactEmail &&
      updatedPortal.content?.welcomeMessage &&
      updatedPortal.defaultSubdomain
    );

    const isConfigured = !!(
      hasRequiredFields &&
      updatedPortal.branding?.primaryColor &&
      updatedPortal.content?.helpText
    );

    const setupComplete = isConfigured && updatedPortal.isPublished;

    setPortalStatus({
      exists: true,
      isPublished: updatedPortal.isPublished,
      isConfigured,
      hasRequiredFields,
      setupComplete,
      loading: false,
    });
  }, []);

  // Tab click handler with access control
  const handleTabClick = useCallback((tab: 'general' | 'portal' | 'email') => {
    // Portal tab is always accessible
    if (tab === 'portal') {
      setActiveTab('portal');
      return;
    }

    // Other tabs require portal configuration completion (not publication)
    if (!portalStatus.setupComplete) {
      // Show user feedback about why tab is disabled
      showError('Portal Configuration Required', 'Please complete the customer portal configuration before accessing other settings.');
      return;
    }

    // Allow access to other tabs when setup is complete
    setActiveTab(tab);
  }, [portalStatus.setupComplete, showError]);

  // Handle URL query parameter for deep linking with access control
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const tab = urlParams.get('tab');

    // If portal configuration is incomplete, always force portal tab
    if (!portalStatus.loading && !portalStatus.setupComplete) {
      setActiveTab('portal');
      return;
    }

    // Only allow navigation to other tabs if portal configuration is complete
    if (tab === 'support' || !portalStatus.setupComplete) {
      setActiveTab('portal');
    } else if (portalStatus.setupComplete) {
      // Allow normal tab navigation only when configuration is complete
      if (tab === 'general') {
        setActiveTab('general');
      }
    }
  }, [portalStatus.loading, portalStatus.setupComplete]);

  // Business hours edit dialog state
  const [businessHoursDialogOpen, setBusinessHoursDialogOpen] = useState(false);
  const [editingBusinessHours, setEditingBusinessHours] = useState<BusinessHour[]>([]);

  // Fetch support settings on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const [settingsResult, portalResult] = await Promise.all([
          getSupportSettings(organizationId),
          getSupportPortal(organizationId)
        ]);

        // Handle support settings
        if (settingsResult.success && settingsResult.data) {
          const { responseTimeGoals, ticketAssignment, businessHours: hours } = settingsResult.data;
          setFirstResponse(responseTimeGoals.firstResponse);
          setResolution(responseTimeGoals.resolution);
          setRoundRobin(ticketAssignment.enableRoundRobin);
          setAutoEscalate(ticketAssignment.autoEscalate);
          setBusinessHours(hours);
          setEditingBusinessHours([...hours]);
        } else {
          setError(settingsResult.error || 'Failed to load support settings');
          logger.error('Failed to load support settings:', settingsResult.error);
        }

        // Handle portal configuration and status validation
        if (portalResult.success && portalResult.data) {
          const portal = portalResult.data;
          setPortal(portal);

          // Validate portal setup status - portal exists regardless of publication status
          const hasRequiredFields = !!(
            portal.branding?.companyName &&
            portal.content?.contactEmail &&
            portal.content?.welcomeMessage &&
            portal.defaultSubdomain
          );

          const isConfigured = !!(
            hasRequiredFields &&
            portal.branding?.primaryColor &&
            portal.content?.helpText
          );

          // Portal setup is complete when it's configured, regardless of publication status
          // Publication status only affects public visibility, not admin management capabilities
          const setupComplete = isConfigured;

          setPortalStatus({
            exists: true,
            isPublished: portal.isPublished,
            isConfigured,
            hasRequiredFields,
            setupComplete,
            loading: false,
          });
        } else {
          logger.error('Failed to load portal configuration:', portalResult.error);
          setPortalStatus({
            exists: false,
            isPublished: false,
            isConfigured: false,
            hasRequiredFields: false,
            setupComplete: false,
            loading: false,
          });
        }
      } catch (error) {
        logger.error('Error loading data:', error);
        setError('An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [organizationId]);

  // Check for changes
  useEffect(() => {
    const checkIfDirty = async () => {
      try {
        const result = await getSupportSettings(organizationId);
        if (result.success && result.data) {
          const { responseTimeGoals, ticketAssignment } = result.data;

          const isDirty =
            firstResponse !== responseTimeGoals.firstResponse ||
            resolution !== responseTimeGoals.resolution ||
            roundRobin !== ticketAssignment.enableRoundRobin ||
            autoEscalate !== ticketAssignment.autoEscalate;

          setIsDirty(isDirty);
        }
      } catch (error) {
        logger.error('Error checking for changes:', error);
      }
    };

    if (!isLoading) {
      checkIfDirty();
    }
  }, [firstResponse, resolution, roundRobin, autoEscalate, isLoading, organizationId]);

  // Save support settings
  const handleSaveSettings = async () => {
    setIsSaving(true);
    setError(null);

    try {
      const result = await updateSupportSettings({
        organizationId,
        settings: {
          responseTimeGoals: {
            firstResponse,
            resolution,
          },
          ticketAssignment: {
            enableRoundRobin: roundRobin,
            autoEscalate,
          },
          businessHours,
        },
      });

      if (result.success) {
        success('Settings saved', 'Support settings have been updated successfully.');
        setIsDirty(false);
      } else {
        setError(result.error || 'Failed to save settings');
        showError('Error', result.error || 'Failed to save settings');
      }
    } catch (error) {
      logger.error('Error saving support settings:', error);
      setError('An unexpected error occurred');
      showError('Error', 'An unexpected error occurred');
    } finally {
      setIsSaving(false);
    }
  };

  // Reset settings to original values
  const handleCancel = useCallback(async () => {
    try {
      const result = await getSupportSettings(organizationId);
      if (result.success && result.data) {
        const { responseTimeGoals, ticketAssignment } = result.data;

        setFirstResponse(responseTimeGoals.firstResponse);
        setResolution(responseTimeGoals.resolution);
        setRoundRobin(ticketAssignment.enableRoundRobin);
        setAutoEscalate(ticketAssignment.autoEscalate);
        setIsDirty(false);
      }
    } catch (error) {
      logger.error('Error resetting support settings:', error);
    }
  }, [organizationId]);

  // Update business hours
  const handleUpdateBusinessHours = async () => {
    try {
      const result = await updateBusinessHours({
        organizationId,
        businessHours: editingBusinessHours,
      });

      if (result.success) {
        setBusinessHours(editingBusinessHours);
        setBusinessHoursDialogOpen(false);
        success('Business hours updated', 'Business hours have been updated successfully.');
      } else {
        showError('Error', result.error || 'Failed to update business hours');
      }
    } catch (error) {
      logger.error('Error updating business hours:', error);
      showError('Error', 'An unexpected error occurred');
    }
  };

  // Handle time change for a specific day
  const handleTimeChange = (index: number, field: 'openTime' | 'closeTime', value: string) => {
    const updated = [...editingBusinessHours];
    updated[index] = { ...updated[index], [field]: value };
    setEditingBusinessHours(updated);
  };

  // Handle toggle for open/closed
  const handleToggleDay = (index: number, isOpen: boolean) => {
    const updated = [...editingBusinessHours];
    updated[index] = {
      ...updated[index],
      isOpen,
      // Reset times if closed
      ...(!isOpen && { openTime: undefined, closeTime: undefined })
    };
    setEditingBusinessHours(updated);
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <div className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-8"></div>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-3"></div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full mb-6"></div>
                <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-3"></div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full mb-6"></div>
              </div>
              <div>
                <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-3"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-full mb-3"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-6"></div>
                <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-full mb-3"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-6"></div>
              </div>
            </div>
          </div>
        </div>
      </Card>
    );
  }

  // Tab content renderer
  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'portal':
        return renderPortalSettings();
      case 'email':
        return renderEmailSettings();
      default:
        return renderGeneralSettings();
    }
  };

  const renderGeneralSettings = () => (
    <div>
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-lg mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Response Time Goals */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg">Response Time Goals</h3>
          <div>
            <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1">
              First Response
            </label>
            <Select
              value={firstResponse}
              options={responseTimeOptions}
              onChange={(value) => setFirstResponse(value as string)}
              placeholder="Select response time"
            />
          </div>

          <div>
            <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] font-medium mb-1">
              Resolution Time
            </label>
            <Select
              value={resolution}
              options={resolutionTimeOptions}
              onChange={(value) => setResolution(value as string)}
              placeholder="Select resolution time"
            />
          </div>
        </div>

        {/* Auto Assignment */}
        <div className="space-y-4">
          <h3 className="font-medium text-lg">Ticket Assignment</h3>
          <div>
            <label className="flex items-center space-x-3">
              <Switch
                checked={roundRobin}
                onChange={(e) => setRoundRobin(e.value)}
              />
              <span className="text-[#5E5E5E] dark:text-[#C6C6C6] font-medium">
                Enable Round-Robin Assignment
              </span>
            </label>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
              Automatically assign new tickets to team members in rotation.
            </p>
          </div>

          <div>
            <label className="flex items-center space-x-3">
              <Switch
                checked={autoEscalate}
                onChange={(e) => setAutoEscalate(e.value)}
              />
              <span className="text-[#5E5E5E] dark:text-[#C6C6C6] font-medium">
                Auto-escalate Overdue Tickets
              </span>
            </label>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
              Escalate tickets that exceed response time goals.
            </p>
          </div>
        </div>
      </div>

      <div className="mt-8 border-t border-[#E0D7FF] dark:border-[#2a2a38] pt-6">
        <h3 className="font-medium text-lg mb-4">Business Hours</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {businessHours.map((day) => (
            <div key={day.day}>
              <div className="text-sm font-medium mb-1 capitalize">{day.day}</div>
              <div className="text-[#5E5E5E] dark:text-[#C6C6C6]">
                {day.isOpen
                  ? `${formatTime(day.openTime || '')} - ${formatTime(day.closeTime || '')}`
                  : 'Closed'}
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4">
          <Button
            variant="secondary"
            onClick={() => {
              setEditingBusinessHours([...businessHours]);
              setBusinessHoursDialogOpen(true);
            }}
          >
            Edit Business Hours
          </Button>
        </div>
      </div>

      <div className="mt-8 border-t border-[#E0D7FF] dark:border-[#2a2a38] pt-6">
        <h3 className="font-medium text-lg mb-4">Custom Fields</h3>
        <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 rounded-lg mb-4">
          <p className="text-center text-[#5E5E5E] dark:text-[#C6C6C6]">
            No custom fields configured yet
          </p>
        </div>
        <Button
          variant="secondary"
          disabled
        >
          Add Custom Field
        </Button>
        <div className="mt-2 text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
          <div className="bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-300 p-3 rounded">
            Custom fields feature coming soon
          </div>
        </div>
      </div>

      <div className="flex justify-end mt-8 space-x-3">
        {isDirty && (
          <Button
            variant="secondary"
            onClick={handleCancel}
            disabled={isSaving}
          >
            Cancel
          </Button>
        )}
        <Button
          variant="primary"
          onClick={handleSaveSettings}
          loading={isSaving}
          disabled={!isDirty || isSaving}
        >
          Save Changes
        </Button>
      </div>
    </div>
  );

  const renderPortalSettings = () => {
    // Show setup wizard only if no portal exists at all
    if (!portal) {
      return (
        <PortalSetupWizard
          organizationId={organizationId}
          existingPortal={portal}
          onComplete={handlePortalSetupComplete}
        />
      );
    }

    // Show management interface for existing portals (published or unpublished)
    return (
      <PortalManagement
        organizationId={organizationId}
        portal={portal}
        onPortalUpdate={handlePortalUpdate}
      />
    );
  };

  const renderEmailSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-lg mb-4">Email Integration</h3>
        <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-8 rounded-lg text-center">
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
            Email integration features coming soon
          </p>
          <p className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
            Configure SMTP settings, Gmail/Outlook OAuth, and automatic ticket creation from emails.
          </p>
        </div>
      </div>
    </div>
  );

  return (
    <Card>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold">Support Settings</h2>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mt-1">
              Configure settings for your {"organization's"} support system.
            </p>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="mb-6 border-b border-[#E0D7FF] dark:border-[#2a2a38]">
          <div className="flex overflow-x-auto">
            <button
              className={`px-4 py-2 font-medium text-sm whitespace-nowrap transition-colors ${
                !portalStatus.setupComplete
                  ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : activeTab === 'general'
                    ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
                    : 'text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-indigo-600 dark:hover:text-indigo-400'
              }`}
              onClick={() => handleTabClick('general')}
              disabled={!portalStatus.setupComplete}
              title={!portalStatus.setupComplete ? 'Complete portal setup to access general settings' : ''}
            >
              General Settings
            </button>
            <button
              className={`px-4 py-2 font-medium text-sm whitespace-nowrap relative ${
                activeTab === 'portal'
                  ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
                  : 'text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-indigo-600 dark:hover:text-indigo-400'
              }`}
              onClick={() => handleTabClick('portal')}
            >
              Customer Portal
            </button>
            <button
              className={`px-4 py-2 font-medium text-sm whitespace-nowrap transition-colors ${
                !portalStatus.setupComplete
                  ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                  : activeTab === 'email'
                    ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
                    : 'text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-indigo-600 dark:hover:text-indigo-400'
              }`}
              onClick={() => handleTabClick('email')}
              disabled={!portalStatus.setupComplete}
              title={!portalStatus.setupComplete ? 'Complete portal setup to access email integration' : ''}
            >
              Email Integration
            </button>
          </div>
        </div>

        {/* Setup Status Banner */}
        {!portalStatus.loading && !portalStatus.setupComplete && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Complete Portal Setup to Access All Settings
                </h3>
                <div className="mt-1 text-sm text-blue-700 dark:text-blue-300">
                  <p>
                    General Settings and Email Integration will be available after you complete the customer portal configuration.
                    {!portalStatus.exists && ' Start by setting up your portal below.'}
                    {portalStatus.exists && !portalStatus.hasRequiredFields && ' Complete the required fields (company name, contact email, welcome message).'}
                    {portalStatus.hasRequiredFields && !portalStatus.isConfigured && ' Finish the branding and content configuration.'}
                    {portalStatus.isConfigured && ' Your portal is configured and all settings are now available.'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tab Content */}
        <div className="mt-6">
          {renderTabContent()}
        </div>
      </div>

      {/* Business Hours Dialog */}
      <Dialog
        open={businessHoursDialogOpen}
        onOpenChange={() => setBusinessHoursDialogOpen(false)}
        title="Edit Business Hours"
      >
        <div className="space-y-4 mb-6">
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mb-4">
            Set your organization&#39;s business hours. Support response time goals will only be counted during business hours.
          </p>

          {editingBusinessHours.map((day, index) => (
            <div key={day.day} className="border-b border-[#E0D7FF] dark:border-[#2a2a38] pb-4 last:border-b-0">
              <div className="flex items-center justify-between mb-2">
                <div className="text-base font-medium capitalize">{day.day}</div>
                <Switch
                  checked={day.isOpen}
                  onChange={(e) => handleToggleDay(index, e.value)}
                />
              </div>

              {day.isOpen && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mb-1">
                      Open Time
                    </label>
                    <Input
                      type="time"
                      value={day.openTime || '09:00'}
                      onChange={(e) => handleTimeChange(index, 'openTime', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-[#5E5E5E] dark:text-[#C6C6C6] text-sm mb-1">
                      Close Time
                    </label>
                    <Input
                      type="time"
                      value={day.closeTime || '17:00'}
                      onChange={(e) => handleTimeChange(index, 'closeTime', e.target.value)}
                    />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-end space-x-3">
          <Button
            variant="secondary"
            onClick={() => setBusinessHoursDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleUpdateBusinessHours}
          >
            Save Business Hours
          </Button>
        </div>
      </Dialog>
    </Card>
  );
}

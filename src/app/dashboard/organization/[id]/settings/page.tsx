'use client';

import {useEffect, useState} from 'react';
import {use<PERSON><PERSON><PERSON>, useRouter, useSearchParams} from 'next/navigation';
import Image from 'next/image';
import {getOrganizationById, getUserData} from '@/server/actions/user-actions';
import {UserOrgRole} from '@/constants/role-constants';
import {Card} from '@/components/ui/Card';
import {Button} from '@/components/ui/Button';
import {useToast} from '@/components/ui/Toast';
import {Tab, TabList, TabPanel, Tabs} from '@/components/ui/CustomTabs';

// Components
import GeneralSettingsComponent from './components/GeneralSettings';
import SupportSettingsComponent from './components/SupportSettings';
import UserManagementComponent from './components/UserManagement';
import ApiSettingsComponent from './components/ApiSettings';

// Organization data structure - matches server action return type
interface OrganizationData {
  id: string;
  name: string;
  description: string | null;
  logoUrl: string | null;
  email: string | null;
  domain: string | null;
  industry: string | null;
  subscriptionPlan: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  memberCount: number;
  members: {
    userId: string;
    role: string;
    name: string;
    email: string;
  }[];
  userRole: string | null;
}

interface UserData {
  id: string;
  name: string;
  email: string;
}

type TabType = 'general' | 'support' | 'users' | 'api';

export default function OrganizationSettingsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const params = useParams();
  const organizationId = params.id as string;
  const { success } = useToast();

  // UI state
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [organizationData, setOrganizationData] = useState<OrganizationData | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Get active tab from URL or default to 'general'
  const tabFromUrl = searchParams.get('tab') as TabType | null;
  const [activeTab, setActiveTab] = useState<TabType>(
    tabFromUrl && ['general', 'support', 'users', 'api'].includes(tabFromUrl)
      ? tabFromUrl
      : 'general'
  );

  // Update URL when tab changes
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    const newUrl = `/dashboard/organization/${organizationId}/settings?tab=${tab}`;
    // Update URL without reloading the page
    window.history.pushState({}, '', newUrl);
  };

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Check authentication status
        const userResult = await getUserData();
        if (!userResult || !userResult.success) {
          router.push('/auth?mode=login');
          return;
        }

        setUserData(userResult.success && 'data' in userResult ? userResult.data : null);

        // Fetch organization data
        const orgResult = await getOrganizationById(organizationId);
        if (!orgResult.success) {
          setError('error' in orgResult ? orgResult.error : 'Failed to fetch organization data');
          setOrganizationData(null);
        } else {
          const orgData = orgResult.data;
          if (orgData) {
            setOrganizationData(orgData);

            // Check if user has admin privileges
            if (orgData.userRole !== UserOrgRole.ADMIN) {
              setError('You do not have permission to manage this organization');
            }
          } else {
            setError('Organization data not found');
          }
        }
      } catch (error: any) {
        console.error('Error loading organization settings:', error);
        setError(error.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [organizationId, router]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-[#6964D3] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">Loading organization settings...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex items-center justify-center">
        <Card className="max-w-md w-full p-6">
          <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-4">Error</h2>
          <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mb-6">{error}</p>
          <Button
            variant="primary"
            className="w-full"
            onClick={() => router.push(`/dashboard/organization/${organizationId}`)}
          >
            Back to Organization
          </Button>
        </Card>
      </div>
    );
  }

  const handleUpdateSuccess = (updatedData: Partial<OrganizationData>) => {
    if (organizationData) {
      setOrganizationData({
        ...organizationData,
        ...updatedData
      });

      success("Success", "Organization settings updated successfully!");
    }
  };

  return (
    <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex flex-col">
      <header className="bg-white dark:bg-[#1e1e28] shadow">
        <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <button
              className="mr-4 text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-[#6964D3] dark:hover:text-white transition-colors"
              onClick={() => router.push(`/dashboard/organization/${organizationId}`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
            </button>
            <Image
              src="/assets/logos/purple-cloud-yellow-dots.svg"
              alt="New Instance Logo"
              width={40}
              height={40}
              className="mr-3"
            />
            <span className="text-xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
          </div>

          <div className="flex items-center gap-4">
            <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">
              {userData?.name || 'User'}
            </span>
          </div>
        </div>
      </header>

      <main className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6 flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold mb-1">Organization Settings</h1>
            <p className="text-[#5E5E5E] dark:text-[#C6C6C6]">
              Manage settings for {organizationData?.name}
            </p>
          </div>

          <Button
            variant="secondary"
            onClick={() => router.push(`/dashboard/organization/${organizationId}`)}
          >
            Back to Dashboard
          </Button>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-6">
          <Tabs
            activeIndex={['general', 'support', 'users', 'api'].indexOf(activeTab)}
            onTabChange={(index: number) => handleTabChange(['general', 'support', 'users', 'api'][index] as TabType)}
            className="border-b border-[#E0D7FF] dark:border-[#2a2a38]"
          >
            <TabList className="flex overflow-x-auto">
              <Tab className="px-4 py-2 font-medium text-sm whitespace-nowrap cursor-pointer" data-state={activeTab === 'general' ? 'active' : 'inactive'}>
                General Settings
              </Tab>
              <Tab className="px-4 py-2 font-medium text-sm whitespace-nowrap cursor-pointer" data-state={activeTab === 'support' ? 'active' : 'inactive'}>
                Support Settings
              </Tab>
              <Tab className="px-4 py-2 font-medium text-sm whitespace-nowrap cursor-pointer" data-state={activeTab === 'users' ? 'active' : 'inactive'}>
                User Management
              </Tab>
              <Tab className="px-4 py-2 font-medium text-sm whitespace-nowrap cursor-pointer" data-state={activeTab === 'api' ? 'active' : 'inactive'}>
                API Settings
              </Tab>
            </TabList>
            <TabPanel>
              {/* General Settings content will be rendered here */}
              {activeTab === 'general' && organizationData && (
                <GeneralSettingsComponent
                  organizationId={organizationId}
                  organizationData={organizationData}
                  onUpdateSuccess={handleUpdateSuccess}
                />
              )}
            </TabPanel>
            <TabPanel>
              {/* Support Settings content will be rendered here */}
              {activeTab === 'support' && (
                <SupportSettingsComponent
                  organizationId={organizationId}
                />
              )}
            </TabPanel>
            <TabPanel>
              {/* User Management content will be rendered here */}
              {activeTab === 'users' && (
                <UserManagementComponent
                  organizationId={organizationId}
                  organizationData={organizationData}
                />
              )}
            </TabPanel>
            <TabPanel>
              {/* API Settings content will be rendered here */}
              {activeTab === 'api' && (
                <ApiSettingsComponent
                  organizationId={organizationId}
                />
              )}
            </TabPanel>
          </Tabs>
        </div>
      </main>
    </div>
  );
}

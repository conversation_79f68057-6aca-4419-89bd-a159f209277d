'use client';

import {useEffect, useState} from 'react';
import {useParams} from 'next/navigation';
import {Card} from '@/components/ui/Card';
import {getOrganizationInvites} from '@/server/actions/invite-actions';
import InviteList from '@/components/organization/InviteList';
import {getRoles} from '@/utils/role-utils';

export default function MembersPage() {
  const params = useParams();
  const organizationId = params.id as string;

  const [invites, setInvites] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch invites
  const fetchInvites = async () => {
    setIsLoading(true);
    try {
      const response = await getOrganizationInvites(organizationId);
      if (response.success) {
        setInvites(response.data || []);
      } else {
        setError(response.message);
      }
    } catch (error) {
      setError('Failed to fetch invitations');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch invites when component mounts
  useEffect(() => {
    fetchInvites();
  }, [organizationId]);

  // Get roles for the organization
  const roles = getRoles().map(role => ({
    label: role.name,
    value: role.id
  }));

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">Organization Members</h1>

      {error && (
        <div className="mb-6 p-3 bg-red-50 text-red-700 rounded-md border border-red-200">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 gap-6">
        {/* Organization Members List */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Team Members</h2>
          <p className="text-gray-600 mb-6">
            Manage your organization's team members and their roles.
          </p>

          {/* Member List Component would go here */}
          <div className="bg-[#F3F3F3]/50 dark:bg-[#1e1e28]/30 p-4 rounded-md">
            <p className="text-center text-gray-500">
              Member management functionality coming soon.
            </p>
          </div>
        </Card>

        {/* Invitations */}
        <Card className="p-6">
          {isLoading ? (
            <div className="flex justify-center items-center p-6">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#5451B8]"></div>
            </div>
          ) : (
            <InviteList
              organizationId={organizationId}
              invites={invites}
              roles={roles}
              onInviteCreated={fetchInvites}
              onInviteCancelled={fetchInvites}
            />
          )}
        </Card>
      </div>
    </div>
  );
}

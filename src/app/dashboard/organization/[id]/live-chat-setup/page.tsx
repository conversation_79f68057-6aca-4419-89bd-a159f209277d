import { Suspense } from 'react';
import { notFound, redirect } from 'next/navigation';
import { getAuthUser } from '@/utils/auth';
import { getLiveChatConfig } from '@/server/actions/live-chat-actions';
import { LiveChatSetupWizard } from '@/components/dashboard/live-chat/LiveChatSetupWizard';
import { Card } from '@/components/ui/Card';
import { Skeleton } from '@/components/ui/Skeleton';

interface LiveChatSetupPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getLiveChatSetupData(organizationId: string) {
  const result = await getLiveChatConfig(organizationId);

  if (!result.success) {
    throw new Error('error' in result ? result.error : 'Failed to load live chat configuration');
  }

  return 'data' in result ? result.data : null;
}

export default async function LiveChatSetupPage({ params }: LiveChatSetupPageProps) {
  // Verify authentication
  const authResult = await getAuthUser();
  if (!authResult.success) {
    notFound();
  }

  const { id } = await params;

  try {
    const liveChatConfig = await getLiveChatSetupData(id);

    // If live chat is already set up, redirect to the main live chat page
    if (liveChatConfig && liveChatConfig.isSetupComplete) {
      redirect(`/dashboard/organization/${id}/live-chat`);
    }

    return (
      <div className="p-6 md:p-8  space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Live Chat Setup
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Set up live chat to provide real-time customer support on your website
            </p>
          </div>
        </div>

        {/* Setup Wizard */}
        <Suspense fallback={<LiveChatSetupSkeleton />}>
          <LiveChatSetupWizard
            organizationId={id}
            existingConfig={liveChatConfig}
          />
        </Suspense>
      </div>
    );
  } catch (error) {
    console.error('Error loading live chat setup:', error);
    notFound();
  }
}

function LiveChatSetupSkeleton() {
  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="space-y-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </Card>
    </div>
  );
}

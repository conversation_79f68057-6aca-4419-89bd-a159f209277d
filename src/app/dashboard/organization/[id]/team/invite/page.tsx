'use client';

import {useEffect, useMemo, useState} from 'react';
import {use<PERSON><PERSON><PERSON>, useRouter} from 'next/navigation';
import {Button} from '@/components/ui/Button';
import {Card} from '@/components/ui/Card';
import {Input} from '@/components/ui/Input';
import {ProductSelect} from '@/components/ui/ProductSelect';
import {useToast} from '@/components/ui/Toast';
import {Controller, useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {z} from 'zod';
import {getActiveSubscriptionWithDetails} from '@/server/actions/billing';
import {getOrganizationMemberships} from '@/server/actions/user-management';
import {createInvite, createProductInvite} from '@/server/actions/invite-actions';
import { SelectUserPermissionGroup } from '@/components/ui/SelectUserPermissionGroup';
import { logger } from '@/utils/logger';

// Form validation schema
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  role: z.string().min(1, 'Please select a role'),
  inviteType: z.enum(['org', 'product']),
  productCode: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ProductItem {
  productId: string;
  product?: {
    _id: string;
    id: string;
    code: string;
    name: string;
  };
  features?: any[];
}

interface SubscriptionDetails {
  id: string;
  pricingPlan: {
    id: string;
    name: string;
    code: string;
    description: string;
    isPerUser: boolean;
    timeUnit: string;
    duration: number;
    amountUSD: number;
    amountNGN: number;
  };
  metadata?: {
    productItems?: ProductItem[];
  }
}

export default function TeamInvitePage() {
  const params = useParams();
  const router = useRouter();
  const organizationId = params.id as string;
  const { success, error: showError } = useToast();

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [subscription, setSubscription] = useState<SubscriptionDetails | null>(null);
  const [globalUserCount, setGlobalUserCount] = useState(0);

  // Derived values instead of state
  const supportsAllProducts = useMemo(() => {
    // Check if subscription includes all products
    return subscription?.metadata?.productItems?.some(item => item.product?.code === 'all_products') || false;
  }, [subscription]);

  const canInviteGlobalUser = useMemo(() => {
    // If subscription supports all products, global users are allowed
    // Otherwise, only allow 1 global user for single-product subscriptions
    return supportsAllProducts || globalUserCount < 1;
  }, [supportsAllProducts, globalUserCount]);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      role: '',
      inviteType: 'product',
      productCode: '',
    },
  });

  const inviteType = watch('inviteType');

  // Fetch subscription and check if it supports all products
  useEffect(() => {
    const fetchData = async () => {
      setInitialLoading(true);
      try {
        // Get active subscription with details in a single query
        const subscriptionResult = await getActiveSubscriptionWithDetails({
          organizationId,
        });

        if (subscriptionResult.success && 'data' in subscriptionResult && subscriptionResult.data) {
          setSubscription(subscriptionResult.data as SubscriptionDetails);
        }

        // Get organization members to count global users
        const membersResult = await getOrganizationMemberships(organizationId);
        if (membersResult.success && 'data' in membersResult && membersResult.data) {
          // Count users with isOrgWide = true (global access)
          const globalUsers = membersResult.data.filter(user => user.isOrgWide);
          setGlobalUserCount(globalUsers.length);
        }
      } catch (err) {
        logger.error('Error loading subscription data:', err);
        showError('Error', 'Failed to load subscription data');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchData();
  }, [organizationId]);

  const onSubmit = async (data: FormValues) => {
    setLoading(true);
    try {
      let result;

      // Check if user can invite as org-wide based on business rules
      const inviteAsOrgWide = data.inviteType === 'org' && canInviteGlobalUser;

      if (inviteAsOrgWide) {
        // Invite user to the entire organization (isOrgWide will be set to true)
        result = await createInvite({
          organizationId,
          email: data.email,
          role: data.role,
        });
      } else {
        // Invite user to a specific product (isOrgWide will be set to false)
        if (!data.productCode) {
          throw new Error('No product selected for invitation');
        }

        result = await createProductInvite({
          organizationId,
          productCode: data.productCode,
          email: data.email,
          role: data.role
        });
      }

      if (result.success) {
        success(
          'Success',
          `Invitation sent to ${data.email}${inviteAsOrgWide ? ' with organization-wide access' : ' with product-specific access'}`
        );
        // Navigate back to team management page
        router.push(`/dashboard/organization/${organizationId}/team`);
      } else {
        showError('Error', result.message || 'Failed to send invitation');
      }
    } catch (err: any) {
      logger.error('Error inviting user:', err);
      showError('Error', err.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-center items-center h-64">
          <div className="w-8 h-8 border-4 border-t-blue-500 border-blue-200 rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">Invite Team Member</h1>
        <p className="text-gray-500">
          Add a new member to your organization or to a specific product.
        </p>
      </div>

      <Card className="p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Input */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium mb-1">
              Email Address
            </label>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  id="email"
                  placeholder="Enter email address"
                  {...field}
                  className={errors.email ? 'border-red-500' : ''}
                />
              )}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* Invite Type Selection - Only show if subscription supports all products or has 1 or fewer global users */}
          {(supportsAllProducts || globalUserCount <= 1) && (
            <div>
              <label className="block text-sm font-medium mb-1">
                Invitation Type
              </label>
              <div className="flex space-x-4">
                <Controller
                  name="inviteType"
                  control={control}
                  render={({ field }) => (
                    <>
                      <label className={`flex items-center ${!canInviteGlobalUser ? 'opacity-50 cursor-not-allowed' : ''}`}>
                        <input
                          type="radio"
                          className="mr-2"
                          value="org"
                          checked={field.value === 'org'}
                          onChange={() => canInviteGlobalUser && field.onChange('org')}
                          disabled={!canInviteGlobalUser}
                        />
                        <span>Organization-wide access</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          className="mr-2"
                          value="product"
                          checked={field.value === 'product'}
                          onChange={() => field.onChange('product')}
                        />
                        <span>Product-specific access</span>
                      </label>
                    </>
                  )}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {supportsAllProducts ? (
                  <p>Organization-wide access gives the user access to all products in your subscription.</p>
                ) : globalUserCount < 1 ? (
                  <p>Your plan allows for 1 organization-wide user who can access all products. You currently have {globalUserCount} organization-wide user{globalUserCount !== 1 ? 's' : ''}.</p>
                ) : (
                  <p>Your plan only allows for 1 organization-wide user, and you&#39;ve already reached this limit. New users must be given product-specific access.</p>
                )}
              </div>
            </div>
          )}

          {/* Product Selection - Only show if in product mode */}
          {(inviteType === 'product') && (
            <div>
              <label htmlFor="productCode" className="block text-sm font-medium mb-1">
                Product
              </label>
              <Controller
                name="productCode"
                control={control}
                render={({ field }) => (
                  <ProductSelect
                    organizationId={organizationId}
                    value={field.value || ''}
                    onChange={field.onChange}
                    error={errors.productCode?.message}
                    disabled={loading}
                  />
                )}
              />
            </div>
          )}

          {/* Role Selection */}
          <div>
            <label htmlFor="role" className="block text-sm font-medium mb-1">
              Role
            </label>
            <Controller
              name="role"
              control={control}
              render={({ field }) => (
                <SelectUserPermissionGroup
                  organizationId={organizationId}
                  value={field.value}
                  onChange={field.onChange}
                  error={errors.role?.message}
                  disabled={loading}
                />
              )}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.push(`/dashboard/organization/${organizationId}/team`)}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              loading={loading}
            >
              Send Invitation
            </Button>
          </div>
        </form>
      </Card>
    </div>
  );
}

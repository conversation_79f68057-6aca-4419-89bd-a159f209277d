'use client';

import {useEffect, useState} from 'react';
import {useParams, useRouter} from 'next/navigation';
import Image from 'next/image';
import {getOrganizationById, getUserData, updateOrganizationSubscriptionPlan} from '@/server/actions/user-actions';
import {UserOrgRole} from '@/constants/role-constants';
import {Card} from '@/components/ui/Card';
import {Button} from '@/components/ui/Button';
import {Badge} from '@/components/ui/Badge';
import {ErrorView} from '@/components/ui/ErrorView';
import {LoadingView} from '@/components/ui/LoadingView';
import { logger } from '@/utils/logger';

type OrganizationData = Awaited<ReturnType<typeof getOrganizationById>>

interface UserData {
  id: string;
  name: string;
  email: string;
}

// This page displays detailed information about an organization
export default function OrganizationDashboardPage() {
  const router = useRouter();
  const params = useParams();
  const organizationId = params.id as string;
  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [organizationData, setOrganizationData] = useState<OrganizationData['data'] | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Check authentication status
        const userResult = await getUserData();
        if (!userResult || !userResult.success) {
          router.push('/auth?mode=login');
          return;
        }

        setUserData(userResult.success && 'data' in userResult ? userResult.data : null);

        // Update the organization's subscription plan based on active subscriptions
        await updateOrganizationSubscriptionPlan(organizationId);

        // Fetch organization data
        const orgResult = await getOrganizationById(organizationId);
        if (!orgResult.success) {
          setError('error' in orgResult ? orgResult.error : 'Failed to fetch organization data');
          setOrganizationData(null);
        } else {
          setOrganizationData(orgResult.data);
        }
      } catch (error: any) {
        logger.error('Error loading organization dashboard:', error);
        setError(error.message || 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [organizationId, router]);

  const isAdmin = organizationData?.userRole === UserOrgRole.ADMIN;

  if (isLoading) {
    return <LoadingView message="Loading organization dashboard..." />;
  }

  if (error) {
    return (
      <ErrorView
        error={error}
        title="Organization Error"
        buttonText="Back to Dashboard"
        onButtonClick={() => router.push('/dashboard')}
      />
    );
  }

  return (
    <div className="min-h-screen bg-[#F3F3F3] dark:bg-[#0a0a14] flex flex-col">
      <header className="bg-white dark:bg-[#1e1e28] shadow">
        <div className="mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center">
            <button
              className="mr-2 sm:mr-4 text-[#5E5E5E] dark:text-[#C6C6C6] hover:text-[#6964D3] dark:hover:text-white transition-colors"
              onClick={() => router.push('/dashboard')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                   stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
            </button>
            <Image
              src="/assets/logos/purple-cloud-yellow-dots.svg"
              alt="New Instance Logo"
              width={40}
              height={40}
              className="mr-3 hidden sm:block"
            />
            <span className="text-lg sm:text-xl font-bold font-[family-name:var(--font-jakarta)]">New Instance</span>
          </div>

          <div className="flex items-center gap-4">
            <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6] truncate max-w-[120px] sm:max-w-none">
              {userData?.name || 'User'}
            </span>
          </div>
        </div>
      </header>

      <main className="flex-1 w-full mx-auto px-4 py-4 sm:py-8">
        <div className="mb-4 sm:mb-8 flex flex-col sm:flex-row justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-xl sm:text-2xl font-bold mb-2">{organizationData?.name || 'Organization'}</h1>
            <div className="flex items-center">
              <p className="text-[#5E5E5E] dark:text-[#C6C6C6] mr-3 text-sm sm:text-base">
                {organizationData?.industry}
              </p>
              <Badge variant={organizationData?.isActive ? "success" : "danger"}>
                {organizationData?.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>
          {isAdmin && (
            <Button
              className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] w-full sm:w-auto"
              onClick={() => router.push(`/dashboard/organization/${organizationId}/settings`)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
              Manage Organization
            </Button>
          )}
        </div>

        {/* Organization Overview */}
        <Card className="bg-white dark:bg-[#1e1e28] shadow rounded-xl p-4 sm:p-6 mb-4 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-semibold mb-4">Organization Overview</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">Details</h3>
              <div className="space-y-2 text-sm sm:text-base">
                <div>
                  <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Description:</span>
                  <p className="break-words">{organizationData?.description || 'No description provided'}</p>
                </div>
                {organizationData?.email && (
                  <div>
                    <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Email:</span>
                    <p className="break-words">{organizationData.email}</p>
                  </div>
                )}
                {organizationData?.domain && (
                  <div>
                    <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Domain:</span>
                    <p className="break-words">{organizationData.domain}</p>
                  </div>
                )}
                <div>
                  <span className="text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">Created:</span>
                  <p>{organizationData?.createdAt ? new Date(organizationData.createdAt).toLocaleDateString() : 'Unknown'}</p>
                </div>
              </div>
            </div>

            <div className="mt-4 md:mt-0">
              <h3 className="font-semibold mb-2">Subscription</h3>
              <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-sm sm:text-base">Current Plan:</span>
                  <Badge className="bg-[#6964D3]">{organizationData?.subscriptionPlan || 'Free'}</Badge>
                </div>
                <p className="text-xs sm:text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
                  {organizationData?.subscriptionPlan === 'Free'
                    ? 'You have access to all services with the Free plan, but limited to only one team member per organization.'
                    : `You have access to all features in the ${organizationData?.subscriptionPlan} plan with multiple team members.`}
                </p>
                {organizationData?.subscriptionPlan === 'Free' && (
                  <Button
                    className="w-full bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-sm sm:text-base py-2"
                    onClick={() => router.push(`/dashboard/organization/${organizationId}/billing/upgrade`)}
                  >
                    Upgrade to Add Team Members
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Service Modules */}
        <Card className="bg-white dark:bg-[#1e1e28] shadow rounded-xl p-4 sm:p-6 mb-4 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-semibold mb-3">Available Services</h2>
          <p className="text-xs sm:text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-4">
            {organizationData?.subscriptionPlan === 'Free'
              ? 'All services are available on your Free plan, with a limit of one team member per organization.'
              : 'All services are available on your subscription plan with multiple team members.'}
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mt-4 sm:mt-6">
            <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 sm:p-5 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-3">
                <div
                  className="w-8 h-8 sm:w-10 sm:h-10 bg-[#E0D7FF] dark:bg-[#424098] rounded-lg flex items-center justify-center text-[#6964D3] dark:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                       stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                  </svg>
                </div>
                <h3 className="ml-3 font-semibold text-sm sm:text-base">Customer Support</h3>
              </div>
              <p className="text-xs sm:text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-3">
                Access real-time chat with end users and manage support tickets.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs sm:text-sm"
                onClick={() => router.push(`/dashboard/organization/${organizationId}/support`)}
              >
                Open Dashboard
              </Button>
            </div>

            <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 sm:p-5 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-3">
                <div
                  className="w-8 h-8 sm:w-10 sm:h-10 bg-[#F0DAB8] dark:bg-[#BE9544] rounded-lg flex items-center justify-center text-[#BE9544] dark:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                       stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path
                      d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                    <line x1="12" y1="9" x2="12" y2="13"></line>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                  </svg>
                </div>
                <h3 className="ml-3 font-semibold text-sm sm:text-base">Error Logging</h3>
              </div>
              <p className="text-xs sm:text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-3">
                Track and analyze errors across your applications with advanced analytics.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs sm:text-sm"
                onClick={() => router.push(`/dashboard/organization/${organizationId}/errors`)}
              >
                View Errors
              </Button>
            </div>

            <div className="bg-[#F3F3F3] dark:bg-[#2a2a38] p-4 sm:p-5 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-3">
                <div
                  className="w-8 h-8 sm:w-10 sm:h-10 bg-[#E0D7FF] dark:bg-[#424098] rounded-lg flex items-center justify-center text-[#6964D3] dark:text-white">
                  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none"
                       stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
                <h3 className="ml-3 font-semibold text-sm sm:text-base">Team Management</h3>
              </div>
              <p className="text-xs sm:text-sm text-[#5E5E5E] dark:text-[#C6C6C6] mb-3">
                Manage your team members and their access permissions.
              </p>
              <Button
                variant="outline"
                size="sm"
                className="w-full text-xs sm:text-sm"
                onClick={() => router.push(`/dashboard/organization/${organizationId}/team`)}
              >
                Manage Team
              </Button>
            </div>
          </div>
        </Card>

        {/* Team Members */}
        <Card className="bg-white dark:bg-[#1e1e28] shadow rounded-xl p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-3 mb-4">
            <h2 className="text-lg sm:text-xl font-semibold">Team Members</h2>
            {isAdmin && organizationData?.subscriptionPlan !== 'Free' && (
              <Button
                className="bg-gradient-to-r from-[#8178E8] to-[#6964D3] text-xs sm:text-sm w-full sm:w-auto"
                onClick={() => router.push(`/dashboard/organization/${organizationId}/settings?tab=users`)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                Invite Member
              </Button>
            )}
            {organizationData?.subscriptionPlan === 'Free' && (
              <Button
                className="bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300 hover:opacity-80 text-xs sm:text-sm w-full sm:w-auto"
                onClick={() => router.push(`/dashboard/organization/${organizationId}/billing/upgrade`)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                  <polygon points="16 3 21 8 8 21 3 21 3 16 16 3"></polygon>
                </svg>
                Upgrade to Add Team Members
              </Button>
            )}
          </div>

          {organizationData?.subscriptionPlan === 'Free' && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
              <p className="text-xs sm:text-sm text-blue-600 dark:text-blue-300 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2 flex-shrink-0">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
                <span>Free accounts are limited to one team member per organization. Upgrade your plan to invite additional members.</span>
              </p>
            </div>
          )}

          <div className="overflow-x-auto -mx-4 sm:mx-0">
            <div className="inline-block min-w-full align-middle">
              <div className="overflow-hidden sm:rounded-lg">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Name
                      </th>
                      <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Email
                      </th>
                      <th scope="col" className="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Role
                      </th>
                      {isAdmin && (
                        <th scope="col" className="px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                          Actions
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-[#1e1e28] divide-y divide-gray-200 dark:divide-gray-700">
                    {organizationData?.members.map((member) => (
                      <tr key={member.userId}>
                        <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                          <div className="text-xs sm:text-sm font-medium">{member.name}</div>
                        </td>
                        <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap truncate max-w-[120px] sm:max-w-none">
                          <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">{member.email}</div>
                        </td>
                        <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                          <Badge className={
                            member.role === UserOrgRole.ADMIN
                              ? "bg-purple-600 text-[10px] sm:text-xs"
                              : member.role === UserOrgRole.MEMBER
                              ? "bg-blue-500 text-[10px] sm:text-xs"
                              : "bg-gray-500 text-[10px] sm:text-xs"
                          }>
                            {member.role}
                          </Badge>
                        </td>
                        {isAdmin && (
                          <td className="px-4 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-[10px] sm:text-xs px-2 py-1 sm:px-3 sm:py-1"
                              onClick={() => router.push(`/dashboard/organization/${organizationId}/team/edit/${member.userId}`)}
                            >
                              Edit
                            </Button>
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {(!organizationData?.members || organizationData.members.length === 0) && (
            <div className="text-center py-6">
              <p className="text-xs sm:text-sm text-[#5E5E5E] dark:text-[#C6C6C6]">No team members found.</p>
            </div>
          )}
        </Card>
      </main>
    </div>
  );
}

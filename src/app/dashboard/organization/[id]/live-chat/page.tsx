import { Suspense } from 'react';
import { notFound, redirect } from 'next/navigation';
import { getLiveChatDashboardData } from '@/server/actions/live-chat-actions';
import { LiveChatDashboard } from '@/components/dashboard/live-chat/LiveChatDashboard';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Skeleton } from '@/components/ui/Skeleton';
import Link from 'next/link';

interface LiveChatPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function LiveChatPage({ params }: LiveChatPageProps) {
  const { id } = await params;

  // Get live chat dashboard data using server action
  const result = await getLiveChatDashboardData(id);

  if (!result.success) {
    // Handle error cases
    if ('error' in result) {
      if (result.error === 'Authentication required') {
        notFound();
      }
      if (result.error === 'Organization not found') {
        notFound();
      }
    }
    // For other errors, we could show an error page or redirect
    notFound();
  }

  // At this point, TypeScript knows result.success is true and result.data exists
  if (!('data' in result) || !result.data) {
    notFound();
  }

  const data = result.data;

  // If live chat is not configured, show setup prompt
  if (!data.liveChatConfig) {
    return (
      <div className=" p-6 md:p-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Live Chat
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Provide real-time customer support with live chat
            </p>
          </div>
        </div>

        <Card className="max-w-2xl mx-auto p-8 text-center">
          <div className="space-y-4">
            <div className="w-16 h-16 mx-auto bg-[#8178E8]/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-[#8178E8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Set Up Live Chat
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Live chat is not configured for your organization. Set it up to start providing real-time customer support.
            </p>
            <div className="pt-4">
              <Link href={`/dashboard/organization/${id}/live-chat-setup`}>
                <Button>
                  Set Up Live Chat
                </Button>
              </Link>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // If live chat needs setup completion, redirect to setup
  if ('needsSetup' in data && data.needsSetup) {
    redirect(`/dashboard/organization/${id}/live-chat-setup`);
  }

  // At this point, we know we have the full dashboard data
  const dashboardData = data as {
    organization: { id: string; name: string };
    liveChatConfig: any;
    conversations: any[];
    stats: { active: number; waiting: number; resolved: number; closed: number };
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Live Chat
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Manage customer conversations and support requests
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <div className="w-6 h-6 bg-green-600 rounded"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Active Chats
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.stats.active}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <div className="w-6 h-6 bg-yellow-600 rounded"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Waiting
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.stats.waiting}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <div className="w-6 h-6 bg-blue-600 rounded"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Resolved
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.stats.resolved}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center">
            <div className="p-2 bg-gray-100 dark:bg-gray-900/20 rounded-lg">
              <div className="w-6 h-6 bg-gray-600 rounded"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Total Closed
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardData.stats.closed}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Live Chat Dashboard */}
      <Suspense fallback={<LiveChatSkeleton />}>
        <LiveChatDashboard
          organizationId={id}
          conversations={dashboardData.conversations}
          organization={dashboardData.organization}
          liveChatConfig={dashboardData.liveChatConfig}
        />
      </Suspense>
    </div>
  );
}

function LiveChatSkeleton() {
  return (
    <div className="p-6 md:p-8 space-y-4">
      <Skeleton className="h-8 w-48" />
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <Skeleton className="h-96 w-full" />
        </div>
        <div className="lg:col-span-2">
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    </div>
  );
}

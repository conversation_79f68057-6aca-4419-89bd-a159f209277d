import { 
  ErrorSeverity, 
  ErrorStatus, 
  <PERSON>rror<PERSON>ate<PERSON>y,
  BugWatchError,
  BugWatchErrorGroup 
} from '@/types/bugwatch';

// Severity color mapping
export const getSeverityColor = (severity: ErrorSeverity): string => {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 'bg-red-500 text-white';
    case ErrorSeverity.HIGH:
      return 'bg-orange-500 text-white';
    case ErrorSeverity.MEDIUM:
      return 'bg-yellow-500 text-white';
    case ErrorSeverity.LOW:
      return 'bg-blue-500 text-white';
    default:
      return 'bg-gray-500 text-white';
  }
};

// Severity background color for cards/badges
export const getSeverityBgColor = (severity: ErrorSeverity): string => {
  switch (severity) {
    case ErrorSeverity.CRITICAL:
      return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800';
    case ErrorSeverity.HIGH:
      return 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800';
    case ErrorSeverity.MEDIUM:
      return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800';
    case ErrorSeverity.LOW:
      return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800';
    default:
      return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-800';
  }
};

// Status color mapping
export const getStatusColor = (status: ErrorStatus): string => {
  switch (status) {
    case ErrorStatus.OPEN:
      return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
    case ErrorStatus.INVESTIGATING:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
    case ErrorStatus.IN_PROGRESS:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
    case ErrorStatus.RESOLVED:
      return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
    case ErrorStatus.CLOSED:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    case ErrorStatus.IGNORED:
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
  }
};

// Category icon mapping
export const getCategoryIcon = (category: ErrorCategory): string => {
  switch (category) {
    case ErrorCategory.RUNTIME:
      return 'pi pi-exclamation-triangle';
    case ErrorCategory.SYNTAX:
      return 'pi pi-code';
    case ErrorCategory.NETWORK:
      return 'pi pi-wifi';
    case ErrorCategory.DATABASE:
      return 'pi pi-database';
    case ErrorCategory.AUTHENTICATION:
      return 'pi pi-lock';
    case ErrorCategory.VALIDATION:
      return 'pi pi-check-circle';
    case ErrorCategory.PERFORMANCE:
      return 'pi pi-clock';
    case ErrorCategory.SECURITY:
      return 'pi pi-shield';
    case ErrorCategory.INTEGRATION:
      return 'pi pi-link';
    case ErrorCategory.UI_UX:
      return 'pi pi-eye';
    default:
      return 'pi pi-question-circle';
  }
};

// Format relative time
export const formatRelativeTime = (timestamp: string): string => {
  const now = new Date();
  const time = new Date(timestamp);
  const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours > 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days > 1 ? 's' : ''} ago`;
  } else {
    return time.toLocaleDateString();
  }
};

// Format error count
export const formatErrorCount = (count: number): string => {
  if (count < 1000) {
    return count.toString();
  } else if (count < 1000000) {
    return `${(count / 1000).toFixed(1)}K`;
  } else {
    return `${(count / 1000000).toFixed(1)}M`;
  }
};

// Calculate error rate
export const calculateErrorRate = (errors: number, total: number): number => {
  if (total === 0) return 0;
  return Number(((errors / total) * 100).toFixed(2));
};

// Group errors by time period
export const groupErrorsByTime = (
  errors: BugWatchError[], 
  period: 'hour' | 'day' | 'week' = 'day'
): Record<string, number> => {
  const grouped: Record<string, number> = {};
  
  errors.forEach(error => {
    const date = new Date(error.createdAt);
    let key: string;
    
    switch (period) {
      case 'hour':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      default: // day
        key = date.toISOString().split('T')[0];
    }
    
    grouped[key] = (grouped[key] || 0) + 1;
  });
  
  return grouped;
};

// Filter errors by criteria
export const filterErrors = (
  errors: BugWatchError[],
  filters: {
    severity?: ErrorSeverity[];
    status?: ErrorStatus[];
    category?: ErrorCategory[];
    search?: string;
    dateRange?: {
      start: string;
      end: string;
    };
  }
): BugWatchError[] => {
  return errors.filter(error => {
    // Severity filter
    if (filters.severity && filters.severity.length > 0) {
      if (!filters.severity.includes(error.severity)) {
        return false;
      }
    }
    
    // Status filter
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(error.status)) {
        return false;
      }
    }
    
    // Category filter
    if (filters.category && filters.category.length > 0) {
      if (!filters.category.includes(error.category)) {
        return false;
      }
    }
    
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      const searchableText = `${error.message} ${error.type} ${error.tags.join(' ')}`.toLowerCase();
      if (!searchableText.includes(searchLower)) {
        return false;
      }
    }
    
    // Date range filter
    if (filters.dateRange) {
      const errorDate = new Date(error.createdAt);
      const startDate = new Date(filters.dateRange.start);
      const endDate = new Date(filters.dateRange.end);
      
      if (errorDate < startDate || errorDate > endDate) {
        return false;
      }
    }
    
    return true;
  });
};

// Sort errors
export const sortErrors = (
  errors: BugWatchError[],
  sortBy: 'createdAt' | 'severity' | 'occurrenceCount' | 'affectedUsers',
  sortOrder: 'asc' | 'desc' = 'desc'
): BugWatchError[] => {
  return [...errors].sort((a, b) => {
    let comparison = 0;
    
    switch (sortBy) {
      case 'createdAt':
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      case 'severity':
        const severityOrder = {
          [ErrorSeverity.CRITICAL]: 4,
          [ErrorSeverity.HIGH]: 3,
          [ErrorSeverity.MEDIUM]: 2,
          [ErrorSeverity.LOW]: 1
        };
        comparison = severityOrder[a.severity] - severityOrder[b.severity];
        break;
      case 'occurrenceCount':
        comparison = a.occurrenceCount - b.occurrenceCount;
        break;
      case 'affectedUsers':
        comparison = a.affectedUsers - b.affectedUsers;
        break;
    }
    
    return sortOrder === 'asc' ? comparison : -comparison;
  });
};

// Generate error fingerprint (simplified version)
export const generateErrorFingerprint = (error: Partial<BugWatchError>): string => {
  const components = [
    error.type || '',
    error.message || '',
    error.stackTrace?.[0]?.fileName || '',
    error.stackTrace?.[0]?.lineNumber || ''
  ];
  
  return btoa(components.join('|')).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
};

// Check if error is recent (within last 24 hours)
export const isRecentError = (timestamp: string): boolean => {
  const now = new Date();
  const errorTime = new Date(timestamp);
  const diffInHours = (now.getTime() - errorTime.getTime()) / (1000 * 60 * 60);
  return diffInHours <= 24;
};

// Get severity priority (higher number = higher priority)
export const getSeverityPriority = (severity: ErrorSeverity): number => {
  switch (severity) {
    case ErrorSeverity.CRITICAL: return 4;
    case ErrorSeverity.HIGH: return 3;
    case ErrorSeverity.MEDIUM: return 2;
    case ErrorSeverity.LOW: return 1;
    default: return 0;
  }
};

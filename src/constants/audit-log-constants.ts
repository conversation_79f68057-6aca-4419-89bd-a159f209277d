export enum AuditLogAction {
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  USER_ADDED_TO_ORG = 'user_added_to_org',
  USER_REMOVED_FROM_ORG = 'user_removed_from_org',
  USER_ADDED_TO_PRODUCT = 'user_added_to_product',
  USER_REMOVED_FROM_PRODUCT = 'user_removed_from_product',
  
  INVITE_CREATED = 'invite_created',
  INVITE_ACCEPTED = 'invite_accepted',
  INVITE_DECLINED = 'invite_declined',
  INVITE_RESENT = 'invite_resent',
  PRODUCT_INVITE_CREATED = 'product_invite_created',
  PRODUCT_INVITE_ACCEPTED = 'product_invite_accepted',
  PRODUCT_INVITE_DECLINED = 'product_invite_declined',
  PRODUCT_INVITE_RESENT = 'product_invite_resent',
  INVITE_CANCELLED = 'invite_cancelled',

} 
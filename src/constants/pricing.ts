// pricing.ts - Define pricing-related constants

export enum Currency {
  NGN = "NGN",
  USD = "USD"
}

export enum TimeUnit {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year'
}

export enum PlanStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived'
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELED = 'canceled',
  EXPIRED = 'expired',
  PENDING_PAYMENT='pending_payment',
  TRIAL = 'trial',
  UPGRADED = 'upgraded',
  PAST_DUE = 'past_due',
  PENDING_RENEWAL = 'pending_renewal',
  SCHEDULED_FOR_REPLACEMENT = 'scheduled_for_replacement'
}

export enum PaymentStatus {
  PAID = 'paid',
  PENDING = 'pending',
  FAILED = 'failed',
  REFUNDED = 'refunded'
}

export enum SubscriptionInterval {
  MONTHLY = "monthly",
  YEARLY = "yearly"
}

export enum SubscriptionTier {
  FREE = "free",
  STARTER = "starter",
  PROFESSIONAL = "professional",
  ENTERPRISE = "enterprise"
}

export const DEFAULT_CURRENCY = Currency.NGN;

export const TIER_PRICES = {
  [SubscriptionTier.FREE]: {
    [SubscriptionInterval.MONTHLY]: 0,
    [SubscriptionInterval.YEARLY]: 0
  },
  [SubscriptionTier.STARTER]: {
    [SubscriptionInterval.MONTHLY]: 5000,
    [SubscriptionInterval.YEARLY]: 50000
  },
  [SubscriptionTier.PROFESSIONAL]: {
    [SubscriptionInterval.MONTHLY]: 15000,
    [SubscriptionInterval.YEARLY]: 150000
  },
  [SubscriptionTier.ENTERPRISE]: {
    [SubscriptionInterval.MONTHLY]: 50000,
    [SubscriptionInterval.YEARLY]: 500000
  }
}

export const CURRENCY_SYMBOLS = {
  [Currency.NGN]: "₦",
  [Currency.USD]: "$"
}

// Time unit display options for dropdown menus
export const timeUnitOptions = [
  { label: 'Day', value: TimeUnit.DAY },
  { label: 'Week', value: TimeUnit.WEEK },
  { label: 'Month', value: TimeUnit.MONTH },
  { label: 'Year', value: TimeUnit.YEAR },
];

// Currency options for dropdown menus
export const currencyOptions = [
  { label: 'USD ($)', value: Currency.USD },
  { label: 'NGN (₦)', value: Currency.NGN },
];

// transactions.ts - Define transaction-related constants

export enum TransactionType {
  SUBSCRIPTION = 'subscription',
  ADDON = 'addon',
  CREDIT = 'credit',
  REFUND = 'refund',
  OVERAGE = 'overage',
  SUBSCRIPTION_UPDATE = 'subscription_update'
}

export enum TransactionStatus {
  PENDING = 'pending',
  SUCCESSFUL = 'successful',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  CANCELLED = 'cancelled'
}

export enum PaymentProvider {
  PAYSTACK = 'paystack',
  NETAPPSPAY = 'netappspay',
  STRIPE = 'stripe',
  MANUAL = 'manual'
}

export enum PaymentMethod {
  CARD = 'card',
  BANK = 'bank',
  USSD = 'ussd',
  QR = 'qr',
  TRANSFER = 'transfer',
  MOBILE_MONEY = 'mobile_money',
  MANUAL = 'manual'
}

// Options for payment providers (for dropdowns)
export const PAYMENT_PROVIDER_OPTIONS = [
  { label: 'Paystack', value: PaymentProvider.PAYSTACK },
  { label: 'NetAppsPay', value: PaymentProvider.NETAPPSPAY },
  { label: 'Manual', value: PaymentProvider.MANUAL }
];

// Options for payment methods (for dropdowns)
export const PAYMENT_METHOD_OPTIONS = [
  { label: 'Card', value: PaymentMethod.CARD },
  { label: 'Bank Transfer', value: PaymentMethod.TRANSFER },
  { label: 'USSD', value: PaymentMethod.USSD },
  { label: 'QR Code', value: PaymentMethod.QR },
  { label: 'Mobile Money', value: PaymentMethod.MOBILE_MONEY },
  { label: 'Manual', value: PaymentMethod.MANUAL }
];

// Transaction status options (for dropdowns)
export const TRANSACTION_STATUS_OPTIONS = [
  { label: 'Pending', value: TransactionStatus.PENDING },
  { label: 'Successful', value: TransactionStatus.SUCCESSFUL },
  { label: 'Failed', value: TransactionStatus.FAILED },
  { label: 'Refunded', value: TransactionStatus.REFUNDED },
  { label: 'Cancelled', value: TransactionStatus.CANCELLED }
];

// Payment status options (for dropdowns)
export const PAYMENT_STATUS_OPTIONS = [
  { label: 'Pending', value: PaymentStatus.PENDING },
  { label: 'Paid', value: PaymentStatus.PAID },
  { label: 'Failed', value: PaymentStatus.FAILED },
  { label: 'Refunded', value: PaymentStatus.REFUNDED },
  { label: 'Cancelled', value: PaymentStatus.CANCELLED }
]; 
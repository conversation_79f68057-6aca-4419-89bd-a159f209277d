/**
 * Admin role constants to ensure consistency across the application
 * This is the single source of truth for admin roles
 */

// Admin roles for the platform
export enum AdminRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  STAFF = 'staff',
}

// User roles within an organization
export enum UserOrgRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  MEMBER = 'member',
  SUPPORT = 'support',
  GUEST = 'guest',
}

// Service access levels
export enum ServiceAccessLevel {
  FULL = 'full',
  STANDARD = 'standard',
  BASIC = 'basic',
  NONE = 'none',
}

// Default permission mappings
export const DefaultRolePermissions = {
  [UserOrgRole.ADMIN]: {
    canManageUsers: true,
    canManageBilling: true,
    canAccessAllServices: true,
    canConfigureServices: true,
    canViewAnalytics: true,
    canManageIntegrations: true,
  },
  [UserOrgRole.MANAGER]: {
    canManageUsers: true,
    canManageBilling: false,
    canAccessAllServices: true,
    canConfigureServices: true,
    canViewAnalytics: true,
    canManageIntegrations: false,
  },
  [UserOrgRole.MEMBER]: {
    canManageUsers: false,
    canManageBilling: false,
    canAccessAllServices: false,
    canConfigureServices: false,
    canViewAnalytics: true,
    canManageIntegrations: false,
  },
  [UserOrgRole.SUPPORT]: {
    canManageUsers: false,
    canManageBilling: false,
    canAccessAllServices: false,
    canConfigureServices: false,
    canViewAnalytics: false,
    canManageIntegrations: false,
  },
};

// Type based on the enum for backward compatibility
export type AdminRoleType = typeof AdminRole[keyof typeof AdminRole];

// Role options for dropdowns
export const ADMIN_ROLE_OPTIONS = [
  { label: 'Super Admin', value: AdminRole.SUPER_ADMIN },
  { label: 'Staff', value: AdminRole.STAFF },
];

// Helper functions for case-insensitive comparisons
export function isSuperAdmin(role: string): boolean {
  return role.toLowerCase?.() === AdminRole.SUPER_ADMIN.toLowerCase?.();
}

export function isStaff(role: string): boolean {
  return role.toLowerCase?.() === AdminRole.STAFF.toLowerCase?.();
}

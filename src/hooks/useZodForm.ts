import { useState } from 'react';
import { useForm, UseFormProps, FieldValues, SubmitHandler, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FormEventHandler } from 'react';
import { logger } from '@/utils/logger';

interface UseZodFormProps<TFormValues extends FieldValues, Schema extends z.ZodType<any, any, any>> 
  extends UseFormProps<TFormValues> {
  schema: Schema;
  onSubmit: SubmitHandler<z.infer<Schema>>;
}

interface UseZodFormReturn<TFormValues extends FieldValues> 
  extends Omit<UseFormReturn<TFormValues>, 'handleSubmit'> {
  handleSubmit: FormEventHandler<HTMLFormElement>;
  isSubmitting: boolean;
  serverError: string | null;
  setServerError: (error: string | null) => void;
}

/**
 * Custom hook for handling forms with Zod schema validation and react-hook-form
 */
export function useZodForm<TFormValues extends FieldValues, <PERSON>hema extends z.ZodType<any, any, any>>({
  schema,
  onSubmit,
  ...formProps
}: UseZodFormProps<TFormValues, Schema>): UseZodFormReturn<TFormValues> {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serverError, setServerError] = useState<string | null>(null);

  const form = useForm<TFormValues>({
    ...formProps,
    resolver: zodResolver(schema),
  });

  // Create a handleSubmit that's compatible with form onSubmit prop
  const handleSubmit: FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault();
    
    form.handleSubmit(async (data) => {
      setIsSubmitting(true);
      setServerError(null);
      
      try {
        await onSubmit(data as z.infer<Schema>);
      } catch (error) {
        if (error instanceof Error) {
          setServerError(error.message);
        } else {
          setServerError('An unexpected error occurred');
        }
        logger.error('Form submission error:', error);
      } finally {
        setIsSubmitting(false);
      }
    })(e);
  };

  return {
    ...form,
    handleSubmit,
    isSubmitting,
    serverError,
    setServerError,
  };
}

/**
 * Helper type for inferring type from a zod schema
 */
export type InferredZodType<T extends z.ZodType<any, any, any>> = z.infer<T>; 
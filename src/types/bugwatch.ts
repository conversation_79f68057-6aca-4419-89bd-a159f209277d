// Bug Watch TypeScript Interfaces and Types

export enum ErrorSeverity {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low'
}

export enum ErrorStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  IN_PROGRESS = 'in_progress',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
  IGNORED = 'ignored'
}

export enum ErrorCategory {
  RUNTIME = 'runtime',
  SYNTAX = 'syntax',
  NETWORK = 'network',
  DATABASE = 'database',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  INTEGRATION = 'integration',
  UI_UX = 'ui_ux',
  OTHER = 'other'
}

export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TESTING = 'testing'
}

export enum ProgrammingLanguage {
  JAVASCRIPT = 'javascript',
  TYPESCRIPT = 'typescript',
  PYTHON = 'python',
  JAVA = 'java',
  CSHARP = 'csharp',
  PHP = 'php',
  RUBY = 'ruby',
  GO = 'go',
  RUST = 'rust',
  SWIFT = 'swift',
  KOTLIN = 'kotlin',
  OTHER = 'other'
}

export enum FrameworkType {
  REACT = 'react',
  NEXTJS = 'nextjs',
  VUE = 'vue',
  ANGULAR = 'angular',
  SVELTE = 'svelte',
  EXPRESS = 'express',
  FASTAPI = 'fastapi',
  DJANGO = 'django',
  FLASK = 'flask',
  SPRING = 'spring',
  LARAVEL = 'laravel',
  RAILS = 'rails',
  OTHER = 'other'
}

export interface UserContextType {
  userId?: string;
  userAgent: string;
  ipAddress: string;
  sessionId?: string;
  userEmail?: string;
  userName?: string;
  customData?: Record<string, any>;
}

export interface EnvironmentDataType {
  environment: Environment;
  version: string;
  buildNumber?: string;
  commitHash?: string;
  branch?: string;
  deploymentId?: string;
  serverInfo?: {
    hostname: string;
    platform: string;
    nodeVersion?: string;
    memory?: number;
    cpu?: number;
  };
}

export interface StackTraceFrame {
  fileName: string;
  functionName?: string;
  lineNumber?: number;
  columnNumber?: number;
  source?: string;
  context?: string[];
}

export interface BugWatchError {
  id: string;
  projectId: string;
  organizationId: string;
  appId: string;
  
  // Error details
  message: string;
  type: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  status: ErrorStatus;
  
  // Stack trace and context
  stackTrace: StackTraceFrame[];
  errorHash: string; // For grouping similar errors
  fingerprint: string;
  
  // User and environment context
  userContext: UserContextType;
  environmentData: EnvironmentDataType;
  
  // Request context
  requestUrl?: string;
  requestMethod?: string;
  requestHeaders?: Record<string, string>;
  requestBody?: any;
  responseStatus?: number;
  
  // Occurrence data
  firstOccurrence: string;
  lastOccurrence: string;
  occurrenceCount: number;
  affectedUsers: number;
  
  // Assignment and resolution
  assignedTo?: string;
  assignedBy?: string;
  assignedAt?: string;
  resolvedBy?: string;
  resolvedAt?: string;
  resolutionNotes?: string;
  
  // Metadata
  tags: string[];
  customData?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface BugWatchErrorGroup {
  id: string;
  projectId: string;
  errorHash: string;
  fingerprint: string;
  
  // Group details
  title: string;
  message: string;
  type: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  status: ErrorStatus;
  
  // Aggregated data
  totalOccurrences: number;
  uniqueUsers: number;
  firstSeen: string;
  lastSeen: string;
  
  // Representative error
  latestError: BugWatchError;
  
  // Assignment
  assignedTo?: string;
  
  createdAt: string;
  updatedAt: string;
}

export interface BugWatchProject {
  id: string;
  organizationId: string;
  appId: string;
  
  // Project details
  name: string;
  description?: string;
  language: ProgrammingLanguage;
  framework?: FrameworkType;
  
  // Configuration
  apiKey: string;
  isActive: boolean;
  retentionDays: number;
  
  // Settings
  settings: {
    autoAssignment: boolean;
    alertThreshold: number;
    ignoredErrors: string[];
    customFields: Record<string, any>;
  };
  
  // Statistics
  stats: {
    totalErrors: number;
    criticalErrors: number;
    resolvedErrors: number;
    errorRate: number;
    lastErrorAt?: string;
  };
  
  createdAt: string;
  updatedAt: string;
}

export interface AlertRule {
  id: string;
  projectId: string;
  
  // Rule details
  name: string;
  description?: string;
  isActive: boolean;
  
  // Conditions
  conditions: {
    severity?: ErrorSeverity[];
    category?: ErrorCategory[];
    environment?: Environment[];
    errorRate?: {
      threshold: number;
      timeWindow: number; // minutes
    };
    occurrenceCount?: {
      threshold: number;
      timeWindow: number; // minutes
    };
  };
  
  // Actions
  actions: {
    email?: {
      recipients: string[];
      template?: string;
    };
    webhook?: {
      url: string;
      headers?: Record<string, string>;
    };
    slack?: {
      channel: string;
      webhook: string;
    };
  };
  
  // Escalation
  escalation?: {
    enabled: boolean;
    timeToEscalate: number; // minutes
    escalateTo: string[];
  };
  
  createdAt: string;
  updatedAt: string;
}

export interface ErrorAnalytics {
  projectId: string;
  timeRange: {
    start: string;
    end: string;
  };
  
  // Overview metrics
  totalErrors: number;
  newErrors: number;
  resolvedErrors: number;
  errorRate: number;
  affectedUsers: number;
  
  // Trends
  errorTrend: {
    timestamp: string;
    count: number;
    severity: Record<ErrorSeverity, number>;
  }[];
  
  // Distribution
  severityDistribution: Record<ErrorSeverity, number>;
  categoryDistribution: Record<ErrorCategory, number>;
  environmentDistribution: Record<Environment, number>;
  
  // Top errors
  topErrors: {
    errorHash: string;
    message: string;
    count: number;
    affectedUsers: number;
    severity: ErrorSeverity;
  }[];
  
  // Performance metrics
  responseTime: {
    average: number;
    p95: number;
    p99: number;
  };
  
  // Resolution metrics
  resolutionTime: {
    average: number; // hours
    median: number;
    fastest: number;
    slowest: number;
  };
}
